import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  useTheme,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  CalculateOutlined as CalculateIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Functions as FunctionsIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';
import { StatsCard } from '../UI';
import jStat from 'jstat';

// Matched Case-Control Calculator class with statistical methods
class MatchedCaseControlUtils {
  // Matched Odds Ratio (McNemar's OR)
  static matchedOddsRatio(b: number, c: number) {
    if (b === 0 && c === 0) return { or: undefined, lower: undefined, upper: undefined };
    
    // If either b or c is 0, apply continuity correction
    const bAdj = b === 0 ? 0.5 : b;
    const cAdj = c === 0 ? 0.5 : c;
    
    const or = bAdj / cAdj;
    const logOR = Math.log(or);
    const se = Math.sqrt(1/bAdj + 1/cAdj);
    
    return {
      or: or,
      lower: Math.exp(logOR - 1.96*se),
      upper: Math.exp(logOR + 1.96*se)
    };
  }

  // McNemar's Test (with continuity correction)
  static mcnemarTest(b: number, c: number) {
    const n = b + c;
    if (n === 0) return { chi2: undefined, pChi2: undefined, pExact: undefined };
    
    // Chi-square version with continuity correction
    const chi2 = Math.pow(Math.abs(b - c) - 1, 2) / n;
    const pChi2 = this.chiSquarePValue(chi2, 1);

    // Exact binomial version
    const pExact = this.exactMcNemarPValue(b, c);
    
    return { chi2, pChi2, pExact };
  }

  // Sensitivity/Specificity Calculations
  static diagnosticMetrics(tp: number, fp: number, fn: number, tn: number) {
    // Avoid division by zero
    if (tp + fn === 0 || tn + fp === 0 || tp + fp === 0 || tn + fn === 0) {
      return {
        sensitivity: { estimate: undefined, lower: undefined, upper: undefined },
        specificity: { estimate: undefined, lower: undefined, upper: undefined },
        ppv: { estimate: undefined, lower: undefined, upper: undefined },
        npv: { estimate: undefined, lower: undefined, upper: undefined }
      };
    }
    
    const sensitivity = tp / (tp + fn);
    const specificity = tn / (tn + fp);
    const ppv = tp / (tp + fp);
    const npv = tn / (tn + fn);
    
    return {
      sensitivity: this.withCI(sensitivity, tp + fn),
      specificity: this.withCI(specificity, tn + fp),
      ppv: this.withCI(ppv, tp + fp),
      npv: this.withCI(npv, tn + fn)
    };
  }

  // Helper methods
  static exactMcNemarPValue(b: number, c: number) {
    const n = b + c;
    if (n === 0) return undefined;
    
    // If b and c are equal, the p-value is 1 (no difference)
    if (b === c) {
      return 1.0;
    }

    // For large n, use normal approximation
    if (n > 20) {
      const z = Math.abs(b - c) / Math.sqrt(n);
      return 2 * (1 - jStat.normal.cdf(z, 0, 1));
    }
    
    // For small n, calculate exact p-value
    let p = 0;
    const k = Math.min(b, c);
    for (let i = 0; i <= k; i++) {
      p += this.binomialPMF(n, i, 0.5);
    }
    return 2 * p; // Two-tailed
  }

  static binomialPMF(n: number, k: number, p: number) {
    return this.binomialCoefficient(n, k) * Math.pow(p, k) * Math.pow(1 - p, n - k);
  }

  static binomialCoefficient(n: number, k: number) {
    if (k < 0 || k > n) return 0;
    if (k === 0 || k === n) return 1;
    
    // Use logarithms for large numbers to avoid overflow
    if (n > 20) {
      let result = 0;
      for (let i = n - k + 1; i <= n; i++) {
        result += Math.log(i);
      }
      for (let i = 1; i <= k; i++) {
        result -= Math.log(i);
      }
      return Math.exp(result);
    }
    
    // Direct calculation for small numbers
    let result = 1;
    for (let i = 1; i <= k; i++) {
      result *= (n - (k - i));
      result /= i;
    }
    return result;
  }

  static withCI(p: number, n: number) {
    if (p === undefined || n === 0) {
      return { estimate: undefined, lower: undefined, upper: undefined };
    }
    
    // Wilson score interval for proportions
    const z = 1.96; // 95% CI
    const z2 = z * z;
    const term1 = p + z2/(2*n);
    const term2 = z * Math.sqrt((p*(1-p) + z2/(4*n))/n);
    const denominator = 1 + z2/n;
    
    return {
      estimate: p,
      lower: Math.max(0, (term1 - term2) / denominator),
      upper: Math.min(1, (term1 + term2) / denominator)
    };
  }

  static chiSquarePValue(chi2: number, df: number) {
    // Use jStat for accurate p-value calculation from chi-square distribution
    if (chi2 < 0 || df < 1) return undefined;
    return 1 - jStat.chisquare.cdf(chi2, df);
  }
}

const MatchedCaseControlCalculator: React.FC = () => {
  const theme = useTheme();

  // State for 2x2 table values
  const [cellValues, setCellValues] = useState({
    a: 0, // Case exposed, Control exposed
    b: 0, // Case exposed, Control unexposed
    c: 0, // Case unexposed, Control exposed
    d: 0  // Case unexposed, Control unexposed
  });

  // Function to render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // State for calculation results
  const [results, setResults] = useState<{
    matchedOR?: { or: number | undefined; lower: number | undefined; upper: number | undefined };
    mcnemar?: { chi2: number | undefined; pChi2: number | undefined; pExact: number | undefined };
    diagnostics?: {
      sensitivity: { estimate: number | undefined; lower: number | undefined; upper: number | undefined };
      specificity: { estimate: number | undefined; lower: number | undefined; upper: number | undefined };
      ppv: { estimate: number | undefined; lower: number | undefined; upper: number | undefined };
      npv: { estimate: number | undefined; lower: number | undefined; upper: number | undefined };
    };
  }>({});

  // Handle input changes for 2x2 table
  const handleInputChange = (cell: 'a' | 'b' | 'c' | 'd', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setCellValues(prev => ({
      ...prev,
      [cell]: isNaN(numValue) ? 0 : numValue
    }));
  };

  // Calculate results
  const calculateResults = () => {
    try {
      const { a, b, c, d } = cellValues;
      const total = a + b + c + d;

      if (total === 0) return;

      // Calculate matched odds ratio
      const matchedOR = MatchedCaseControlUtils.matchedOddsRatio(b, c);
      
      // Calculate McNemar's test
      const mcnemar = MatchedCaseControlUtils.mcnemarTest(b, c);
      
      // Calculate diagnostic metrics
      // For matched case-control, we interpret the cells differently:
      // a = true positives (both case and control exposed)
      // b = false positives (case exposed, control unexposed)
      // c = false negatives (case unexposed, control exposed)
      // d = true negatives (neither case nor control exposed)
      const diagnostics = MatchedCaseControlUtils.diagnosticMetrics(a, b, c, d);

      setResults({
        matchedOR,
        mcnemar,
        diagnostics
      });
    } catch (error) {
      console.error('Error calculating results:', error);
    }
  };

  // Reset form
  const resetForm = () => {
    setCellValues({ a: 0, b: 0, c: 0, d: 0 });
    setResults({});
  };

  // Format number with specified decimal places
  const formatNumber = (num: number | undefined, decimals: number = 3) => {
    if (num === undefined || isNaN(num)) return 'N/A';
    return num.toFixed(decimals);
  };

  // Format confidence interval
  const formatCI = (lower: number | undefined, upper: number | undefined, decimals: number = 3) => {
    if (lower === undefined || upper === undefined || isNaN(lower) || isNaN(upper)) return 'N/A';
    return `${formatNumber(lower, decimals)} - ${formatNumber(upper, decimals)}`;
  };

  // Format percentage with specified decimal places
  const formatPercent = (num: number | undefined, decimals: number = 1) => {
    if (num === undefined || isNaN(num)) return 'N/A';
    return `${(num * 100).toFixed(decimals)}%`;
  };

  // Format p-value
  const formatPValue = (p: number | undefined) => {
    if (p === undefined || isNaN(p)) return 'N/A';
    if (p < 0.001) return '< 0.001';
    return p.toFixed(4);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Matched Case-Control Calculator</Typography>
      <Typography variant="body1" paragraph>
        Calculate matched odds ratios and McNemar's test for matched case-control studies.
      </Typography>
      
      {/* Formulas Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="formulas-content"
          id="formulas-header"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FunctionsIcon color="primary" />
            <Typography variant="h6">Mathematical Formulas</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            {/* Matched Odds Ratio */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Matched Odds Ratio (McNemar's OR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('OR_{matched} = \\frac{b}{c}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = OR_{matched} \\times \\exp(\\pm 1.96 \\times SE_{\\ln(OR)})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(OR)} = \\sqrt{\\frac{1}{b} + \\frac{1}{c}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: b = discordant pairs (case exposed, control unexposed), c = discordant pairs (case unexposed, control exposed)
                </Typography>
              </Paper>
            </Grid>

            {/* McNemar's Test */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  McNemar's Test
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Without Continuity Correction:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2 = \\frac{(b - c)^2}{b + c}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>With Continuity Correction:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2 = \\frac{(|b - c| - 1)^2}{b + c}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Tests the null hypothesis that the marginal probabilities are equal (no association)
                </Typography>
              </Paper>
            </Grid>

            {/* Exact McNemar Test */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Exact McNemar Test
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('P = 2 \\times \\sum_{i=0}^{\\min(b,c)} \\binom{b+c}{i} \\times 0.5^{b+c}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Uses binomial distribution for exact p-value calculation when sample sizes are small
                </Typography>
              </Paper>
            </Grid>

            {/* Diagnostic Test Measures */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Diagnostic Test Performance Measures
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Sensitivity (True Positive Rate):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('Sensitivity = \\frac{a}{a + c} = \\frac{\\text{True Positives}}{\\text{True Positives + False Negatives}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Specificity (True Negative Rate):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('Specificity = \\frac{d}{b + d} = \\frac{\\text{True Negatives}}{\\text{True Negatives + False Positives}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Positive Predictive Value (PPV):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('PPV = \\frac{a}{a + b} = \\frac{\\text{True Positives}}{\\text{True Positives + False Positives}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Negative Predictive Value (NPV):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('NPV = \\frac{d}{c + d} = \\frac{\\text{True Negatives}}{\\text{True Negatives + False Negatives}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  These measures evaluate the performance of diagnostic tests or screening procedures
                </Typography>
              </Paper>
            </Grid>

            {/* Likelihood Ratios */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Likelihood Ratios
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Positive Likelihood Ratio (LR+):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('LR+ = \\frac{Sensitivity}{1 - Specificity} = \\frac{\\frac{a}{a+c}}{\\frac{b}{b+d}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Negative Likelihood Ratio (LR-):</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('LR- = \\frac{1 - Sensitivity}{Specificity} = \\frac{\\frac{c}{a+c}}{\\frac{d}{b+d}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Likelihood ratios indicate how much a test result changes the probability of having the condition
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>2×2 Contingency Table for Matched Pairs</Typography>
        <Typography variant="body2" paragraph>
          Enter the values for your matched pairs to calculate epidemiological measures.
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <TableContainer component={Paper} variant="outlined">
              <Table aria-label="2x2 contingency table for matched pairs">
                <TableHead>
                  <TableRow>
                    <TableCell></TableCell>
                    <TableCell align="center" colSpan={2}>
                      Control
                      <Tooltip title="The matched control subject in each pair">
                        <IconButton size="small">
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell></TableCell>
                    <TableCell align="center">Exposed</TableCell>
                    <TableCell align="center">Unexposed</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row" rowSpan={2}>
                      Case
                      <Tooltip title="The case subject in each matched pair">
                        <IconButton size="small">
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.a || ''}
                        onChange={(e) => handleInputChange('a', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        Cell a: Both exposed
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.b || ''}
                        onChange={(e) => handleInputChange('b', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        Cell b: Case exposed, Control unexposed
                      </Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.c || ''}
                        onChange={(e) => handleInputChange('c', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        Cell c: Case unexposed, Control exposed
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.d || ''}
                        onChange={(e) => handleInputChange('d', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        Cell d: Both unexposed
                      </Typography>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CalculateIcon />}
                onClick={calculateResults}
                disabled={Object.values(cellValues).every(val => val === 0)}
              >
                Calculate
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetForm}
              >
                Reset
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Enter the values in the 2×2 table and click Calculate to compute epidemiological measures.
            </Typography>
            <Typography variant="body2" sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <strong>Note:</strong> For matched case-control studies, only the discordant pairs (cells b and c) are used to calculate the matched odds ratio and McNemar's test.
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Results Section */}
      {Object.keys(results).length > 0 && (
        <Box sx={{ mt: 4 }}>
          <Divider sx={{ mb: 3 }} />
          <Typography variant="h6" gutterBottom>Results</Typography>
          
          <Grid container spacing={3}>
            {/* Matched Odds Ratio */}
            {results.matchedOR && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Matched Odds Ratio"
                  value={formatNumber(results.matchedOR.or)}
                  description={`95% CI: ${formatCI(results.matchedOR.lower, results.matchedOR.upper)}`}
                  color="primary"
                  variant="outlined"
                  tooltip="The odds ratio for matched case-control data, calculated using discordant pairs"
                />
              </Grid>
            )}
            
            {/* McNemar's Test */}
            {results.mcnemar && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="McNemar's Test"
                  value={`χ² = ${formatNumber(results.mcnemar.chi2, 2)}`}
                  description={`p-value: ${formatPValue(results.mcnemar.pChi2)}`}
                  color="info"
                  variant="outlined"
                  tooltip="McNemar's chi-square test for paired data with continuity correction"
                />
              </Grid>
            )}
            
            {/* Exact McNemar's Test */}
            {results.mcnemar && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Exact McNemar's Test"
                  value={`p = ${formatPValue(results.mcnemar.pExact)}`}
                  description="Binomial exact test for paired data"
                  color="info"
                  variant="outlined"
                  tooltip="Exact binomial test for paired data, recommended for small sample sizes"
                />
              </Grid>
            )}
            
            {/* Sensitivity */}
            {results.diagnostics && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Sensitivity"
                  value={formatPercent(results.diagnostics.sensitivity.estimate)}
                  description={`95% CI: ${formatPercent(results.diagnostics.sensitivity.lower)} - ${formatPercent(results.diagnostics.sensitivity.upper)}`}
                  color="success"
                  variant="outlined"
                  tooltip="The proportion of true positives that are correctly identified by the test"
                />
              </Grid>
            )}
            
            {/* Specificity */}
            {results.diagnostics && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Specificity"
                  value={formatPercent(results.diagnostics.specificity.estimate)}
                  description={`95% CI: ${formatPercent(results.diagnostics.specificity.lower)} - ${formatPercent(results.diagnostics.specificity.upper)}`}
                  color="success"
                  variant="outlined"
                  tooltip="The proportion of true negatives that are correctly identified by the test"
                />
              </Grid>
            )}
            
            {/* Positive Predictive Value */}
            {results.diagnostics && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Positive Predictive Value"
                  value={formatPercent(results.diagnostics.ppv.estimate)}
                  description={`95% CI: ${formatPercent(results.diagnostics.ppv.lower)} - ${formatPercent(results.diagnostics.ppv.upper)}`}
                  color="warning"
                  variant="outlined"
                  tooltip="The proportion of positive test results that are true positives"
                />
              </Grid>
            )}
            
            {/* Negative Predictive Value */}
            {results.diagnostics && (
              <Grid item xs={12} sm={6} md={4}>
                <StatsCard
                  title="Negative Predictive Value"
                  value={formatPercent(results.diagnostics.npv.estimate)}
                  description={`95% CI: ${formatPercent(results.diagnostics.npv.lower)} - ${formatPercent(results.diagnostics.npv.upper)}`}
                  color="warning"
                  variant="outlined"
                  tooltip="The proportion of negative test results that are true negatives"
                />
              </Grid>
            )}
          </Grid>
          
          {/* Interpretation Guidelines */}
          <Paper elevation={1} sx={{ p: 3, mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>Interpretation Guidelines</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>Matched Odds Ratio</Typography>
                <Typography variant="body2" paragraph dangerouslySetInnerHTML={{ __html: `
                  • OR = 1: No association between exposure and disease<br />
                  • OR > 1: Positive association (exposure may increase risk)<br />
                  • OR < 1: Negative association (exposure may decrease risk)<br />
                  • Check if the confidence interval includes 1 to assess statistical significance
                `}} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>McNemar's Test</Typography>
                <Typography variant="body2" paragraph dangerouslySetInnerHTML={{ __html: `
                  • Tests the null hypothesis that the proportions of discordant pairs are equal<br />
                  • p < 0.05: Reject null hypothesis (significant difference between pairs)<br />
                  • Use the exact test when the number of discordant pairs is small (< 20)
                `}} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>Sensitivity & Specificity</Typography>
                <Typography variant="body2" paragraph dangerouslySetInnerHTML={{ __html: `
                  • Sensitivity: Ability to correctly identify those with the disease<br />
                  • Specificity: Ability to correctly identify those without the disease<br />
                  • Higher values indicate better test performance
                `}} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>Predictive Values</Typography>
                <Typography variant="body2" paragraph dangerouslySetInnerHTML={{ __html: `
                  • PPV: Probability that subjects with a positive test truly have the disease<br />
                  • NPV: Probability that subjects with a negative test truly don't have the disease<br />
                  • These values are affected by disease prevalence in the population
                `}} />
              </Grid>
            </Grid>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default MatchedCaseControlCalculator;
