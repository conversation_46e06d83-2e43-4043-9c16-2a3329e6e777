import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Pagination,
  Tooltip,
  Switch,
  FormControlLabel,
  TableSortLabel
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Refresh as RefreshIcon,
  CardMembership as OverrideIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { useTableTheme } from '../../hooks';
import { OverrideStatusBadge } from '../OverrideStatus';
import QuickOverrideDialog from './QuickOverrideDialog';

interface User {
  id: string;
  email: string | null; // Made nullable since it might not be available
  username: string | null;
  full_name: string | null;
  institution: string | null;
  country: string | null;
  avatar_url: string | null;
  updated_at: string | null; // Made nullable for safety
  is_admin: boolean | null; // Made nullable for safety
  accounttype: string | null;
  created_at: string | null; // Made nullable since it's a fallback field
  last_sign_in_at: string | null;
}

interface EditUserDialogProps {
  open: boolean;
  user: User | null;
  onClose: () => void;
  onSave: (userId: string, updates: { accounttype: string; is_admin: boolean }) => Promise<void>;
}

const EditUserDialog: React.FC<EditUserDialogProps> = ({ open, user, onClose, onSave }) => {
  const [accountType, setAccountType] = useState<string>('standard');
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setAccountType(user.accounttype || 'standard');
      setIsAdmin(user.is_admin || false);
    }
  }, [user]);

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      await onSave(user.id, { accounttype: accountType, is_admin: isAdmin });
      onClose();
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Edit User: {user?.full_name || user?.email}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Account Type</InputLabel>
            <Select
              value={accountType}
              label="Account Type"
              onChange={(e) => setAccountType(e.target.value)}
            >
              <MenuItem value="standard">Standard</MenuItem>
              <MenuItem value="pro">Pro</MenuItem>
              <MenuItem value="edu">Educational</MenuItem>
              <MenuItem value="edu_pro">Educational Pro</MenuItem>
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Switch
                checked={isAdmin}
                onChange={(e) => setIsAdmin(e.target.checked)}
                color="error"
              />
            }
            label="Admin Privileges"
            sx={{ mb: 2 }}
          />

          {isAdmin && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> Admin privileges grant full system access. 
                Only assign to trusted users.
              </Typography>
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={saving}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={saving}
          startIcon={saving ? <CircularProgress size={16} /> : undefined}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

type SortField = 'full_name' | 'email' | 'accounttype' | 'is_admin' | 'institution' | 'created_at' | 'last_sign_in_at';
type SortDirection = 'asc' | 'desc';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [quickOverrideDialogOpen, setQuickOverrideDialogOpen] = useState(false);
  const [selectedOverrideUser, setSelectedOverrideUser] = useState<User | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const pageSize = 25;

  useEffect(() => {
    fetchUsers();
  }, [page, searchTerm, sortField, sortDirection]);

  // Client-side sorting fallback
  const sortUsers = (users: User[], field: SortField, direction: SortDirection): User[] => {
    return [...users].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];
      
      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';
      
      // Convert to strings for comparison (except for dates and booleans)
      if (field === 'created_at' || field === 'last_sign_in_at') {
        aValue = new Date(aValue || 0).getTime();
        bValue = new Date(bValue || 0).getTime();
      } else if (field === 'is_admin') {
        aValue = aValue ? 1 : 0;
        bValue = bValue ? 1 : 0;
      } else {
        aValue = String(aValue).toLowerCase();
        bValue = String(bValue).toLowerCase();
      }
      
      if (direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setPage(1); // Reset to first page when sorting
  };

  const fetchUsers = async (retryCount = 0) => {
    try {
      if (retryCount === 0) {
        setLoading(true);
      }
      setError(null);

      console.log('🔄 Fetching users with params:', {
        page_size: pageSize,
        page_offset: (page - 1) * pageSize,
        search_term: searchTerm || null,
        sort_field: sortField,
        sort_direction: sortDirection
      });

      const offset = (page - 1) * pageSize;
      const { data, error } = await supabase.rpc('get_all_users', {
        page_size: pageSize,
        page_offset: offset,
        search_term: searchTerm || null
      });

      if (error) {
        console.error('❌ Database function error:', error);
        throw error;
      }

      console.log('📊 Users data received:', data);
      
      // Apply client-side sorting since database function doesn't support it yet
      const sortedData = data ? sortUsers(data, sortField, sortDirection) : [];
      setUsers(sortedData);

      // Calculate total pages (this is a simplified approach)
      // In a real implementation, you'd want a separate count query
      setTotalPages(Math.max(1, Math.ceil((data?.length || 0) / pageSize)));
      console.log('✅ Users loaded successfully');
    } catch (err: any) {
      console.error('Error fetching users:', err);

      // Retry logic for connection issues
      if (retryCount < 2 && (err.message?.includes('network') || err.message?.includes('connection'))) {
        console.log(`🔄 Retrying users fetch (attempt ${retryCount + 1})`);
        setTimeout(() => fetchUsers(retryCount + 1), 1000 * (retryCount + 1));
        return;
      }

      setError(err.message || 'Failed to load users. Please try refreshing the page.');
    } finally {
      if (retryCount === 0) {
        setLoading(false);
      }
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleSaveUser = async (userId: string, updates: { accounttype: string; is_admin: boolean }) => {
    try {
      // Update account type
      const { error: accountError } = await supabase.rpc('update_user_account_type', {
        target_user_id: userId,
        new_account_type: updates.accounttype
      });

      if (accountError) {
        throw accountError;
      }

      // Update admin status
      const { error: adminError } = await supabase.rpc('update_user_admin_status', {
        target_user_id: userId,
        new_admin_status: updates.is_admin
      });

      if (adminError) {
        throw adminError;
      }

      // Refresh users list
      await fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  };

  const handleQuickOverride = (user: User) => {
    setSelectedOverrideUser(user);
    setQuickOverrideDialogOpen(true);
  };

  const handleQuickOverrideSuccess = () => {
    // Refresh the users list to update override status
    fetchUsers();
    // Trigger refresh for all OverrideStatusBadge components
    setRefreshTrigger(prev => prev + 1);
  };

  const getAccountTypeChip = (accountType: string | null) => {
    const type = accountType || 'standard';
    const config = {
      standard: { label: 'Standard', color: 'default' as const, icon: <PersonIcon /> },
      pro: { label: 'Pro', color: 'warning' as const, icon: <StarIcon /> },
      edu: { label: 'Educational', color: 'info' as const, icon: <SchoolIcon /> },
      edu_pro: { label: 'Edu Pro', color: 'secondary' as const, icon: <SchoolIcon /> }
    };

    const { label, color, icon } = config[type as keyof typeof config] || config.standard;

    return (
      <Chip
        label={label}
        color={color}
        size="small"
        icon={icon}
        variant="outlined"
      />
    );
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not available';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && users.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading users...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* Header */}
      <Box sx={{
        mb: 3,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Manage user accounts, permissions, and account types
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => {
            fetchUsers();
            setRefreshTrigger(prev => prev + 1);
          }}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search users by name, email, or institution..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: { xs: '100%', sm: 500 } }}
          size="small"
        />
      </Box>

      {/* Users Table */}
      <Box sx={{ width: '100%', overflow: 'auto' }}>
        <TableContainer
          component={Paper}
          variant="outlined"
          sx={{
            borderRadius: 2,
            maxHeight: { xs: '60vh', lg: '70vh' },
            minWidth: 800, // Ensure table doesn't get too cramped
            transition: 'max-height 0.3s ease-in-out'
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'full_name'}
                    direction={sortField === 'full_name' ? sortDirection : 'asc'}
                    onClick={() => handleSort('full_name')}
                  >
                    User
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'accounttype'}
                    direction={sortField === 'accounttype' ? sortDirection : 'asc'}
                    onClick={() => handleSort('accounttype')}
                  >
                    Account Type
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Override Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'is_admin'}
                    direction={sortField === 'is_admin' ? sortDirection : 'asc'}
                    onClick={() => handleSort('is_admin')}
                  >
                    Admin
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'institution'}
                    direction={sortField === 'institution' ? sortDirection : 'asc'}
                    onClick={() => handleSort('institution')}
                  >
                    Institution
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'created_at'}
                    direction={sortField === 'created_at' ? sortDirection : 'asc'}
                    onClick={() => handleSort('created_at')}
                  >
                    Created
                  </TableSortLabel>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>
                  <TableSortLabel
                    active={sortField === 'last_sign_in_at'}
                    direction={sortField === 'last_sign_in_at' ? sortDirection : 'asc'}
                    onClick={() => handleSort('last_sign_in_at')}
                  >
                    Last Sign In
                  </TableSortLabel>
                </TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell sx={{ minWidth: 200 }}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold" sx={{ wordBreak: 'break-word' }}>
                        {user.full_name || 'No name'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                        {user.email || user.username || 'No email available'}
                      </Typography>
                      {user.username && user.username !== user.email && (
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ wordBreak: 'break-word' }}>
                          @{user.username}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell sx={{ minWidth: 120 }}>
                    {getAccountTypeChip(user.accounttype)}
                  </TableCell>
                  <TableCell sx={{ minWidth: 150 }}>
                    <OverrideStatusBadge
                      variant="chip"
                      userId={user.id}
                      showDetails={false}
                      size="small"
                      refreshTrigger={refreshTrigger}
                    />
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    {user.is_admin === true ? (
                      <Chip
                        label="Admin"
                        color="error"
                        size="small"
                        icon={<SecurityIcon />}
                        variant="outlined"
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        User
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ minWidth: 150 }}>
                    <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                      {user.institution || 'Not specified'}
                    </Typography>
                    {user.country && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {user.country}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    <Typography variant="body2">
                      {formatDate(user.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ minWidth: 100 }}>
                    <Typography variant="body2">
                      {formatDate(user.last_sign_in_at)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center" sx={{ minWidth: 120 }}>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="Edit User">
                        <IconButton
                          size="small"
                          onClick={() => handleEditUser(user)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Manage Override">
                        <IconButton
                          size="small"
                          onClick={() => handleQuickOverride(user)}
                          color="secondary"
                        >
                          <OverrideIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={(_, newPage) => setPage(newPage)}
            color="primary"
          />
        </Box>
      )}

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editDialogOpen}
        user={selectedUser}
        onClose={() => {
          setEditDialogOpen(false);
          setSelectedUser(null);
        }}
        onSave={handleSaveUser}
      />

      {/* Quick Override Dialog */}
      <QuickOverrideDialog
        open={quickOverrideDialogOpen}
        user={selectedOverrideUser}
        onClose={() => {
          setQuickOverrideDialogOpen(false);
          setSelectedOverrideUser(null);
        }}
        onSuccess={handleQuickOverrideSuccess}
      />
    </Box>
  );
};

export default UserManagement;
