import { supabase } from './supabaseClient';
import { adminLogger, logAdminStats } from './adminLogger';

export interface SystemStats {
  total_users: number;
  total_profiles: number;
  admin_users: number;
  standard_users: number;
  pro_users: number;
  edu_users: number;
  edu_pro_users: number;
  users_with_datasets: number;
  total_datasets: number;
  users_last_7_days: number;
  users_last_30_days: number;
  active_users_last_7_days: number;
  active_users_last_30_days: number;
}

/**
 * Enhanced function to get user statistics with multiple fallback strategies
 * This function tries multiple approaches to ensure data is always available
 */
export const getEnhancedUserStatistics = async (): Promise<SystemStats> => {
  const startTime = Date.now();
  logAdminStats.fetchStart('enhanced');
  let lastError: any = null;
  
  // Strategy 1: Try the enhanced database function first
  try {
    adminLogger.databaseQuery('get_enhanced_user_statistics', 'start');
    const { data: enhancedStats, error: enhancedError } = await supabase.rpc('get_enhanced_user_statistics');
    
    if (enhancedError) {
      adminLogger.databaseQuery('get_enhanced_user_statistics', 'error', undefined, enhancedError);
      lastError = enhancedError;
    } else if (enhancedStats) {
      adminLogger.databaseQuery('get_enhanced_user_statistics', 'success', enhancedStats);
      adminLogger.performance('get_enhanced_user_statistics', Date.now() - startTime, enhancedStats);
      logAdminStats.fetchSuccess('enhanced', enhancedStats);
      return enhancedStats;
    }
  } catch (error: any) {
    adminLogger.databaseQuery('get_enhanced_user_statistics', 'error', undefined, error);
    lastError = error;
  }
  
  // Strategy 2: Try the original database function
  try {
    adminLogger.databaseQuery('get_user_statistics', 'start');
    const { data: dbStats, error: dbError } = await supabase.rpc('get_user_statistics');
    
    if (dbError) {
      adminLogger.databaseQuery('get_user_statistics', 'error', undefined, dbError);
      lastError = dbError;
    } else if (dbStats) {
      adminLogger.databaseQuery('get_user_statistics', 'success', dbStats);
      
      // Supplement with additional data if needed
      adminLogger.info('STATS', 'Supplementing original stats with additional data');
      const supplementalStats = await getSupplementalStats();
      const enhancedStats = {
        ...dbStats,
        ...supplementalStats
      };
      
      adminLogger.performance('get_user_statistics_enhanced', Date.now() - startTime, enhancedStats);
      logAdminStats.fetchSuccess('original-enhanced', enhancedStats);
      return enhancedStats;
    }
  } catch (error: any) {
    adminLogger.databaseQuery('get_user_statistics', 'error', undefined, error);
    lastError = error;
  }
  
  // Strategy 3: Fallback to client-side calculations
  try {
    adminLogger.info('STATS', 'Falling back to client-side calculations');
    const clientStats = await getClientSideStatistics();
    adminLogger.performance('client_side_statistics', Date.now() - startTime, clientStats);
    logAdminStats.fetchSuccess('client-side', clientStats);
    return clientStats;
  } catch (error: any) {
    adminLogger.error('STATS', 'Client-side calculation failed', error);
    lastError = error;
  }
  
  // Strategy 4: Return minimal safe defaults
  adminLogger.error('STATS', 'All strategies failed, returning safe defaults', lastError);
  logAdminStats.fetchError('all-strategies', lastError);
  
  const safeDefaults = {
    total_users: 0,
    total_profiles: 0,
    admin_users: 0,
    standard_users: 0,
    pro_users: 0,
    edu_users: 0,
    edu_pro_users: 0,
    users_with_datasets: 0,
    total_datasets: 0,
    users_last_7_days: 0,
    users_last_30_days: 0,
    active_users_last_7_days: 0,
    active_users_last_30_days: 0
  };
  
  adminLogger.performance('safe_defaults_fallback', Date.now() - startTime, safeDefaults);
  return safeDefaults;
};

/**
 * Get supplemental statistics that can help fill in missing data
 * Includes retry logic and graceful error handling
 */
const getSupplementalStats = async (retryCount = 0): Promise<Partial<SystemStats>> => {
  const stats: Partial<SystemStats> = {};
  const maxRetries = 2;
  
  try {
    // Try to get some time-based data from user_datasets (which has created_at)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // Count datasets created in the last 7 days as a proxy for user activity
    const { count: recentDatasets7d, error: error7d } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', sevenDaysAgo.toISOString());
    
    if (error7d) {
      console.warn('Error getting 7-day dataset count:', error7d);
    } else if (recentDatasets7d !== null) {
      stats.active_users_last_7_days = recentDatasets7d;
      stats.users_last_7_days = recentDatasets7d;
    }
    
    const { count: recentDatasets30d, error: error30d } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', thirtyDaysAgo.toISOString());
    
    if (error30d) {
      console.warn('Error getting 30-day dataset count:', error30d);
    } else if (recentDatasets30d !== null) {
      stats.active_users_last_30_days = recentDatasets30d;
      stats.users_last_30_days = recentDatasets30d;
    }
    
    // Try to get total dataset count and users with datasets
    const { count: totalDatasets, error: totalError } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true });
    
    if (totalError) {
      console.warn('Error getting total dataset count:', totalError);
    } else if (totalDatasets !== null) {
      stats.total_datasets = totalDatasets;
    }
    
    // Try to get unique users with datasets
    const { data: uniqueUsers, error: uniqueError } = await supabase
      .from('user_datasets')
      .select('user_id')
      .not('user_id', 'is', null);
    
    if (uniqueError) {
      console.warn('Error getting unique users with datasets:', uniqueError);
    } else if (uniqueUsers) {
      const uniqueUserIds = new Set(uniqueUsers.map(u => u.user_id));
      stats.users_with_datasets = uniqueUserIds.size;
    }
    
    console.log('📊 Supplemental stats:', stats);
    
  } catch (error: any) {
    console.error('Error getting supplemental stats:', error);
    
    // Retry logic for network/connection issues
    if (retryCount < maxRetries && (
      error.message?.includes('network') ||
      error.message?.includes('connection') ||
      error.message?.includes('timeout')
    )) {
      console.log(`🔄 Retrying supplemental stats (attempt ${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return getSupplementalStats(retryCount + 1);
    }
    
    // Return partial stats with defaults for failed queries
    return {
      total_datasets: stats.total_datasets || 0,
      users_with_datasets: stats.users_with_datasets || 0,
      active_users_last_7_days: stats.active_users_last_7_days || 0,
      active_users_last_30_days: stats.active_users_last_30_days || 0,
      users_last_7_days: stats.users_last_7_days || 0,
      users_last_30_days: stats.users_last_30_days || 0
    };
  }
  
  return stats;
};

/**
 * Fallback function to calculate all statistics client-side
 */
const getClientSideStatistics = async (): Promise<SystemStats> => {
  const stats: SystemStats = {
    total_users: 0,
    total_profiles: 0,
    admin_users: 0,
    standard_users: 0,
    pro_users: 0,
    edu_users: 0,
    edu_pro_users: 0,
    users_with_datasets: 0,
    total_datasets: 0,
    users_last_7_days: 0,
    users_last_30_days: 0,
    active_users_last_7_days: 0,
    active_users_last_30_days: 0
  };
  
  try {
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('accounttype, is_admin');
    
    if (profilesError) throw profilesError;
    
    if (profiles) {
      stats.total_users = profiles.length;
      stats.total_profiles = profiles.length;
      
      // Count by account type
      profiles.forEach(profile => {
        if (profile.is_admin) stats.admin_users++;
        
        switch (profile.accounttype) {
          case 'pro':
            stats.pro_users++;
            break;
          case 'edu':
            stats.edu_users++;
            break;
          case 'edu_pro':
            stats.edu_pro_users++;
            break;
          default:
            stats.standard_users++;
        }
      });
    }
    
    // Get dataset statistics
    const { count: totalDatasets } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true });
    
    const { data: uniqueUsers } = await supabase
      .from('user_datasets')
      .select('user_id')
      .not('user_id', 'is', null);
    
    if (totalDatasets !== null) stats.total_datasets = totalDatasets;
    if (uniqueUsers) {
      const uniqueUserIds = new Set(uniqueUsers.map(u => u.user_id));
      stats.users_with_datasets = uniqueUserIds.size;
    }
    
    // For time-based stats, we'll use dataset creation as a proxy
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const { count: recentActivity } = await supabase
      .from('user_datasets')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', sevenDaysAgo.toISOString());
    
    if (recentActivity !== null) {
      stats.active_users_last_7_days = Math.min(recentActivity, stats.total_users);
      stats.users_last_7_days = Math.min(recentActivity, stats.total_users);
    }
    
    console.log('📊 Client-side calculated stats:', stats);
    
  } catch (error) {
    console.error('Error calculating client-side statistics:', error);
  }
  
  return stats;
};
