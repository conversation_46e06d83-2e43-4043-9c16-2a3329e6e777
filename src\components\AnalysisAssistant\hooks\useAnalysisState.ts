import { useState, useEffect, useContext } from 'react';
import { DataContext } from '../../../context/DataContext';
import { analyzeDataset, generateSmartRecommendations } from '../../../utils/dataAnalysisService';
import { AnalysisAssistantState, AnalysisSuggestion } from '../utils/types';
import { trainingDataInitializer } from '../../../utils/trainingDatabase';

/**
 * Custom hook to manage the state of the AnalysisAssistant component
 * Handles dataset analysis, smart recommendations, variable insights, and user feedback
 */
export const useAnalysisState = (): AnalysisAssistantState => {
  const { currentDataset, isDatasetLoaded } = useContext(DataContext);
  
  // Main analysis state
  const [datasetAnalysis, setDatasetAnalysis] = useState<any>(null);
  const [smartRecommendations, setSmartRecommendations] = useState<AnalysisSuggestion[]>([]);
  const [variableInsights, setVariableInsights] = useState<any>(null);
  
  // Feedback dialog state
  const [showFeedbackDialog, setShowFeedbackDialog] = useState<boolean>(false);
  const [selectedSuggestionForFeedback, setSelectedSuggestionForFeedback] = useState<AnalysisSuggestion | null>(null);
  const [feedbackRating, setFeedbackRating] = useState<number>(0);
  const [feedbackComment, setFeedbackComment] = useState<string>('');
  

  
  // Initialize training system and analyze dataset when loaded
  useEffect(() => {
    // Initialize training data
    trainingDataInitializer.initializeTrainingData();
    
    if (isDatasetLoaded && currentDataset) {
      // Analyze the dataset
      const analysis = analyzeDataset(currentDataset);
      setDatasetAnalysis(analysis);
      
      // Generate smart recommendations based on the analysis
      const recommendations = generateSmartRecommendations(analysis, currentDataset);
      setSmartRecommendations(recommendations);
      
      // Extract variable insights
      setVariableInsights({
        numericVariables: analysis.numericVariables || [],
        categoricalVariables: analysis.categoricalVariables || [],
        dateVariables: analysis.dateVariables || [],
        missingDataPatterns: analysis.missingDataPatterns || [],
        outliers: analysis.outliers || [],
        distributionAssessments: analysis.distributionAssessments || []
      });
      

    }
  }, [isDatasetLoaded, currentDataset]);
  
  // Handle feedback dialog
  const openFeedbackDialog = (suggestion: AnalysisSuggestion) => {
    setSelectedSuggestionForFeedback(suggestion);
    setShowFeedbackDialog(true);
  };
  
  const closeFeedbackDialog = () => {
    setShowFeedbackDialog(false);
    setSelectedSuggestionForFeedback(null);
    setFeedbackRating(0);
    setFeedbackComment('');
  };
  
  const submitFeedback = () => {
    if (selectedSuggestionForFeedback && feedbackRating > 0) {
      const newFeedback = {
        id: `feedback-${Date.now()}`,
        suggestionId: selectedSuggestionForFeedback.id,
        rating: feedbackRating,
        comment: feedbackComment,
        timestamp: new Date().toISOString()
      };
      
      // Handle feedback submission logic here if needed
      closeFeedbackDialog();
    }
  };
  
  return {
    // Main analysis state
    datasetAnalysis,
    smartRecommendations,
    variableInsights,
    
    // Feedback dialog state
    showFeedbackDialog,
    selectedSuggestionForFeedback,
    feedbackRating,
    feedbackComment,
    
    // State setters
    setDatasetAnalysis,
    setSmartRecommendations,
    setVariableInsights,
    setShowFeedbackDialog,
    setSelectedSuggestionForFeedback,
    setFeedbackRating,
    setFeedbackComment,
    
    // Helper functions
    openFeedbackDialog,
    closeFeedbackDialog,
    submitFeedback
  };
};