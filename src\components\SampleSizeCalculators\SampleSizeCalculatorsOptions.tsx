import React from 'react';
import { Helmet } from 'react-helmet-async';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  LooksOne as OneSampleIcon,
  LooksTwo as TwoSampleIcon,
  CompareArrows as PairedSampleIcon,
  MoreHoriz as MoreThanTwoGroupsIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface SampleSizeCalculatorOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Means' | 'Proportions' | 'Other'; // Define categories relevant to sample size
  color: string;
}

interface SampleSizeCalculatorsOptionsProps {
  onNavigate: (path: string) => void;
}

export const sampleSizeCalculatorOptions: SampleSizeCalculatorOption[] = [
  {
    name: 'One Sample Calculator',
    shortDescription: 'Calculate sample size for a single group',
    detailedDescription: 'Determine the required sample size for studies involving a single population mean or proportion.',
    path: 'samplesize/one-sample',
    icon: <OneSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#2196F3', // Blue
  },
  {
    name: 'Two Sample Calculator',
    shortDescription: 'Calculate sample size for comparing two groups',
    detailedDescription: 'Determine the required sample size for studies comparing the means or proportions of two independent groups.',
    path: 'samplesize/two-sample',
    icon: <TwoSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#4CAF50', // Green
  },
  {
    name: 'Paired Sample Calculator',
    shortDescription: 'Calculate sample size for paired or matched data',
    detailedDescription: 'Determine the required sample size for studies involving paired observations or matched subjects, such as pre-post designs.',
    path: 'samplesize/paired-sample',
    icon: <PairedSampleIcon />,
    category: 'Means', // Assuming it can be used for means or proportions
    color: '#FF9800', // Orange
  },
  {
    name: 'More Than Two Groups Calculator',
    shortDescription: 'Calculate sample size for comparing multiple groups (ANOVA)',
    detailedDescription: 'Determine the required sample size for studies comparing the means of more than two independent groups, typically used for ANOVA.',
    path: 'samplesize/more-than-two-groups',
    icon: <MoreThanTwoGroupsIcon />,
    category: 'Means', // Specifically for means (ANOVA)
    color: '#9C27B0', // Purple
  },
];

const SampleSizeCalculatorsOptions: React.FC<SampleSizeCalculatorsOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  // Sample size calculators might not need categories like Advanced Analysis,
  // but keeping the structure for consistency. Can simplify if needed.
  const [selectedCategory, setSelectedCategory] = React.useState<string>('All');

  // Define categories based on the options above or simplify
  const categories = ['All', 'Means', 'Proportions', 'Other']; // Example categories

  const filteredOptions = selectedCategory === 'All'
    ? sampleSizeCalculatorOptions
    : sampleSizeCalculatorOptions.filter(option => option.category === selectedCategory);

  // Placeholder for category icons if needed, or remove category filtering
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Means': return <CalculateIcon />; // Example icon
      case 'Proportions': return <CalculateIcon />; // Example icon
      case 'Other': return <CalculateIcon />; // Example icon
      default: return <CalculateIcon />;
    }
  };

  // Schema.org structured data for sample size calculators
  const structuredData = {
    "@context": "https://schema.org",
    "@type": ["HowTo", "SoftwareApplication"],
    "name": "Sample Size Calculators - Statistical Power Analysis Tools",
    "description": "Comprehensive sample size calculation tools for research studies including one-sample, two-sample, paired-sample, and ANOVA designs with statistical power analysis.",
    "applicationCategory": "StatisticalSoftware",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "One-sample size calculation",
      "Two-sample comparison power analysis",
      "Paired-sample design calculations",
      "ANOVA sample size determination",
      "Statistical power analysis",
      "Effect size estimation",
      "Alpha and beta error control"
    ],
    "step": [
      {
        "@type": "HowToStep",
        "name": "Select study design",
        "text": "Choose appropriate calculator based on your research design (one-sample, two-sample, paired, or ANOVA)"
      },
      {
        "@type": "HowToStep",
        "name": "Set statistical parameters",
        "text": "Define significance level (α), statistical power (1-β), and expected effect size"
      },
      {
        "@type": "HowToStep",
        "name": "Calculate sample size",
        "text": "Use the calculator to determine minimum required sample size for adequate statistical power"
      },
      {
        "@type": "HowToStep",
        "name": "Interpret results",
        "text": "Review calculated sample size and adjust parameters if needed for practical constraints"
      }
    ],
    "about": [
      {
        "@type": "Thing",
        "name": "Statistical Power Analysis",
        "description": "Method for determining adequate sample size to detect meaningful effects"
      },
      {
        "@type": "Thing",
        "name": "Effect Size",
        "description": "Quantitative measure of the magnitude of experimental effect"
      },
      {
        "@type": "Thing",
        "name": "Type I and Type II Errors",
        "description": "Statistical errors controlled through proper sample size planning"
      }
    ]
  };

  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How do I choose the right sample size calculator?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Select based on your study design: one-sample for single group studies, two-sample for comparing independent groups, paired-sample for matched or repeated measures, and ANOVA calculator for comparing multiple groups."
        }
      },
      {
        "@type": "Question",
        "name": "What is statistical power and why is it important?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Statistical power is the probability of detecting a true effect when it exists. Typically set at 80% or higher, adequate power ensures your study can reliably detect meaningful differences."
        }
      },
      {
        "@type": "Question",
        "name": "How do I determine the expected effect size?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Effect size can be estimated from pilot studies, previous research, or clinical significance thresholds. Common benchmarks are small (0.2), medium (0.5), and large (0.8) for Cohen's d."
        }
      },
      {
        "@type": "Question",
        "name": "What if my calculated sample size is too large for my study?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Consider adjusting parameters: increase effect size if clinically justified, reduce power slightly (minimum 80%), or use more efficient study designs like matched pairs or stratification."
        }
      }
    ]
  };

  return (
    <>
      <Helmet>
        <title>Sample Size Calculators - Statistical Power Analysis | DataStatPro</title>
        <meta name="description" content="Professional sample size calculators for research studies. Calculate required sample sizes for one-sample, two-sample, paired-sample, and ANOVA designs with statistical power analysis." />
        <meta name="keywords" content="sample size calculator, statistical power analysis, effect size, research design, ANOVA, t-test, power analysis, statistical significance, research methodology" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="sample-size-calculation" />
        <meta name="ai:methods" content="power-analysis,effect-size-estimation,statistical-planning" />
        <meta name="ai:study-designs" content="one-sample,two-sample,paired-sample,anova,experimental-design" />
        <meta name="ai:parameters" content="alpha-level,beta-level,effect-size,statistical-power" />
        
        {/* Open Graph tags */}
        <meta property="og:title" content="Sample Size Calculators - Statistical Power Analysis" />
        <meta property="og:description" content="Calculate required sample sizes for research studies with comprehensive power analysis tools." />
        <meta property="og:type" content="website" />
        
        {/* Structured data */}
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(faqData)}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }} itemScope itemType="https://schema.org/SoftwareApplication">
      {/* Header Section */}
        <Paper
          elevation={0}
          sx={{
            p: 4,
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
            borderRadius: 2
          }}
        >
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" itemProp="name">
            Sample Size Calculators
          </Typography>
          <Typography variant="h6" color="text.secondary" paragraph itemProp="description">
            Determine the necessary sample size for your study
          </Typography>
          <Typography variant="body1" color="text.secondary" itemProp="applicationCategory">
            Use these calculators to estimate the minimum number of participants or observations
            required to detect a statistically significant effect with a given level of power.
          </Typography>
          
          {/* AI-friendly feature overview */}
          <Box sx={{ mt: 3, p: 2, bgcolor: alpha(theme.palette.info.main, 0.05), borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary" itemProp="featureList">
              <strong>Key Features:</strong> Statistical power analysis, effect size estimation, 
              multiple study design support (one-sample, two-sample, paired, ANOVA), 
              alpha and beta error control, research methodology guidance
            </Typography>
          </Box>
        </Paper>

      {/* Category Filter (Optional - can remove if categories aren't useful here) */}
      {/*
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>
      */}

      {/* Options Grid */}
        <Grid container spacing={3} itemProp="offers">
          {sampleSizeCalculatorOptions.map((option) => ( // Using sampleSizeCalculatorOptions directly if no filtering
            <Grid item xs={12} md={6} lg={4} key={option.name}>
              <Card
                elevation={2}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8],
                    '& .launch-button': {
                      backgroundColor: option.color,
                      color: 'white',
                    }
                  }
                }}
                itemScope
                itemType="https://schema.org/SoftwareApplication"
              >
                <CardHeader
                  avatar={
                    <Avatar
                      sx={{
                        bgcolor: option.color,
                        width: 48,
                        height: 48,
                      }}
                    >
                      {option.icon}
                    </Avatar>
                  }
                  title={
                    <Typography variant="h6" fontWeight="bold" itemProp="name">
                      {option.name}
                    </Typography>
                  }
                  subheader={
                     <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                      <Chip
                        label={option.category}
                        size="small"
                        variant="outlined"
                        color="primary"
                        itemProp="applicationSubCategory"
                      />
                    </Box>
                  }
                  action={
                    <Tooltip title="More information">
                      <IconButton size="small" aria-label={`More information about ${option.name}`}>
                        <InfoIcon />
                      </IconButton>
                    </Tooltip>
                  }
                />

                <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                  <Typography variant="body2" color="text.secondary" paragraph itemProp="description">
                    {option.shortDescription}
                  </Typography>

                  <Typography variant="body2" paragraph itemProp="additionalProperty">
                    {option.detailedDescription}
                  </Typography>
                </CardContent>

                <Box sx={{ p: 2, pt: 0 }}>
                  <Button
                    className="launch-button"
                    variant="outlined"
                    fullWidth
                    onClick={() => onNavigate(option.path)}
                    endIcon={<LaunchIcon />}
                    aria-label={`Launch ${option.name} calculator`}
                    sx={{
                      borderColor: option.color,
                      color: option.color,
                      fontWeight: 'bold',
                      '&:hover': {
                        borderColor: option.color,
                      }
                    }}
                  >
                    Launch {option.name}
                  </Button>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>

      {/* Help Section */}
        <Paper
          elevation={1}
          sx={{
            p: 3,
            mt: 4,
            backgroundColor: alpha(theme.palette.info.main, 0.05),
            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
          }}
          itemScope
          itemType="https://schema.org/HowTo"
        >
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
            <InfoIcon color="info" />
            <Box>
              <Typography variant="h6" gutterBottom color="info.main" itemProp="name">
                Need Help Choosing?
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph itemProp="step">
                • <strong>Single group?</strong> Use the One Sample Calculator for studies with one population
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph itemProp="step">
                • <strong>Comparing two independent groups?</strong> Use the Two Sample Calculator for between-subjects designs
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph itemProp="step">
                • <strong>Paired or matched data?</strong> Use the Paired Sample Calculator for within-subjects or matched designs
              </Typography>
               <Typography variant="body2" color="text.secondary" itemProp="step">
                • <strong>Comparing more than two groups?</strong> Use the More Than Two Groups Calculator for ANOVA designs
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Container>
    </>
  );
};

export default SampleSizeCalculatorsOptions;
