import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Divider,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
} from '@mui/material';
import FunctionsIcon from '@mui/icons-material/Functions';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';
import {
  CalculateOutlined as CalculateIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { StatsCard } from '../UI';

// Cohort Calculator class with statistical methods
class CohortCalculatorUtils {
  // Incidence Rate (Risk) calculation
  static incidenceRate(cases: number, total: number) {
    // Incidence proportion (risk)
    let risk = cases / total;
    // 95% CI for a proportion, using Wald method
    let z = 1.96;
    let se = Math.sqrt(risk * (1 - risk) / total);
    let lower = risk - z * se;
    let upper = risk + z * se;
    // Boundaries between 0 and 1
    lower = Math.max(0, lower);
    upper = Math.min(1, upper);
    return { risk, lower, upper };
  }

  // Risk Ratio (Relative Risk, RR) calculation
  static riskRatio(a: number, n1: number, c: number, n0: number, alpha: number = 0.05) {
    let Re = a / n1;
    let Ru = c / n0;
    let RR = Re / Ru;
    // Variance of ln(RR)
    let varLnRR = (1 - Re) / a + (1 - Ru) / c;
    // 95% CI
    let z = 1.96; // Standard for alpha=0.05
    let seLnRR = Math.sqrt(varLnRR);
    let lower = RR * Math.exp(-z * seLnRR);
    let upper = RR * Math.exp(z * seLnRR);
    return { RR, lower, upper };
  }

  // Risk Difference (RD) calculation
  static riskDifference(a: number, n1: number, c: number, n0: number, alpha: number = 0.05) {
    let Re = a / n1;
    let Ru = c / n0;
    let RD = Re - Ru;
    let varRD = (Re * (1 - Re)) / n1 + (Ru * (1 - Ru)) / n0;
    let z = 1.96; // for 95% CI
    let seRD = Math.sqrt(varRD);
    let lower = RD - z * seRD;
    let upper = RD + z * seRD;
    return { RD, lower, upper };
  }

  // Directly Adjusted Risk Ratio
  static directlyAdjustedRR(strata: Array<{ai: number, bi: number, ci: number, di: number, n1i: number, n0i: number}>, alpha: number = 0.05) {
    let wi: number[] = [], lnRRi: number[] = [];
    strata.forEach(s => {
      // RR_i for this stratum
      let RRi = (s.ai / s.n1i) / (s.ci / s.n0i);
      lnRRi.push(Math.log(RRi));
      // Weight for this stratum
      let w = 1 / (s.bi / (s.ai * s.n1i) + s.di / (s.ci * s.n0i));
      wi.push(w);
    });
  
    let sumWi = wi.reduce((a, b) => a + b, 0);
    let lnRR_direct = wi.map((w, i) => w * lnRRi[i]).reduce((a, b) => a + b, 0) / sumWi;
    let RR_direct = Math.exp(lnRR_direct);
  
    let se = 1 / Math.sqrt(sumWi);
    let z = 1.96; // 95% CI
    let lower = Math.exp(lnRR_direct - z * se);
    let upper = Math.exp(lnRR_direct + z * se);
  
    return { RR_direct, lower, upper };
  }

  // Directly Adjusted Risk Difference
  static directlyAdjustedRD(strata: Array<{ai: number, bi: number, ci: number, di: number, n1i: number, n0i: number}>, alpha: number = 0.05) {
    let wi: number[] = [], RDi: number[] = [];
    strata.forEach(s => {
      let RD = (s.ai / s.n1i) - (s.ci / s.n0i);
      RDi.push(RD);
      // Weight for this stratum
      let w = 1 / ((s.ai * s.bi) / Math.pow(s.n1i, 3) + (s.ci * s.di) / Math.pow(s.n0i, 3));
      wi.push(w);
    });
    let sumWi = wi.reduce((a, b) => a + b, 0);
    let RD_direct = wi.map((w, i) => w * RDi[i]).reduce((a, b) => a + b, 0) / sumWi;
  
    let se = 1 / Math.sqrt(sumWi);
    let z = 1.96;
    let lower = RD_direct - z * se;
    let upper = RD_direct + z * se;
    return { RD_direct, lower, upper };
  }

  // Mantel-Haenszel Adjusted Risk Ratio
  static mantelHaenszelRR(strata: Array<{ai: number, bi: number, ci: number, di: number, n1i: number, n0i: number}>, alpha: number = 0.05) {
    // Step 1: calculate sums
    let sumA = 0, sumC = 0;
    strata.forEach(s => {
      let n_i = s.n1i + s.n0i;
      sumA += s.ai * s.n0i / n_i;
      sumC += s.ci * s.n1i / n_i;
    });
  
    // Step 2: compute RR_MH
    let RR_MH = sumA / sumC;
  
    // Step 3: Calculate Variance of log(RR_MH)
    let numerator = 0;
    strata.forEach(s => {
      let n_i = s.n1i + s.n0i;
      let m_1i = s.ai + s.ci; // exposed cases + unexposed cases in stratum i
      numerator += (m_1i * s.n1i * s.n0i - s.ai * s.ci * n_i) / (n_i * n_i);
    });
  
    // Re-calc sums for denominator
    let sumAi_n0i_over_ni = 0, sumCi_n1i_over_ni = 0;
    strata.forEach(s => {
      let n_i = s.n1i + s.n0i;
      sumAi_n0i_over_ni += (s.ai * s.n0i) / n_i;
      sumCi_n1i_over_ni += (s.ci * s.n1i) / n_i;
    });
    let denominator = sumAi_n0i_over_ni * sumCi_n1i_over_ni;
  
    let var_lnRR_MH = numerator / denominator;
  
    // Step 4: CI for RR_MH
    let z = 1.96; // 95%
    let se = Math.sqrt(var_lnRR_MH);
    let lower = RR_MH * Math.exp(-z * se);
    let upper = RR_MH * Math.exp(z * se);
  
    return { RR_MH, lower, upper };
  }
}

interface Stratum {
  id: string;
  ai: number; // Exposed cases
  bi: number; // Exposed non-cases
  ci: number; // Unexposed cases
  di: number; // Unexposed non-cases
  n1i: number; // Total exposed (ai + bi)
  n0i: number; // Total unexposed (ci + di)
}

const CohortCalculator: React.FC = () => {
  const theme = useTheme();

  // State for 2x2 table values
  const [cellValues, setCellValues] = useState({
    a: 0, // Exposed cases
    b: 0, // Exposed non-cases
    c: 0, // Unexposed cases
    d: 0  // Unexposed non-cases
  });

  // Function to render mathematical formulas using KaTeX
  const renderFormula = useCallback((formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  }, []);

  // Derived values
  const n1 = cellValues.a + cellValues.b; // Total exposed
  const n0 = cellValues.c + cellValues.d; // Total unexposed

  // State for stratified analysis
  const [strata, setStrata] = useState<Stratum[]>([]);
  // Replace showStratifiedAnalysis with currentStratumIndex
  const [currentStratumIndex, setCurrentStratumIndex] = useState<number>(-1);

  // State for calculation results
  const [results, setResults] = useState<{
    incidenceExposed?: { risk: number; lower: number; upper: number };
    incidenceUnexposed?: { risk: number; lower: number; upper: number };
    riskRatio?: { RR: number; lower: number; upper: number };
    riskDifference?: { RD: number; lower: number; upper: number };
    directlyAdjustedRR?: { RR_direct: number; lower: number; upper: number };
    directlyAdjustedRD?: { RD_direct: number; lower: number; upper: number };
    mantelHaenszelRR?: { RR_MH: number; lower: number; upper: number };
  }>({});

  // Load saved data from localStorage on component mount
  useEffect(() => {
    const savedCellValues = localStorage.getItem('cohort_calculator_cell_values');
    const savedStrata = localStorage.getItem('cohort_calculator_strata');
    const savedResults = localStorage.getItem('cohort_calculator_results');
    const savedCurrentStratumIndex = localStorage.getItem('cohort_calculator_current_stratum_index');

    if (savedCellValues) {
      setCellValues(JSON.parse(savedCellValues));
    }
    
    if (savedStrata) {
      setStrata(JSON.parse(savedStrata));
    }
    
    if (savedResults) {
      setResults(JSON.parse(savedResults));
    }
    
    if (savedCurrentStratumIndex) {
      setCurrentStratumIndex(JSON.parse(savedCurrentStratumIndex));
    }
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cohort_calculator_cell_values', JSON.stringify(cellValues));
  }, [cellValues]);

  useEffect(() => {
    localStorage.setItem('cohort_calculator_strata', JSON.stringify(strata));
  }, [strata]);

  useEffect(() => {
    localStorage.setItem('cohort_calculator_results', JSON.stringify(results));
  }, [results]);

  useEffect(() => {
    localStorage.setItem('cohort_calculator_current_stratum_index', JSON.stringify(currentStratumIndex));
  }, [currentStratumIndex]);

  // Handle input changes for main 2x2 table
  const handleInputChange = (cell: 'a' | 'b' | 'c' | 'd', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setCellValues(prev => ({
      ...prev,
      [cell]: isNaN(numValue) ? 0 : numValue
    }));
  };

  // Handle input changes for stratified analysis
  const handleStratumInputChange = (id: string, cell: 'ai' | 'bi' | 'ci' | 'di', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setStrata(prev => prev.map(stratum => {
      if (stratum.id === id) {
        const updatedStratum = { 
          ...stratum, 
          [cell]: isNaN(numValue) ? 0 : numValue 
        };
        
        // Update n1i and n0i based on ai, bi, ci, di
        if (cell === 'ai' || cell === 'bi') {
          updatedStratum.n1i = updatedStratum.ai + updatedStratum.bi;
        }
        if (cell === 'ci' || cell === 'di') {
          updatedStratum.n0i = updatedStratum.ci + updatedStratum.di;
        }
        
        return updatedStratum;
      }
      return stratum;
    }));
  };

  // Add a new stratum for stratified analysis
  const addStratum = () => {
    const newStratum: Stratum = {
      id: `stratum_${Date.now()}`,
      ai: 0,
      bi: 0,
      ci: 0,
      di: 0,
      n1i: 0,
      n0i: 0
    };
    const newStrata = [...strata, newStratum];
    setStrata(newStrata);
    // Automatically select the newly added stratum
    setCurrentStratumIndex(newStrata.length - 1);
  };

  // Remove a stratum
  const removeStratum = (id: string) => {
    const index = strata.findIndex(s => s.id === id);
    const newStrata = strata.filter(stratum => stratum.id !== id);
    setStrata(newStrata);
    
    // Adjust current index if needed
    if (newStrata.length === 0) {
      setCurrentStratumIndex(-1);
    } else if (index <= currentStratumIndex) {
      setCurrentStratumIndex(Math.min(currentStratumIndex - 1, newStrata.length - 1));
    }
  };

  // Calculate results
  const calculateResults = () => {
    try {
      const { a, b, c, d } = cellValues;
      const n1 = a + b; // Total exposed
      const n0 = c + d; // Total unexposed
      const total = n1 + n0;

      if (total === 0) return;

      // Initialize results object
      const resultsObj: any = {};

      // If strata exist, create consolidated table by summing all cells
      if (strata.length > 0) {
        // Convert main table to stratum format and combine with other strata
        const mainTableAsStratum = {
          ai: a,
          bi: b,
          ci: c,
          di: d,
          n1i: n1,
          n0i: n0
        };
        
        const allStrata = [mainTableAsStratum, ...strata];
        
        // Create consolidated table by summing all cells
        let consolidatedA = 0;
        let consolidatedB = 0;
        let consolidatedC = 0;
        let consolidatedD = 0;
        
        allStrata.forEach(stratum => {
          consolidatedA += stratum.ai;
          consolidatedB += stratum.bi;
          consolidatedC += stratum.ci;
          consolidatedD += stratum.di;
        });
        
        const consolidatedN1 = consolidatedA + consolidatedB;
        const consolidatedN0 = consolidatedC + consolidatedD;
        
        // Calculate measures using consolidated data
        resultsObj.incidenceExposed = CohortCalculatorUtils.incidenceRate(consolidatedA, consolidatedN1);
        resultsObj.incidenceUnexposed = CohortCalculatorUtils.incidenceRate(consolidatedC, consolidatedN0);
        resultsObj.riskRatio = CohortCalculatorUtils.riskRatio(consolidatedA, consolidatedN1, consolidatedC, consolidatedN0);
        resultsObj.riskDifference = CohortCalculatorUtils.riskDifference(consolidatedA, consolidatedN1, consolidatedC, consolidatedN0);
        
        // Calculate directly adjusted measures
        resultsObj.directlyAdjustedRR = CohortCalculatorUtils.directlyAdjustedRR(allStrata);
        resultsObj.directlyAdjustedRD = CohortCalculatorUtils.directlyAdjustedRD(allStrata);
        
        // Calculate Mantel-Haenszel adjusted risk ratio
        resultsObj.mantelHaenszelRR = CohortCalculatorUtils.mantelHaenszelRR(allStrata);
      } else {
        // If no strata, use main table values
        resultsObj.incidenceExposed = CohortCalculatorUtils.incidenceRate(a, n1);
        resultsObj.incidenceUnexposed = CohortCalculatorUtils.incidenceRate(c, n0);
        resultsObj.riskRatio = CohortCalculatorUtils.riskRatio(a, n1, c, n0);
        resultsObj.riskDifference = CohortCalculatorUtils.riskDifference(a, n1, c, n0);
      }

      setResults(resultsObj);
    } catch (error) {
      console.error('Error calculating results:', error);
    }
  };

  // Reset form
  const resetForm = () => {
    setCellValues({ a: 0, b: 0, c: 0, d: 0 });
    setStrata([]);
    setResults({});
    setCurrentStratumIndex(-1);
    
    // Clear localStorage
    localStorage.removeItem('cohort_calculator_cell_values');
    localStorage.removeItem('cohort_calculator_strata');
    localStorage.removeItem('cohort_calculator_results');
    localStorage.removeItem('cohort_calculator_current_stratum_index');
  };

  // Format number with specified decimal places
  const formatNumber = (num: number, decimals: number = 3) => {
    return num.toFixed(decimals);
  };

  // Format confidence interval
  const formatCI = (lower: number, upper: number, decimals: number = 3) => {
    return `${formatNumber(lower, decimals)} - ${formatNumber(upper, decimals)}`;
  };

  // Format percentage with specified decimal places
  const formatPercent = (num: number, decimals: number = 1) => {
    return `${(num * 100).toFixed(decimals)}%`;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Cohort Study Calculator</Typography>
      <Typography variant="body1" paragraph>
        Calculate risk ratios, risk differences, and incidence rates for cohort studies.
      </Typography>
      
      {/* Formulas Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="formulas-content"
          id="formulas-header"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FunctionsIcon color="primary" />
            <Typography variant="h6">Mathematical Formulas</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            {/* Risk Ratio */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Risk Ratio (Relative Risk - RR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('RR = \\frac{\\text{Risk in exposed}}{\\text{Risk in unexposed}} = \\frac{\\frac{a}{a+b}}{\\frac{c}{c+d}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = RR \\times \\exp(\\pm 1.96 \\times SE_{\\ln(RR)})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(RR)} = \\sqrt{\\frac{1}{a} - \\frac{1}{a+b} + \\frac{1}{c} - \\frac{1}{c+d}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: a = exposed with outcome, b = exposed without outcome, c = unexposed with outcome, d = unexposed without outcome
                </Typography>
              </Paper>
            </Grid>

            {/* Risk Difference */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Risk Difference (Attributable Risk - AR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('RD = \\text{Risk in exposed} - \\text{Risk in unexposed} = \\frac{a}{a+b} - \\frac{c}{c+d}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = RD \\pm 1.96 \\times SE_{RD}')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{RD} = \\sqrt{\\frac{a \\times b}{(a+b)^3} + \\frac{c \\times d}{(c+d)^3}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Risk difference represents the excess risk attributable to exposure
                </Typography>
              </Paper>
            </Grid>

            {/* Incidence Rate */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Incidence Rate and Rate Ratio
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Incidence Rate:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('IR = \\frac{\\text{Number of cases}}{\\text{Person-time at risk}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Rate Ratio:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('IRR = \\frac{IR_1}{IR_0} = \\frac{\\frac{a}{PT_1}}{\\frac{c}{PT_0}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval for Rate Ratio:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = IRR \\times \\exp(\\pm 1.96 \\times \\sqrt{\\frac{1}{a} + \\frac{1}{c}})')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: PT₁ = person-time in exposed group, PT₀ = person-time in unexposed group
                </Typography>
              </Paper>
            </Grid>

            {/* Mantel-Haenszel RR */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Mantel-Haenszel Adjusted Risk Ratio
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('RR_{MH} = \\frac{\\sum_{i=1}^{k} \\frac{a_i(c_i+d_i)}{n_i}}{\\sum_{i=1}^{k} \\frac{c_i(a_i+b_i)}{n_i}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = RR_{MH} \\times \\exp(\\pm 1.96 \\times SE_{\\ln(RR_{MH})})')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Adjusts for confounding by combining stratum-specific risk ratios
                </Typography>
              </Paper>
            </Grid>

            {/* Directly Adjusted RR and RD */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Directly Adjusted Risk Ratio and Risk Difference
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Directly Adjusted Risk Ratio:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('RR_{adj} = \\frac{\\sum_{i=1}^{k} w_i \\times RR_i}{\\sum_{i=1}^{k} w_i}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Directly Adjusted Risk Difference:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('RD_{adj} = \\sum_{i=1}^{k} w_i \\times RD_i')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Uses standard population weights to adjust for confounding variables across strata
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>2×2 Contingency Table</Typography>
        <Typography variant="body2" paragraph>
          Enter the values for your 2×2 table to calculate epidemiological measures.
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <TableContainer component={Paper} variant="outlined">
              <Table aria-label="2x2 contingency table">
                <TableHead>
                  <TableRow>
                    <TableCell></TableCell>
                    <TableCell align="center">Cases</TableCell>
                    <TableCell align="center">Non-cases</TableCell>
                    <TableCell align="center">Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row">Exposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.a || ''}
                        onChange={(e) => handleInputChange('a', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.b || ''}
                        onChange={(e) => handleInputChange('b', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{n1}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Unexposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.c || ''}
                        onChange={(e) => handleInputChange('c', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.d || ''}
                        onChange={(e) => handleInputChange('d', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{n0}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Total</TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.c}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.b + cellValues.d}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{n1 + n0}</Typography>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CalculateIcon />}
                onClick={calculateResults}
                disabled={Object.values(cellValues).every(val => val === 0) && strata.length === 0}
              >
                Calculate
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetForm}
              >
                Reset
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Enter the values in the 2×2 table and click Calculate to compute epidemiological measures.
            </Typography>
          </Grid>
        </Grid>

        {/* Replace the entire Accordion-based Stratified Analysis Section with the new UI */}
        <Box sx={{ mb: 3, border: '1px solid rgba(25, 118, 210, 0.5)', borderRadius: '4px', p: 2 }}>
          <Typography variant="subtitle1" sx={{ mb: 2 }}><strong>Stratified Analysis</strong></Typography>
          
          <Typography variant="body2" paragraph>
            Add strata to calculate adjusted risk ratios and risk differences. The crude measures will be calculated by combining all tables.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={addStratum}
            >
              Add Stratum
            </Button>
            
            {strata.length > 0 && (
              <>
                <Box sx={{ minWidth: 120 }}>
                  <select
                    value={currentStratumIndex}
                    onChange={(e) => setCurrentStratumIndex(Number(e.target.value))}
                    style={{
                      padding: '8px 12px',
                      borderRadius: '4px',
                      border: '1px solid rgba(0, 0, 0, 0.23)',
                      backgroundColor: 'transparent',
                      minWidth: '120px'
                    }}
                  >
                    {strata.map((_, index) => (
                      <option key={index} value={index}>
                        Stratum {index + 1}
                      </option>
                    ))}
                  </select>
                </Box>
                
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => removeStratum(strata[currentStratumIndex].id)}
                  disabled={currentStratumIndex === -1}
                >
                  Delete Stratum
                </Button>
              </>
            )}
          </Box>
          
          {/* Display only the currently selected stratum */}
          {currentStratumIndex !== -1 && strata[currentStratumIndex] && (
            <Box sx={{ mb: 2, p: 2, border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`, borderRadius: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Stratum {currentStratumIndex + 1}</Typography>
              
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell align="center">Cases</TableCell>
                      <TableCell align="center">Non-cases</TableCell>
                      <TableCell align="center">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Exposed</TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex].ai || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex].id, 'ai', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex].bi || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex].id, 'bi', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{strata[currentStratumIndex].n1i}</Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Unexposed</TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex].ci || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex].id, 'ci', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex].di || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex].id, 'di', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{strata[currentStratumIndex].n0i}</Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell component="th" scope="row">Total</TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{strata[currentStratumIndex].ai + strata[currentStratumIndex].ci}</Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{strata[currentStratumIndex].bi + strata[currentStratumIndex].di}</Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{strata[currentStratumIndex].n1i + strata[currentStratumIndex].n0i}</Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </Box>

        {/* Results Section */}
        {Object.keys(results).length > 0 && (
          <Box sx={{ mt: 4 }}>
            <Divider sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>Results</Typography>
            
            <Grid container spacing={3}>
              {/* Incidence Rates */}
              {results.incidenceExposed && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Incidence Rate (Exposed)"
                    value={formatPercent(results.incidenceExposed.risk)}
                    description={`95% CI: ${formatPercent(results.incidenceExposed.lower)} - ${formatPercent(results.incidenceExposed.upper)}`}
                    color="primary"
                    variant="outlined"
                    tooltip="Incidence proportion (risk) in the exposed group"
                  />
                </Grid>
              )}
              
              {results.incidenceUnexposed && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Incidence Rate (Unexposed)"
                    value={formatPercent(results.incidenceUnexposed.risk)}
                    description={`95% CI: ${formatPercent(results.incidenceUnexposed.lower)} - ${formatPercent(results.incidenceUnexposed.upper)}`}
                    color="primary"
                    variant="outlined"
                    tooltip="Incidence proportion (risk) in the unexposed group"
                  />
                </Grid>
              )}
              
              {/* Risk Ratio */}
              {results.riskRatio && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Risk Ratio (Relative Risk)"
                    value={formatNumber(results.riskRatio.RR)}
                    description={`95% CI: ${formatCI(results.riskRatio.lower, results.riskRatio.upper)}`}
                    color="secondary"
                    variant="outlined"
                    tooltip="Ratio of risk in exposed vs. unexposed groups"
                  />
                </Grid>
              )}
              
              {/* Risk Difference */}
              {results.riskDifference && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Risk Difference"
                    value={formatNumber(results.riskDifference.RD)}
                    description={`95% CI: ${formatCI(results.riskDifference.lower, results.riskDifference.upper)}`}
                    color="info"
                    variant="outlined"
                    tooltip="Absolute difference in risk between exposed and unexposed groups"
                  />
                </Grid>
              )}
              
              {/* Directly Adjusted Risk Ratio */}
              {results.directlyAdjustedRR && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Directly Adjusted Risk Ratio"
                    value={formatNumber(results.directlyAdjustedRR.RR_direct)}
                    description={`95% CI: ${formatCI(results.directlyAdjustedRR.lower, results.directlyAdjustedRR.upper)}`}
                    color="warning"
                    variant="outlined"
                    tooltip="Directly adjusted risk ratio for stratified data"
                  />
                </Grid>
              )}
              
              {/* Directly Adjusted Risk Difference */}
              {results.directlyAdjustedRD && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Directly Adjusted Risk Difference"
                    value={formatNumber(results.directlyAdjustedRD.RD_direct)}
                    description={`95% CI: ${formatCI(results.directlyAdjustedRD.lower, results.directlyAdjustedRD.upper)}`}
                    color="warning"
                    variant="outlined"
                    tooltip="Directly adjusted risk difference for stratified data"
                  />
                </Grid>
              )}
              
              {/* Mantel-Haenszel Risk Ratio */}
              {results.mantelHaenszelRR && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Mantel-Haenszel Risk Ratio"
                    value={formatNumber(results.mantelHaenszelRR.RR_MH)}
                    description={`95% CI: ${formatCI(results.mantelHaenszelRR.lower, results.mantelHaenszelRR.upper)}`}
                    color="success"
                    variant="outlined"
                    tooltip="Mantel-Haenszel adjusted risk ratio across strata"
                  />
                </Grid>
              )}
            </Grid>
            
            {/* Interpretation */}
            <Paper 
              variant="outlined" 
              sx={{ 
                mt: 3, 
                p: 2, 
                backgroundColor: alpha(theme.palette.info.main, 0.05),
                borderColor: alpha(theme.palette.info.main, 0.2)
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Interpretation Guidelines:
              </Typography>
              <Typography variant="body2">
                • <strong>Risk Ratio = 1:</strong> No association between exposure and outcome<br />
                • <strong>Risk Ratio &gt; 1:</strong> Positive association (exposure may increase risk of outcome)<br />
                • <strong>Risk Ratio &lt; 1:</strong> Negative association (exposure may decrease risk of outcome)<br />
                • <strong>Risk Difference = 0:</strong> No absolute difference in risk between groups<br />
                • <strong>Statistical significance:</strong> If the 95% confidence interval does not include the null value (1 for RR, 0 for RD)
              </Typography>
            </Paper>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default CohortCalculator;
