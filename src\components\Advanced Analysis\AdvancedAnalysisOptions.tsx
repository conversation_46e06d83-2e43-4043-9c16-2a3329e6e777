import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  Psychology as EFAIcon,
  CompareArrows as MediationModerationIcon,
  CheckCircleOutline as ReliabilityIcon,
  Timeline as SurvivalIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  CompareArrows as CompareArrowsIcon, // Import CompareArrowsIcon
  Hub as HubIcon, // Import HubIcon for Cluster Analysis
  Schema as CFACardIcon, // Import SchemaIcon for Confirmatory Factor Analysis
  AccountTree as VariableTreeIcon, // Import AccountTreeIcon for Variable Tree Analysis

} from '@mui/icons-material';

interface AdvancedAnalysisOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Factor Analysis' | 'Regression' | 'Reliability' | 'Survival' | 'Meta Analysis' | 'Cluster Analysis' | 'Confirmatory Factor Analysis' | 'Variable Tree Analysis';
  color: string;
}

interface AdvancedAnalysisOptionsProps {
  onNavigate: (path: string) => void;
}

export const advancedAnalysisOptions: AdvancedAnalysisOption[] = [
  {
    name: 'Exploratory Factor Analysis (EFA)',
    shortDescription: 'Identify underlying factors in a set of variables',
    detailedDescription: 'Perform Exploratory Factor Analysis to uncover the latent structure among a set of observed variables. This technique is used to reduce a large number of variables into a smaller, more manageable set of factors for use in other analyses.',
    path: 'advanced-analysis/efa',
    icon: <EFAIcon />,
    category: 'Factor Analysis',
    color: '#FF5722', // Deep Orange
  },
  {
    name: 'Confirmatory Factor Analysis (CFA)',
    shortDescription: 'Test a hypothesized factor structure',
    detailedDescription: 'Conduct Confirmatory Factor Analysis to evaluate how well a measured set of variables represents a smaller number of latent constructs. This method is used to confirm or reject measurement models.',
    path: 'advanced-analysis/cfa',
    icon: <CFACardIcon />,
    category: 'Confirmatory Factor Analysis',
    color: '#4CAF50', // Green
  },
  {
    name: 'Mediation and Moderation',
    shortDescription: 'Analyze indirect and conditional effects',
    detailedDescription: 'Examine how a third variable (mediator) explains the relationship between two other variables, or how the relationship between two variables depends on a third variable (moderator).',
    path: 'advanced-analysis/mediation',
    icon: <MediationModerationIcon />,
    category: 'Regression',
    color: '#673AB7', // Deep Purple
  },
  {
    name: 'Reliability Analysis',
    shortDescription: 'Assess the consistency of measurements',
    detailedDescription: 'Evaluate the internal consistency of a scale or test using methods like Cronbach\'s alpha. Essential for validating questionnaires and psychometric instruments.',
    path: 'advanced-analysis/reliability',
    icon: <ReliabilityIcon />,
    category: 'Reliability',
    color: '#00BCD4', // Cyan
  },
  {
    name: 'Survival Analysis',
    shortDescription: 'Analyze time-to-event data',
    detailedDescription: 'Study the time until an event occurs, such as death, disease onset, or equipment failure. Includes methods like Kaplan-Meier curves and Cox proportional hazards regression.',
    path: 'advanced-analysis/survival',
    icon: <SurvivalIcon />,
    category: 'Survival',
  color: '#8BC34A', // Light Green
  },
  {
    name: 'Cluster Analysis',
    shortDescription: 'Group similar data points into clusters',
    detailedDescription: 'Identify natural groupings in your data using algorithms like K-means, hierarchical clustering, and DBSCAN. Useful for customer segmentation, pattern recognition, and data exploration.',
    path: 'advanced-analysis/cluster',
    icon: <HubIcon />,
    category: 'Cluster Analysis',
    color: '#9C27B0', // Purple
  },
  {
    name: 'Meta Analysis',
    shortDescription: 'Synthesize findings from multiple studies',
    detailedDescription: 'Combine results from independent studies to produce a single estimate of a treatment effect or association. Essential for evidence-based practice and systematic reviews.',
    path: 'advanced-analysis/meta-analysis',
    icon: <CompareArrowsIcon />, // Using CompareArrowsIcon as a placeholder, can be changed later
    category: 'Meta Analysis',
    color: '#2196F3', // Blue
  },
  {
    name: 'Variable Tree Analysis',
    shortDescription: 'Create hierarchical tree visualizations of variable relationships',
    detailedDescription: 'Build custom tree structures to explore how 2-4 variables relate to each other at different hierarchical levels. Visualize data relationships with interactive tree diagrams showing statistical summaries at each node.',
    path: 'advanced-analysis/variable-tree',
    icon: <VariableTreeIcon />,
    category: 'Variable Tree Analysis',
    color: '#795548', // Brown
  },
];

const AdvancedAnalysisOptions: React.FC<AdvancedAnalysisOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  // Generate structured data for advanced analysis tools
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Perform Advanced Statistical Analysis",
      "description": "Comprehensive guide to advanced statistical methods including factor analysis, survival analysis, meta-analysis, and multivariate techniques for research and data science.",
      "totalTime": "PT60M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Prepared dataset with appropriate variables"
        },
        {
          "@type": "HowToSupply",
          "name": "Research hypotheses and theoretical framework"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Advanced Analysis Suite"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Select analysis method",
          "text": "Choose appropriate advanced technique: EFA/CFA for factor structure, survival analysis for time-to-event data, meta-analysis for research synthesis"
        },
        {
          "@type": "HowToStep",
          "name": "Verify assumptions",
          "text": "Check statistical assumptions including normality, linearity, multicollinearity, and sample size requirements for chosen method"
        },
        {
          "@type": "HowToStep",
          "name": "Execute analysis",
          "text": "Run the selected advanced statistical procedure with appropriate parameters and model specifications"
        },
        {
          "@type": "HowToStep",
          "name": "Interpret results",
          "text": "Analyze output including factor loadings, hazard ratios, effect sizes, and model fit indices with statistical significance testing"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Advanced Statistical Results",
        "description": "Comprehensive analysis output with statistical models, effect sizes, and interpretation guidelines"
      }
    };
  };

  const categories = ['All', 'Factor Analysis', 'Confirmatory Factor Analysis', 'Regression', 'Reliability', 'Survival', 'Cluster Analysis', 'Meta Analysis', 'Variable Tree Analysis'];

  const filteredOptions = selectedCategory === 'All'
    ? advancedAnalysisOptions
    : advancedAnalysisOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Factor Analysis': return <EFAIcon />;
      case 'Regression': return <MediationModerationIcon />;
      case 'Reliability': return <ReliabilityIcon />;
      case 'Survival': return <SurvivalIcon />;
      case 'Cluster Analysis': return <HubIcon />; // Add case for Cluster Analysis icon
      case 'Meta Analysis': return <CompareArrowsIcon />; // Add case for Meta Analysis icon
      case 'Confirmatory Factor Analysis': return <CFACardIcon />; // Add case for Confirmatory Factor Analysis icon
      case 'Variable Tree Analysis': return <VariableTreeIcon />; // Add case for Variable Tree Analysis icon
      default: return <EFAIcon />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Advanced Statistical Analysis | DataStatPro - Factor Analysis, Survival Analysis & Meta-Analysis</title>
        <meta name="description" content="Advanced statistical methods for research: Exploratory & Confirmatory Factor Analysis (EFA/CFA), Survival Analysis, Meta-Analysis, Reliability Testing, Cluster Analysis, and Multivariate Techniques with AI-powered interpretation." />
        <meta name="keywords" content="advanced statistics, factor analysis, EFA, CFA, survival analysis, meta analysis, reliability analysis, cluster analysis, multivariate statistics, structural equation modeling, Kaplan-Meier, Cox regression, Cronbach alpha, variable tree analysis" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Advanced statistical analysis and multivariate research methods" />
        <meta name="ai:methods" content="Factor Analysis, Survival Analysis, Meta-Analysis, Reliability Testing, Cluster Analysis" />
        <meta name="ai:techniques" content="EFA, CFA, SEM, Kaplan-Meier, Cox Regression, Random Effects, Fixed Effects" />
        
        {/* Structured data for advanced analysis */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for advanced analysis */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What is the difference between EFA and CFA?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Exploratory Factor Analysis (EFA) discovers underlying factor structure in data without prior hypotheses, while Confirmatory Factor Analysis (CFA) tests specific hypothesized factor models and evaluates model fit."
                }
              },
              {
                "@type": "Question",
                "name": "When should I use survival analysis?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Survival analysis is used for time-to-event data where you want to analyze the time until an event occurs (death, failure, recovery) and handle censored observations where the event hasn't occurred by study end."
                }
              },
              {
                "@type": "Question",
                "name": "What is meta-analysis used for?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Meta-analysis combines results from multiple independent studies to produce a single, more precise estimate of treatment effects or associations, providing stronger evidence than individual studies."
                }
              },
              {
                "@type": "Question",
                "name": "How do I assess reliability with Cronbach's alpha?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Cronbach's alpha measures internal consistency reliability of scale items. Values above 0.7 indicate acceptable reliability, above 0.8 good reliability, and above 0.9 excellent reliability for research purposes."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section with AI-optimized content */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Advanced Analysis Tools - Factor Analysis, Survival Analysis & Meta-Analysis
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Sophisticated multivariate statistical methods for complex research: EFA/CFA, Survival Analysis, 
          Meta-Analysis, Reliability Testing, Cluster Analysis with AI-powered interpretation and model validation.
        </Typography>
        
        {/* AI-friendly feature overview */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(theme.palette.secondary.main, 0.05), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Advanced Methods:</strong> Factor Analysis (EFA/CFA) • Survival Analysis (Kaplan-Meier, Cox) • 
            Meta-Analysis • Reliability (Cronbach's α) • Cluster Analysis • Variable Tree Analysis • SEM
          </Typography>
        </Box>
        
        <Typography variant="body1" color="text.secondary">
          Access a suite of powerful tools for complex statistical modeling and analysis,
          including factor analysis, mediation/moderation, reliability assessment, and survival analysis.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography 
                    variant="h6" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Launch ${option.name} analysis`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Exploring data structure?</strong> Try Exploratory Factor Analysis (EFA)
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Understanding relationships?</strong> Use Mediation and Moderation
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Validating scales?</strong> Assess with Reliability Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Analyzing time-to-event?</strong> Survival Analysis is the tool for you
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Looking for natural groupings in data?</strong> Try Cluster Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Confirming a measurement model?</strong> Use Confirmatory Factor Analysis (CFA)
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Synthesizing research findings?</strong> Meta Analysis can help
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Exploring variable hierarchies?</strong> Variable Tree Analysis provides visual insights
            </Typography>
          </Box>
        </Box>
      </Paper>
      </Container>
    </>
  );
};

export default AdvancedAnalysisOptions;
