import React, { useState, useEffect } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Paper,
  useTheme
} from '@mui/material';
import {
  GridOn as GridOnIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon
} from '@mui/icons-material';
import SocialShareWidget from '../UI/SocialShareWidget';
import CorrelationMatrix from './CorrelationMatrix';
import LinearRegression from './LinearRegression';
import LogisticRegression from './LogisticRegression';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface CorrelationAnalysisProps {
  initialTab?: string;
}

// Tab panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`correlation-tabpanel-${index}`}
      aria-labelledby={`correlation-tab-${index}`}
      {...other}
      style={{ width: '100%' }}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Helper function for a11y props
const a11yProps = (index: number) => {
  return {
    id: `correlation-tab-${index}`,
    'aria-controls': `correlation-tabpanel-${index}`,
  };
};

// Map tab names to indices
const tabNameToIndex: Record<string, number> = {
  'pearson': 0,
  'linear': 1,
  'regression': 1, // Add mapping for 'regression' to Linear Regression tab
  'logistic': 2
};

// Main component for the Correlation Analysis section
const CorrelationAnalysis: React.FC<CorrelationAnalysisProps> = ({ initialTab = '' }) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);

  // Set initial tab based on URL or prop
  useEffect(() => {
    if (initialTab && tabNameToIndex[initialTab] !== undefined) {
      setTabValue(tabNameToIndex[initialTab]);
    }
  }, [initialTab]);

  // Handle tab change
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible={true}
      />
      <Paper elevation={1} sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleChange}
          aria-label="Correlation Analysis Tabs"
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Tab 
            icon={<GridOnIcon />} 
            label="Correlation Matrix" 
            {...a11yProps(0)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<ShowChartIcon />} 
            label="Linear Regression" 
            {...a11yProps(1)} 
            sx={{ py: 2 }}
          />
          <Tab 
            icon={<FunctionsIcon />} 
            label="Logistic Regression" 
            {...a11yProps(2)}
            sx={{ py: 2 }}
          />
        </Tabs>

        <Box sx={{ p: 0 }}>
          <TabPanel value={tabValue} index={0}>
            <CorrelationMatrix />
          </TabPanel>
          <TabPanel value={tabValue} index={1}>
            <LinearRegression />
          </TabPanel>
          <TabPanel value={tabValue} index={2}>
            <LogisticRegression />
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default CorrelationAnalysis;
