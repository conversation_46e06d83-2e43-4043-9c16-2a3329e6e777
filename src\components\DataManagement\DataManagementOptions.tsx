import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  CloudUpload as ImportIcon,
  CloudDownload as ExportIcon,
  Edit as EditIcon,
  Transform as TransformIcon,
  ListAlt as ListIcon,
  EditNote as VariableEditorIcon, // Import icon for Variable Editor
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface DataManagementOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Import' | 'Export' | 'Edit' | 'Transform' | 'Manage';
  color: string;
}

interface DataManagementOptionsProps {
  onNavigate?: (path: string) => void;
}

export const dataManagementOptions: DataManagementOption[] = [
  {
    name: 'Data Import',
    shortDescription: 'Import data from various sources',
    detailedDescription: 'Import your datasets from local files (CSV, Excel, etc.) or connect to external sources like Google Sheets to begin your analysis.',
    path: 'data-management/import',
    icon: <ImportIcon />,
    category: 'Import',
    color: '#4CAF50', // Green
  },
  {
    name: 'Data Export',
    shortDescription: 'Export your processed data',
    detailedDescription: 'Save your cleaned and transformed datasets to various file formats for use in other applications or for sharing.',
    path: 'data-management/export',
    icon: <ExportIcon />,
    category: 'Export',
    color: '#2196F3', // Blue
  },
  {
    name: 'Data Editor',
    shortDescription: 'View and edit your datasets',
    detailedDescription: 'Inspect and modify your data directly within the application. Make corrections, add new entries, or adjust variable properties.',
    path: 'data-management/editor',
    icon: <EditIcon />,
    category: 'Edit',
    color: '#FF9800', // Orange
  },
  {
    name: 'Data Transform',
    shortDescription: 'Apply transformations to your data',
    detailedDescription: 'Perform common data transformations such as creating new variables, recoding existing ones, or handling missing values.',
    path: 'data-management/transform',
    icon: <TransformIcon />,
    category: 'Transform',
    color: '#9C27B0', // Purple
  },
  {
    name: 'Dataset Manager',
    shortDescription: 'Manage your imported datasets',
    detailedDescription: 'View a list of all your imported datasets, rename them, or remove them from your workspace.',
    path: 'data-management/datasets',
    icon: <ListIcon />,
    category: 'Manage',
    color: '#795548', // Brown
  },
  {
    name: 'Variable Editor',
    shortDescription: 'Edit variable properties',
    detailedDescription: 'Modify variable names, types, labels, and other properties.',
    path: 'data-management/variables',
    icon: <VariableEditorIcon />,
    category: 'Edit',
    color: '#00BCD4', // Cyan
  },
];

const DataManagementOptions: React.FC<DataManagementOptionsProps> = ({ onNavigate }) => {
  const navigate = useNavigate();
  const handleNavigate = (path: string) => {
    if (onNavigate) {
      onNavigate(path);
    } else {
      navigate(path);
    }
  };
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  // Generate structured data for data management
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Manage Statistical Data",
      "description": "Comprehensive guide to importing, cleaning, transforming, and managing datasets for statistical analysis.",
      "totalTime": "PT20M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Raw dataset files (CSV, Excel, SPSS, etc.)"
        },
        {
          "@type": "HowToSupply",
          "name": "Data dictionary or codebook"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Data Management Suite"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Import data",
          "text": "Upload datasets from various sources including CSV, Excel, SPSS, or connect to external databases and APIs"
        },
        {
          "@type": "HowToStep",
          "name": "Clean and validate",
          "text": "Remove duplicates, handle missing values, validate data types, and check for outliers or inconsistencies"
        },
        {
          "@type": "HowToStep",
          "name": "Transform variables",
          "text": "Create new variables, recode existing ones, apply mathematical transformations, and merge datasets"
        },
        {
          "@type": "HowToStep",
          "name": "Export results",
          "text": "Save cleaned datasets in multiple formats for analysis, sharing, or archival purposes"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Clean Statistical Dataset",
        "description": "Analysis-ready dataset with proper variable types, labels, and documentation"
      }
    };
  };

  const categories = ['All', 'Import', 'Export', 'Edit', 'Transform', 'Manage'];

  const filteredOptions = selectedCategory === 'All'
    ? dataManagementOptions
    : dataManagementOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Import': return <ImportIcon />;
      case 'Export': return <ExportIcon />;
      case 'Edit': return <EditIcon />;
      case 'Transform': return <TransformIcon />;
      case 'Manage': return <ListIcon />;
      default: return <ImportIcon />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Data Management Tools | DataStatPro - Import, Clean & Transform Data</title>
        <meta name="description" content="Professional data management tools for statistical analysis: import CSV/Excel/SPSS files, clean datasets, transform variables, handle missing values, and export results. Comprehensive data preparation suite." />
        <meta name="keywords" content="data management, data import, data cleaning, data transformation, CSV import, Excel import, SPSS import, missing values, data validation, variable transformation, dataset management, data export" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Statistical data management and preparation" />
        <meta name="ai:file-formats" content="CSV, Excel, SPSS, SAS, Stata, JSON, XML" />
        <meta name="ai:operations" content="Import, Clean, Transform, Validate, Export, Merge" />
        
        {/* Structured data for data management */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for data management */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What file formats can I import?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "DataStatPro supports CSV, Excel (.xlsx, .xls), SPSS (.sav), SAS, Stata, JSON, XML, and can connect to databases and APIs. Most common statistical software formats are supported."
                }
              },
              {
                "@type": "Question",
                "name": "How do I handle missing values in my dataset?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Use the Data Cleaning tools to identify missing values, choose appropriate handling methods (deletion, imputation, or flagging), and validate the impact on your analysis."
                }
              },
              {
                "@type": "Question",
                "name": "Can I merge multiple datasets?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Yes, use the Data Transformation tools to merge datasets by common variables, append rows, or join tables using various merge types (inner, outer, left, right joins)."
                }
              },
              {
                "@type": "Question",
                "name": "What export formats are available?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Export cleaned datasets as CSV, Excel, SPSS, JSON, or directly to statistical software. Include metadata, variable labels, and documentation for reproducible research."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section with AI-optimized content */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Data Management - Import, Clean & Transform Statistical Data
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Professional data management suite for statistical analysis: import from multiple formats, clean datasets, 
          handle missing values, transform variables, and export analysis-ready data with full documentation.
        </Typography>
        
        {/* AI-friendly feature overview */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(theme.palette.warning.main, 0.05), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Capabilities:</strong> Import (CSV, Excel, SPSS, SAS, Stata) • Clean (Missing Values, Duplicates, Outliers) • 
            Transform (Variables, Merging, Recoding) • Export (Multiple Formats) • Validate (Data Quality, Types)
          </Typography>
        </Box>
        
        <Typography variant="body1" color="text.secondary">
          Prepare your data for analysis with a comprehensive suite of data management tools with data validation, 
          quality checks, and reproducible workflows for reliable statistical analysis.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography 
                    variant="h6" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => handleNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Launch ${option.name} data management tool`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Bringing data in?</strong> Use Data Import
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Saving your work?</strong> Use Data Export
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Correcting values?</strong> Use the Data Editor
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Modifying variables?</strong> Use Data Transform
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Modifying variable properties?</strong> Use the Variable Editor
            </Typography>
             <Typography variant="body2" color="text.secondary">
              • <strong>Viewing/deleting datasets?</strong> Use the Dataset Manager
            </Typography>
          </Box>
        </Box>
      </Paper>
      </Container>
    </>
  );
};

export default DataManagementOptions;
