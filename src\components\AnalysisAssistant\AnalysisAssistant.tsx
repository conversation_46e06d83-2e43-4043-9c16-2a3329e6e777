import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  AlertTitle,
  CircularProgress,
  useTheme,
  alpha,
  Snackbar,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup,
  Collapse,
  IconButton,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Badge,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Avatar,
  Rating,
  LinearProgress,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Lightbulb as LightbulbIcon,
  Psychology as PsychologyIcon,
  Help as HelpIcon,
  ArrowForward as ArrowForwardIcon,
  CheckCircle as CheckCircleIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  FilterList as FilterListIcon,
  Timeline as TimelineIcon,
  Calculate as CalculateIcon,
  Assessment as AssessmentIcon,
  Science as ScienceIcon,
  TableChart as TableChartIcon,
  BarChart as BarChartIcon,
  Settings as SettingsIcon,
  School as SchoolIcon,
  ViewList as ViewListIcon,
  GridView as GridViewIcon,
  Category as CategoryIcon,
  Search as ExploreIcon,
  AutoAwesome as AutoAwesomeIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  Star as StarIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,

  ModelTraining as ModelTrainingIcon,
  Feedback as FeedbackIcon,
  Speed as SpeedIcon,
  TrendingUp as TrendingUpIcon,
  SmartToy as SmartToyIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Insights as InsightsIcon,
  EmojiObjects as EmojiObjectsIcon,
  Memory as MemoryIcon,
  Tune as TuneIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useData } from '../../context/DataContext';
import {
  analyzeDataset,
  generateSmartRecommendations,
  generateVariableInsights,
  DatasetAnalysis,
  AnalysisRecommendation
} from '../../utils/dataAnalysisService';
import { trainingDB, CuratedSuggestion } from '../../utils/trainingDatabase';
import { processQuery as enhancedProcessQuery, fuzzyMatchKeywords, generateContextualSuggestions, ProcessedQuery } from '../../utils/queryProcessor';
import { initializeCompleteTrainingSystem } from '../../utils/trainingDataInitializer';
import DataQualityAssessment from './DataQualityAssessment';
import InteractiveStatisticalAdvisor from './InteractiveStatisticalAdvisor';


interface AnalysisAssistantProps {
  onNavigate?: (path: string) => void;
}

interface MatchDetails {
  scenarioMatch: boolean;
  patternMatch: boolean;
  keywordMatches: number;
  directMatches: number;
}

interface AnalysisSuggestion {
  id: string;
  text: string;
  description?: string;
  type: string;
  category: string;
  path?: string;
  tags?: string[];
  prerequisites?: string[];
  relevanceScore?: number;
  reason?: string;
  matchDetails?: MatchDetails;
}

interface AnalysisScenario {
  name: string;
  keywords: string[];
  patterns?: RegExp[];
  suggestions: string[];
  contextualAdvice?: string;
}

// Phase 4: Advanced Features Interfaces
interface UserFeedback {
  id: string;
  suggestionId: string;
  query: string;
  rating: number; // 1-5 stars
  helpful: boolean;
  comment?: string;
  timestamp: Date;
  context?: QueryContext;
}

interface PerformanceMetrics {
  totalQueries: number;
  successfulMatches: number;
  averageRating: number;
  userSatisfactionScore: number;
  responseTime: number;
  accuracyScore: number;
}



interface LearningSession {
  id: string;
  queries: string[];
  feedback: UserFeedback[];
  improvements: string[];
  timestamp: Date;
}

type ViewMode = 'assistant' | 'quality' | 'feedback' | 'advisor';

const AnalysisAssistant: React.FC<AnalysisAssistantProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentDataset } = useData();

  const [query, setQuery] = useState<string>('');
  const [viewMode, setViewMode] = useState<ViewMode>('assistant');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [notification, setNotification] = useState<{open: boolean, message: string}>({open: false, message: ''});
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(false);
  const [listView, setListView] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<AnalysisSuggestion[]>([]);
  const [showAllAnalyses, setShowAllAnalyses] = useState(false);

  // New state for dataset analysis
  const [datasetAnalysis, setDatasetAnalysis] = useState<DatasetAnalysis | null>(null);
  const [smartRecommendations, setSmartRecommendations] = useState<AnalysisRecommendation[]>([]);
  const [variableInsights, setVariableInsights] = useState<string[]>([]);

  // Feedback dialog state
  const [showFeedbackDialog, setShowFeedbackDialog] = useState<boolean>(false);
  const [feedbackRating, setFeedbackRating] = useState<number>(0);
  const [feedbackComment, setFeedbackComment] = useState<string>('');
  const [selectedSuggestionForFeedback, setSelectedSuggestionForFeedback] = useState<AnalysisSuggestion | null>(null);

  // Submit user feedback function
  const submitUserFeedback = useCallback((suggestion: AnalysisSuggestion, rating: number, isPositive: boolean, comment: string) => {
    try {
      // Create feedback object
      const feedback = {
        id: `feedback-${Date.now()}`,
        suggestionId: suggestion.id,
        rating,
        isPositive,
        comment,
        timestamp: new Date().toISOString(),
        suggestionText: suggestion.text,
        suggestionCategory: suggestion.category
      };

      // Log feedback for debugging
      console.log('User feedback submitted:', feedback);

      // Show success notification
      setNotification({
        open: true,
        message: 'Thank you for your feedback!'
      });

      // Close feedback dialog and reset state
      setShowFeedbackDialog(false);
      setFeedbackRating(0);
      setFeedbackComment('');
      setSelectedSuggestionForFeedback(null);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setNotification({
        open: true,
        message: 'Error submitting feedback. Please try again.'
      });
    }
  }, []);

  // Initialize training system and analyze dataset when it changes
  useEffect(() => {
    // Initialize training system on first load
    initializeCompleteTrainingSystem();

    if (currentDataset) {
      const analysis = analyzeDataset(currentDataset);
      setDatasetAnalysis(analysis);

      if (analysis.hasData) {
        const recommendations = generateSmartRecommendations(currentDataset);
        setSmartRecommendations(recommendations);

        const insights = generateVariableInsights(analysis.variableAnalysis);
        setVariableInsights(insights);
      } else {
        setSmartRecommendations([]);
        setVariableInsights([]);
      }
    } else {
      setDatasetAnalysis(null);
      setSmartRecommendations([]);
      setVariableInsights([]);
    }
  }, [currentDataset]);

  // Comprehensive knowledge base with all analyses
  const allAnalyses: AnalysisSuggestion[] = [
    // Descriptive Analysis
    { 
      id: 'DESC1', 
      text: "Descriptive Analysis", 
      description: "Calculate mean, median, mode, SD, variance, range, quartiles, skewness, kurtosis",
      type: "descriptive", 
      category: "Descriptive Analysis",
      path: "stats/summary",
      tags: ["summary", "statistics", "mean", "median", "standard deviation", "basic", "describe", "central tendency", "variability"]
    },
    { 
      id: 'DESC2', 
      text: "Frequency Tables", 
      description: "Generate frequency and percentage tables for categorical variables",
      type: "descriptive", 
      category: "Descriptive Analysis",
      path: "stats/frequency",
      tags: ["frequency", "categorical", "count", "percentage", "basic", "nominal", "ordinal"]
    },
    { 
      id: 'DESC3', 
      text: "Cross Tabulation", 
      description: "Create two-way tables showing joint frequency distribution",
      type: "descriptive", 
      category: "Descriptive Analysis",
      path: "stats/crosstabulation",
      tags: ["crosstab", "contingency", "two-way", "association", "joint distribution"]
    },
    { 
      id: 'DESC4', 
      text: "Normality Test", 
      description: "Test if data follows normal distribution (Kolmogorov-Smirnov, Shapiro-Wilk)",
      type: "assumption", 
      category: "Descriptive Analysis",
      path: "stats/normality",
      tags: ["normal", "distribution", "assumption", "K-S test", "shapiro", "gaussian", "parametric assumptions"]
    },

    // T-Tests
    { 
      id: 'TTEST1', 
      text: "One-Sample T-Test", 
      description: "Compare sample mean to a hypothesized population mean",
      type: "test", 
      category: "T-Tests",
      path: "inferential-stats/one-sample-ttest",
      tags: ["t-test", "one sample", "mean comparison", "hypothesis", "parametric", "population mean"]
    },
    { 
      id: 'TTEST2', 
      text: "Independent Samples T-Test", 
      description: "Compare means of two independent groups",
      type: "test", 
      category: "T-Tests",
      path: "inferential-stats/independent-samples-ttest",
      tags: ["t-test", "two groups", "independent", "between subjects", "parametric", "unpaired", "two sample"]
    },
    { 
      id: 'TTEST3', 
      text: "Paired Samples T-Test", 
      description: "Compare means of two related samples or repeated measurements",
      type: "test", 
      category: "T-Tests",
      path: "inferential-stats/paired-samples-ttest",
      tags: ["t-test", "paired", "related", "within subjects", "pre-post", "parametric", "repeated", "matched", "before after"]
    },

    // Non-parametric Tests
    { 
      id: 'NONPAR1', 
      text: "Mann-Whitney U Test", 
      description: "Non-parametric alternative to independent t-test",
      type: "test", 
      category: "Non-parametric Tests",
      path: "inferential-stats/mann-whitney-u-test",
      tags: ["mann-whitney", "wilcoxon rank-sum", "non-parametric", "two groups", "independent", "rank"]
    },
    { 
      id: 'NONPAR2', 
      text: "Wilcoxon Signed-Rank Test", 
      description: "Non-parametric alternative to paired t-test",
      type: "test", 
      category: "Non-parametric Tests",
      path: "inferential-stats/wilcoxon-signed-rank-test",
      tags: ["wilcoxon", "signed-rank", "non-parametric", "paired", "related", "matched","pre-post", "Before After"]
    },
    { 
      id: 'NONPAR3', 
      text: "Kruskal-Wallis Test", 
      description: "Non-parametric alternative to one-way ANOVA",
      type: "test", 
      category: "Non-parametric Tests",
      path: "inferential-stats/kruskal-wallis-test",
      tags: ["kruskal-wallis", "non-parametric", "multiple groups", "ranks", "three groups", "Three or more groups","H-test"]
    },
    { 
      id: 'NONPAR4', 
      text: "Friedman Test", 
      description: "Non-parametric alternative to repeated measures ANOVA",
      type: "test", 
      category: "Non-parametric Tests",
      path: "inferential-stats/friedman-test",
      tags: ["friedman", "non-parametric", "repeated measures", "ranks", "within subjects"]
    },

    // ANOVA
    { 
      id: 'ANOVA1', 
      text: "One-Way ANOVA", 
      description: "Compare means of three or more independent groups",
      type: "test", 
      category: "ANOVA",
      path: "inferential-stats/one-way-anova",
      tags: ["anova", "analysis of variance", "multiple groups", "f-test", "parametric", "three groups", "omnibus"]
    },
    { 
      id: 'ANOVA2', 
      text: "Two-Way ANOVA", 
      description: "Examine effects of two factors and their interaction",
      type: "test", 
      category: "ANOVA",
      path: "inferential-stats/two-way-anova",
      tags: ["anova", "two-way", "factorial", "interaction", "parametric", "main effects"]
    },
    { 
      id: 'ANOVA3', 
      text: "Repeated Measures ANOVA", 
      description: "Compare means across multiple time points or conditions",
      type: "test", 
      category: "ANOVA",
      path: "inferential-stats/repeated-measures-anova",
      tags: ["anova", "repeated measures", "within subjects", "longitudinal", "parametric", "time points"]
    },

    // Categorical Tests
    { 
      id: 'CAT1', 
      text: "Chi-Square Test", 
      description: "Test association between two categorical variables",
      type: "categorical", 
      category: "Categorical Tests",
      path: "inferential-stats/chi-square-test",
      tags: ["chi-square", "χ²", "association", "independence", "categorical", "contingency", "nominal"]
    },

    // Correlation & Regression
    { 
      id: 'CORR1', 
      text: "Correlation Matrix", 
      description: "Calculate Pearson, Spearman, or Kendall correlations between variables",
      type: "correlation", 
      category: "Correlation & Regression",
      path: "correlation-analysis/matrix",
      tags: ["correlation", "pearson", "spearman", "relationship", "association", "linear", "monotonic"]
    },
    { 
      id: 'REG1', 
      text: "Linear Regression", 
      description: "Model linear relationships between dependent and independent variables",
      type: "regression", 
      category: "Correlation & Regression",
      path: "correlation-analysis/regression",
      tags: ["regression", "linear", "prediction", "model", "least squares", "continuous outcome", "predictor"]
    },
    { 
      id: 'REG2', 
      text: "Logistic Regression", 
      description: "Model binary outcomes and calculate odds ratios",
      type: "regression", 
      category: "Correlation & Regression",
      path: "correlation-analysis/logistic",
      tags: ["logistic", "binary", "classification", "odds ratio", "probability", "dichotomous", "yes no"]
    },
    { 
      id: 'WORKFLOW1', 
      text: "Correlation & Regression Workflow", 
      description: "Guided step-by-step workflow for correlation and regression analysis",
      type: "workflow", 
      category: "Guided Workflows",
      path: "correlation-regression-workflow",
      tags: ["workflow", "guided", "step-by-step", "correlation", "regression", "analysis", "tutorial"]
    },

    // Advanced Methods
    { 
      id: 'ADV1', 
      text: "Exploratory Factor Analysis", 
      description: "Identify underlying factors in a set of variables",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/efa",
      tags: ["factor analysis", "EFA", "latent variables", "dimension reduction", "construct"]
    },
    { 
      id: 'ADV2', 
      text: "Mediation and Moderation", 
      description: "Analyze indirect effects and conditional relationships",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/mediation",
      tags: ["mediation", "moderation", "indirect effect", "interaction", "conditional"]
    },
    { 
      id: 'ADV3', 
      text: "Reliability Analysis", 
      description: "Assess internal consistency with Cronbach's alpha",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/reliability",
      tags: ["reliability", "cronbach alpha", "internal consistency", "scale", "questionnaire"]
    },
    { 
      id: 'ADV4', 
      text: "Survival Analysis", 
      description: "Analyze time-to-event data with Kaplan-Meier and Cox regression",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/survival",
      tags: ["survival", "time-to-event", "kaplan-meier", "cox", "hazard", "censored"]
    },
    { 
      id: 'ADV5', 
      text: "Cluster Analysis", 
      description: "Group similar data points using K-means, hierarchical clustering",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/cluster",
      tags: ["cluster", "grouping", "k-means", "hierarchical", "segmentation", "similarity"]
    },
    { 
      id: 'ADV6', 
      text: "Meta Analysis", 
      description: "Synthesize findings from multiple studies",
      type: "advanced", 
      category: "Advanced Methods",
      path: "advanced-analysis/meta-analysis",
      tags: ["meta-analysis", "systematic review", "effect size", "forest plot", "heterogeneity"]
    },

    // Epidemiology Calculators
    { 
      id: 'EPI1', 
      text: "2x2 Table Analysis", 
      description: "Analyze 2x2 contingency tables",
      type: "epidemiology", 
      category: "Epidemiology",
      path: "epicalc/2x2",
      tags: ["2x2 table", "odds ratio", "epidemiology", "contingency", "case-control"]
    },
    { 
      id: 'EPI2', 
      text: "Diagnostic Test Evaluation", 
      description: "Evaluate diagnostic test performance",
      type: "epidemiology", 
      category: "Epidemiology",
      path: "epicalc/diagnostic",
      tags: ["diagnostic", "sensitivity", "specificity", "test performance", "accuracy"]
    },
    { 
      id: 'EPI3', 
      text: "Screening Test Analysis", 
      description: "Analyze screening test characteristics",
      type: "epidemiology", 
      category: "Epidemiology",
      path: "epicalc/screening",
      tags: ["screening", "test characteristics", "prevalence", "predictive values"]
    },
    { 
      id: 'EPI4', 
      text: "Epidemiological Calculators", 
      description: "Calculate epidemiological measures and statistics",
      type: "epidemiology", 
      category: "Epidemiology",
      path: "epicalc",
      tags: ["epidemiology", "measures", "statistics", "public health"]
    },

    // Sample Size Calculators
    { 
      id: 'SS1', 
      text: "One Sample Calculator", 
      description: "Calculate sample size for single group studies",
      type: "sample_size", 
      category: "Sample Size",
      path: "samplesize/one-sample",
      tags: ["sample size", "power", "one sample", "single group", "planning"]
    },
    { 
      id: 'SS2', 
      text: "Two Sample Calculator", 
      description: "Calculate sample size for comparing two groups",
      type: "sample_size", 
      category: "Sample Size",
      path: "samplesize/two-sample",
      tags: ["sample size", "power", "two groups", "comparison", "planning"]
    },
    { 
      id: 'SS3', 
      text: "Paired Sample Calculator", 
      description: "Calculate sample size for paired or matched designs",
      type: "sample_size", 
      category: "Sample Size",
      path: "samplesize/paired-sample",
      tags: ["sample size", "power", "paired", "matched", "repeated", "planning"]
    },
    { 
      id: 'SS4', 
      text: "More Than Two Groups Calculator", 
      description: "Calculate sample size for comparing multiple groups",
      type: "sample_size", 
      category: "Sample Size",
      path: "samplesize/more-than-two-groups",
      tags: ["sample size", "power", "anova", "multiple groups", "planning"]
    },

    // Visualizations
    { 
      id: 'VIZ1', 
      text: "Bar Chart", 
      description: "Compare values across categories",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/bar",
      tags: ["bar chart", "categorical", "comparison", "frequency", "counts"]
    },
    { 
      id: 'VIZ2', 
      text: "Box Plot", 
      description: "Show distribution, median, quartiles, and outliers",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/boxplot",
      tags: ["box plot", "distribution", "quartiles", "outliers", "whiskers", "five number summary"]
    },
    { 
      id: 'VIZ3', 
      text: "Histogram", 
      description: "Visualize frequency distribution of numerical data",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/histogram",
      tags: ["histogram", "frequency", "distribution", "bins", "continuous", "shape"]
    },
    { 
      id: 'VIZ4', 
      text: "Scatter Plot", 
      description: "Show relationship between two numerical variables",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/scatter",
      tags: ["scatter", "correlation", "relationship", "x-y plot", "regression", "bivariate"]
    },
    { 
      id: 'VIZ5', 
      text: "RainCloud Plot", 
      description: "Combine box plot, scatter plot, and density visualization",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/raincloud",
      tags: ["raincloud", "distribution", "density", "hybrid", "violin", "raw data"]
    },
    { 
      id: 'VIZ6', 
      text: "Pie Chart", 
      description: "Show proportions of categorical data",
      type: "visualization", 
      category: "Visualizations",
      path: "charts/pie",
      tags: ["pie chart", "proportions", "percentage", "parts of whole", "composition"]
    },

    // Data Management
    { 
      id: 'DATA1', 
      text: "Data Import", 
      description: "Import data from CSV, Excel, or Google Sheets",
      type: "data", 
      category: "Data Management",
      path: "data-management/import",
      tags: ["import", "load", "csv", "excel", "upload", "file"]
    },
    { 
      id: 'DATA2', 
      text: "Data Editor", 
      description: "View and edit your datasets directly",
      type: "data", 
      category: "Data Management",
      path: "data-management/editor",
      tags: ["edit", "modify", "clean", "transform", "spreadsheet"]
    },
    { 
      id: 'DATA3', 
      text: "Variable Editor", 
      description: "Modify variable names, types, and properties",
      type: "data", 
      category: "Data Management",
      path: "data-management/variables",
      tags: ["variables", "rename", "recode", "type", "label", "metadata"]
    },
    { 
      id: 'DATA4', 
      text: "Data Transform", 
      description: "Create new variables, handle missing values",
      type: "data", 
      category: "Data Management",
      path: "data-management/transform",
      tags: ["transform", "compute", "missing", "recode", "derive", "calculate"]
    },

    // Publication Tools
    { 
      id: 'PUB1', 
      text: "Table 1", 
      description: "Generate baseline characteristics table",
      type: "publication", 
      category: "Publication Tools",
      path: "publication-ready/table1",
      tags: ["table 1", "baseline", "characteristics", "demographics", "descriptive"]
    },
    { 
      id: 'PUB2', 
      text: "Table 2", 
      description: "Create outcome comparison table",
      type: "publication", 
      category: "Publication Tools",
      path: "publication-ready/table2",
      tags: ["table 2", "outcomes", "results", "comparison", "primary endpoint"]
    },
    { 
      id: 'PUB3', 
      text: "SMD Table", 
      description: "Calculate standardized mean differences",
      type: "publication", 
      category: "Publication Tools",
      path: "publication-ready/smd-table",
      tags: ["SMD", "standardized", "effect size", "Cohen's d", "balance"]
    },
    { 
      id: 'PUB4', 
      text: "Regression Table", 
      description: "Format regression results for publication",
      type: "publication", 
      category: "Publication Tools",
      path: "publication-ready/regression-table",
      tags: ["regression table", "coefficients", "odds ratios", "confidence intervals", "formatted"]
    },
    { 
      id: 'PUB5', 
      text: "Flow Diagram", 
      description: "Create CONSORT/STROBE/PRISMA flow diagrams",
      type: "publication", 
      category: "Publication Tools",
      path: "publication-ready/flow-diagram",
      tags: ["flow diagram", "CONSORT", "STROBE", "PRISMA", "participant flow", "enrollment"]
    }
  ];

  // Enhanced analysis scenarios with patterns and contextual advice
  const analysisScenarios: AnalysisScenario[] = [
    {
      name: "Comparing Two Independent Groups",
      keywords: [
        "compare means of two independent groups", "difference in test scores between male and female students",
        "compare average height of two distinct populations", "is there a significant difference between two unrelated samples",
        "test if two groups are statistically different on a continuous variable", "compare the average income of two different cities",
        "compare group a and group b on a numerical outcome", "is there a difference in means for two separate samples",
        "compare two independent groups", "difference between two groups", "group a vs group b",
        "control experimental two groups", "compare means between two groups", "compare two groups",
        "two independent groups", "difference between two groups", "group a vs group b", "control experimental two"
      ],
      patterns: [/compar\w*\s+two\s+independent\s+groups?/i, /difference\s+between\s+two\s+unrelated\s+samples/i, /group\s+a\s+vs\s+group\s+b/i],
      suggestions: ["TTEST2", "NONPAR1", "VIZ2", "DESC1", "PUB1"],
      contextualAdvice: "For comparing two independent groups, use Independent T-Test if data is normally distributed, or Mann-Whitney U Test if not."
    },
    {
      name: "Linear Regression",
      keywords: [
        "is there a relationship between hours studied and exam performance", "predict house prices based on square footage",
        "effect of advertising spend on sales", "model the relationship between a dependent variable and one or more independent variables",
        "forecast future sales based on past marketing efforts", "predict a continuous variable", "predict a number",
        "simple linear regression", "multiple linear regression", "predict blood pressure",
        "how do i predict blood pressure", "predict continuous variable", "predict a number", "linear regression",
        "check for linearity and homoscedasticity", "validate regression model assumptions with Residual Plots"
      ],
      patterns: [/linear\s+regression/i, /predict\s+(house\s+prices|sales|blood\s+pressure|continuous\s+variable|number)/i, /relationship\s+between\s+hours\s+studied\s+and\s+exam\s+performance/i],
      suggestions: ["REG1", "CORR1", "VIZ4", "PUB4"],
      contextualAdvice: "Use Linear Regression to predict continuous outcomes. Check assumptions: linearity, normality of residuals, homoscedasticity, and independence."
    },
    {
      name: "Paired Samples T-Test",
      keywords: [
        "pre and post treatment measurements for the same group", "compare blood pressure before and after medication in the same patients",
        "evaluate the impact of a training program on employee productivity", "test for a difference between two related measurements",
        "assess the change in a variable over time for the same subjects", "pre-post analysis", "related groups comparison",
        "before after study", "pre-post", "pre post", "related groups", "paired measurements", "before after", "pre/post"
      ],
      patterns: [/pre\s*and\s*post\s+treatment/i, /before\s*and\s*after\s+medication/i, /paired\s+samples?\s+t-test/i, /change\s+in\s+a\s+variable\s+over\s+time/i],
      suggestions: ["TTEST3", "NONPAR2", "ANOVA3", "VIZ4", "VIZ2"],
      contextualAdvice: "For paired or repeated measurements, use Paired T-Test for two time points or Repeated Measures ANOVA for multiple time points."
    },
    {
      name: "One-Way ANOVA",
      keywords: [
        "compare average across three different plant species", "difference in customer satisfaction across multiple product versions",
        "impact of different teaching methods on student engagement", "test for differences among means of three or more independent groups",
        "determine if there's a significant effect of a single categorical factor on a continuous outcome", "compare three groups",
        "compare multiple groups", "anova for independent groups", "compare over two groups", "multiple group comparison anova",
        "comparing more than two groups", "anova test", "3 groups", "4 groups", "multiple independent groups",
        "anova for multiple groups", "perform post-hoc tests after ANOVA", "compare means of more than two groups", "anova", "more than two groups"
      ],
      patterns: [/one-way\s+anova/i, /compare\s+(three|multiple|more\s+than\s+two)\s+groups?/i, /impact\s+of\s+different\s+teaching\s+methods/i],
      suggestions: ["ANOVA1", "NONPAR3", "VIZ2", "PUB1", "ANOVA2"],
      contextualAdvice: "For comparing multiple groups, use One-Way ANOVA if data is normally distributed, or Kruskal-Wallis Test if not. Consider post-hoc tests if significant."
    },
    {
      name: "Logistic Regression",
      keywords: [
        "determine if a categorical variable influences a binary outcome", "predict customer churn based on demographic data",
        "likelihood of loan default given credit score", "model the probability of a binary event occurring",
        "classify emails as spam or not spam based on content", "predict a binary outcome", "predict disease status",
        "predict categorical variable", "predict a category", "how do i predict disease status",
        "predict categorical variable", "predict a category", "logistic regression", "predict binary outcome",
        "evaluate binary classification model performance", "roc curve", "odds ratio"
      ],
      patterns: [/logistic\s+regression/i, /predict\s+(customer\s+churn|loan\s+default|binary\s+outcome|disease\s+status|categorical\s+variable|category)/i, /classify\s+emails\s+as\s+spam/i],
      suggestions: ["REG2", "PUB4", "EPI1"],
      contextualAdvice: "Use Logistic Regression for binary outcomes (yes/no, disease/healthy). Results are expressed as odds ratios with confidence intervals."
    },
    {
      name: "Correlation Analysis",
      keywords: [
        "relationship between two continuous variables", "strength and direction of association between two variables",
        "explore the link between exercise and cholesterol levels", "measure how two quantitative variables move together",
        "investigate the association between temperature and ice cream sales", "correlation between two numerical variables",
        "how related are two variables", "pearson correlation", "spearman correlation", "correlation",
        "relationship between two variables", "association numerical", "how related are",
        "visualize correlations between multiple variables"
      ],
      patterns: [/correlat/i, /relationship\s+between/i, /associat\w*\s+between/i, /how\s+related\s+are/i],
      suggestions: ["CORR1", "REG1", "VIZ4", "REG2"],
      contextualAdvice: "Use Pearson correlation for linear relationships between continuous variables, or Spearman correlation for monotonic relationships or ordinal data."
    },
    {
      name: "Guided Correlation & Regression Workflow",
      keywords: [
        "step by step correlation analysis", "guided regression analysis", "help me with correlation",
        "walk me through regression", "correlation and regression tutorial", "learn correlation analysis",
        "regression analysis tutorial", "guided analysis workflow", "step-by-step statistical analysis",
        "help with statistical analysis", "correlation regression workflow", "guided statistical workflow",
        "tutorial for correlation", "tutorial for regression", "learn regression analysis",
        "beginner correlation analysis", "beginner regression analysis", "statistical analysis guide"
      ],
      patterns: [/guided\s+(correlation|regression)/i, /step\s+by\s+step\s+(correlation|regression|statistical)/i, /tutorial\s+(correlation|regression)/i, /workflow\s+(correlation|regression)/i, /help\s+me\s+with\s+(correlation|regression)/i],
      suggestions: ["WORKFLOW1", "CORR1", "REG1", "REG2"],
      contextualAdvice: "Use the guided workflow for step-by-step assistance with correlation and regression analysis, including assumption checking and result interpretation."
    },
    {
      name: "One-Sample T-Test",
      keywords: [
        "compare a sample mean to a known population mean", "is the average weight of a new product batch different from the standard",
        "test if a drug's effect differs from a placebo's known effect", "determine if a sample mean is significantly different from a hypothesized population mean",
        "compare a sample to a population standard"
      ],
      patterns: [/one-sample\s+t-test/i, /compare\s+a\s+sample\s+mean\s+to\s+a\s+known\s+population\s+mean/i],
      suggestions: ["TTEST1", "DESC1"],
      contextualAdvice: "Use a One-Sample T-Test to compare a sample mean to a known or hypothesized population mean."
    },
    {
      name: "Kruskal-Wallis Test",
      keywords: [
        "compare more than two independent groups, non-parametric", "difference in rankings among several independent samples",
        "non-parametric alternative to one-way ANOVA", "test if there are significant differences in medians across multiple independent groups",
        "compare three or more groups non-normal data", "non-parametric comparison of more than two independent groups"
      ],
      patterns: [/kruskal-wallis\s+test/i, /compare\s+(three|more\s+than\s+two)\s+groups\s+non-parametric/i],
      suggestions: ["NONPAR3", "VIZ2"],
      contextualAdvice: "Kruskal-Wallis is the non-parametric alternative to One-Way ANOVA for comparing three or more independent groups when data is not normally distributed."
    },
    {
      name: "Mann-Whitney U Test",
      keywords: [
        "compare two independent groups, non-parametric", "difference in distributions between two independent samples",
        "non-parametric alternative to independent samples t-test", "test if two independent samples come from the same distribution",
        "compare two groups non-normal data", "compare two independent groups non-parametric"
      ],
      patterns: [/mann-whitney\s+u\s+test/i, /compare\s+two\s+independent\s+groups\s+non-parametric/i],
      suggestions: ["NONPAR1", "VIZ2"],
      contextualAdvice: "Mann-Whitney U Test is the non-parametric alternative to the Independent Samples T-Test for comparing two independent groups when data is not normally distributed."
    },
    {
      name: "Wilcoxon Signed-Rank Test",
      keywords: [
        "compare two related samples, non-parametric", "difference in paired observations, non-parametric",
        "non-parametric alternative to paired samples t-test", "test for differences between two related samples when data is not normally distributed",
        "pre-post non-normal data", "compare population mean non-parametric", "compare one group to a median value",
        "compare two related groups non-parametric"
      ],
      patterns: [/wilcoxon\s+signed-rank\s+test/i, /compare\s+two\s+related\s+samples\s+non-parametric/i, /pre-post\s+non-normal\s+data/i],
      suggestions: ["NONPAR2", "VIZ2"],
      contextualAdvice: "Wilcoxon Signed-Rank Test is the non-parametric alternative to the Paired Samples T-Test for comparing two related samples when data is not normally distributed."
    },
    {
      name: "Two-Way ANOVA",
      keywords: [
        "analyze the effect of two categorical independent variables on a continuous dependent variable",
        "investigate interaction effects between two factors", "compare means across groups defined by two factors",
        "examine the main effects and interaction effects of two independent variables", "two factors continuous outcome",
        "interaction effects in two-way ANOVA", "analyze effects and interaction of two categorical variables"
      ],
      patterns: [/two-way\s+anova/i, /effect\s+of\s+two\s+categorical\s+independent\s+variables/i, /interaction\s+effects\s+between\s+two\s+factors/i],
      suggestions: ["ANOVA2", "VIZ2"],
      contextualAdvice: "Two-Way ANOVA examines the main effects of two independent categorical variables and their interaction effect on a continuous dependent variable."
    },
    {
      name: "Repeated Measures ANOVA",
      keywords: [
        "compare means of three or more related groups", "effect of time on a dependent variable within the same subjects",
        "assess changes in performance over multiple trials", "test for differences in means across multiple time points for the same subjects",
        "analyze repeated measurements over time", "changes over time in the same subjects"
      ],
      patterns: [/repeated\s+measures\s+anova/i, /effect\s+of\s+time\s+on\s+a\s+dependent\s+variable\s+within\s+the\s+same\s+subjects/i],
      suggestions: ["ANOVA3", "VIZ4"],
      contextualAdvice: "Repeated Measures ANOVA is used to compare means across multiple time points or conditions for the same subjects."
    },
    {
      name: "Friedman Test",
      keywords: [
        "test for differences among three or more related samples, non-parametric", "compare multiple treatments applied to the same subjects, non-parametric",
        "non-parametric alternative to repeated measures ANOVA", "assess differences among repeated measurements on the same subjects when data is not normal",
        "compare three or more related groups non-parametric"
      ],
      patterns: [/friedman\s+test/i, /compare\s+(three|more\s+than\s+two)\s+related\s+samples\s+non-parametric/i],
      suggestions: ["NONPAR4", "VIZ2"],
      contextualAdvice: "Friedman Test is the non-parametric alternative to Repeated Measures ANOVA for comparing three or more related samples when data is not normally distributed."
    },
    {
      name: "Descriptive Statistics",
      keywords: [
        "summarize central tendency and spread of data", "calculate mean, median, mode, standard deviation",
        "understand the distribution of a single variable", "get summary statistics for a dataset",
        "explore data patterns", "data summary", "identify outliers", "data transformation",
        "grouped statistics", "summarize my data", "pivot table"
      ],
      patterns: [/summarize\s+data/i, /descriptive\s+statistics/i, /calculate\s+(mean|median|mode|standard\s+deviation)/i, /explore\s+data\s+patterns/i],
      suggestions: ["DESC1", "DESC2", "DESC3", "VIZ3", "VIZ2", "CORR1"],
      contextualAdvice: "Start with descriptive statistics and visualizations to understand your data distribution, identify outliers, and check for missing values."
    },
    {
      name: "Normality Test",
      keywords: [
        "check if data follows a normal distribution", "assess the gaussian nature of a dataset",
        "determine if a dataset is normally distributed", "is my data normal", "test normality",
        "test for equal variances", "test for normal distribution", "check for linearity and homoscedasticity",
        "what to do if my data is not normally distributed", "data not normal", "non-normal data",
        "nonparametric tests", "non-parametric", "normal distribution", "normally distributed", "check normality"
      ],
      patterns: [/normal\w*\s+distribut/i, /test\w*\s+normal/i, /check\w*\s+normal/i, /is\s+my\s+data\s+normal/i, /non-normal\s+data/i],
      suggestions: ["DESC4", "VIZ3", "VIZ2", "DESC1"],
      contextualAdvice: "Check normality using statistical tests (Shapiro-Wilk, K-S test) and visual methods (Histogram, Q-Q plot). Consider sample size when interpreting."
    },
    {
      name: "Histogram",
      keywords: ["visualize data distribution", "show frequency of data points in bins", "display the shape of a continuous variable's distribution", "visualize distribution of a single variable"],
      patterns: [/histogram/i, /visualize\s+data\s+distribution/i, /show\s+frequency\s+of\s+data\s+points/i],
      suggestions: ["VIZ3", "DESC1"],
      contextualAdvice: "Histograms are ideal for visualizing the frequency distribution and shape of a single continuous variable."
    },
    {
      name: "Bar Chart",
      keywords: ["display categories and their counts", "compare values across different categories", "show the frequency of categorical data", "visualize categorical frequency", "clustered bar chart", "grouped bar chart", "show relationships stratified by a third variable", "compare counts of categories"],
      patterns: [/bar\s+chart/i, /display\s+categories\s+and\s+their\s+counts/i, /compare\s+values\s+across\s+different\s+categories/i],
      suggestions: ["VIZ1", "DESC2"],
      contextualAdvice: "Bar charts are best for displaying and comparing counts or values across different categories."
    },
    {
      name: "Pie Chart",
      keywords: ["show parts of a whole", "represent proportions of a total", "visualize the composition of a single categorical variable", "visualize categorical proportions", "show proportions of a whole"],
      patterns: [/pie\s+chart/i, /show\s+parts\s+of\s+a\s+whole/i, /represent\s+proportions\s+of\s+a\s+total/i],
      suggestions: ["VIZ6", "DESC2"],
      contextualAdvice: "Pie charts are used to visualize the composition of a single categorical variable, showing proportions of a total."
    },
    {
      name: "Scatter Plot",
      keywords: ["display relationship between two continuous variables", "identify trends or clusters in bivariate data", "show the relationship between two quantitative variables", "visualize linear relationship with trendline", "faceted scatter plot", "show relationships stratified by a third variable", "plot relationship between two continuous variables"],
      patterns: [/scatter\s+plot/i, /display\s+relationship\s+between\s+two\s+continuous\s+variables/i, /identify\s+trends\s+or\s+clusters/i],
      suggestions: ["VIZ4", "CORR1", "REG1"],
      contextualAdvice: "Scatter plots are excellent for visualizing the relationship between two continuous variables and identifying trends or clusters."
    },
    {
      name: "Box Plot",
      keywords: ["show distribution and outliers for a single variable", "compare distributions across different groups", "visualize the five-number summary of a dataset", "visually compare group variances", "stratified box plot", "show distributions stratified by a third variable", "visualize data spread and outliers"],
      patterns: [/box\s+plot/i, /show\s+distribution\s+and\s+outliers/i, /compare\s+distributions\s+across\s+different\s+groups/i],
      suggestions: ["VIZ2", "DESC1", "DESC4"],
      contextualAdvice: "Box plots summarize the distribution of a variable, showing median, quartiles, and potential outliers, and are useful for comparing distributions across groups."
    },
    {
      name: "Cross-Tabulation",
      keywords: ["examine relationships between two categorical variables", "create a contingency table", "analyze the relationship between two nominal variables", "cross tabulation of categorical data", "test association between two categorical variables", "measure strength of association for categorical variables", "chi-square", "contingency table", "association categorical", "relationship categorical", "what test should i use for categorical data", "test for categorical data", "categorical data test", "test categorical", "analyze categorical", "contingency table analysis"],
      patterns: [/cross-tabulation/i, /contingency\s+table/i, /relationship\s+between\s+two\s+categorical\s+variables/i, /chi-square/i],
      suggestions: ["DESC3", "CAT1", "VIZ1"],
      contextualAdvice: "Cross-tabulation (contingency tables) is used to examine the relationship between two categorical variables, often followed by a Chi-Square test."
    },
    {
      name: "Frequency Tables",
      keywords: ["count occurrences of each category in a variable", "summarize qualitative data", "get counts and percentages for categorical variables", "count frequencies of categories"],
      patterns: [/frequency\s+tables?/i, /count\s+occurrences\s+of\s+each\s+category/i, /summarize\s+qualitative\s+data/i],
      suggestions: ["DESC2", "VIZ1"],
      contextualAdvice: "Frequency tables provide counts and percentages for each category of a categorical variable."
    },
    {
      name: "Sample Size and Power Estimation",
      keywords: [
        "how many subjects do I need for my study", "calculate required sample size", "determine sample size for an experiment",
        "what is the power of my study", "calculate statistical power", "power estimation for t-test",
        "sample size calculator", "sample size", "power analysis", "how many participants", "study planning", "calculate n",
        "determine sample size for a t-test", "calculate power for an ANOVA"
      ],
      patterns: [/sample\s+size/i, /power\s+analys/i, /how\s+many\s+(subjects|participants|patients)/i, /calculate\s+required\s+sample\s+size/i],
      suggestions: ["SS1", "SS2", "SS3", "SS4"],
      contextualAdvice: "Calculate sample size based on your study design, expected effect size, desired power (usually 80%), and significance level (usually 0.05)."
    },
    {
      name: "Epidemiological Calculators",
      keywords: [
        "epidemiological calculator", "2x2 table analysis", "calculate odds ratio from a 2x2 table",
        "calculate risk ratio from a 2x2 table", "epidemiology", "risk ratio", "odds ratio",
        "case control", "cohort", "prevalence", "odds ratio", "odds ratio for case-control study",
        "risk ratio calculation", "relative risk calculation", "risk ratio for cohort study",
        "adjusted odds ratio", "calculate adjusted OR", "control for confounding variables odds ratio",
        "adjusted risk ratio", "calculate adjusted RR", "control for confounding variables risk ratio",
        "multivariable logistic regression for adjusted odds ratio"
      ],
      patterns: [/epi-?calculator/i, /2x2\s+table\s+analys/i, /(calculate|what\s+is|interpret)\s+(odds|risk)\s+ratio/i, /adjusted\s+(odds|risk)\s+ratio/i],
      suggestions: ["EPI1", "EPI2", "EPI3", "EPI4", "REG2"],
      contextualAdvice: "Choose the appropriate epidemiological calculator based on your study design (case-control, cohort, cross-sectional) or the measure of association you need (Odds Ratio, Risk Ratio, Adjusted OR/RR)."
    },
    {
      name: "Cross-Sectional Study",
      keywords: ["study at a single point in time", "prevalence study", "what is a cross-sectional study", "study design for disease prevalence"],
      patterns: [/cross-sectional\s+study/i, /single\s+point\s+in\s+time/i, /prevalence\s+study/i],
      suggestions: ["EPI3", "DESC1", "DESC2"],
      contextualAdvice: "Cross-sectional studies assess prevalence at a single point in time. Use the Cross-Sectional Calculator for analysis."
    },
    {
      name: "Cohort Study",
      keywords: ["follow a group over time", "prospective study design", "retrospective cohort study", "incidence study", "longitudinal study", "longitudinal vs cohort", "study design for disease incidence"],
      patterns: [/cohort\s+study/i, /follow\s+a\s+group\s+over\s+time/i, /prospective\s+study\s+design/i, /incidence\s+study/i],
      suggestions: ["EPI2", "ANOVA3", "ADV4"],
      contextualAdvice: "Cohort studies follow groups over time to assess incidence and risk. Use the Cohort Calculator for analysis."
    },
    {
      name: "Case-Control Study",
      keywords: ["compare people with and without a disease", "study to find risk factors for a condition", "retrospective study design", "odds ratio", "odds ratio for case-control study", "study design for risk factors for a rare disease"],
      patterns: [/case-control\s+study/i, /compare\s+people\s+with\s+and\s+without\s+a\s+disease/i, /risk\s+factors\s+for\s+a\s+condition/i],
      suggestions: ["EPI1", "REG2"],
      contextualAdvice: "Case-control studies are retrospective and compare cases (with disease) to controls (without disease) to identify risk factors. Use the Case-Control Calculator for odds ratios."
    },
    {
      name: "Matched Case-Control Study",
      keywords: ["matched case-control study analysis", "analysis for matched pairs", "conditional logistic regression", "odds ratio for matched pairs"],
      patterns: [/matched\s+case-control\s+study/i, /analysis\s+for\s+matched\s+pairs/i, /conditional\s+logistic\s+regression/i],
      suggestions: ["EPI4", "TTEST3"],
      contextualAdvice: "Matched case-control studies involve pairing cases and controls. Use the Matched Case-Control Calculator for analysis, often involving McNemar's test or conditional logistic regression."
    },
    {
      name: "Survival Analysis",
      keywords: ["analyze time to event data", "Kaplan-Meier curve", "Cox proportional hazards model", "predict survival time", "compare survival rates between groups", "hazard ratio calculation", "time to relapse analysis", "event history analysis", "survival", "time to event", "kaplan meier", "cox regression", "censored data"],
      patterns: [/survival\s+analys/i, /kaplan-meier/i, /cox\s+regression/i, /time\s+to\s+event\s+data/i],
      suggestions: ["ADV4", "VIZ4"],
      contextualAdvice: "Survival analysis handles time-to-event data with censoring. Use Kaplan-Meier for descriptive analysis and Cox regression for modeling."
    },
    {
      name: "Mediation Analysis",
      keywords: ["test for mediation effect", "indirect effect analysis", "does a third variable explain the relationship"],
      patterns: [/mediation\s+effect/i, /indirect\s+effect/i, /third\s+variable\s+explain/i],
      suggestions: ["ADV2", "REG1"],
      contextualAdvice: "Mediation analysis examines if the relationship between two variables is explained by a third, mediating variable."
    },
    {
      name: "Moderation Analysis",
      keywords: ["test for moderation effect", "does a third variable change the relationship", "interaction effect analysis", "conditional effect analysis"],
      patterns: [/moderation\s+effect/i, /third\s+variable\s+change/i, /interaction\s+effect/i],
      suggestions: ["ADV2", "ANOVA2"],
      contextualAdvice: "Moderation analysis investigates if the relationship between two variables changes depending on the level of a third, moderating variable."
    },
    {
      name: "Exploratory Factor Analysis",
      keywords: ["reduce dimensionality of data", "identify underlying constructs", "factor analysis", "group correlated variables"],
      patterns: [/exploratory\s+factor\s+analys/i, /reduce\s+dimensionality/i, /underlying\s+constructs/i],
      suggestions: ["ADV1"],
      contextualAdvice: "Exploratory Factor Analysis (EFA) is used to identify underlying factors or constructs that explain the correlations among a set of observed variables."
    },
    {
      name: "Meta-Analysis",
      keywords: ["combine results from multiple studies", "synthesize research findings", "calculate pooled effect size", "forest plot"],
      patterns: [/meta-analys/i, /combine\s+results\s+from\s+multiple\s+studies/i, /synthesize\s+research\s+findings/i],
      suggestions: ["ADV6"],
      contextualAdvice: "Meta-analysis systematically combines results from multiple independent studies to derive a single pooled estimate of effect."
    },
    {
      name: "Cluster Analysis",
      keywords: ["group similar observations", "segment customers", "identify natural groupings in data", "k-means clustering", "hierarchical clustering"],
      patterns: [/cluster\s+analys/i, /group\s+similar\s+observations/i, /identify\s+natural\s+groupings/i],
      suggestions: ["ADV5"],
      contextualAdvice: "Cluster analysis groups similar data points together, identifying natural groupings or segments within a dataset."
    },
    {
      name: "Reliability Analysis",
      keywords: ["assess internal consistency of a scale", "Cronbach's Alpha", "test questionnaire reliability", "reliability", "cronbach", "internal consistency", "scale validation", "questionnaire"],
      patterns: [/reliabilit\w*\s+analys/i, /cronbach's\s+alpha/i, /internal\s+consistency/i],
      suggestions: ["ADV3", "CORR1"],
      contextualAdvice: "Reliability analysis, often using Cronbach's alpha, assesses the internal consistency of a scale or questionnaire."
    },
    {
      name: "Table 1",
      keywords: ["create a table of baseline characteristics", "summarize demographic data", "descriptive table for study participants", "table 1", "publication", "manuscript", "baseline characteristics", "demographics table"],
      patterns: [/table\s+1/i, /baseline\s+characteristics/i, /summarize\s+demographic\s+data/i],
      suggestions: ["PUB1", "DESC1", "DESC2"],
      contextualAdvice: "Table 1 typically presents the baseline characteristics of study participants, summarizing demographic and clinical data."
    },
    {
      name: "Table 2",
      keywords: ["create a comparative table", "compare variables between groups in a table", "show differences in outcomes by exposure", "table 2", "outcomes", "results", "comparison", "primary endpoint"],
      patterns: [/table\s+2/i, /compare\s+variables\s+between\s+groups\s+in\s+a\s+table/i],
      suggestions: ["PUB2", "TTEST2", "ANOVA1"],
      contextualAdvice: "Table 2 often presents comparative outcomes or results between different groups, such as treatment vs. control."
    },
    {
      name: "Regression Interpretation",
      keywords: ["interpret regression coefficients", "explain regression output", "understand p-values in regression"],
      patterns: [/interpret\s+regression\s+coefficients/i, /explain\s+regression\s+output/i],
      suggestions: ["PUB4", "REG1", "REG2"],
      contextualAdvice: "Regression interpretation involves understanding the meaning of coefficients, p-values, and model fit statistics."
    },
    {
      name: "Flow Diagram",
      keywords: ["create a study flow diagram", "visualize participant flow", "CONSORT diagram"],
      patterns: [/flow\s+diagram/i, /study\s+flow\s+diagram/i, /consort\s+diagram/i],
      suggestions: ["PUB5"],
      contextualAdvice: "A flow diagram, like a CONSORT diagram, visually represents the flow of participants through a study."
    },
    {
      name: "SMD Table",
      keywords: ["calculate standardized mean differences", "create a table of SMDs", "compare effect sizes between groups"],
      patterns: [/smd\s+table/i, /standardized\s+mean\s+differences/i],
      suggestions: ["PUB3"],
      contextualAdvice: "An SMD table presents standardized mean differences, useful for comparing effect sizes across different studies or groups."
    }
  ];

  // Get all unique categories and types
  const allCategories = useMemo(() => {
    return Array.from(new Set(allAnalyses.map(a => a.category))).sort();
  }, []);

  const allTypes = useMemo(() => {
    return Array.from(new Set(allAnalyses.map(a => a.type))).sort();
  }, []);

  // Phase 3: Intelligent Context Detection and Adaptive Response Mechanisms
  const detectQueryContext = (query: string, dataset?: any): {
    intent: string;
    domain: string;
    complexity: 'basic' | 'intermediate' | 'advanced';
    dataContext: {
      hasNumerical: boolean;
      hasCategorical: boolean;
      sampleSize: 'small' | 'medium' | 'large';
      missingData: boolean;
    };
    userExperience: 'beginner' | 'intermediate' | 'expert';
    suggestedWorkflow: string[];
  } => {
    const queryLower = query.toLowerCase();
    
    // Intent detection patterns
    const intentPatterns = {
      compare: /\b(compare|difference|between|vs|versus|against)\b/i,
      correlate: /\b(correlat|relationship|associat|connect)\b/i,
      predict: /\b(predict|forecast|model|regression)\b/i,
      describe: /\b(describe|summary|overview|distribution)\b/i,
      test: /\b(test|significant|p.?value|hypothesis)\b/i,
      visualize: /\b(plot|chart|graph|visualiz|show)\b/i,
      clean: /\b(clean|missing|outlier|quality)\b/i
    };
    
    // Domain detection patterns
    const domainPatterns = {
      medical: /\b(patient|treatment|clinical|medical|health|disease|therapy)\b/i,
      business: /\b(customer|sales|marketing|revenue|profit|business)\b/i,
      education: /\b(student|grade|score|education|learning|teaching)\b/i,
      psychology: /\b(behavior|personality|psychology|survey|scale)\b/i,
      research: /\b(study|research|experiment|trial|hypothesis)\b/i
    };
    
    // Complexity indicators
    const complexityIndicators = {
      basic: /\b(simple|basic|easy|beginner|first time)\b/i,
      advanced: /\b(advanced|complex|multivariate|interaction|mediation|moderation)\b/i
    };
    
    // Experience level indicators
    const experienceIndicators = {
      beginner: /\b(new to|beginner|don't know|help me|what is|how do i)\b/i,
      expert: /\b(advanced|sophisticated|multivariate|interaction|assumption)\b/i
    };
    
    // Detect intent
    let intent = 'explore';
    for (const [key, pattern] of Object.entries(intentPatterns)) {
      if (pattern.test(queryLower)) {
        intent = key;
        break;
      }
    }
    
    // Detect domain
    let domain = 'general';
    for (const [key, pattern] of Object.entries(domainPatterns)) {
      if (pattern.test(queryLower)) {
        domain = key;
        break;
      }
    }
    
    // Detect complexity
    let complexity: 'basic' | 'intermediate' | 'advanced' = 'intermediate';
    if (complexityIndicators.basic.test(queryLower)) {
      complexity = 'basic';
    } else if (complexityIndicators.advanced.test(queryLower)) {
      complexity = 'advanced';
    }
    
    // Detect user experience
    let userExperience: 'beginner' | 'intermediate' | 'expert' = 'intermediate';
    if (experienceIndicators.beginner.test(queryLower)) {
      userExperience = 'beginner';
    } else if (experienceIndicators.expert.test(queryLower)) {
      userExperience = 'expert';
    }
    
    // Analyze data context if dataset available
    const dataContext = {
      hasNumerical: dataset?.variableTypes?.numeric > 0 || false,
      hasCategorical: dataset?.variableTypes?.categorical > 0 || false,
      sampleSize: dataset?.totalRows > 1000 ? 'large' as const : 
                 dataset?.totalRows > 100 ? 'medium' as const : 'small' as const,
      missingData: dataset?.dataQuality?.missingDataPercentage > 5 || false
    };
    
    // Generate suggested workflow based on context
    const suggestedWorkflow = generateWorkflowSuggestions(intent, domain, complexity, dataContext);
    
    return {
      intent,
      domain,
      complexity,
      dataContext,
      userExperience,
      suggestedWorkflow
    };
  };
  
  // Generate workflow suggestions based on context
  const generateWorkflowSuggestions = (
    intent: string, 
    domain: string, 
    complexity: string, 
    dataContext: any
  ): string[] => {
    const workflows: { [key: string]: string[] } = {
      compare: [
        'Check data quality and assumptions',
        'Explore data distributions',
        'Choose appropriate statistical test',
        'Perform analysis and interpret results'
      ],
      correlate: [
        'Visualize relationships with scatter plots',
        'Check for linearity and outliers',
        'Calculate correlation coefficients',
        'Test significance and interpret'
      ],
      predict: [
        'Explore predictor variables',
        'Check model assumptions',
        'Build and validate model',
        'Interpret coefficients and predictions'
      ],
      describe: [
        'Calculate descriptive statistics',
        'Create appropriate visualizations',
        'Check data distribution',
        'Summarize key findings'
      ],
      clean: [
        'Assess data quality',
        'Handle missing values',
        'Detect and address outliers',
        'Validate data integrity'
      ]
    };
    
    return workflows[intent] || workflows.describe;
  };
  
  // Adaptive response generation based on context
  const generateAdaptiveResponse = (
    suggestions: AnalysisSuggestion[], 
    context: ReturnType<typeof detectQueryContext>
  ): AnalysisSuggestion[] => {
    return suggestions.map(suggestion => {
      let adaptedReason = suggestion.reason || '';
      
      // Adapt explanation based on user experience
      if (context.userExperience === 'beginner') {
        adaptedReason = `🔰 Beginner-friendly: ${adaptedReason}. This analysis is suitable for users new to statistics.`;
      } else if (context.userExperience === 'expert') {
        adaptedReason = `🎓 Advanced: ${adaptedReason}. Consider checking assumptions and effect sizes.`;
      }
      
      // Add domain-specific context
      if (context.domain !== 'general') {
        const domainAdvice = {
          medical: 'Consider clinical significance alongside statistical significance.',
          business: 'Focus on practical business impact and ROI.',
          education: 'Consider educational significance and learning outcomes.',
          psychology: 'Pay attention to scale reliability and construct validity.',
          research: 'Ensure proper study design and methodology.'
        };
        adaptedReason += ` 📋 ${domainAdvice[context.domain as keyof typeof domainAdvice] || ''}`;
      }
      
      // Add data-specific warnings
      if (context.dataContext.missingData) {
        adaptedReason += ' ⚠️ Note: Missing data detected - consider data cleaning first.';
      }
      
      if (context.dataContext.sampleSize === 'small') {
        adaptedReason += ' 📊 Small sample size - consider non-parametric alternatives.';
      }
      
      return {
        ...suggestion,
        reason: adaptedReason,
        workflowSteps: context.suggestedWorkflow
      };
    });
  };



  // Enhanced query processing using curated database first, then fallback to scenarios
  const processQueryWithTraining = async (userQuery: string): Promise<AnalysisSuggestion[]> => {
    // Phase 3: Detect query context for intelligent responses
    const queryContext = detectQueryContext(userQuery, datasetAnalysis);
    
    // Process query with enhanced text processing
    const processedQuery = enhancedProcessQuery(userQuery);
    
    // Try multiple query variations for better matching
    const queryVariations = [
      processedQuery.original,
      processedQuery.normalized,
      ...processedQuery.expandedQueries
    ];
    
    let bestMatches: CuratedSuggestion[] = [];
    let bestScore = 0;
    
    // Try each query variation and find the best matches
    queryVariations.forEach(queryVariation => {
      const matches = trainingDB.findMatchingSuggestions(queryVariation);
      if (matches.length > 0) {
        const avgConfidence = matches.reduce((sum, match) => 
          sum + Math.max(...match.suggestions.map(s => s.confidence)), 0
        ) / matches.length;
        
        if (avgConfidence > bestScore) {
          bestMatches = matches;
          bestScore = avgConfidence;
        }
      }
    });

    if (bestMatches.length > 0) {
      // Convert curated suggestions to AnalysisSuggestion format
      const suggestions: AnalysisSuggestion[] = [];

      bestMatches.forEach(curatedSuggestion => {
        curatedSuggestion.suggestions.forEach(suggestion => {
          const analysis = allAnalyses.find(a => a.id === suggestion.analysisId);
          if (analysis) {
            // Enhanced relevance scoring based on query processing and context
            let relevanceScore = Math.round(suggestion.confidence * 100);
            
            // Boost score based on detected intents and context
            if (processedQuery.intents.length > 0) {
              relevanceScore = Math.min(relevanceScore + 5, 100);
            }
            if (processedQuery.variableTypes.length > 0) {
              relevanceScore = Math.min(relevanceScore + 3, 100);
            }
            
            // Context-based scoring adjustments
            if (queryContext.intent === 'compare' && analysis.category === 'T-Tests') {
              relevanceScore = Math.min(relevanceScore + 10, 100);
            }
            if (queryContext.intent === 'correlate' && analysis.category === 'Correlation & Regression') {
              relevanceScore = Math.min(relevanceScore + 10, 100);
            }
            
            // Enhanced reason with contextual information
            let enhancedReason = suggestion.reason;
            const contextualSuggestions = generateContextualSuggestions(processedQuery);
            if (contextualSuggestions.length > 0) {
              enhancedReason += ` ${contextualSuggestions[0]}`;
            }
            
            suggestions.push({
              ...analysis,
              relevanceScore,
              reason: enhancedReason,
              matchDetails: {
                scenarioMatch: true,
                patternMatch: true,
                keywordMatches: curatedSuggestion.keywords.length,
                directMatches: 0
              }
            });
          }
        });
      });

      // Sort by priority and confidence, remove duplicates
      const uniqueSuggestions = suggestions.reduce((acc, current) => {
        const existing = acc.find(item => item.id === current.id);
        if (!existing || (current.relevanceScore || 0) > (existing.relevanceScore || 0)) {
          return [...acc.filter(item => item.id !== current.id), current];
        }
        return acc;
      }, [] as AnalysisSuggestion[]);

      const sortedSuggestions = uniqueSuggestions
        .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
        .slice(0, 8); // Limit to top 8 suggestions
        
      // Phase 3: Apply adaptive response generation
      return generateAdaptiveResponse(sortedSuggestions, queryContext);
    }

    // If no curated matches found, fall back to enhanced original processing
    const fallbackSuggestions = processQueryEnhanced(userQuery, processedQuery);
    
    return generateAdaptiveResponse(fallbackSuggestions, queryContext);
  };

  // Enhanced query processing with multiple variations and fuzzy matching
  const processQueryWithVariations = (queryVariations: string[], queryInfo: ProcessedQuery): AnalysisSuggestion[] => {
    const suggestions: Map<string, AnalysisSuggestion & { 
      score: number, 
      matchDetails: MatchDetails
    }> = new Map();

    queryVariations.forEach((queryVariation, index) => {
      const queryLower = queryVariation.toLowerCase().trim();
      const isOriginal = index === 0;
      const scoreMultiplier = isOriginal ? 1.0 : 0.8; // Slightly lower score for expanded queries

      // Check each scenario with enhanced matching
      analysisScenarios.forEach(scenario => {
        let scenarioScore = 0;
        let keywordMatchCount = 0;
        let patternMatched = false;
        let fuzzyMatches = 0;

        // Enhanced keyword matching with fuzzy support
        scenario.keywords.forEach(keyword => {
          if (queryLower.includes(keyword)) {
            scenarioScore += 3;
            keywordMatchCount++;
          } else {
            // Try fuzzy matching for keywords
            const fuzzyKeywords = fuzzyMatchKeywords(queryLower, [keyword], 0.7);
            if (fuzzyKeywords.length > 0) {
              scenarioScore += 2; // Lower score for fuzzy matches
              fuzzyMatches++;
            }
          }
        });

        // Pattern matching
        scenario.patterns?.forEach(pattern => {
          if (pattern.test(queryVariation)) {
            scenarioScore += 5;
            patternMatched = true;
          }
        });

        // Boost score based on detected intents and context
        if (queryInfo.intents.length > 0) {
          scenarioScore += queryInfo.intents.length;
        }
        if (queryInfo.domain && scenario.keywords.some(k => k.includes(queryInfo.domain!))) {
          scenarioScore += 2;
        }

        // Apply score multiplier
        scenarioScore = Math.round(scenarioScore * scoreMultiplier);

        // If scenario matches, add its suggestions
        if (scenarioScore > 0) {
          scenario.suggestions.forEach(suggestionId => {
            const analysis = allAnalyses.find(a => a.id === suggestionId);
            if (analysis) {
              const existing = suggestions.get(suggestionId);
              if (!existing || existing.score < scenarioScore) {
                suggestions.set(suggestionId, {
                  ...analysis,
                  score: scenarioScore,
                  matchDetails: {
                    scenarioMatch: true,
                    patternMatch: patternMatched,
                    keywordMatches: keywordMatchCount + fuzzyMatches,
                    directMatches: 0
                  },
                  reason: scenario.contextualAdvice
                });
              }
            }
          });
        }
      });

      // Enhanced direct tag/text matching
      const queryWords = queryLower.split(/\s+/).filter(word => word.length > 1);
      
      allAnalyses.forEach(analysis => {
        let directScore = 0;
        let directMatchCount = 0;

        queryWords.forEach(word => {
          // Exact matches
          if (analysis.text.toLowerCase().includes(word)) {
            directScore += 3;
            directMatchCount++;
          }
          if (analysis.description?.toLowerCase().includes(word)) {
            directScore += 2;
            directMatchCount++;
          }
          if (analysis.tags?.some(tag => tag.toLowerCase() === word || tag.toLowerCase().includes(word))) {
            directScore += 4;
            directMatchCount++;
          }

          // Fuzzy matches for analysis text and tags
          if (analysis.tags) {
            const fuzzyTagMatches = fuzzyMatchKeywords(word, analysis.tags.map(t => t.toLowerCase()), 0.75);
            if (fuzzyTagMatches.length > 0) {
              directScore += 2; // Lower score for fuzzy tag matches
              directMatchCount++;
            }
          }
        });

        // Boost score for variable type compatibility
        if (queryInfo.variableTypes.length > 0 && analysis.tags) {
          const compatibleTypes = queryInfo.variableTypes.filter(type => 
            analysis.tags!.some(tag => tag.toLowerCase().includes(type))
          );
          directScore += compatibleTypes.length;
        }

        // Apply score multiplier
        directScore = Math.round(directScore * scoreMultiplier);

        if (directScore > 0) {
          const existing = suggestions.get(analysis.id);
          if (!existing || existing.score < directScore) {
            suggestions.set(analysis.id, {
              ...analysis,
              score: directScore,
              matchDetails: {
                scenarioMatch: false,
                patternMatch: false,
                keywordMatches: 0,
                directMatches: directMatchCount
              }
            });
          }
        }
      });
    });

    // Convert to array and calculate relevance scores
    const results = Array.from(suggestions.values())
      .map(suggestion => {
        const relevanceScore = Math.min(Math.round((suggestion.score / 10) * 100), 100);
        return {
          ...suggestion,
          relevanceScore: Math.max(relevanceScore, 50) // Minimum 50% relevance
        };
      })
      .filter(suggestion => suggestion.score >= 2) // Minimum score threshold
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);

    return results;
  };

  // Enhanced AI-like query processing with fuzzy matching and synonym support
  const processQueryEnhanced = (userQuery: string, processedQuery?: ProcessedQuery): AnalysisSuggestion[] => {
    const queryInfo = processedQuery || enhancedProcessQuery(userQuery);
    const queryVariations = [queryInfo.original, queryInfo.normalized, ...queryInfo.expandedQueries];
    
    return processQueryWithVariations(queryVariations, queryInfo);
  };

  // Original AI-like query processing with proper scoring (kept as fallback)
  const processQuery = (userQuery: string): AnalysisSuggestion[] => {
    const queryLower = userQuery.toLowerCase().trim();
    const suggestions: Map<string, AnalysisSuggestion & { 
      score: number, 
      matchDetails: MatchDetails
    }> = new Map();

    // Check each scenario
    analysisScenarios.forEach(scenario => {
      let scenarioScore = 0;
      let keywordMatchCount = 0;
      let patternMatched = false;

      // Check keywords
      scenario.keywords.forEach(keyword => {
        if (queryLower.includes(keyword)) {
          scenarioScore += 3;
          keywordMatchCount++;
        }
      });

      // Check patterns
      scenario.patterns?.forEach(pattern => {
        if (pattern.test(userQuery)) {
          scenarioScore += 5;
          patternMatched = true;
        }
      });

      // If scenario matches, add its suggestions
      if (scenarioScore > 0) {
        scenario.suggestions.forEach(suggestionId => {
          const analysis = allAnalyses.find(a => a.id === suggestionId);
          if (analysis) {
            const existing = suggestions.get(suggestionId);
            if (!existing || existing.score < scenarioScore) {
              suggestions.set(suggestionId, {
                ...analysis,
                score: scenarioScore,
                matchDetails: {
                  scenarioMatch: true,
                  patternMatch: patternMatched,
                  keywordMatches: keywordMatchCount,
                  directMatches: 0
                },
                reason: scenario.contextualAdvice
              });
            }
          }
        });
      }
    });

    // Also do direct tag/text matching
    // Allow shorter words for direct matching, especially for tags like 'χ²'
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 1); // Changed from > 2 to > 1
    
    allAnalyses.forEach(analysis => {
      let directScore = 0;
      let directMatchCount = 0;

      queryWords.forEach(word => {
        // Prioritize exact tag matches or full word matches in text/description
        if (analysis.text.toLowerCase().includes(word)) {
          directScore += 3;
          directMatchCount++;
        }
        if (analysis.description?.toLowerCase().includes(word)) {
          directScore += 2;
          directMatchCount++;
        }
        // Check if any tag exactly matches the word or includes it
        if (analysis.tags?.some(tag => tag.toLowerCase() === word || tag.toLowerCase().includes(word))) {
          directScore += 4; // Increased score for tag matches
          directMatchCount++;
        }
      });

      if (directScore > 0) {
        const existing = suggestions.get(analysis.id);
        if (!existing || existing.score < directScore) {
          suggestions.set(analysis.id, {
            ...analysis,
            score: directScore,
            matchDetails: {
              scenarioMatch: false,
              patternMatch: false,
              keywordMatches: 0,
              directMatches: directMatchCount
            }
          });
        }
      }
    });

    // Calculate relevance scores as percentages (capped at 100%)
    const maxScore = Math.max(...Array.from(suggestions.values()).map(s => s.score));
    
    return Array.from(suggestions.values())
      .map(suggestion => ({
        ...suggestion,
        relevanceScore: maxScore > 0 ? Math.round((suggestion.score / maxScore) * 100) : 0
      }))
      .filter(suggestion => suggestion.relevanceScore >= 50) // Filter out suggestions with less than 50% relevance
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);
  };

  // Filter analyses based on current selections
  const filteredAnalyses = useMemo(() => {
    let results = [...allAnalyses];
    
    // Filter by selected categories
    if (selectedCategories.length > 0) {
      results = results.filter(a => selectedCategories.includes(a.category));
    }
    
    // Filter by selected types
    if (selectedTypes.length > 0) {
      results = results.filter(a => selectedTypes.includes(a.type));
    }
    
    // Filter by availability if dataset is loaded
    if (showOnlyAvailable && currentDataset) {
      // Here you would add logic to check if the analysis is available for the current dataset
    }
    
    return results;
  }, [selectedCategories, selectedTypes, showOnlyAvailable, currentDataset]);

  // Group filtered analyses by category
  const groupedAnalyses = useMemo(() => {
    const groups: { [key: string]: AnalysisSuggestion[] } = {};
    const analysesToGroup = showAllAnalyses ? filteredAnalyses : aiSuggestions;
    
    analysesToGroup.forEach(analysis => {
      if (!groups[analysis.category]) {
        groups[analysis.category] = [];
      }
      groups[analysis.category].push(analysis);
    });
    return groups;
  }, [filteredAnalyses, aiSuggestions, showAllAnalyses]);

  // Phase 3: Enhanced query submission with context detection display
  const [queryContext, setQueryContext] = useState<ReturnType<typeof detectQueryContext> | null>(null);
  
  const handleQuerySubmit = async () => {
    if (!query.trim()) return;

    setIsProcessing(true);
    setShowAllAnalyses(false);
    
    // Phase 3: Detect and display query context
    const detectedContext = detectQueryContext(query, datasetAnalysis);
    setQueryContext(detectedContext);

    try {
      let suggestions: AnalysisSuggestion[] = [];

      if (currentDataset && datasetAnalysis?.hasData) {
        // Generate smart recommendations based on query and dataset
        const smartRecs = generateSmartRecommendations(currentDataset, query);
        suggestions = smartRecs.map(rec => ({
          id: rec.id,
          text: rec.title,
          description: rec.description,
          type: rec.analysisType,
          category: rec.category,
          path: rec.path,
          reason: rec.reason,
          relevanceScore: rec.priority === 'high' ? 90 : rec.priority === 'medium' ? 70 : 50
        }));

        // If no smart recommendations, fall back to curated training database
        if (suggestions.length === 0) {
          suggestions = await processQueryWithTraining(query);
        }
      } else {
        // No dataset loaded, use curated training database first
        suggestions = await processQueryWithTraining(query);
      }

      setAiSuggestions(suggestions);
      setIsProcessing(false);

      if (suggestions.length === 0) {
        setNotification({
          open: true,
          message: currentDataset
            ? "No specific matches found for your dataset. Try browsing all analyses or rephrasing your question."
            : "No specific matches found. Try browsing all analyses or rephrasing your question."
        });
      }
    } catch (error) {
      console.error('Error processing query:', error);
      setIsProcessing(false);
      setNotification({
        open: true,
        message: 'An error occurred while processing your query. Please try again.'
      });
    }
  };
  
  // Phase 3: Generate intelligent query suggestions based on context
  const generateIntelligentSuggestions = (context: ReturnType<typeof detectQueryContext>): string[] => {
    const suggestions: string[] = [];
    
    // Intent-based suggestions
    if (context.intent === 'compare') {
      suggestions.push(
        "Compare means between two groups",
        "Test difference in proportions",
        "Compare multiple groups with ANOVA"
      );
    } else if (context.intent === 'correlate') {
      suggestions.push(
        "Analyze correlation between variables",
        "Test linear relationship",
        "Create scatter plot with trend line"
      );
    } else if (context.intent === 'predict') {
      suggestions.push(
        "Build regression model",
        "Predict outcomes using variables",
        "Analyze predictor importance"
      );
    }
    
    // Domain-specific suggestions
    if (context.domain === 'medical') {
      suggestions.push(
        "Analyze treatment effectiveness",
        "Calculate odds ratio",
        "Survival analysis"
      );
    } else if (context.domain === 'business') {
      suggestions.push(
        "Customer satisfaction analysis",
        "Sales performance comparison",
        "Market segmentation"
      );
    }
    
    // Data-specific suggestions
    if (context.dataContext.hasNumerical && context.dataContext.hasCategorical) {
      suggestions.push(
        "Compare numerical values by categories",
        "Analyze group differences"
      );
    }
    
    if (context.dataContext.missingData) {
      suggestions.push(
        "Handle missing data",
        "Assess data quality"
      );
    }
    
    return suggestions.slice(0, 6); // Limit to 6 suggestions
  };

  // Quick question examples
  const exampleQuestions = [
    "How do I compare means between two groups?",
    "Is my data normally distributed?",
    "What test for categorical data?",
    "Compare pre and post measurements",
    "Calculate sample size for my study",
    "Create publication-ready tables",
    "Analyze survival data",
    "Test correlation between variables"
  ];

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'Descriptive Analysis': return <AssessmentIcon />;
      case 'T-Tests': return <CalculateIcon />;
      case 'Non-parametric Tests': return <ScienceIcon />;
      case 'ANOVA': return <TimelineIcon />;
      case 'Categorical Tests': return <TableChartIcon />;
      case 'Correlation & Regression': return <TimelineIcon />;
      case 'Advanced Methods': return <SchoolIcon />;
      case 'Epidemiology': return <ScienceIcon />;
      case 'Sample Size': return <CalculateIcon />;
      case 'Visualizations': return <BarChartIcon />;
      case 'Data Management': return <SettingsIcon />;
      case 'Publication Tools': return <TableChartIcon />;
      default: return <AssessmentIcon />;
    }
  };

  // Get type color
  const getTypeColor = (type: string) => {
    switch(type) {
      case 'test': return 'primary';
      case 'descriptive': return 'success';
      case 'visualization': return 'warning';
      case 'regression': return 'secondary';
      case 'categorical': return 'info';
      case 'assumption': return 'default';
      case 'advanced': return 'error';
      case 'epidemiology': return 'primary';
      case 'sample_size': return 'secondary';
      case 'data': return 'default';
      case 'publication': return 'info';
      case 'correlation': return 'secondary';
      default: return 'default';
    }
  };

  // Handle analysis selection
  const handleAnalysisClick = (analysis: AnalysisSuggestion) => {
    if (onNavigate && analysis.path) {
      // onNavigate will handle the path formatting (adding /app prefix and handling leading slashes)
      onNavigate(analysis.path);
    } else {
      setNotification({
        open: true,
        message: `Selected: ${analysis.text}`
      });
    }
  };



  // Toggle category expansion
  const toggleCategoryExpansion = (category: string) => {
    setExpandedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  // Get relevance stars (1-5 based on percentage)
  const getRelevanceStars = (relevanceScore: number): number => {
    if (relevanceScore >= 90) return 5;
    if (relevanceScore >= 70) return 4;
    if (relevanceScore >= 50) return 3;
    if (relevanceScore >= 30) return 2;
    return 1;
  };

  // Phase 3: Enhanced AI suggestion card with workflow visualization
  const renderAiSuggestion = (analysis: AnalysisSuggestion) => (
    <Card 
      key={analysis.id}
      variant="outlined"
      sx={{ 
        mb: 2,
        transition: 'all 0.2s',
        borderColor: analysis.relevanceScore && analysis.relevanceScore > 70 
          ? theme.palette.primary.main 
          : theme.palette.divider,
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 2
        }
      }}
    >
      <CardContent sx={{ cursor: 'pointer' }} onClick={() => handleAnalysisClick(analysis)}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
          <Avatar sx={{ 
            bgcolor: alpha(theme.palette.primary.main, 0.1), 
            width: 36, 
            height: 36,
            mr: 2 
          }}>
            {getCategoryIcon(analysis.category)}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontSize: '1.1rem', mb: 0.5 }}>
              {analysis.text}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {analysis.description}
            </Typography>
            {analysis.reason && (
              <Alert 
                severity="info" 
                sx={{ 
                  mt: 1, 
                  py: 0.5,
                  '& .MuiAlert-message': { fontSize: '0.875rem' }
                }}
                icon={<TipsAndUpdatesIcon fontSize="small" />}
              >
                {analysis.reason}
              </Alert>
            )}
          </Box>
          
          {/* Phase 4: Feedback Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedSuggestionForFeedback(analysis);
                setShowFeedbackDialog(true);
                setFeedbackRating(0);
                setFeedbackComment('');
              }}
              sx={{ color: 'text.secondary' }}
            >
              <ThumbUpIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
        
        {/* Relevance Score and Category */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
          <Chip 
            label={analysis.category} 
            size="small" 
            color={getTypeColor(analysis.type)}
          />
          {analysis.relevanceScore && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Rating 
                value={getRelevanceStars(analysis.relevanceScore)} 
                readOnly 
                size="small" 
                sx={{ mr: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                {Math.round(analysis.relevanceScore)}% match
              </Typography>
            </Box>
           )}
         </Box>
      </CardContent>
    </Card>
   );
  return (
    <Box sx={{ position: 'relative', minHeight: '600px' }}>
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({...notification, open: false})}
        message={notification.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
      
      <Paper elevation={0} sx={{ p: 3, mb: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <PsychologyIcon color="primary" sx={{ mr: 1, fontSize: 28 }} />
          <Typography variant="h6">Analysis Assistant</Typography>
          <Box sx={{ flexGrow: 1 }} />

          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={(e, newMode) => newMode && setViewMode(newMode)}
            size="small"
          >
            <ToggleButton value="assistant">
              <AutoAwesomeIcon sx={{ mr: 0.5 }} />
              AI Analysis Assistant
            </ToggleButton>
            <ToggleButton value="quality">
              <PsychologyIcon sx={{ mr: 0.5 }} />
              AI Data Quality Assistant
              {currentDataset && datasetAnalysis?.qualityAssessment && (
                <Badge
                  badgeContent="AI"
                  color="primary"
                  sx={{
                    ml: 1,
                    '& .MuiBadge-badge': {
                      fontSize: '0.6rem',
                      height: '16px',
                      minWidth: '16px'
                    }
                  }}
                />
              )}
            </ToggleButton>
            <ToggleButton value="advisor">
              <AutoAwesomeIcon sx={{ mr: 0.5 }} />
              Statistical Analysis Advisor
            </ToggleButton>

          </ToggleButtonGroup>
        </Box>

        {viewMode === 'assistant' ? (
          // AI Assistant Mode
          <>

            
            <Typography variant="body2" color="text.secondary" paragraph>
              {currentDataset && datasetAnalysis?.hasData
                ? `Describe your research question about "${currentDataset.name}" and I'll suggest the most appropriate analyses based on your actual data.`
                : "Describe your research question or analysis needs in plain language, and I'll suggest the most appropriate statistical tests and tools."
              }
            </Typography>

            <Alert
              severity="info"
              icon={<LightbulbIcon />}
              sx={{ mb: 3 }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                💡 {currentDataset ? 'Ask about your data' : 'Pro tip: Be specific about your data and goals'}
              </Typography>
              <Typography variant="body2">
                {currentDataset && datasetAnalysis?.hasData ? (
                  <>
                    Try asking: "Compare {datasetAnalysis.variableAnalysis.find(v => v.type === 'numeric')?.columnName || 'numeric variables'} between groups"
                    or "What's the relationship between {datasetAnalysis.variableAnalysis.slice(0, 2).map(v => v.columnName).join(' and ')}?"
                  </>
                ) : (
                  <>
                    For example: "I want to compare blood pressure between treatment and control groups"
                    or "How do I test if age is correlated with test scores?"
                  </>
                )}
              </Typography>
            </Alert>

            {/* AI Search Bar */}
            <Box sx={{ display: 'flex', mb: 3 }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Describe what you want to analyze..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleQuerySubmit();
                  }
                }}
                disabled={isProcessing}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PsychologyIcon color="primary" />
                    </InputAdornment>
                  ),
                  endAdornment: query && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setQuery('');
                          setAiSuggestions([]);
                          setQueryContext(null);
                        }}
                        edge="end"
                      >
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleQuerySubmit}
                disabled={isProcessing || !query.trim()}
                sx={{ ml: 1, minWidth: 120 }}
                startIcon={isProcessing ? <CircularProgress size={20} color="inherit" /> : <AutoAwesomeIcon />}
              >
                {isProcessing ? 'Analyzing...' : 'Analyze'}
              </Button>
            </Box>
            
            {/* Phase 3: Context Detection Display */}
            {queryContext && (
              <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PsychologyIcon fontSize="small" color="primary" />
                  Query Analysis
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                  {queryContext.intent !== 'unknown' && (
                    <Chip 
                      size="small" 
                      label={`Intent: ${queryContext.intent}`} 
                      color="primary" 
                      variant="outlined"
                    />
                  )}
                  {queryContext.domain !== 'general' && (
                    <Chip 
                      size="small" 
                      label={`Domain: ${queryContext.domain}`} 
                      color="secondary" 
                      variant="outlined"
                    />
                  )}
                  <Chip 
                    size="small" 
                    label={`Level: ${queryContext.complexity}`} 
                    color="info" 
                    variant="outlined"
                  />
                  <Chip 
                    size="small" 
                    label={`Experience: ${queryContext.userExperience}`} 
                    color="success" 
                    variant="outlined"
                  />
                </Box>
                
                {/* Intelligent Suggestions */}
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{ mb: 1, fontWeight: 'medium' }}>
                    💡 Related Suggestions:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {generateIntelligentSuggestions(queryContext).map((suggestion, index) => (
                      <Chip
                        key={index}
                        size="small"
                        label={suggestion}
                        onClick={() => setQuery(suggestion)}
                        sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}
                      />
                    ))}
                  </Box>
                </Box>
              </Box>
            )}

            {/* Example Questions */}
            {!aiSuggestions.length && !isProcessing && (
              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Try asking:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {exampleQuestions.map((question, index) => (
                    <Chip
                      key={index}
                      label={question}
                      onClick={() => setQuery(question)}
                      variant="outlined"
                      clickable
                      icon={<HelpIcon />}
                    />
                  ))}
                </Box>
              </Box>
            )}

            {/* AI Suggestions */}
            {aiSuggestions.length > 0 && (
              <Box>
                <Divider sx={{ mb: 3 }} />
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AutoAwesomeIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">
                    Recommended Analyses
                  </Typography>
                  <Box sx={{ flexGrow: 1 }} />
                  <Button
                    size="small"
                    onClick={() => setShowAllAnalyses(!showAllAnalyses)}
                    endIcon={showAllAnalyses ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  >
                    {showAllAnalyses ? 'Show Less' : 'Show All'}
                  </Button>
                </Box>
                
                {!showAllAnalyses ? (
                  // Show AI suggestions
                  aiSuggestions.map(renderAiSuggestion)
                ) : (
                  // Show all analyses grouped by category
                  Object.entries(groupedAnalyses).map(([category, analyses]) => (
                    <Accordion 
                      key={category}
                      expanded={expandedCategories.includes(category)}
                      onChange={() => toggleCategoryExpansion(category)}
                      sx={{ mb: 1 }}
                    >
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          {getCategoryIcon(category)}
                          <Typography sx={{ ml: 1, fontWeight: 500 }}>
                            {category}
                          </Typography>
                          <Chip
                            label={analyses.length}
                            size="small"
                            sx={{ ml: 2 }}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <List sx={{ pt: 0 }}>
                          {analyses.map(analysis => (
                            <ListItem
                              key={analysis.id}
                              button
                              onClick={() => handleAnalysisClick(analysis)}
                              sx={{
                                mb: 1,
                                borderRadius: 1,
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.primary.main, 0.05)
                                }
                              }}
                            >
                              <ListItemText
                                primary={analysis.text}
                                secondary={analysis.description}
                                primaryTypographyProps={{ fontSize: '0.9rem' }}
                                secondaryTypographyProps={{ fontSize: '0.8rem' }}
                              />
                              <Chip
                                label={analysis.type}
                                size="small"
                                color={getTypeColor(analysis.type) as any}
                                variant="outlined"
                              />
                            </ListItem>
                          ))}
                        </List>
                      </AccordionDetails>
                    </Accordion>
                  ))
                )}
              </Box>
            )}
          </>
        ) : viewMode === 'quality' ? (
          // Data Quality Assessment Mode
          <>
            {currentDataset && datasetAnalysis?.qualityAssessment ? (
              <DataQualityAssessment
                assessment={datasetAnalysis.qualityAssessment}
                datasetName={currentDataset.name}
              />
            ) : (
              <Box sx={{ textAlign: 'center', py: 6 }}>
                <PsychologyIcon
                  sx={{
                    fontSize: 80,
                    color: 'primary.main',
                    mb: 2,
                    opacity: 0.7
                  }}
                />
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                  AI Data Quality Assistant
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 600, mx: 'auto' }}>
                  Get intelligent insights about your data quality with AI-powered analysis
                </Typography>

                {!currentDataset ? (
                  <Card sx={{ maxWidth: 500, mx: 'auto', p: 3, bgcolor: theme.palette.action.hover }}>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <LightbulbIcon sx={{ mr: 1, color: 'warning.main' }} />
                      Load a Dataset to Get Started
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      To use the AI Data Quality Assistant, you need to load a dataset first.
                      Once loaded, the AI will analyze:
                    </Typography>
                    <List dense sx={{ textAlign: 'left' }}>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="primary" fontSize="small" /></ListItemIcon>
                        <ListItemText primary="Missing data patterns and recommendations" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="primary" fontSize="small" /></ListItemIcon>
                        <ListItemText primary="Outlier detection and handling suggestions" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="primary" fontSize="small" /></ListItemIcon>
                        <ListItemText primary="Data distribution assessment" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="primary" fontSize="small" /></ListItemIcon>
                        <ListItemText primary="Variable type validation" />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon><CheckCircleIcon color="primary" fontSize="small" /></ListItemIcon>
                        <ListItemText primary="Data improvement recommendations" />
                      </ListItem>
                    </List>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => onNavigate?.('data-management/datasets')}
                      sx={{ mt: 2 }}
                      startIcon={<ArrowForwardIcon />}
                    >
                      Go to Datasets
                    </Button>
                  </Card>
                ) : (
                  <Card sx={{ maxWidth: 500, mx: 'auto', p: 3, bgcolor: theme.palette.action.hover }}>
                    <Typography variant="h6" component="div" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      Analyzing Your Data
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      The AI is processing "{currentDataset.name}" to generate comprehensive data quality insights.
                      This may take a moment for large datasets.
                    </Typography>
                  </Card>
                )}
              </Box>
            )}
          </>
        ) : viewMode === 'advisor' ? (
          // Interactive Statistical Analysis Advisor Mode
          <InteractiveStatisticalAdvisor
            currentDataset={currentDataset}
            datasetAnalysis={datasetAnalysis}
          />
        ) : null}

        {/* Dataset Status and Insights */}
        {currentDataset && datasetAnalysis?.hasData ? (
          <Box sx={{ mt: 3 }}>
            {/* Dataset Overview */}
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                📊 Dataset Loaded: {currentDataset.name}
              </Typography>
              <Typography variant="body2">
                {datasetAnalysis.totalRows} observations • {datasetAnalysis.totalColumns} variables
                {datasetAnalysis.variableTypes.numeric > 0 && ` • ${datasetAnalysis.variableTypes.numeric} numeric`}
                {datasetAnalysis.variableTypes.categorical > 0 && ` • ${datasetAnalysis.variableTypes.categorical} categorical`}
                {datasetAnalysis.qualityAssessment && (
                  <>
                    {' • '}
                    <Chip
                      label={`Quality: ${datasetAnalysis.qualityAssessment.overallGrade.toUpperCase()}`}
                      size="small"
                      color={datasetAnalysis.qualityAssessment.overallGrade === 'excellent' ? 'success' :
                             datasetAnalysis.qualityAssessment.overallGrade === 'good' ? 'info' :
                             datasetAnalysis.qualityAssessment.overallGrade === 'fair' ? 'warning' : 'error'}
                      sx={{ ml: 1 }}
                    />
                  </>
                )}
              </Typography>
            </Alert>

            {/* Data Quality Insights */}
            {datasetAnalysis.qualityAssessment && datasetAnalysis.qualityAssessment.prioritizedRecommendations.some(r => r.priority === 'critical') && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  🚨 Critical Data Quality Issues
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2">
                    {datasetAnalysis.qualityAssessment.prioritizedRecommendations.filter(r => r.priority === 'critical').length} critical issues detected.
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => setViewMode('quality')}
                  >
                    View Details
                  </Button>
                </Box>
              </Alert>
            )}
            {datasetAnalysis.dataQuality.missingDataPercentage > 5 && !datasetAnalysis.qualityAssessment?.prioritizedRecommendations.some(r => r.priority === 'critical') && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  ⚠️ Data Quality Notice
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                  <Typography variant="body2">
                    {datasetAnalysis.dataQuality.missingDataPercentage.toFixed(1)}% missing data detected.
                    Consider data cleaning before analysis.
                  </Typography>
                  {datasetAnalysis.qualityAssessment && (
                    <Button
                      size="small"
                      onClick={() => setViewMode('quality')}
                    >
                      View Assessment
                    </Button>
                  )}
                </Box>
              </Alert>
            )}

            {/* Variable Insights */}
            {variableInsights.length > 0 && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  💡 Data Insights
                </Typography>
                {variableInsights.slice(0, 2).map((insight, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: index < variableInsights.slice(0, 2).length - 1 ? 1 : 0 }}>
                    • {insight}
                  </Typography>
                ))}
              </Alert>
            )}

            {/* Smart Recommendations */}
            {smartRecommendations.length > 0 && viewMode === 'assistant' && !query && !aiSuggestions.length && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                  <AutoAwesomeIcon sx={{ mr: 1 }} />
                  Recommended for Your Data
                </Typography>
                <Grid container spacing={2}>
                  {smartRecommendations.slice(0, 4).map((rec) => (
                    <Grid item xs={12} sm={6} key={rec.id}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          borderColor: rec.priority === 'high' ? theme.palette.primary.main : theme.palette.divider,
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 2
                          }
                        }}
                        onClick={() => {
                          if (rec.path && onNavigate) {
                            // If onNavigate is provided, it will handle the path formatting
                            onNavigate(rec.path);
                          }
                        }}
                      >
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Chip
                              label={rec.priority.toUpperCase()}
                              size="small"
                              color={rec.priority === 'high' ? 'primary' : rec.priority === 'medium' ? 'secondary' : 'default'}
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="caption" color="text.secondary">
                              {rec.category}
                            </Typography>
                          </Box>
                          <Typography variant="h6" sx={{ fontSize: '1rem', mb: 1 }}>
                            {rec.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {rec.description}
                          </Typography>
                          <Typography variant="caption" color="primary">
                            {rec.reason}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Box>
        ) : (
          <Alert severity="info" sx={{ mt: 3 }}>
            No dataset loaded. You can still explore available analyses and plan your study.
          </Alert>
        )}
      </Paper>

      {/* Phase 4: Feedback Dialog */}
      <Dialog open={showFeedbackDialog} onClose={() => setShowFeedbackDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Provide Feedback</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            How helpful was this suggestion?
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Rating
              value={feedbackRating}
              onChange={(_, newValue) => setFeedbackRating(newValue || 0)}
              size="large"
            />
          </Box>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Additional Comments (Optional)"
            value={feedbackComment}
            onChange={(e) => setFeedbackComment(e.target.value)}
            placeholder="Tell us more about your experience..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFeedbackDialog(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (selectedSuggestionForFeedback && feedbackRating > 0) {
                submitUserFeedback(
                  selectedSuggestionForFeedback,
                  feedbackRating,
                  feedbackRating >= 3,
                  feedbackComment
                );
              }
            }}
            variant="contained"
            disabled={feedbackRating === 0}
          >
            Submit Feedback
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
};

export default AnalysisAssistant;