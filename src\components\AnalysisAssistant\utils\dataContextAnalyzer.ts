// Data Context Analyzer for Enhanced Statistical Analysis Advisor
// Analyzes dataset characteristics to provide intelligent method recommendations

interface VariableAnalysis {
  name: string;
  type: 'numeric' | 'categorical' | 'datetime' | 'text';
  uniqueValues?: any[];
  missingCount?: number;
  distribution?: {
    mean?: number;
    median?: number;
    std?: number;
    skewness?: number;
    kurtosis?: number;
  };
  categories?: {
    value: any;
    count: number;
    percentage: number;
  }[];
}

interface DatasetContext {
  totalRows: number;
  totalColumns: number;
  numericVariables: VariableAnalysis[];
  categoricalVariables: VariableAnalysis[];
  datetimeVariables: VariableAnalysis[];
  textVariables: VariableAnalysis[];
  missingDataPercentage: number;
  identifierVariables: VariableAnalysis[];
  potentialOutliers: number;
  dataQualityScore: number;
}

interface AnalysisRecommendation {
  methodId: string;
  priority: 'high' | 'medium' | 'low';
  confidence: number; // 0-1 scale
  reasoning: string[];
  contextualTips: string[];
  prerequisites: string[];
  estimatedTime: string;
}

export class DataContextAnalyzer {
  private context: DatasetContext;

  constructor(datasetAnalysis: any) {
    this.context = this.parseDatasetAnalysis(datasetAnalysis);
  }

  private parseDatasetAnalysis(analysis: any): DatasetContext {
    const variableAnalysis = analysis?.variableAnalysis || [];
    
    const numericVars = variableAnalysis.filter((v: any) => v.type === 'numeric');
    const categoricalVars = variableAnalysis.filter((v: any) => v.type === 'categorical');
    const datetimeVars = variableAnalysis.filter((v: any) => v.type === 'datetime');
    const textVars = variableAnalysis.filter((v: any) => v.type === 'text');
    
    // Identify potential identifier variables
    const identifierVars = variableAnalysis.filter((v: any) => 
      v.name.toLowerCase().includes('id') || 
      v.name.toLowerCase().includes('key') ||
      (v.type === 'numeric' && v.uniqueValues && v.uniqueValues.length === variableAnalysis.length)
    );

    const totalMissing = variableAnalysis.reduce((sum: number, v: any) => sum + (v.missingCount || 0), 0);
    const totalCells = variableAnalysis.length * (analysis.totalRows || 0);
    const missingPercentage = totalCells > 0 ? (totalMissing / totalCells) * 100 : 0;

    return {
      totalRows: analysis.totalRows || 0,
      totalColumns: variableAnalysis.length,
      numericVariables: numericVars,
      categoricalVariables: categoricalVars,
      datetimeVariables: datetimeVars,
      textVariables: textVars,
      missingDataPercentage: missingPercentage,
      identifierVariables: identifierVars,
      potentialOutliers: analysis.potentialOutliers || 0,
      dataQualityScore: this.calculateDataQualityScore(analysis)
    };
  }

  private calculateDataQualityScore(analysis: any): number {
    let score = 100;
    
    // Deduct points for missing data
    if (this.context?.missingDataPercentage > 10) score -= 20;
    else if (this.context?.missingDataPercentage > 5) score -= 10;
    
    // Deduct points for small sample size
    if (analysis.totalRows < 30) score -= 15;
    else if (analysis.totalRows < 100) score -= 5;
    
    // Deduct points for too many identifier variables
    const idRatio = this.context?.identifierVariables.length / this.context?.totalColumns;
    if (idRatio > 0.3) score -= 10;
    
    return Math.max(0, Math.min(100, score));
  }

  public getRecommendations(): AnalysisRecommendation[] {
    const recommendations: AnalysisRecommendation[] = [];

    // Descriptive Statistics Recommendations
    if (this.context.numericVariables.length > 0) {
      recommendations.push({
        methodId: 'descriptive_single',
        priority: 'high',
        confidence: 0.95,
        reasoning: [
          `You have ${this.context.numericVariables.length} numeric variable(s)`,
          'Descriptive statistics provide essential data understanding',
          'Always recommended as a first step in analysis'
        ],
        contextualTips: [
          'Start with descriptive statistics to understand your data distribution',
          'Look for outliers and unusual patterns',
          'Check for normality before parametric tests'
        ],
        prerequisites: ['Numeric variables present'],
        estimatedTime: '2-3 minutes'
      });
    }

    // Frequency Analysis Recommendations
    if (this.context.categoricalVariables.length > 0) {
      recommendations.push({
        methodId: 'frequency_table',
        priority: 'high',
        confidence: 0.9,
        reasoning: [
          `You have ${this.context.categoricalVariables.length} categorical variable(s)`,
          'Frequency tables reveal category distributions',
          'Essential for understanding categorical data patterns'
        ],
        contextualTips: [
          'Examine category frequencies for imbalanced data',
          'Look for rare categories that might need grouping',
          'Consider creating cross-tabulations for relationships'
        ],
        prerequisites: ['Categorical variables present'],
        estimatedTime: '1-2 minutes'
      });
    }

    // Normality Testing Recommendations
    if (this.context.numericVariables.length > 0 && this.context.totalRows >= 10) {
      recommendations.push({
        methodId: 'normality_tests',
        priority: 'medium',
        confidence: 0.8,
        reasoning: [
          'Normality testing is crucial before parametric analyses',
          'Your dataset has sufficient observations for testing',
          'Results will guide choice between parametric and non-parametric tests'
        ],
        contextualTips: [
          'Test normality for each numeric variable separately',
          'Consider transformations if data is not normal',
          'Use visual methods (histograms, Q-Q plots) alongside tests'
        ],
        prerequisites: ['At least 10 observations', 'Numeric variables'],
        estimatedTime: '3-4 minutes'
      });
    }

    // Group Comparison Recommendations
    const binaryGroupingVars = this.context.categoricalVariables.filter(v => 
      v.uniqueValues && v.uniqueValues.length === 2
    );

    if (this.context.numericVariables.length > 0 && binaryGroupingVars.length > 0) {
      const sampleSize = this.context.totalRows;
      const priority = sampleSize >= 30 ? 'high' : 'medium';
      const confidence = sampleSize >= 30 ? 0.85 : 0.7;

      recommendations.push({
        methodId: 'independent_ttest',
        priority,
        confidence,
        reasoning: [
          `You have numeric variables and ${binaryGroupingVars.length} binary grouping variable(s)`,
          'Independent t-tests compare means between two groups',
          sampleSize >= 30 ? 'Sample size is adequate for t-tests' : 'Consider non-parametric alternatives for small samples'
        ],
        contextualTips: [
          'Check normality assumptions before running t-tests',
          'Consider Welch\'s t-test if variances are unequal',
          'Use Mann-Whitney U test if normality assumptions are violated'
        ],
        prerequisites: ['Numeric dependent variable', 'Binary grouping variable', 'Independent observations'],
        estimatedTime: '5-7 minutes'
      });
    }

    // Multiple Group Comparison Recommendations
    const multiGroupVars = this.context.categoricalVariables.filter(v => 
      v.uniqueValues && v.uniqueValues.length > 2 && v.uniqueValues.length <= 10
    );

    if (this.context.numericVariables.length > 0 && multiGroupVars.length > 0) {
      recommendations.push({
        methodId: 'one_way_anova',
        priority: 'medium',
        confidence: 0.75,
        reasoning: [
          `You have numeric variables and ${multiGroupVars.length} multi-level grouping variable(s)`,
          'ANOVA compares means across multiple groups',
          'Useful for comparing 3 or more groups simultaneously'
        ],
        contextualTips: [
          'Check normality and homogeneity of variance assumptions',
          'Consider post-hoc tests if ANOVA is significant',
          'Use Kruskal-Wallis test for non-parametric alternative'
        ],
        prerequisites: ['Numeric dependent variable', 'Categorical grouping variable (3+ levels)', 'Normal distribution', 'Equal variances'],
        estimatedTime: '8-10 minutes'
      });
    }

    // Association Analysis Recommendations
    if (this.context.categoricalVariables.length >= 2) {
      recommendations.push({
        methodId: 'chi_square',
        priority: 'medium',
        confidence: 0.8,
        reasoning: [
          `You have ${this.context.categoricalVariables.length} categorical variables`,
          'Chi-square tests examine associations between categorical variables',
          'Useful for testing independence between categories'
        ],
        contextualTips: [
          'Ensure expected frequencies are at least 5 in each cell',
          'Consider Fisher\'s exact test for small samples',
          'Examine residuals to understand the nature of associations'
        ],
        prerequisites: ['Two or more categorical variables', 'Expected frequencies ≥ 5'],
        estimatedTime: '4-6 minutes'
      });
    }

    // Data Quality Warnings
    if (this.context.dataQualityScore < 70) {
      recommendations.unshift({
        methodId: 'data_quality_check',
        priority: 'high',
        confidence: 1.0,
        reasoning: [
          `Data quality score: ${this.context.dataQualityScore}/100`,
          'Address data quality issues before statistical analysis',
          'Poor data quality can lead to misleading results'
        ],
        contextualTips: [
          'Handle missing data appropriately',
          'Check for and address outliers',
          'Validate data entry and coding'
        ],
        prerequisites: ['Dataset loaded'],
        estimatedTime: '10-15 minutes'
      });
    }

    // Sort by priority and confidence
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.confidence - a.confidence;
    });
  }

  public getDataSummary(): {
    overview: string;
    strengths: string[];
    concerns: string[];
    recommendations: string[];
  } {
    const { numericVariables, categoricalVariables, totalRows, missingDataPercentage, dataQualityScore } = this.context;

    const overview = `Your dataset contains ${totalRows} observations with ${numericVariables.length} numeric and ${categoricalVariables.length} categorical variables.`;

    const strengths: string[] = [];
    const concerns: string[] = [];
    const recommendations: string[] = [];

    // Assess strengths
    if (totalRows >= 100) strengths.push('Good sample size for statistical analysis');
    if (missingDataPercentage < 5) strengths.push('Minimal missing data');
    if (dataQualityScore >= 80) strengths.push('High data quality score');
    if (numericVariables.length > 0 && categoricalVariables.length > 0) {
      strengths.push('Good mix of variable types for diverse analyses');
    }

    // Identify concerns
    if (totalRows < 30) concerns.push('Small sample size may limit analysis options');
    if (missingDataPercentage > 10) concerns.push('High percentage of missing data');
    if (dataQualityScore < 70) concerns.push('Data quality issues detected');
    if (this.context.identifierVariables.length / this.context.totalColumns > 0.3) {
      concerns.push('Many identifier variables detected');
    }

    // Generate recommendations
    if (numericVariables.length > 0) {
      recommendations.push('Start with descriptive statistics to understand your data');
    }
    if (concerns.length > 0) {
      recommendations.push('Address data quality concerns before analysis');
    }
    if (totalRows < 30) {
      recommendations.push('Consider non-parametric methods for small samples');
    }

    return { overview, strengths, concerns, recommendations };
  }

  public getContext(): DatasetContext {
    return this.context;
  }
}
