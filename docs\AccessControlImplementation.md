# Enhanced Statistical Analysis Advisor - Access Control Implementation

## Overview

The Enhanced Statistical Analysis Advisor implements a comprehensive access control system that differentiates between user tiers (Guest/Standard/Pro/Educational) and provides appropriate method access based on subscription levels.

## Access Control Logic

### User Tier Hierarchy

```typescript
const tierHierarchy = { 
  guest: 0,      // No account, basic methods only
  standard: 1,   // Free account, basic + intermediate methods
  edu: 1,        // Educational free tier = standard level
  pro: 2,        // Paid subscription, all methods
  edu_pro: 2     // Educational pro tier = pro level
};
```

### Method Access Levels

#### Guest Access (Level 0) - Free for all users
- **Descriptive Statistics** - Basic data summarization
- **Frequency Tables** - Categorical data distribution
- **Normality Tests** - Distribution testing
- **Cross-Tabulation** - Categorical relationships
- **Independent t-test** - Two-group comparison
- **Paired t-test** - Before/after comparison
- **One-Sample t-test** - Sample vs population comparison

#### Standard Access (Level 1) - Requires Standard account or higher
- **Mann-Whitney U Test** - Non-parametric two-group comparison
- **One-Way ANOVA** - Multiple group comparison
- **Wilcoxon Signed-Rank Test** - Non-parametric paired comparison
- **Kruskal-Wallis Test** - Non-parametric multiple group comparison

#### Pro Access (Level 2) - Requires Pro subscription
- **Two-Way ANOVA** - Factorial analysis with interaction effects
- **Repeated Measures ANOVA** - Within-subjects analysis

## Visual Access Control Indicators

### User Tier Status Display
Each user sees their current tier status in the header:
- **Guest Access** - Gray chip with explore icon
- **Standard Account** - Blue chip with person icon
- **Pro Account** - Green chip with star icon
- **Educational** - Purple chip with school icon
- **Educational Pro** - Green chip with school icon

### Method Access Indicators
Each method card shows:
- **Access Level Badge** - Color-coded indicator (Free/Standard+/Pro Only)
- **Upgrade Overlay** - For restricted methods, shows upgrade prompt
- **Action Buttons** - "Run Analysis" for accessible methods, "Upgrade" for restricted

### Access Level Legend
The tree view includes a legend showing:
- **Guest** methods (gray)
- **Standard** methods (blue) 
- **Pro** methods (green)
- Current user tier highlighted

## Implementation Details

### Access Control Function

```typescript
const canAccessMethod = useCallback((methodId: string): boolean => {
  const requiredLevel = ACCESS_REQUIREMENTS[methodId];
  if (!requiredLevel) return true;
  
  // Guest users can only access guest-level methods
  if (isGuest) return requiredLevel === 'guest';
  if (!user) return requiredLevel === 'guest';
  
  // Check tier hierarchy
  const requiredTierLevel = tierHierarchy[requiredLevel] || 0;
  const userTierLevel = tierHierarchy[effectiveTier] || 0;
  
  return userTierLevel >= requiredTierLevel;
}, [isGuest, user, effectiveTier]);
```

### Method Filtering
Methods are filtered based on user access:
- **Recommendations View**: Shows all methods with access indicators
- **Tree View**: Option to show only accessible methods
- **Search View**: Filters results based on access level

### Upgrade Prompts
For restricted methods:
- **Guest Users**: "Sign in for more advanced features" with login link
- **Standard Users**: "Upgrade to Pro for advanced statistical analyses" with pricing link
- **Method Cards**: Overlay with upgrade button for restricted methods

## User Experience by Tier

### Guest Users
- See 7 basic statistical methods
- Clear indicators for premium features
- Prominent sign-in prompts
- No access to advanced methods

### Standard Users  
- Access to 11 methods (Guest + Standard)
- See Pro methods with upgrade prompts
- Clear indication of Pro benefits
- Upgrade path clearly presented

### Pro Users
- Full access to all 13 methods
- No upgrade prompts
- Premium badge display
- Complete feature access

### Educational Users
- **Educational Free**: Same as Standard (11 methods)
- **Educational Pro**: Same as Pro (13 methods)
- Special educational branding
- Academic pricing indicators

## Testing Access Control

### Demo Component
A comprehensive demo component (`AccessControlDemo.tsx`) allows testing all user scenarios:
- Switch between user types
- See real-time access control changes
- Validate method filtering
- Test upgrade prompts

### Test Scenarios
1. **Guest Access**: Only basic methods visible and functional
2. **Standard Access**: Intermediate methods unlocked
3. **Pro Access**: All methods available
4. **Educational Tiers**: Proper academic access levels
5. **Upgrade Flows**: Appropriate prompts and navigation

## Security Considerations

### Client-Side Validation
- Access control is enforced in the UI
- Methods are filtered based on user tier
- Upgrade prompts prevent unauthorized access

### Server-Side Enforcement
- Backend APIs should validate user permissions
- Database queries respect user access levels
- Subscription status verified server-side

### Data Protection
- User tier information securely managed
- Access logs for audit purposes
- Graceful handling of authentication failures

## Accessibility Features

### Visual Indicators
- Color-coded access levels with text labels
- Icons supplement color coding
- High contrast for accessibility

### Keyboard Navigation
- All interactive elements keyboard accessible
- Focus management for modal dialogs
- Screen reader friendly labels

### Progressive Enhancement
- Core functionality works without JavaScript
- Graceful degradation for older browsers
- Mobile-responsive design

## Future Enhancements

### Dynamic Access Control
- Real-time subscription status updates
- Temporary access grants for trials
- Feature-specific permissions

### Usage Analytics
- Track method access patterns
- Identify upgrade conversion opportunities
- Monitor user engagement by tier

### Advanced Filtering
- Custom access level configurations
- Institution-specific permissions
- Time-limited access grants

## Troubleshooting

### Common Issues
1. **Methods not showing**: Check user authentication status
2. **Incorrect access level**: Verify effectiveTier calculation
3. **Upgrade prompts not working**: Check navigation configuration
4. **Visual indicators missing**: Verify theme and styling

### Debug Information
The component logs detailed access control decisions to the console for debugging:
```
🔍 Auth Access Check - Method: two_way_anova
{
  requiredLevel: "pro",
  effectiveTier: "standard", 
  hasAccess: false,
  accountType: "standard"
}
```

This comprehensive access control system ensures that users see appropriate content for their subscription level while providing clear upgrade paths and maintaining a professional user experience.
