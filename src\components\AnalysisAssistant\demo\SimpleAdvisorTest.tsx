import React from 'react';
import {
  Box,
  Typography,
  Alert,
  Paper
} from '@mui/material';
import { AuthContext } from '../../../context/AuthContext';
import { ResultsProvider } from '../../../context/ResultsContext';

// Simple test component to verify the Interactive Statistical Advisor works
const SimpleAdvisorTest: React.FC = () => {
  // Mock auth context for testing
  const mockAuthContext = {
    user: { id: '1', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'standard',
    effectiveTier: 'standard',
    canAccessAdvancedAnalysis: false,
    canAccessPublicationReady: false,
    isEducationalUser: false,
    educationalTier: null
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Simple Interactive Statistical Advisor Test
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          This is a simple test to verify the Interactive Statistical Advisor component loads correctly.
        </Typography>
      </Alert>

      <Paper elevation={2} sx={{ p: 3 }}>
        <AuthContext.Provider value={mockAuthContext as any}>
          <ResultsProvider>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Interactive Statistical Advisor Test
            </Typography>
            <Typography variant="body2" color="text.secondary">
              If you can see this message, the basic component structure is working.
              The full Interactive Statistical Advisor will be loaded here.
            </Typography>
            
            {/* We'll add the actual component here once we verify imports work */}
            <Alert severity="success" sx={{ mt: 2 }}>
              Component structure test passed! ✅
            </Alert>
          </ResultsProvider>
        </AuthContext.Provider>
      </Paper>
    </Box>
  );
};

export default SimpleAdvisorTest;
