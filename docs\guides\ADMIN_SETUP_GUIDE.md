# Admin Dashboard Setup Guide

## Overview

This guide explains how to set up and use the Admin Dashboard system in DataStatPro. The admin dashboard provides comprehensive user management, system statistics, and administrative controls.

## Initial Setup

### 1. Apply Database Migration

First, apply the admin role migration to your Supabase database:

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Navigate to your project
3. Go to the SQL Editor
4. Copy the contents of `supabase/migrations/20250705000000_add_admin_role_support.sql`
5. Paste into the SQL Editor and run the script

### 2. Create Your First Admin User

Since admin privileges are required to manage other admins, you need to manually create the first admin user directly in the database:

**Option A: Using Supabase Dashboard**
1. Go to the Table Editor in your Supabase dashboard
2. Navigate to the `profiles` table
3. Find your user profile (use your email to identify it)
4. Edit the row and set `is_admin` to `true`
5. Save the changes

**Option B: Using SQL**
```sql
-- Replace '<EMAIL>' with your actual email
UPDATE public.profiles 
SET is_admin = true 
WHERE id = (
  SELECT id FROM auth.users 
  WHERE email = '<EMAIL>'
);
```

### 3. Verify Admin Access

1. Sign in to DataStatPro with your admin account
2. You should see an "Admin Dashboard" item in the sidebar
3. Navigate to `/admin-dashboard` to access the admin interface

## Admin Dashboard Features

### User Management
- View all registered users with pagination and search
- Manage user account types (Standard, Pro, Educational, Educational Pro)
- Assign/remove admin privileges
- View user activity and statistics

### System Statistics
- Total user counts by account type
- User registration trends
- Active user metrics
- Dataset usage statistics
- System health monitoring

### Notification Management
- Create and manage system-wide notifications
- Target specific user groups
- Set notification priorities and expiration dates
- Track notification engagement

### Admin Management
- View all admin users
- Manage admin privileges (cannot remove your own)
- Admin activity logging

## Security Features

### Role-Based Access Control
- All admin functions require verified admin status
- Database-level security with Row Level Security (RLS)
- Function-level security with admin privilege checks
- Prevents privilege escalation attacks

### Safety Measures
- Admins cannot remove their own admin privileges
- All admin actions are logged
- Input validation for all admin operations
- Secure database functions with proper error handling

## Admin Functions Reference

### Database Functions Available to Admins

1. **`is_user_admin(user_id)`** - Check if a user has admin privileges
2. **`get_admin_users()`** - Get list of all admin users
3. **`get_all_users(page_size, page_offset, search_term)`** - Get paginated user list
4. **`get_user_statistics()`** - Get comprehensive system statistics
5. **`update_user_admin_status(user_id, is_admin)`** - Update admin status
6. **`update_user_account_type(user_id, account_type)`** - Update account type

### Account Types
- `standard` - Free account with basic features
- `pro` - Paid account with full features
- `edu` - Educational free account with advanced analysis
- `edu_pro` - Educational paid account with all features

## Troubleshooting

### Cannot Access Admin Dashboard
1. Verify your user has `is_admin = true` in the profiles table
2. Clear browser cache and cookies
3. Sign out and sign back in
4. Check browser console for any JavaScript errors

### Admin Functions Not Working
1. Ensure the migration was applied successfully
2. Check that RLS policies are enabled
3. Verify function permissions in Supabase
4. Check Supabase logs for any database errors

### Cannot Create First Admin
1. Ensure you're using the correct email address
2. Verify the user profile exists in the profiles table
3. Check that the profiles table has the `is_admin` column
4. Try using the SQL method instead of the dashboard

## Best Practices

### Admin Account Security
- Use strong passwords for admin accounts
- Enable two-factor authentication if available
- Regularly review admin user list
- Remove admin privileges when no longer needed

### User Management
- Document reasons for account type changes
- Communicate with users before making changes
- Monitor user activity for unusual patterns
- Regularly review user statistics

### System Maintenance
- Monitor system statistics regularly
- Keep admin user list minimal
- Review and clean up old notifications
- Monitor database performance

## Support

If you encounter issues with the admin dashboard:

1. Check the browser console for JavaScript errors
2. Review Supabase logs for database errors
3. Verify all migration scripts were applied
4. Ensure proper RLS policies are in place

For additional support, refer to the main DataStatPro documentation or contact the development team.

## Testing & Validation Checklist

### Pre-Deployment Testing

#### 1. Database Migration Testing
- [ ] Apply the admin role migration successfully
- [ ] Verify all admin functions are created
- [ ] Test RLS policies are working correctly
- [ ] Confirm indexes are created for performance

#### 2. Admin Role Assignment Testing
- [ ] Create first admin user manually
- [ ] Verify admin status is reflected in AuthContext
- [ ] Test admin role detection on login
- [ ] Confirm admin status persists across sessions

#### 3. Admin Dashboard Access Testing
- [ ] Test direct URL access to `/admin-dashboard`
- [ ] Verify non-admin users are redirected
- [ ] Test admin sidebar item visibility
- [ ] Confirm route guards are working

#### 4. Admin Dashboard Functionality Testing
- [ ] Test all admin dashboard tabs load correctly
- [ ] Verify system statistics display properly
- [ ] Test user management functions
- [ ] Confirm notification management works
- [ ] Test admin settings functionality

#### 5. Security Testing
- [ ] Verify non-admin users cannot access admin functions
- [ ] Test admin function security at database level
- [ ] Confirm admin cannot remove own privileges
- [ ] Test error handling for unauthorized access

#### 6. Integration Testing
- [ ] Verify existing functionality remains unaffected
- [ ] Test admin features don't break guest access
- [ ] Confirm regular user experience unchanged
- [ ] Test admin features work with all account types

### Post-Deployment Validation

#### 1. Production Environment Testing
- [ ] Verify admin dashboard loads in production
- [ ] Test admin functions with real data
- [ ] Confirm performance is acceptable
- [ ] Monitor for any errors or issues

#### 2. User Acceptance Testing
- [ ] Admin users can access all required features
- [ ] System statistics are accurate and helpful
- [ ] User management functions work as expected
- [ ] Admin workflow is intuitive and efficient

#### 3. Security Validation
- [ ] Penetration testing of admin features
- [ ] Verify no privilege escalation vulnerabilities
- [ ] Confirm audit logging is working
- [ ] Test backup and recovery procedures

### Rollback Procedures

If issues are discovered after deployment:

1. **Immediate Actions**
   - Disable admin dashboard route temporarily
   - Remove admin privileges from affected users
   - Monitor system for any cascading issues

2. **Database Rollback**
   - Revert admin role migration if necessary
   - Restore previous RLS policies
   - Remove admin-related functions

3. **Code Rollback**
   - Revert admin dashboard components
   - Remove admin routing configuration
   - Restore previous sidebar configuration

### Performance Monitoring

Monitor these metrics after deployment:

- Admin dashboard page load times
- Database query performance for admin functions
- System resource usage during admin operations
- User session management with admin features

### Success Criteria

The admin dashboard implementation is considered successful when:

- [ ] All admin users can access the dashboard without issues
- [ ] System statistics are accurate and update in real-time
- [ ] User management functions work reliably
- [ ] Security controls prevent unauthorized access
- [ ] Existing functionality remains completely unaffected
- [ ] Performance impact is minimal and acceptable
- [ ] Admin workflow improves system management efficiency
