import React from 'react';
import { Box, Container } from '@mui/material';
import EpiCalcOptions from '../components/EpiCalc/EpiCalcOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';

interface EpiCalcPageProps {
  onNavigate: (path: string) => void;
}

const EpiCalcPage: React.FC<EpiCalcPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <EpiCalcOptions onNavigate={onNavigate} />
      </Box>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible
      />
    </Container>
  );
};

export default EpiCalcPage;
