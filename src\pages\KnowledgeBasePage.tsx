import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Chip,
  Avatar,
  CardHeader,
  useTheme,
  alpha,
  Paper,
  Container,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  School as TutorialIcon, // Using School icon for tutorials
  Launch as LaunchIcon,
} from '@mui/icons-material';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';
// Removed RouterLink import - using onNavigate prop instead

interface KnowledgeBasePageProps {
  onNavigate?: (path: string) => void;
}

// Define the structure for a tutorial option
interface TutorialOption {
  id: string; // Unique identifier
  name: string;
  shortDescription: string;
  detailedDescription: string;
  filePath: string; // Path to the markdown file
  icon: React.ReactNode;
  category: 'Descriptive Statistics' | 'Inferential Statistics' | 'Regression Analysis' | 'Advanced Analysis' | 'Epidemiological Methods' | 'Study Design' | 'Data Visualization' | 'Data Management' | 'Publication Ready' | 'Other'; // Define tutorial categories
  color: string;
}

// Define the list of available tutorials
// This list will grow as more tutorials are added
export const tutorialOptions: TutorialOption[] = [
  {
    id: 'logistic-regression',
    name: 'Logistic Regression Tutorial',
    shortDescription: 'Understand and use the Logistic Regression analysis feature.',
    detailedDescription: 'A comprehensive guide covering the basics of logistic regression, how to use the component, computational details, formula explanations, and interpretation of results.',
    filePath: 'src/docs/tutorials/logistic-regression-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Regression Analysis',
    color: '#FF9800', // Orange
  },
  {
    id: 't-tests-and-alternatives',
    name: 'T-Tests and Alternatives',
    shortDescription: 'Comprehensive reference guide for t-tests and non-parametric alternatives.',
    detailedDescription: 'Detailed coverage of one-sample, independent samples, and paired t-tests, plus non-parametric alternatives including Mann-Whitney U, Wilcoxon signed-rank, and Sign tests. Includes formulas, assumptions, effect sizes, and interpretation guidelines.',
    filePath: 'src/docs/tutorials/t-tests-and-alternatives-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Inferential Statistics',
    color: '#2196F3', // Blue
  },
  {
    id: 'anova-tests-and-alternatives',
    name: 'ANOVA Tests and Alternatives',
    shortDescription: 'Comprehensive reference guide for ANOVA tests and non-parametric alternatives.',
    detailedDescription: 'Detailed coverage of one-way, two-way, repeated measures, and mixed-design ANOVA, plus non-parametric alternatives including Kruskal-Wallis, Friedman, and aligned rank transform tests. Includes formulas, assumptions, effect sizes, post-hoc testing, and interpretation guidelines.',
    filePath: 'src/docs/tutorials/anova-tests-and-alternatives-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Inferential Statistics',
    color: '#9C27B0', // Purple
  },
  {
    id: 'numerical-descriptives',
    name: 'Numerical Descriptives and Distributions',
    shortDescription: 'Comprehensive reference guide for descriptive statistics of numerical data.',
    detailedDescription: 'Detailed coverage of measures of central tendency, variability, and distribution shape. Includes confidence intervals, normality testing, data transformations, outlier detection, and interpretation guidelines for numerical data analysis.',
    filePath: 'src/docs/tutorials/numerical-descriptives-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Descriptive Statistics',
    color: '#4CAF50', // Green
  },
  {
    id: 'categorical-descriptives',
    name: 'Categorical Descriptives and Association',
    shortDescription: 'Comprehensive reference guide for categorical data analysis and association measures.',
    detailedDescription: 'Detailed coverage of frequency tables, cross-tabulations, chi-square tests, measures of association, odds ratios, and specialized tests for categorical data. Includes effect sizes, confidence intervals, and interpretation guidelines.',
    filePath: 'src/docs/tutorials/categorical-descriptives-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Descriptive Statistics',
    color: '#FF5722', // Deep Orange
  },
  {
    id: 'epidemiological-calculators',
    name: 'Epidemiological Calculators and Study Design',
    shortDescription: 'Comprehensive reference guide for epidemiological methods and study designs.',
    detailedDescription: 'Detailed coverage of case-control, cohort, and cross-sectional studies. Includes odds ratios, relative risk, attributable risk, diagnostic test evaluation, ROC curves, and sample size calculations for epidemiological research.',
    filePath: 'src/docs/tutorials/epidemiological-calculators-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Epidemiological Methods',
    color: '#795548', // Brown
  },
  {
    id: 'sample-size-power-analysis',
    name: 'Sample Size and Power Analysis',
    shortDescription: 'Comprehensive reference guide for sample size calculations and power analysis.',
    detailedDescription: 'Detailed coverage of power analysis fundamentals, sample size calculations for various study designs, effect size determination, survival analysis, cluster randomized trials, and practical guidelines for study planning.',
    filePath: 'src/docs/tutorials/sample-size-power-analysis-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Study Design',
    color: '#607D8B', // Blue Grey
  },
  {
    id: 'correlation-linear-regression',
    name: 'Correlation and Linear Regression Analysis',
    shortDescription: 'Comprehensive reference guide for correlation analysis and linear regression modeling.',
    detailedDescription: 'Detailed coverage of Pearson and Spearman correlations, simple and multiple linear regression, model assumptions, diagnostics, selection techniques, coefficient interpretation, and prediction intervals with practical examples.',
    filePath: 'src/docs/tutorials/correlation-linear-regression-tutorial.md', // Path to the markdown file
    icon: <TutorialIcon />,
    category: 'Regression Analysis',
    color: '#E91E63', // Pink
  },
  {
    id: 'exploratory-factor-analysis',
    name: 'Exploratory Factor Analysis (EFA)',
    shortDescription: 'Comprehensive reference guide for exploratory factor analysis and latent structure discovery.',
    detailedDescription: 'Detailed coverage of factor extraction methods, rotation techniques, factor retention criteria, interpretation guidelines, and practical applications for identifying underlying factors in multivariate data.',
    filePath: 'src/docs/tutorials/exploratory-factor-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#FF5722', // Deep Orange
  },
  {
    id: 'confirmatory-factor-analysis',
    name: 'Confirmatory Factor Analysis (CFA)',
    shortDescription: 'Comprehensive reference guide for confirmatory factor analysis and measurement model validation.',
    detailedDescription: 'Detailed coverage of CFA model specification, estimation methods, fit assessment, reliability and validity evaluation, measurement invariance, and structural equation modeling applications.',
    filePath: 'src/docs/tutorials/confirmatory-factor-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#4CAF50', // Green
  },
  {
    id: 'survival-analysis',
    name: 'Survival Analysis',
    shortDescription: 'Comprehensive reference guide for time-to-event analysis and survival modeling.',
    detailedDescription: 'Detailed coverage of Kaplan-Meier estimation, log-rank tests, Cox proportional hazards regression, parametric survival models, and advanced topics in survival analysis for medical and reliability research.',
    filePath: 'src/docs/tutorials/survival-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#8BC34A', // Light Green
  },
  {
    id: 'reliability-analysis',
    name: 'Reliability Analysis',
    shortDescription: 'Comprehensive reference guide for measurement reliability and consistency assessment.',
    detailedDescription: 'Detailed coverage of internal consistency, test-retest reliability, inter-rater reliability, generalizability theory, and advanced reliability measures for psychometric and measurement applications.',
    filePath: 'src/docs/tutorials/reliability-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#00BCD4', // Cyan
  },
  {
    id: 'mediation-moderation',
    name: 'Mediation and Moderation Analysis',
    shortDescription: 'Comprehensive reference guide for mediation and moderation analysis in behavioral research.',
    detailedDescription: 'Detailed coverage of simple and multiple mediation, moderation analysis, conditional process analysis, bootstrap methods, and advanced techniques for understanding complex variable relationships.',
    filePath: 'src/docs/tutorials/mediation-moderation-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#673AB7', // Deep Purple
  },
  {
    id: 'cluster-analysis',
    name: 'Cluster Analysis',
    shortDescription: 'Comprehensive reference guide for cluster analysis and unsupervised learning methods.',
    detailedDescription: 'Detailed coverage of hierarchical clustering, k-means, density-based clustering, model-based clustering, cluster validation, and practical applications for pattern recognition and data segmentation.',
    filePath: 'src/docs/tutorials/cluster-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#9C27B0', // Purple
  },
  {
    id: 'meta-analysis',
    name: 'Meta-Analysis',
    shortDescription: 'Comprehensive reference guide for meta-analysis and systematic review methods.',
    detailedDescription: 'Detailed coverage of effect size calculation, fixed and random effects models, heterogeneity assessment, publication bias detection, moderator analysis, and advanced meta-analytic techniques.',
    filePath: 'src/docs/tutorials/meta-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#2196F3', // Blue
  },
  {
    id: 'variable-tree-analysis',
    name: 'Variable Tree Analysis',
    shortDescription: 'Comprehensive reference guide for hierarchical variable relationship visualization and analysis.',
    detailedDescription: 'Detailed coverage of tree construction algorithms, multi-variable hierarchical analysis, interactive visualization techniques, and practical applications for exploring complex data structures.',
    filePath: 'src/docs/tutorials/variable-tree-analysis-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Advanced Analysis',
    color: '#795548', // Brown
  },
  {
    id: 'data-visualization',
    name: 'Data Visualization Methods',
    shortDescription: 'Comprehensive reference guide for data visualization principles, techniques, and best practices.',
    detailedDescription: 'Detailed coverage of visualization principles, chart type selection, statistical graphics, interactive visualization, color theory, accessibility considerations, and common visualization mistakes with practical solutions.',
    filePath: 'src/docs/tutorials/data-visualization-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Data Visualization',
    color: '#FF6F00', // Deep Orange (distinct from existing colors)
  },
  {
    id: 'publication-ready-tools',
    name: 'Publication Ready Tools',
    shortDescription: 'Comprehensive guide for creating professional, journal-quality research outputs.',
    detailedDescription: 'Complete tutorial covering all Publication Ready tools including tables, statistical analysis, methods documentation, visualization, and reference management. Learn to create professional outputs that meet academic publishing standards.',
    filePath: 'src/docs/tutorials/publication-ready-tools-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Publication Ready',
    color: '#1976D2', // Blue
  },
  {
    id: 'confidence-interval-calculator',
    name: 'Confidence Interval Calculator',
    shortDescription: 'Comprehensive reference guide for confidence interval calculations across various statistical parameters.',
    detailedDescription: 'Detailed coverage of confidence intervals for means, proportions, differences, correlations, regression parameters, variances, and diagnostic test accuracy. Includes multiple calculation methods, interpretation guidelines, and practical applications for statistical inference.',
    filePath: 'src/docs/tutorials/confidence-interval-calculator-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Statistical Inference',
    color: '#2196F3', // Blue
  },
  {
    id: 'effect-size-calculator',
    name: 'Effect Size Calculator',
    shortDescription: 'Comprehensive reference guide for effect size calculations and interpretation.',
    detailedDescription: 'Detailed coverage of Cohen\'s d, Hedge\'s g, eta squared, Cramer\'s V, and other effect size measures. Includes confidence intervals, interpretation guidelines, publication-ready reporting, and practical significance assessment for research studies.',
    filePath: 'src/docs/tutorials/effect-size-calculator-tutorial.md',
    icon: <TutorialIcon />,
    category: 'Statistical Inference',
    color: '#4CAF50', // Green
  },
  // Add more tutorials here as they are created
];

const KnowledgeBasePage: React.FC<KnowledgeBasePageProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  
  // Initialize social meta for knowledge base page
  useSocialMeta();

  // Dynamically get categories from tutorial options
  const categories = ['All', ...Array.from(new Set(tutorialOptions.map(option => option.category)))].sort();

  const filteredOptions = selectedCategory === 'All'
    ? tutorialOptions
    : tutorialOptions.filter(option => option.category === selectedCategory);

  // Function to get category icon (can be expanded later if needed)
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Statistics': return <TutorialIcon />;
      // Add cases for other categories if they need different icons
      default: return <TutorialIcon />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs navigation */}
      <Breadcrumbs sx={{ mb: 2 }}>
        <Link
          component="button"
          onClick={() => onNavigate?.('dashboard')}
          color="inherit"
          sx={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            textDecoration: 'underline',
            '&:hover': { textDecoration: 'none' }
          }}
        >
          Home
        </Link>
        <Typography color="text.primary">Knowledge Base</Typography>
      </Breadcrumbs>

      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Knowledge Base & Tutorials
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Learn how to use the application's features with detailed tutorials
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse our collection of guides and tutorials covering various statistical analyses, data management techniques, and more.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Tutorials Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option) => (
          <Grid item xs={12} md={6} lg={4} key={option.id}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography variant="h6" fontWeight="bold">
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  </Box>
                }
                // Action button for more info could be added here if needed
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {option.shortDescription}
                </Typography>

                <Typography variant="body2" paragraph>
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                {/* Navigate to the tutorial page using onNavigate */}
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate?.(`knowledge-base/${option.id}`)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                >
                  Read Tutorial
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Social Share Widget */}
      <SocialShareWidget 
        variant="floating"
        position="bottom-right"
        platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
        collapsible
      />

      {/* Placeholder for future help section */}
      {/*
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need More Help?
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Explore additional resources or contact support for assistance.
            </Typography>
          </Box>
        </Box>
      </Paper>
      */}
    </Container>
  );
};

export default KnowledgeBasePage;
