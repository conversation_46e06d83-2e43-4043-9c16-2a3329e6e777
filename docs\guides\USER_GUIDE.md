# DataStatPro User Guide

Welcome to DataStatPro, an advanced statistical analysis web application designed to help you analyze, visualize, and interpret your data with ease. This guide provides comprehensive instructions for using all features of the application.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Data Management](#data-management)
3. [Descriptive Statistics](#descriptive-statistics)
4. [Inferential Statistics](#inferential-statistics)
5. [Correlation Analysis](#correlation-analysis)
6. [Data Visualization](#data-visualization)
7. [EpiCalc](#epicalc)
8. [Sample Size Calculators](#sample-size-calculators)
9. [Statistical Formulas](#statistical-formulas)
10. [Which Test](#which-test)
11. [Tips and Best Practices](#tips-and-best-practices)

## Getting Started

### Application Overview

Statistica offers a comprehensive set of statistical tools organized into the following main sections:

- **Data Management**: Import, edit, and transform your data
- **Descriptive Statistics**: Summarize and explore your data
- **Inferential Statistics**: Test hypotheses and make inferences
- **Correlation Analysis**: Examine relationships between variables
- **Data Visualization**: Create informative charts and graphs

### Navigation

- Use the sidebar menu to navigate between different sections
- Each section contains tabs for specific analyses or features
- The home page provides quick access to key features and your datasets

## Data Management

### Importing Data

1. Navigate to **Data Management** → **Import Data**
2. You can import data through:
   - **CSV File**: Upload a CSV file with the "Select CSV File" button
   - **Sample Data**: Generate sample data for testing and learning

#### Import Options

- **First row contains headers**: Enable if your CSV has column names
- **Skip empty lines**: Ignore blank lines in your data
- **Auto-detect data types**: Automatically identify numeric, text, and date values

### Data Editor

1. Navigate to **Data Management** → **Data Editor**
2. View and edit your data in a spreadsheet-like interface
3. Edit cells directly by clicking on them
4. Add new rows or columns using the buttons provided
5. Delete rows or columns by selecting them and clicking the delete button

### Data Transformation

1. Navigate to **Data Management** → **Transform Data**
2. Available transformations include:
   - **Standardize**: Convert to z-scores (mean=0, SD=1)
   - **Normalize**: Scale values to a 0-1 range
   - **Log Transform**: Apply natural logarithm
   - **Square Root**: Apply square root transformation
   - **Binning**: Convert continuous to categorical
   - **Recode**: Change specific values
   - **Create Computed Variable**: Create new variables using formulas

### Pivot Analysis

1. Navigate to **Data Management** → **Pivot Analysis**
2. Create pivot tables to summarize and analyze data
3. Drag and drop variables to define rows, columns, values, and filters
4. Choose aggregation functions (sum, count, average, etc.)
5. Visualize pivot table data with charts

### Results Manager

1. Navigate to **Data Management** → **Results Manager**
2. View and manage the results of your analyses and visualizations
3. Save, export, or delete results

## Descriptive Statistics

### Basic Descriptive Statistics

1. Navigate to **Descriptive Statistics** → **Descriptives**
2. Select your dataset and variables
3. View statistics including:
   - Central tendency (mean, median, mode)
   - Dispersion (variance, standard deviation, range)
   - Distribution shape (skewness, kurtosis)
   - Percentiles

### Frequency Tables

1. Navigate to **Descriptive Statistics** → **Frequencies**
2. Select a categorical variable
3. View count, percentage, and cumulative percentage for each category
4. Optionally visualize as a bar chart or pie chart

### Cross Tabulation

1. Navigate to **Descriptive Statistics** → **Cross Tabulation**
2. Select two categorical variables
3. View the joint frequency distribution
4. Calculate optional statistics:
   - Chi-square test of independence
   - Cramer's V
   - Expected vs. observed counts

### Normality Tests

1. Navigate to **Descriptive Statistics** → **Normality Test**
2. Select a numeric variable
3. View normality test results:
   - Shapiro-Wilk test
   - Q-Q Plot
   - Histogram with normal curve overlay

## Inferential Statistics

### t-Tests

1. Navigate to **Inferential Statistics** → **t-Tests**
2. Choose from:
   - **One-Sample t-Test**: Compare sample mean to a known value
   - **Independent Samples t-Test**: Compare means between two groups
   - **Paired Samples t-Test**: Compare means between paired observations

#### One-Sample t-Test

1. Select a numeric variable
2. Enter the test value (population mean)
3. Choose a significance level
4. View results including:
   - t-statistic
   - Degrees of freedom
   - p-value
   - Confidence interval
   - Effect size (Cohen's d)

#### Independent Samples t-Test

1. Select a numeric (dependent) variable
2. Select a categorical (grouping) variable with exactly two groups
3. Choose a significance level
4. View results including:
   - t-statistic
   - Degrees of freedom
   - p-value
   - Mean difference
   - Confidence interval
   - Effect size (Cohen's d)
   - Levene's test for equality of variances

#### Paired Samples t-Test

1. Select two numeric variables representing before and after measurements
2. Choose a significance level
3. View results including:
   - t-statistic
   - Degrees of freedom
   - p-value
   - Mean difference
   - Confidence interval
   - Effect size (Cohen's d)

### ANOVA

1. Navigate to **Inferential Statistics** → **ANOVA**
2. Select a numeric (dependent) variable
3. Select a categorical (grouping) variable with three or more groups
4. Choose a significance level
5. View results including:
   - F-statistic
   - Degrees of freedom
   - p-value
   - Effect size (eta-squared)
   - Post-hoc tests (Tukey HSD)
   - Homogeneity of variance test

### Analysis Assistant

1. Navigate to **Inferential Statistics** → **Analysis Assistant**
2. Get guidance on choosing the appropriate statistical test
3. Receive assistance with interpreting your results

### Workflow Pages

1. Navigate to **Inferential Statistics** → **Workflows**
2. Access guided workflows for common analyses:
   - **t-Test Workflow**: Step-by-step guide for t-tests
   - **ANOVA Workflow**: Step-by-step guide for one-way ANOVA
   - **Repeated Measures ANOVA Workflow**: Step-by-step guide for repeated measures ANOVA

### Non-parametric Tests

1. Navigate to **Inferential Statistics** → **Non-parametric Tests**
2. Choose from:
   - **Mann-Whitney U Test**: Non-parametric alternative to independent t-test
   - **Wilcoxon Signed-Rank Test**: Non-parametric alternative to paired t-test
   - **Kruskal-Wallis Test**: Non-parametric alternative to one-way ANOVA
   - **Chi-Square Test**: Test for independence between categorical variables

## Correlation Analysis

### Correlation Matrix

1. Navigate to **Correlation Analysis** → **Correlation Matrix**
2. Select multiple numeric variables
3. Choose correlation type:
   - **Pearson**: For linear relationships
   - **Spearman**: For monotonic relationships
4. View the correlation matrix with:
   - Correlation coefficients
   - p-values
   - Visual heatmap
   - Significance indicators

### Linear Regression

1. Navigate to **Correlation Analysis** → **Linear Regression**
2. Select an independent variable (predictor)
3. Select a dependent variable (outcome)
4. View regression results including:
   - Regression equation
   - R-squared value
   - F-statistic and p-value
   - Coefficients with standard errors and p-values
   - Residual plots
   - Prediction tool
   - Statistical assumptions check

### Logistic Regression

1. Navigate to **Correlation Analysis** → **Logistic Regression**
2. Select an independent variable (predictor)
3. Select a binary dependent variable (0/1 outcome)
4. View regression results including:
   - Logistic regression equation
   - Odds ratios
   - Classification metrics (accuracy, precision, recall)
   - ROC curve and AUC
   - Prediction tool
   - Model interpretation

## Data Visualization

### Bar Chart

1. Navigate to **Data Visualization** → **Bar Chart**
2. Select a categorical variable for categories
3. Select one or more numeric variables for values
4. Choose aggregation method (sum, average, count)
5. Customize chart settings:
   - Title and labels
   - Color scheme
   - Bar orientation (vertical/horizontal)
   - Stacked/grouped bars

### Pie Chart

1. Navigate to **Data Visualization** → **Pie Chart**
2. Select a categorical variable for segments
3. Select a numeric variable for segment size
4. Customize chart settings:
   - Title
   - Color scheme
   - Donut hole size
   - Label display options

### Histogram

1. Navigate to **Data Visualization** → **Histogram**
2. Select a numeric variable
3. Choose number of bins
4. Customize chart settings:
   - Normal curve overlay
   - Mean/median reference lines
   - Density vs. frequency display

### Box Plot

1. Navigate to **Data Visualization** → **Box Plot**
2. Select a numeric variable for the box plot
3. Optionally select a categorical variable for grouping
4. Customize chart settings:
   - Orientation (vertical/horizontal)
   - Outlier display
   - Mean marker
   - Notches

### Scatter Plot

1. Navigate to **Data Visualization** → **Scatter Plot**
2. Select X-axis variable (numeric)
3. Select Y-axis variable (numeric)
4. Optional selections:
   - Size variable (for bubble size)
   - Color variable (for point categories)
   - Z-axis variable (for 3D)
5. Customize chart settings:
   - Regression line
   - Confidence intervals
   - Correlation statistics
   - Point labeling
   - Jitter amount

### Rain Cloud Plot

1. Navigate to **Data Visualization** → **Rain Cloud Plot**
2. Select a numeric variable for values
3. Optionally select a categorical variable for grouping
4. Customize chart settings:
   - Orientation (vertical/horizontal)
   - Outlier display
   - Mean line visibility
   - Violin side (positive/negative/both)
   - Jitter amount
   - Color scheme

## EpiCalc

### Overview

EpiCalc provides a collection of calculators for epidemiological statistics.

### Available Calculators

- **Coming Soon**: Details on specific calculators will be added here.

## Sample Size Calculators

### Overview

Sample Size Calculators help determine the appropriate sample size for various study designs.

### Available Calculators

- **One-Sample Calculator**: Calculate sample size for comparing a mean to a known value.
- **Two-Sample Calculator**: Calculate sample size for comparing means between two independent groups.
- **Paired Sample Calculator**: Calculate sample size for comparing means between paired observations.
- **More Than Two Groups Calculator**: Calculate sample size for comparing means among three or more groups (ANOVA).

## Statistical Formulas

### Overview

The Statistical Formulas section provides a reference for common statistical formulas.

### Available Formulas

- **Coming Soon**: Details on specific formulas will be added here.

## Which Test

### Overview

The Which Test guide assists in selecting the appropriate statistical test based on your research question and data characteristics.

### Using the Guide

- **Coming Soon**: Details on how to use the guide will be added here.

## Tips and Best Practices

### Data Preparation

- **Clean your data** before analysis by handling missing values
- **Check for outliers** that might skew results
- **Verify data types** are correct for your variables
- **Transform variables** when necessary to meet test assumptions

### Statistical Test Selection

- **t-tests and ANOVA** require approximately normally distributed data
- Use **non-parametric alternatives** when assumptions are violated
- **Correlation** measures association but not causation
- **Regression** is appropriate when predicting one variable from others

### Interpreting Results

- Consider **practical significance** not just statistical significance
- Look at **effect sizes**, not just p-values
- **Confidence intervals** provide more information than point estimates
- Be cautious about **multiple comparisons** inflating Type I error

### Visualization Best Practices

- Choose the **appropriate chart type** for your data
- Use **clear labels** for axes and titles
- **Limit the number of categories** in bar and pie charts
- Consider **color-blind friendly palettes**
- Avoid **misleading scales** or **3D effects**

### Workflow Efficiency

- Save your dataset after important modifications
- Generate sample data for practicing new techniques
- Use the correlation matrix as a first step in exploring relationships
- Create visualizations to complement your statistical tests
