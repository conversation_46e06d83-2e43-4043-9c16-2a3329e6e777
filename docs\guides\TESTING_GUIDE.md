# Statistica Testing Guide

This guide provides instructions for testing the Statistica application across different browsers and devices to ensure consistent appearance and behavior.

## Overview

Statistica is a responsive web application for statistical analysis that works across a wide range of devices and browsers. This guide will help testers verify that the application maintains its functionality and appearance across these different environments.

## Development Environment Testing Tools

When running the application in development mode (`npm run dev`), a Responsive Design Tester tool is available in the bottom-right corner of the screen. This tool provides:

- Device presets for common screen sizes
- Breakpoint indicators
- Issue reporting capabilities
- Current device information

Use this tool to quickly check how the application looks at different viewport sizes without changing your browser window size.

## Cross-Browser Testing

### Supported Browsers

Test the application on these browsers (latest two versions of each):

- Google Chrome
- Mozilla Firefox
- Safari
- Microsoft Edge
- Opera (if available)

### Browser Testing Checklist

For each browser, check:

1. **Layout:** Does the application render correctly without visual glitches?
2. **Functionality:** Do all interactive elements (buttons, dropdowns, etc.) work properly?
3. **Performance:** Is the application responsive and smooth?
4. **Forms:** Do all form elements (inputs, selects, etc.) work correctly?
5. **Animations:** Do transitions and animations play smoothly?
6. **Charts:** Do data visualizations render correctly?

## Responsive Design Testing

### Screen Sizes to Test

- Mobile: 320px - 600px width
- Tablet: 600px - 1024px width
- Laptop: 1024px - 1440px width
- Desktop: 1440px+ width

### Orientation Testing

For mobile and tablet devices, test in both portrait and landscape orientations.

### Responsive Testing Checklist

For each screen size:

1. **Content Scaling:** Does all content fit appropriately without horizontal scrolling?
2. **Navigation:** Is the navigation accessible and functional?
3. **Touch Targets:** Are buttons and interactive elements large enough to tap on touch devices?
4. **Text Readability:** Is all text readable without zooming?
5. **Layout Shifts:** Does the layout change appropriately between breakpoints without unwanted shifts?

## Device Testing

### Priority Devices

If possible, test on these physical devices:

- iPhone (latest model)
- Android phone (Samsung Galaxy or Google Pixel)
- iPad or Android tablet
- Windows laptop
- macOS laptop
- Desktop computer with large monitor

### Device Emulation

If physical devices aren't available, use:

1. **Chrome DevTools:** Device Mode (F12 > Toggle Device Toolbar)
2. **Firefox Responsive Design Mode:** (F12 > Responsive Design Mode)
3. **BrowserStack:** For cloud-based device testing

## Accessibility Testing

1. **Keyboard Navigation:** Can you navigate the entire application using only the keyboard?
2. **Screen Reader:** Use a screen reader like NVDA (Windows) or VoiceOver (macOS) to test accessibility
3. **Zoom Text:** Increase browser zoom to 200% and check if the layout remains usable

## Performance Testing

1. **Initial Load:** How quickly does the application load initially?
2. **Navigation:** How responsive is navigation between different pages?
3. **Calculations:** How quickly do statistical calculations complete?
4. **Memory Usage:** Does the application maintain reasonable memory usage during extended sessions?

## Testing Procedure

1. Start with a desktop Chrome browser (reference implementation)
2. Test primary user flows on reference implementation
3. Repeat primary user flows on other browsers
4. Repeat primary user flows on mobile devices
5. Check for specific issues on problematic browsers/devices
6. Document any issues with screenshots and detailed descriptions

## Common Issues to Look For

- Layout breaks at specific screen sizes
- Touch targets too small on mobile
- Text truncation making content unreadable
- Charts not rendering correctly on specific browsers
- Performance issues on less powerful devices
- Form elements behaving differently across browsers

## Reporting Issues

When you find an issue, create a report with:

1. **Environment:** Browser name, version, device, and screen size
2. **Description:** Clear description of the issue
3. **Steps to Reproduce:** Numbered steps to reproduce the issue
4. **Expected vs. Actual:** What you expected vs. what happened
5. **Screenshots:** Visual evidence of the issue
6. **Severity:** Critical, High, Medium, or Low impact

## Testing Resources

- **Detailed Testing Checklists:** See `docs/CROSS_BROWSER_TESTING.md` and `docs/RESPONSIVE_DESIGN_CHECKLIST.md`
- **Compatibility Checker:** The application includes a built-in compatibility checker that will warn if critical features are missing

## Automated Testing

While manual testing is essential for UI verification, we also have:

- Unit tests for core statistical functions
- Component tests for UI elements
- End-to-end tests for critical user journeys

Run the automated test suite with:

```
npm run test
```

---

For more detailed testing criteria, refer to the comprehensive checklists in the docs directory.