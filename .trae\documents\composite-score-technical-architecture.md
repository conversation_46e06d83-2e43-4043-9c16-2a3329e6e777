# Composite Score Feature - Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[DataTransform Component]
    C --> D[CompositeScoreDialog Component]
    D --> E[Data Processing Utils]
    E --> F[Dataset Context]
    F --> G[Local State Management]

    subgraph "Frontend Layer"
        B
        C
        D
    end

    subgraph "Data Processing Layer"
        E
        F
        G
    end
```

## 2. Technology Description

* Frontend: React\@18 + Material-UI\@5 + TypeScript\@5 + Vite

* State Management: React Context API + useState/useReducer

* Data Processing: Custom utility functions in JavaScript

* Backend: None (client-side processing)

## 3. Route Definitions

| Route                      | Purpose                                                   |
| -------------------------- | --------------------------------------------------------- |
| /data-management           | Main data management page with transform options          |
| /data-management/transform | Data transformation interface with composite score option |

## 4. Component Architecture

### 4.1 Core Components

**DataTransform Component Enhancement**

```typescript
// Add new transformation type
enum TransformationType {
  // ... existing types
  COMPOSITE_SCORE = 'COMPOSITE_SCORE'
}

// Add composite score dialog state
interface DataTransformState {
  // ... existing state
  isCompositeScoreOpen: boolean;
}
```

**CompositeScoreDialog Component**

```typescript
interface CompositeScoreDialogProps {
  open: boolean;
  onClose: () => void;
  dataset: Dataset;
  onApply: (result: CompositeScoreResult) => void;
}

interface CompositeScoreState {
  selectedVariables: string[];
  categoryMappings: Record<string, number>;
  aggregationMethod: AggregationMethod;
  newVariableName: string;
  step: number;
  isValid: boolean;
}

type AggregationMethod = 'sum' | 'mean' | 'count' | 'std' | 'min' | 'max';
```

### 4.2 Data Processing Functions

**Composite Score Calculation**

```typescript
// Function signature for composite score computation
function computeCompositeScore(
  data: DataRow[],
  selectedVariables: string[],
  categoryMappings: Record<string, number>,
  aggregationMethod: AggregationMethod,
  newVariableName: string
): DataRow[];

// Helper function to extract unique categories
function extractUniqueCategories(
  data: DataRow[],
  variables: string[]
): string[];

// Validation function for mappings
function validateCategoryMappings(
  categories: string[],
  mappings: Record<string, number>
): ValidationResult;
```

## 5. Data Model

### 5.1 Data Model Definition

```mermaid
erDiagram
    DATASET ||--o{ COLUMN : contains
    DATASET ||--o{ DATA_ROW : contains
    COLUMN ||--|| DATA_TYPE : has
    COMPOSITE_SCORE_CONFIG ||--o{ CATEGORY_MAPPING : contains
    
    DATASET {
        string id PK
        string name
        Column[] columns
        DataRow[] data
        Date createdAt
        Date updatedAt
    }
    
    COLUMN {
        string name PK
        DataType type
        VariableRole role
        ColumnStatistics statistics
    }
    
    DATA_ROW {
        string id PK
        Record values
    }
    
    COMPOSITE_SCORE_CONFIG {
        string[] selectedVariables
        string aggregationMethod
        string newVariableName
        CategoryMapping[] mappings
    }
    
    CATEGORY_MAPPING {
        string categoryLabel PK
        number numericValue
    }
```

### 5.2 TypeScript Interface Definitions

```typescript
// Core interfaces for composite score feature
interface CompositeScoreConfig {
  selectedVariables: string[];
  categoryMappings: Record<string, number>;
  aggregationMethod: AggregationMethod;
  newVariableName: string;
}

interface CategoryMapping {
  label: string;
  value: number;
}

interface CompositeScoreResult {
  success: boolean;
  newColumn: Column;
  updatedData: DataRow[];
  errors?: string[];
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Extended transformation type
type TransformationType = 
  | 'RECODE'
  | 'STANDARDIZE' 
  | 'LOG'
  | 'SQUARE_ROOT'
  | 'BINNING'
  | 'DUMMY_CODING'
  | 'ROBUST_SCALE'
  | 'COMPUTE_VARIABLE'
  | 'COMPOSITE_SCORE';
```

## 6. Implementation Plan

### 6.1 File Structure

```
src/
├── components/
│   └── DataManagement/
│       └── DataTransform/
│           ├── DataTransform.tsx (modify)
│           ├── CompositeScoreDialog.tsx (new)
│           └── index.ts (update)
├── types/
│   └── index.ts (update with new types)
├── utils/
│   └── dataUtilities.ts (add composite score functions)
└── hooks/
    └── useCompositeScore.ts (new custom hook)
```

### 6.2 Key Implementation Steps

1. **Update TransformationType enum** in `src/types/index.ts`
2. **Create CompositeScoreDialog component** with wizard-style interface
3. **Implement data processing functions** in `src/utils/dataUtilities.ts`
4. **Add composite score logic** to DataTransform component
5. **Create custom hook** for composite score state management
6. **Add validation and error handling**
7. **Implement preview functionality**
8. **Add unit tests** for all new functions

### 6.3 Integration Points

**DataTransform.tsx Integration**

```typescript
// Add to transformation type handler
case TransformationType.COMPOSITE_SCORE:
  setIsCompositeScoreOpen(true);
  break;

// Add dialog component
<CompositeScoreDialog
  open={isCompositeScoreOpen}
  onClose={() => setIsCompositeScoreOpen(false)}
  dataset={selectedDataset}
  onApply={handleCompositeScoreApply}
/>
```

**Data Processing Integration**

```typescript
// Add to dataUtilities.ts
export function computeCompositeScore(
  data: DataRow[],
  config: CompositeScoreConfig
): CompositeScoreResult {
  // Implementation details
}
```

## 7. Performance Considerations

### 7.1 Optimization Strategies

* **Lazy Loading**: Load dialog component only when needed

* **Memoization**: Cache category extraction and validation results

* **Batch Processing**: Process large datasets in chunks

* **Progress Indicators**: Show progress for long-running calculations

### 7.2 Memory Management

* Efficient data structures for category mappings

* Cleanup of temporary calculation results

* Optimized rendering for large category lists

## 8. Testing Strategy

### 8.1 Unit Tests

* Test composite score calculation accuracy

* Validate category extraction logic

* Test input validation functions

* Verify error handling scenarios

### 8.2 Integration Tests

* Test dialog component interactions

* Validate data flow between components

* Test dataset updates after transformation

### 8.3 User Acceptance Tests

* Wizard workflow completion

* Data accuracy verification

* Error message clarity

* Performance with large datasets

## 9. Security Considerations

### 9.1 Input Validation

* Sanitize user input for variable names

* Validate numeric mappings for reasonable ranges

* Prevent injection attacks through category labels

### 9.2 Data Privacy

* Client-side processing ensures data privacy

* No data transmission to external services

* Secure handling of sensitive categorical data

## 10. Deployment Notes

### 10.1 Build Requirements

* No additional build dependencies required

* Standard React/TypeScript compilation

* Bundle size impact: \~15-20KB additional

### 10.2 Browser Compatibility

* Modern browsers supporting ES2020+

* React 18 compatibility requirements

* Material-UI 5 browser support matrix

