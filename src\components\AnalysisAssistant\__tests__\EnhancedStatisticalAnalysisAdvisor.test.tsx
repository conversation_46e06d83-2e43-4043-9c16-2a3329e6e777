import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import EnhancedStatisticalAnalysisAdvisor from '../EnhancedStatisticalAnalysisAdvisor';
import { AuthContext } from '../../../context/AuthContext';
import { ResultsProvider } from '../../../context/ResultsContext';

// Mock the missing components
jest.mock('../components/EnhancedRecommendationCard', () => {
  return function MockEnhancedRecommendationCard() {
    return <div>Enhanced Recommendation Card</div>;
  };
});

jest.mock('../components/WorkflowProgress', () => {
  return function MockWorkflowProgress() {
    return <div>Workflow Progress</div>;
  };
});

jest.mock('../utils/dataContextAnalyzer', () => ({
  DataContextAnalyzer: jest.fn().mockImplementation(() => ({
    getRecommendations: () => [],
    getDataSummary: () => ({
      overview: 'Test dataset overview',
      strengths: ['Good sample size'],
      concerns: [],
      recommendations: ['Start with descriptive statistics']
    })
  }))
}));

// Mock theme
const theme = createTheme();

// Mock auth context
const mockAuthContext = {
  user: { id: '1', email: '<EMAIL>' },
  isGuest: false,
  accountType: 'pro',
  effectiveTier: 'pro',
  canAccessAdvancedAnalysis: true,
  canAccessPublicationReady: true,
  isEducationalUser: false,
  educationalTier: null
};

// Mock dataset and analysis
const mockDataset = {
  name: 'Test Dataset',
  columns: ['age', 'gender', 'score'],
  rows: 100
};

const mockDatasetAnalysis = {
  totalRows: 100,
  variableAnalysis: [
    {
      name: 'age',
      type: 'numeric',
      uniqueValues: [18, 19, 20, 21, 22, 23, 24, 25],
      missingCount: 0,
      distribution: {
        mean: 21.5,
        median: 21,
        std: 2.1
      }
    },
    {
      name: 'gender',
      type: 'categorical',
      uniqueValues: ['Male', 'Female'],
      missingCount: 0,
      categories: [
        { value: 'Male', count: 50, percentage: 50 },
        { value: 'Female', count: 50, percentage: 50 }
      ]
    },
    {
      name: 'score',
      type: 'numeric',
      uniqueValues: Array.from({length: 50}, (_, i) => i + 50),
      missingCount: 2,
      distribution: {
        mean: 75.2,
        median: 76,
        std: 12.3
      }
    }
  ]
};

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <AuthContext.Provider value={mockAuthContext as any}>
        <ResultsProvider>
          {children}
        </ResultsProvider>
      </AuthContext.Provider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('EnhancedStatisticalAnalysisAdvisor', () => {
  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor />
      </TestWrapper>
    );
    
    expect(screen.getByText('Statistical Analysis Advisor')).toBeInTheDocument();
  });

  it('shows view mode toggle buttons', () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor />
      </TestWrapper>
    );
    
    expect(screen.getByText('Smart Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Browse Methods')).toBeInTheDocument();
    expect(screen.getByText('Search')).toBeInTheDocument();
  });

  it('displays data loading message when no dataset is provided', () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor />
      </TestWrapper>
    );
    
    expect(screen.getByText(/Load a dataset to get personalized analysis recommendations/)).toBeInTheDocument();
  });

  it('generates recommendations when dataset is provided', async () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor 
          currentDataset={mockDataset}
          datasetAnalysis={mockDatasetAnalysis}
        />
      </TestWrapper>
    );
    
    await waitFor(() => {
      expect(screen.getByText('AI-Powered Recommendations')).toBeInTheDocument();
    });
  });

  it('switches between view modes', async () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor 
          currentDataset={mockDataset}
          datasetAnalysis={mockDatasetAnalysis}
        />
      </TestWrapper>
    );
    
    // Switch to tree view
    const treeButton = screen.getByText('Browse Methods');
    fireEvent.click(treeButton);
    
    await waitFor(() => {
      expect(screen.getByText('Browse Analysis Methods')).toBeInTheDocument();
    });
    
    // Switch to search view
    const searchButton = screen.getByText('Search');
    fireEvent.click(searchButton);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search analysis methods...')).toBeInTheDocument();
    });
  });

  it('displays data overview when dataset is loaded', async () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor 
          currentDataset={mockDataset}
          datasetAnalysis={mockDatasetAnalysis}
        />
      </TestWrapper>
    );
    
    await waitFor(() => {
      expect(screen.getByText('Data Overview')).toBeInTheDocument();
      expect(screen.getByText(/Your dataset contains 100 observations/)).toBeInTheDocument();
    });
  });

  it('shows proper access control for Guest users', () => {
    const guestAuthContext = {
      ...mockAuthContext,
      user: null,
      isGuest: true,
      accountType: null,
      effectiveTier: 'guest'
    };

    render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <AuthContext.Provider value={guestAuthContext as any}>
            <ResultsProvider>
              <EnhancedStatisticalAnalysisAdvisor
                currentDataset={mockDataset}
                datasetAnalysis={mockDatasetAnalysis}
              />
            </ResultsProvider>
          </AuthContext.Provider>
        </ThemeProvider>
      </BrowserRouter>
    );

    // Should show Guest Access chip
    expect(screen.getByText('Guest Access')).toBeInTheDocument();

    // Should show upgrade prompts for restricted methods
    expect(screen.getByText(/Sign in.*for more advanced features/)).toBeInTheDocument();
  });

  it('shows proper access control for Standard users', () => {
    const standardAuthContext = {
      ...mockAuthContext,
      accountType: 'standard',
      effectiveTier: 'standard',
      isGuest: false
    };

    render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <AuthContext.Provider value={standardAuthContext as any}>
            <ResultsProvider>
              <EnhancedStatisticalAnalysisAdvisor
                currentDataset={mockDataset}
                datasetAnalysis={mockDatasetAnalysis}
              />
            </ResultsProvider>
          </AuthContext.Provider>
        </ThemeProvider>
      </BrowserRouter>
    );

    // Should show Standard Account chip
    expect(screen.getByText('STANDARD Account')).toBeInTheDocument();

    // Should show upgrade prompt for Pro features
    expect(screen.getByText(/Upgrade to Pro.*for advanced statistical analyses/)).toBeInTheDocument();
  });

  it('shows proper access control for Pro users', () => {
    const proAuthContext = {
      ...mockAuthContext,
      accountType: 'pro',
      effectiveTier: 'pro',
      isGuest: false
    };

    render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <AuthContext.Provider value={proAuthContext as any}>
            <ResultsProvider>
              <EnhancedStatisticalAnalysisAdvisor
                currentDataset={mockDataset}
                datasetAnalysis={mockDatasetAnalysis}
              />
            </ResultsProvider>
          </AuthContext.Provider>
        </ThemeProvider>
      </BrowserRouter>
    );

    // Should show Pro Account chip
    expect(screen.getByText('PRO Account')).toBeInTheDocument();

    // Should not show upgrade prompts
    expect(screen.queryByText(/Upgrade to Pro/)).not.toBeInTheDocument();
  });

  it('shows proper access control for Educational users', () => {
    const eduAuthContext = {
      ...mockAuthContext,
      accountType: 'edu',
      effectiveTier: 'edu',
      isGuest: false,
      isEducationalUser: true,
      educationalTier: 'free'
    };

    render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <AuthContext.Provider value={eduAuthContext as any}>
            <ResultsProvider>
              <EnhancedStatisticalAnalysisAdvisor
                currentDataset={mockDataset}
                datasetAnalysis={mockDatasetAnalysis}
              />
            </ResultsProvider>
          </AuthContext.Provider>
        </ThemeProvider>
      </BrowserRouter>
    );

    // Should show Educational chip
    expect(screen.getByText('Educational')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    render(
      <TestWrapper>
        <EnhancedStatisticalAnalysisAdvisor 
          currentDataset={mockDataset}
          datasetAnalysis={mockDatasetAnalysis}
        />
      </TestWrapper>
    );
    
    // Switch to search mode
    const searchButton = screen.getByText('Search');
    fireEvent.click(searchButton);
    
    // Type in search box
    const searchInput = screen.getByPlaceholderText('Search analysis methods...');
    fireEvent.change(searchInput, { target: { value: 'descriptive' } });
    
    await waitFor(() => {
      expect(screen.getByText(/method.*found/)).toBeInTheDocument();
    });
  });
});

// Integration test for DataContextAnalyzer
describe('DataContextAnalyzer Integration', () => {
  it('generates appropriate recommendations based on data characteristics', () => {
    // This would test the DataContextAnalyzer with various dataset scenarios
    // For now, we'll just ensure it doesn't crash with our mock data
    const { DataContextAnalyzer } = require('../utils/dataContextAnalyzer');
    
    expect(() => {
      const analyzer = new DataContextAnalyzer(mockDatasetAnalysis);
      const recommendations = analyzer.getRecommendations();
      const summary = analyzer.getDataSummary();
      
      expect(recommendations).toBeDefined();
      expect(summary).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
    }).not.toThrow();
  });
});
