// Test script for Pyodide service
// This can be used to verify that the shared Pyodide service works correctly

import pyodideService from './pyodideService';

export async function testPyodideService(): Promise<boolean> {
  try {
    console.log('Testing Pyodide service initialization...');
    
    // Test initialization
    await pyodideService.initialize();
    
    if (!pyodideService.isReady()) {
      console.error('Pyodide service is not ready after initialization');
      return false;
    }
    
    // Test basic Python execution
    const pyodide = pyodideService.getPyodide();
    
    // Test numpy
    pyodide.runPython(`
      import numpy as np
      test_array = np.array([1, 2, 3, 4, 5])
      result = np.mean(test_array)
    `);
    
    const result = pyodide.globals.get('result');
    console.log('Numpy test result:', result);
    
    if (result !== 3) {
      console.error('Numpy test failed');
      return false;
    }
    
    // Test scipy
    pyodide.runPython(`
      from scipy import stats
      import numpy as np
      data = np.array([1, 2, 3, 4, 5])
      t_stat, p_value = stats.ttest_1samp(data, 3)
    `);
    
    const tStat = pyodide.globals.get('t_stat');
    console.log('Scipy test t-statistic:', tStat);
    
    // Test pandas
    pyodide.runPython(`
      import pandas as pd
      df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
      df_mean = df.mean()
    `);
    
    const dfMean = pyodide.globals.get('df_mean').toJs();
    console.log('Pandas test result:', dfMean);
    
    console.log('All Pyodide service tests passed!');
    return true;
    
  } catch (error) {
    console.error('Pyodide service test failed:', error);
    return false;
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testPyodideService = testPyodideService;
}