import React, { useState } from 'react';
import { useData } from '@/context/DataContext'; // Import useData hook
import {
  VariableSelector,
  DatasetSelector,
  Button
} from '@/components/UI'; // Import UI components from barrel file
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert, // For notes and messages
  alpha, // Import alpha for color manipulation
  Typography, // Import Typography
  Box, // Import Box for layout
  Grid, // For layout
  Stack, // For layout
  useTheme // For theme access
} from '@mui/material'; // Import Card parts from MUI
import {
  FlashOn as FlashOnIcon,
  Science as ScienceIcon,
  Info as InfoIcon
} from '@mui/icons-material'; // Import icons
import { DataType } from '@/types'; // Import DataType enum
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'; // Import recharts components
import { calculateRepeatedMeasuresANOVA } from '@/utils/stats/inference/anova'; // Import the calculation function

// Define an interface for the results structure
interface ANOVAResultRow {
  source: string;
  SS: number;
  df: number;
  MS: number;
  F: number | null | string; // Can be NaN, which might be stringified or null
  p: number | null | string;
  etaSquared?: number | null | string;
}

interface DescriptiveStat {
  condition: string;
  mean: number;
  sd: number;
  n: number;
}

interface SphericityResult {
  mauchlyW: number;
  pValue: number;
  assumed: boolean;
  message: string;
}

interface RepeatedMeasuresANOVAResult {
  message: string;
  parameters: {
    subjectIdCol: string;
    withinFactorCols: string[];
    betweenSubjectsFactorCol: string | null; // Add this
    numberOfSubjects: number;
    numberOfLevels: number;
  };
  summary: ANOVAResultRow[];
  means: { [key: string]: number | { [group: string]: number } }; // Update to allow nested object
  descriptives: DescriptiveStat[];
  sphericity: SphericityResult;
  notes: string;
}

interface RepeatedMeasuresANOVAProps {
  // Props if needed, e.g., for integration into a larger workflow
}

export const RepeatedMeasuresANOVA: React.FC<RepeatedMeasuresANOVAProps> = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData(); // Use correct context values
  const theme = useTheme();
  // const [subjectIdVar, setSubjectIdVar] = useState<string | null>(null); // Removed subjectIdVar state
  const [withinFactorVars, setWithinFactorVars] = useState<string[]>([]);
  const [betweenSubjectsFactorVar, setBetweenSubjectsFactorVar] = useState<string | null>(null); // New state for between-subjects factor
  const [results, setResults] = useState<RepeatedMeasuresANOVAResult | null>(null); // Use the defined interface
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use currentDataset directly from the hook
  // Correct type comparison using DataType enum
  const numericColumns = currentDataset?.columns.filter(col => col.type === DataType.NUMERIC).map(col => col.name) || [];
  const categoricalColumns = currentDataset?.columns.filter(col => col.type === DataType.CATEGORICAL).map(col => col.name) || [];
  // Subject ID can be any type, but typically string or number
  // const potentialSubjectIdColumns = currentDataset?.columns.map(col => col.name) || []; // No longer needed for UI

  const handleRunAnalysis = async () => {
    // Validation: betweenSubjectsFactorVar is optional for now
    // Removed subjectIdVar from validation
    if (!currentDataset || withinFactorVars.length < 2) {
      setError("Please select a dataset and at least two within-subject factor levels (columns).");
      return;
    }

    setIsLoading(true);
    setError(null);
    setResults(null);

    try {
      // Ensure currentDataset and its data are available
      if (!currentDataset?.data) {
        setError("Dataset is not loaded or has no data.");
        setIsLoading(false);
        return;
      }

      const analysisResults = await calculateRepeatedMeasuresANOVA(
        currentDataset.data,
        "__implicit_subject_id__", // Pass a placeholder or handle subject ID implicitly in calculation
        withinFactorVars,
        betweenSubjectsFactorVar // Pass the new factor
      );
      setResults(analysisResults);

    } catch (err: any) {
      setError(`Analysis failed: ${err.message}`);
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box>
      <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
        <Stack direction="row" alignItems="center" spacing={2} mb={3}>
          <ScienceIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="bold">Repeated Measures ANOVA Configuration</Typography>
        </Stack>

        {/* Info notice */}
        <Alert severity="info" icon={<InfoIcon />} sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Repeated Measures ANOVA:</strong> This component performs repeated measures analysis of variance
            to analyze data where the same subjects are measured multiple times, accounting for within-subject correlations.
          </Typography>
        </Alert>

        <Typography variant="h6" gutterBottom>Test Options</Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <DatasetSelector
              value={currentDataset?.id ?? ''}
              onChange={(selectedDatasetId: string) => {
                const newDataset = datasets.find(ds => ds.id === selectedDatasetId) || null;
                setCurrentDataset(newDataset);
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <VariableSelector
              label="Within-Subjects Factor Levels (Columns)"
              value={withinFactorVars}
              onChange={(val) => setWithinFactorVars(val as string[])}
              multiple={true}
              datasetId={currentDataset?.id || ''}
              allowedTypes={[DataType.NUMERIC]}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <VariableSelector
              label="Between-Subjects Factor (Optional, Categorical)"
              value={betweenSubjectsFactorVar ?? ''}
              onChange={(val) => setBetweenSubjectsFactorVar(val as string)}
              datasetId={currentDataset?.id || ''}
              allowedTypes={[DataType.CATEGORICAL]}
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<FlashOnIcon />}
                onClick={handleRunAnalysis}
                disabled={isLoading || !currentDataset || withinFactorVars.length < 2}
                size="large"
              >
                {isLoading ? 'Running...' : 'Run Analysis'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2, border: `1px solid ${theme.palette.error.main}` }}>
          <Typography variant="h6" color="error" gutterBottom>Error</Typography>
          <Typography variant="body2">{error}</Typography>
        </Paper>
      )}

      {results && (
        <Paper elevation={0} sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>Results</Typography>
          {results.message && <Alert severity="info" sx={{ mb: 2 }}>{results.message}</Alert>}

          <Typography variant="subtitle1" gutterBottom>ANOVA Summary Table</Typography>
            <TableContainer component={Paper} sx={{ mb: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Source</TableCell>
                    <TableCell align="right">SS</TableCell>
                    <TableCell align="right">df</TableCell>
                    <TableCell align="right">MS</TableCell>
                    <TableCell align="right">F</TableCell>
                    <TableCell align="right">p</TableCell>
                    <TableCell align="right">η²</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {results.summary.map((row) => (
                    <TableRow key={row.source}>
                      <TableCell component="th" scope="row">
                        {row.source}
                      </TableCell>
                      <TableCell align="right">{typeof row.SS === 'number' ? row.SS.toFixed(2) : '-'}</TableCell>
                      <TableCell align="right">{row.df}</TableCell>
                      <TableCell align="right">{typeof row.MS === 'number' && !isNaN(row.MS) ? row.MS.toFixed(2) : '-'}</TableCell>
                      <TableCell align="right">{typeof row.F === 'number' && !isNaN(row.F) ? row.F.toFixed(2) : '-'}</TableCell>
                      <TableCell align="right">{typeof row.p === 'number' && !isNaN(row.p) ? (row.p < 0.001 ? '<0.001' : row.p.toFixed(4)) : '-'}</TableCell>
                      <TableCell align="right">{typeof row.etaSquared === 'number' && !isNaN(row.etaSquared) ? row.etaSquared.toFixed(2) : '-'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          <Typography variant="subtitle1" gutterBottom>Descriptive Statistics</Typography>
            <TableContainer component={Paper} sx={{ mb: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Condition</TableCell>
                    <TableCell align="right">N</TableCell>
                    <TableCell align="right">Mean</TableCell>
                    <TableCell align="right">SD</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {results.descriptives.map((desc) => (
                    <TableRow key={desc.condition}>
                      <TableCell component="th" scope="row">
                        {desc.condition}
                      </TableCell>
                      <TableCell align="right">{desc.n}</TableCell>
                      <TableCell align="right">{desc.mean.toFixed(2)}</TableCell>
                      <TableCell align="right">{desc.sd.toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

          <Typography variant="subtitle1" gutterBottom>Sphericity Test</Typography>
            <Paper sx={{ p: 2, mb: 2, backgroundColor: (theme) => alpha(theme.palette.info.light, 0.1) }}>
              <Typography variant="body2"><strong>Mauchly's W:</strong> {results.sphericity.mauchlyW.toFixed(2)}</Typography>
              <Typography variant="body2"><strong>p-value:</strong> {results.sphericity.pValue < 0.001 ? '<0.001' : results.sphericity.pValue.toFixed(4)}</Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>{results.sphericity.message}</Typography>
            </Paper>

          <Typography variant="subtitle1" gutterBottom>Means Plot</Typography>
            <Paper sx={{ p: 2, mb: 2, height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="name"
                    // If descriptives are per group, XAxis needs unique conditions
                    // This assumes descriptives are now [{ condition: "Time1 (Group: A)", mean: X, ...}, ...]
                    // We need to extract unique conditions for XAxis labels
                    // For simplicity, let's assume withinFactorCols are the primary X-axis ticks for now
                    // A more complex chart would be needed for grouped data directly on X-axis
                    allowDuplicatedCategory={false}
                    padding={{ left: 50, right: 50 }} // Add padding to the x-axis
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  {results.parameters.betweenSubjectsFactorCol !== 'N/A' && results.means && typeof Object.values(results.means)[0] === 'object' ? (
                    // Multiple lines for between-subjects groups
                    Object.keys( (results.means[Object.keys(results.means)[0]] as object) ).map((group, index) => (
                      <Line
                        key={group}
                        type="monotone"
                        dataKey={`mean_${group}`} // We'll need to transform data for this
                        name={`${results.parameters.betweenSubjectsFactorCol}: ${group}`}
                        stroke={index % 2 === 0 ? "#8884d8" : "#82ca9d"} // Different colors for groups
                        activeDot={{ r: 8 }}
                        // Data needs to be [{name: 'Time1', mean_GroupA: X, mean_GroupB: Y}, ...]
                        data={
                          results.parameters.withinFactorCols.map(level => {
                            const meansForLevel = results.means[level] as { [key: string]: number };
                            const entry: any = { name: level };
                            Object.keys(meansForLevel).forEach(g => {
                              entry[`mean_${g}`] = meansForLevel[g];
                            });
                            return entry;
                          })
                        }
                      />
                    ))
                  ) : (
                    // Single line if no between-subjects factor or means are not nested
                    <Line
                      type="monotone"
                      dataKey="mean"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                      data={results.descriptives.map(d => ({ name: d.condition, mean: d.mean }))}
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </Paper>

          {results.notes && <Alert severity="warning">{results.notes}</Alert>}
          {/* Raw JSON for debugging, can be removed later */}
          {/* <pre>{JSON.stringify(results, null, 2)}</pre> */}
        </Paper>
      )}
    </Box>
  );
};

export default RepeatedMeasuresANOVA;
