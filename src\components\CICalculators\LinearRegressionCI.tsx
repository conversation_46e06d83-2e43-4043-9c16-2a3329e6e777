import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Clear as ClearIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface InputData {
  coefficient: string;
  standardError: string;
  sampleSize: string;
  confidenceLevel: string;
  analysisType: 'coefficient' | 'prediction';
  xValue: string; // For prediction intervals
  meanX: string; // For prediction intervals
  sumSquaredX: string; // For prediction intervals
}

interface Results {
  coefficient: number;
  standardError: number;
  tStatistic: number;
  degreesOfFreedom: number;
  tCritical: number;
  marginOfError: number;
  lowerBound: number;
  upperBound: number;
  confidenceLevel: number;
  analysisType: string;
  interpretation: string;
}

const LinearRegressionCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [inputData, setInputData] = useState<InputData>({
    coefficient: '',
    standardError: '',
    sampleSize: '',
    confidenceLevel: '95',
    analysisType: 'coefficient',
    xValue: '',
    meanX: '',
    sumSquaredX: ''
  });
  const [results, setResults] = useState<Results | null>(null);
  const [error, setError] = useState<string>('');

  // Get t-critical value (approximation)
  const getTCritical = (df: number, alpha: number): number => {
    const tTable: { [key: number]: { [key: string]: number } } = {
      1: { '0.10': 6.314, '0.05': 12.706, '0.01': 63.657 },
      2: { '0.10': 2.920, '0.05': 4.303, '0.01': 9.925 },
      3: { '0.10': 2.353, '0.05': 3.182, '0.01': 5.841 },
      4: { '0.10': 2.132, '0.05': 2.776, '0.01': 4.604 },
      5: { '0.10': 2.015, '0.05': 2.571, '0.01': 4.032 },
      10: { '0.10': 1.812, '0.05': 2.228, '0.01': 3.169 },
      15: { '0.10': 1.753, '0.05': 2.131, '0.01': 2.947 },
      20: { '0.10': 1.725, '0.05': 2.086, '0.01': 2.845 },
      25: { '0.10': 1.708, '0.05': 2.060, '0.01': 2.787 },
      30: { '0.10': 1.697, '0.05': 2.042, '0.01': 2.750 },
      40: { '0.10': 1.684, '0.05': 2.021, '0.01': 2.704 },
      60: { '0.10': 1.671, '0.05': 2.000, '0.01': 2.660 },
      120: { '0.10': 1.658, '0.05': 1.980, '0.01': 2.617 }
    };

    const alphaStr = alpha.toFixed(2);
    
    if (df >= 120) {
      return tTable[120][alphaStr] || 1.96;
    }
    
    const availableDf = Object.keys(tTable).map(Number).sort((a, b) => a - b);
    const closestDf = availableDf.reduce((prev, curr) => 
      Math.abs(curr - df) < Math.abs(prev - df) ? curr : prev
    );
    
    return tTable[closestDf][alphaStr] || 1.96;
  };

  const calculateLinearRegressionCI = () => {
    try {
      setError('');
      
      const coefficient = parseFloat(inputData.coefficient);
      const standardError = parseFloat(inputData.standardError);
      const sampleSize = parseInt(inputData.sampleSize);
      const confidenceLevel = parseFloat(inputData.confidenceLevel);
      
      // Validation
      if (isNaN(coefficient) || isNaN(standardError) || isNaN(sampleSize) || isNaN(confidenceLevel)) {
        throw new Error('Please enter valid numeric values for all required fields.');
      }
      
      if (sampleSize <= 2) {
        throw new Error('Sample size must be greater than 2.');
      }
      
      if (standardError <= 0) {
        throw new Error('Standard error must be positive.');
      }
      
      if (confidenceLevel <= 0 || confidenceLevel >= 100) {
        throw new Error('Confidence level must be between 0 and 100.');
      }
      
      // Calculate degrees of freedom
      const degreesOfFreedom = sampleSize - 2; // For simple linear regression
      
      // Calculate alpha and t-critical
      const alpha = (100 - confidenceLevel) / 100;
      const tCritical = getTCritical(degreesOfFreedom, alpha / 2);
      
      // Calculate margin of error
      const marginOfError = tCritical * standardError;
      
      // Calculate confidence interval
      const lowerBound = coefficient - marginOfError;
      const upperBound = coefficient + marginOfError;
      
      // Calculate t-statistic
      const tStatistic = coefficient / standardError;
      
      // Generate interpretation
      let interpretation = '';
      if (inputData.analysisType === 'coefficient') {
        interpretation = `We are ${confidenceLevel}% confident that the true regression coefficient lies between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}. `;
        if (lowerBound > 0) {
          interpretation += 'Since the entire interval is positive, there is evidence of a positive relationship.';
        } else if (upperBound < 0) {
          interpretation += 'Since the entire interval is negative, there is evidence of a negative relationship.';
        } else {
          interpretation += 'Since the interval contains zero, there is no significant evidence of a linear relationship.';
        }
      } else {
        interpretation = `We are ${confidenceLevel}% confident that the predicted value lies between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
      }
      
      const calculatedResults: Results = {
        coefficient,
        standardError,
        tStatistic,
        degreesOfFreedom,
        tCritical,
        marginOfError,
        lowerBound,
        upperBound,
        confidenceLevel,
        analysisType: inputData.analysisType,
        interpretation
      };
      
      setResults(calculatedResults);
      setActiveTab(1); // Switch to Results tab
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    }
  };

  const clearAll = () => {
    setInputData({
      coefficient: '',
      standardError: '',
      sampleSize: '',
      confidenceLevel: '95',
      analysisType: 'coefficient',
      xValue: '',
      meanX: '',
      sumSquaredX: ''
    });
    setResults(null);
    setError('');
    setActiveTab(0);
  };

  const copyToClipboard = () => {
    if (!results) return;
    
    const text = `Linear Regression Confidence Interval Results:
` +
      `Analysis Type: ${results.analysisType === 'coefficient' ? 'Regression Coefficient' : 'Prediction Interval'}\n` +
      `Coefficient: ${results.coefficient}\n` +
      `Standard Error: ${results.standardError}\n` +
      `t-statistic: ${results.tStatistic.toFixed(2)}\n` +
      `Degrees of Freedom: ${results.degreesOfFreedom}\n` +
      `t-critical: ${results.tCritical.toFixed(2)}\n` +
      `Margin of Error: ${results.marginOfError.toFixed(2)}\n` +
      `${results.confidenceLevel}% Confidence Interval: [${results.lowerBound.toFixed(2)}, ${results.upperBound.toFixed(2)}]\n` +
      `Interpretation: ${results.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        Linear Regression Confidence Intervals
      </Typography>
      
      <Card>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!results} />
          <Tab label="Guide" />
        </Tabs>
        
        <TabPanel value={activeTab} index={0}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Analysis Type</InputLabel>
                  <Select
                    value={inputData.analysisType}
                    onChange={(e) => setInputData({ ...inputData, analysisType: e.target.value as 'coefficient' | 'prediction' })}
                    label="Analysis Type"
                  >
                    <MenuItem value="coefficient">Regression Coefficient</MenuItem>
                    <MenuItem value="prediction">Prediction Interval</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Regression Coefficient"
                  type="number"
                  value={inputData.coefficient}
                  onChange={(e) => setInputData({ ...inputData, coefficient: e.target.value })}
                  margin="normal"
                  helperText="The estimated regression coefficient (β)"
                />
                
                <TextField
                  fullWidth
                  label="Standard Error"
                  type="number"
                  value={inputData.standardError}
                  onChange={(e) => setInputData({ ...inputData, standardError: e.target.value })}
                  margin="normal"
                  helperText="Standard error of the coefficient"
                />
                
                <TextField
                  fullWidth
                  label="Sample Size (n)"
                  type="number"
                  value={inputData.sampleSize}
                  onChange={(e) => setInputData({ ...inputData, sampleSize: e.target.value })}
                  margin="normal"
                  helperText="Total number of observations"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Confidence Level (%)"
                  type="number"
                  value={inputData.confidenceLevel}
                  onChange={(e) => setInputData({ ...inputData, confidenceLevel: e.target.value })}
                  margin="normal"
                  helperText="Typically 90, 95, or 99"
                />
                
                {inputData.analysisType === 'prediction' && (
                  <>
                    <TextField
                      fullWidth
                      label="X Value for Prediction"
                      type="number"
                      value={inputData.xValue}
                      onChange={(e) => setInputData({ ...inputData, xValue: e.target.value })}
                      margin="normal"
                      helperText="Value of X for which to predict Y"
                    />
                    
                    <TextField
                      fullWidth
                      label="Mean of X"
                      type="number"
                      value={inputData.meanX}
                      onChange={(e) => setInputData({ ...inputData, meanX: e.target.value })}
                      margin="normal"
                      helperText="Sample mean of X values"
                    />
                    
                    <TextField
                      fullWidth
                      label="Sum of Squared X"
                      type="number"
                      value={inputData.sumSquaredX}
                      onChange={(e) => setInputData({ ...inputData, sumSquaredX: e.target.value })}
                      margin="normal"
                      helperText="Σ(X - X̄)²"
                    />
                  </>
                )}
              </Grid>
            </Grid>
            
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={calculateLinearRegressionCI}
                startIcon={<CalculateIcon />}
                size="large"
              >
                Calculate CI
              </Button>
              
              <Button
                variant="outlined"
                onClick={clearAll}
                startIcon={<ClearIcon />}
                size="large"
              >
                Clear All
              </Button>
            </Box>
          </CardContent>
        </TabPanel>
        
        <TabPanel value={activeTab} index={1}>
          {results && (
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2">
                  Confidence Interval Results
                </Typography>
                <Tooltip title="Copy results to clipboard">
                  <IconButton onClick={copyToClipboard}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Input Summary
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemText 
                          primary="Analysis Type" 
                          secondary={results.analysisType === 'coefficient' ? 'Regression Coefficient' : 'Prediction Interval'} 
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Coefficient" secondary={results.coefficient} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Standard Error" secondary={results.standardError} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Confidence Level" secondary={`${results.confidenceLevel}%`} />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Statistical Results
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemText primary="t-statistic" secondary={results.tStatistic.toFixed(2)} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Degrees of Freedom" secondary={results.degreesOfFreedom} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="t-critical" secondary={results.tCritical.toFixed(2)} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Margin of Error" secondary={results.marginOfError.toFixed(2)} />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>
              </Grid>
              
              <Paper sx={{ p: 3, mt: 3, bgcolor: 'primary.50' }}>
                <Typography variant="h6" gutterBottom>
                  {results.confidenceLevel}% Confidence Interval
                </Typography>
                <Typography variant="h4" component="div" sx={{ textAlign: 'center', my: 2 }}>
                  [{results.lowerBound.toFixed(2)}, {results.upperBound.toFixed(2)}]
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body1">
                  <strong>Interpretation:</strong> {results.interpretation}
                </Typography>
              </Paper>
            </CardContent>
          )}
        </TabPanel>
        
        <TabPanel value={activeTab} index={2}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Linear Regression Confidence Intervals Guide
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                When to Use
              </Typography>
              <Typography paragraph>
                Use linear regression confidence intervals when you want to:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Estimate the precision of regression coefficients" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Test hypotheses about regression parameters" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Create prediction intervals for new observations" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Assess the uncertainty in predicted values" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                How to Calculate
              </Typography>
              <Typography paragraph>
                For regression coefficients: CI = β ± t(α/2, df) × SE(β)
              </Typography>
              <Typography paragraph>
                Where:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• β = regression coefficient" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• t(α/2, df) = critical t-value" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• SE(β) = standard error of coefficient" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• df = n - 2 (for simple regression)" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Interpretation
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Coefficient CI" 
                    secondary="If the interval doesn't contain 0, the coefficient is statistically significant" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Prediction CI" 
                    secondary="Provides a range of plausible values for a new observation" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Width" 
                    secondary="Narrower intervals indicate more precise estimates" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Assumptions
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Linear relationship between variables" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Independence of observations" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Homoscedasticity (constant variance)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Normality of residuals" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• No multicollinearity (for multiple regression)" />
                </ListItem>
              </List>
            </Box>
          </CardContent>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default LinearRegressionCI;