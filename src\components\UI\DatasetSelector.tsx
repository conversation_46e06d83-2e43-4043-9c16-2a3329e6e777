import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  FormHelperText,
  Button,
  Card,
  CardContent,
  CardActionArea,
  Grid,
  Chip,
  alpha,
  useTheme,
  IconButton,
  Tooltip,
  Collapse,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  SelectChangeEvent // Import SelectChangeEvent
} from '@mui/material';
import {
  AddCircleOutline as AddCircleOutlineIcon,
  FileUpload as FileUploadIcon,
  FilterList as FilterListIcon,
  TableChart as TableChartIcon, // Correct icon for placeholder
  TableView as TableViewIcon, // Correct icon for View Data button
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon, // For collapsed state
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  Storage as StorageIcon,
  Check as CheckIcon,
  Delete as DeleteIcon // Import DeleteIcon
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { useNavigate } from 'react-router-dom';

export interface DatasetSelectorProps {
  value?: string;
  onChange?: (datasetId: string) => void;
  showEmpty?: boolean;
  emptyMessage?: string;
  filterDataTypes?: ('numeric' | 'categorical' | 'date' | 'all')[];
  minRows?: number;
  minColumns?: number;
  label?: string;
  helperText?: string;
  variant?: 'default' | 'card' | 'compact' | 'radio' | 'minimal';
  showActions?: boolean;
  error?: boolean;
  required?: boolean;
  size?: 'small' | 'medium';
}

const DatasetSelector: React.FC<DatasetSelectorProps> = ({
  value,
  onChange,
  showEmpty = false,
  emptyMessage = 'Please select a dataset',
  filterDataTypes = ['all'],
  minRows = 0,
  minColumns = 0,
  label = 'Dataset',
  helperText,
  variant = 'default',
  showActions = true,
  error = false,
  required = false,
  size = 'medium',
}) => {
  // Use currentDataset instead of activeDataset
  const { datasets, currentDataset, setCurrentDataset, removeDataset } = useData(); // Add removeDataset
  const theme = useTheme();
  const [viewMode, setViewMode] = useState<'list' | 'grid'>(variant === 'card' ? 'grid' : 'list');
  const [expanded, setExpanded] = useState(false); // For 'compact' variant's column details
  const [datasetInfoExpanded, setDatasetInfoExpanded] = useState(false); // For 'default' variant's dataset info
  const navigate = useNavigate();

  // Filter datasets based on requirements
  const filteredDatasets = datasets.filter(dataset => {
    // Filter by minimum rows and columns
    if (dataset.data.length < minRows || dataset.columns.length < minColumns) {
      return false;
    }
    
    // Handle data type filtering
    if (!filterDataTypes.includes('all')) {
      const hasRequiredTypes = filterDataTypes.some(type => {
        return dataset.columns.some(column => {
          switch (type) {
            case 'numeric':
              return column.type === 'numeric';
            case 'categorical':
              return column.type === 'categorical';
            case 'date':
              return column.type === 'date';
            default:
              return true;
          }
        });
      });
      
      if (!hasRequiredTypes) {
        return false;
      }
    }
    
    return true;
  });
  
  // Handle dataset selection
  const handleSelectDataset = (datasetId: string) => {
    const dataset = datasets.find(d => d.id === datasetId);
    if (dataset) {
      setCurrentDataset(dataset);
      if (onChange) {
        onChange(datasetId);
      }
    }
  };
  
  // Determine initial selected value using currentDataset
  const selectedValue = value || (currentDataset ? currentDataset.id : ''); 
  
  // Handle click in card mode
  const handleCardClick = (datasetId: string) => {
    handleSelectDataset(datasetId);
  };

  // Handle dataset deletion
  const handleDeleteDataset = (event: React.MouseEvent, datasetId: string, datasetName: string) => {
    event.stopPropagation(); // Prevent card click or other parent events
    if (window.confirm(`Are you sure you want to delete the dataset "${datasetName}"? This action cannot be undone.`)) {
      removeDataset(datasetId);
    }
  };
  
  // Handle radio change
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSelectDataset(event.target.value);
  };
  
  // Handle dropdown change with correct event type
  const handleDropdownChange = (event: SelectChangeEvent<string>) => { 
    handleSelectDataset(event.target.value); // No need for 'as string'
  };
  
  // Handle import data click
  const handleImportClick = () => {
    navigate('/app/data-management/import');
  };
  
  // Render the component based on the variant
  switch (variant) {
    case 'card':
      return (
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1" fontWeight="medium">
              {label}
              {required && ' *'}
            </Typography>
            
            <Box>
              <Tooltip title="List view">
                <IconButton 
                  size="small" 
                  onClick={() => setViewMode('list')}
                  color={viewMode === 'list' ? 'primary' : 'default'}
                >
                  <ViewListIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Grid view">
                <IconButton 
                  size="small" 
                  onClick={() => setViewMode('grid')}
                  color={viewMode === 'grid' ? 'primary' : 'default'}
                >
                  <ViewModuleIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              {filteredDatasets.length === 0 && (
                <Button
                  startIcon={<FileUploadIcon />}
                  size="small"
                  variant="outlined"
                  onClick={handleImportClick}
                  sx={{ ml: 1 }}
                >
                  Import Data
                </Button>
              )}
            </Box>
          </Box>
          
          {helperText && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {helperText}
            </Typography>
          )}
          
          {filteredDatasets.length === 0 ? (
            <Paper
              elevation={0}
              sx={{
                p: 3,
                textAlign: 'center',
                borderStyle: 'dashed',
                borderWidth: 1,
                borderColor: 'divider',
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.default, 0.5)
              }}
            >
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {emptyMessage}
              </Typography>
              
              <Button
                startIcon={<FileUploadIcon />}
                variant="outlined"
                onClick={handleImportClick}
              >
                Import Data
              </Button>
            </Paper>
          ) : viewMode === 'grid' ? (
            <Grid container spacing={2}>
              {filteredDatasets.map((dataset) => (
                <Grid item xs={12} sm={6} md={4} key={dataset.id}>
                  <Card 
                    sx={{ 
                      height: '100%', 
                      transition: 'all 0.2s',
                      border: dataset.id === selectedValue ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
                      '&:hover': {
                        boxShadow: theme.shadows[4],
                      },
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <Box 
                      onClick={() => handleCardClick(dataset.id)}
                      sx={{
                        flexGrow: 1,
                        cursor: 'pointer',
                        p: 2, // Replicates CardContent padding
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.action.hover, 0.04)
                        }
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle1" noWrap sx={{ mb:1, maxWidth: '100%' }}>
                          {dataset.name}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                          <StorageIcon sx={{ color: 'text.secondary', fontSize: 18, mr: 0.5 }} />
                          <Typography variant="body2" color="text.secondary">
                            {dataset.data.length} rows × {dataset.columns.length} columns
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 'auto' }}>
                        {dataset.columns
                          .filter((_, index) => index < 5) // Show max 5 columns
                          .map((column, index) => (
                            <Chip
                              key={index}
                              label={column.name}
                              size="small"
                              color={
                                column.type === 'numeric' ? 'primary' :
                                column.type === 'categorical' ? 'secondary' :
                                column.type === 'date' ? 'info' : 'default'
                              }
                              variant="outlined"
                              sx={{ height: '20px', fontSize: '0.75rem' }}
                            />
                          ))}
                        {dataset.columns.length > 5 && (
                          <Chip
                            label={`+${dataset.columns.length - 5} more`}
                            size="small"
                            sx={{ height: '20px', fontSize: '0.75rem' }}
                          />
                        )}
                      </Box>
                    </Box>
                    <Box sx={{ p: 1, display: 'flex', justifyContent: 'flex-end', alignItems: 'center', borderTop: `1px solid ${theme.palette.divider}` }}>
                      {dataset.id === selectedValue && (
                        <CheckIcon color="primary" sx={{ verticalAlign: 'middle', mr: 1 }} />
                      )}
                      <Tooltip title="Delete dataset">
                        <IconButton
                          size="small"
                          onClick={(e) => handleDeleteDataset(e, dataset.id, dataset.name)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Box>
              {filteredDatasets.map((dataset) => (
                <Paper
                  key={dataset.id}
                  sx={{
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    backgroundColor: dataset.id === selectedValue
                      ? alpha(theme.palette.primary.main, 0.05)
                      : theme.palette.background.paper,
                    border: dataset.id === selectedValue
                      ? `1px solid ${theme.palette.primary.main}`
                      : `1px solid ${theme.palette.divider}`,
                    '&:hover': {
                      backgroundColor: dataset.id === selectedValue
                        ? alpha(theme.palette.primary.main, 0.1)
                        : alpha(theme.palette.action.hover, 0.04),
                    },
                    // borderRadius: theme.shape.borderRadius, // Optional: ensure consistent border radius
                  }}
                >
                  <Box
                    onClick={() => handleCardClick(dataset.id)}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      flexGrow: 1,
                      cursor: 'pointer',
                      p: 2,
                    }}
                  >
                    <StorageIcon
                      sx={{
                        color: dataset.id === selectedValue ? 'primary.main' : 'text.secondary',
                        mr: 2
                      }}
                    />
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2">
                        {dataset.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {dataset.data.length} rows × {dataset.columns.length} columns
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ p: 2, pl: 0, display: 'flex', alignItems: 'center' }}>
                    {dataset.id === selectedValue && (
                      <CheckIcon color="primary" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                    )}
                    <Tooltip title="Delete dataset">
                      <IconButton
                        size="small"
                        onClick={(e) => handleDeleteDataset(e, dataset.id, dataset.name)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Paper>
              ))}
            </Box>
          )}
        </Box>
      );
      
    case 'radio':
      return (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 1 }}>
            {label}
            {required && ' *'}
          </Typography>
          
          {helperText && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {helperText}
            </Typography>
          )}
          
          {filteredDatasets.length === 0 ? (
            <Paper
              elevation={0}
              sx={{
                p: 3,
                textAlign: 'center',
                borderStyle: 'dashed',
                borderWidth: 1,
                borderColor: 'divider',
                borderRadius: 1
              }}
            >
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {emptyMessage}
              </Typography>
              
              <Button
                startIcon={<FileUploadIcon />}
                variant="outlined"
                onClick={handleImportClick}
              >
                Import Data
              </Button>
            </Paper>
          ) : (
            <RadioGroup
              value={selectedValue}
              onChange={handleRadioChange}
            >
              {filteredDatasets.map((dataset) => (
                <Paper
                  key={dataset.id}
                  sx={{
                    p: 1,
                    mb: 1,
                    backgroundColor: dataset.id === selectedValue 
                      ? alpha(theme.palette.primary.main, 0.05)
                      : theme.palette.background.paper,
                    border: dataset.id === selectedValue 
                      ? `1px solid ${theme.palette.primary.main}`
                      : `1px solid ${theme.palette.divider}`
                  }}
                >
                  <FormControlLabel
                    value={dataset.id}
                    control={<Radio />}
                    label={
                      <Box>
                        <Typography variant="subtitle2">
                          {dataset.name}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary">
                          {dataset.data.length} rows × {dataset.columns.length} columns
                        </Typography>
                      </Box>
                    }
                    sx={{ width: '100%', m: 0 }}
                  />
                </Paper>
              ))}
            </RadioGroup>
          )}
        </Box>
      );
      
    case 'compact':
      return (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
            <FormControl 
              fullWidth 
              size={size} 
              error={error} 
              sx={{ maxWidth: '300px' }}
            >
              <InputLabel id="dataset-select-label">
                {label}
                {required && ' *'}
              </InputLabel>
              <Select
                labelId="dataset-select-label"
                value={selectedValue}
                onChange={handleDropdownChange}
                label={label + (required ? ' *' : '')}
                displayEmpty={showEmpty}
              >
                {showEmpty && (
                  <MenuItem value="">
                    <em>{emptyMessage}</em>
                  </MenuItem>
                )}
                {filteredDatasets.map((dataset) => (
                  <MenuItem key={dataset.id} value={dataset.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography>
                        {dataset.name}
                      </Typography>
                      <Chip
                        label={`${dataset.data.length} rows`}
                        size="small"
                        sx={{ ml: 1, height: '20px' }}
                      />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
              {helperText && <FormHelperText>{helperText}</FormHelperText>}
            </FormControl>
            
            {showActions && (
              <Box sx={{ ml: 1, mt: size === 'small' ? 0.5 : 1 }}>
                <Tooltip title="Import new dataset">
                  <IconButton
                    size="small"
                    onClick={handleImportClick}
                    color="primary"
                  >
                    <AddCircleOutlineIcon fontSize={size === 'small' ? 'small' : 'medium'} />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
          
          {selectedValue && showActions && (
            <Box sx={{ mt: 1 }}>
              <Button
                size="small"
                variant="text"
                startIcon={<ExpandMoreIcon sx={{ 
                  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s'
                }} />}
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? 'Hide details' : 'Show details'}
              </Button>
              
              <Collapse in={expanded} timeout="auto" unmountOnExit>
                <Box sx={{ mt: 1, p: 1.5, bgcolor: alpha(theme.palette.background.default, 0.5), borderRadius: 1 }}>
                  {datasets.find(d => d.id === selectedValue)?.columns.map((column, index) => (
                    <Box key={index} sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                      <Chip
                        label={column.type}
                        size="small"
                        color={
                          column.type === 'numeric' ? 'primary' :
                          column.type === 'categorical' ? 'secondary' :
                          column.type === 'date' ? 'info' : 'default'
                        }
                        sx={{ minWidth: 90, mr: 1 }}
                      />
                      <Typography variant="body2">
                        {column.name}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </Collapse>
            </Box>
          )}
        </Box>
      );
      
    case 'minimal':
      return (
        <FormControl 
          fullWidth 
          size={size} 
          error={error}
          variant="outlined"
        >
          <InputLabel id="dataset-select-minimal-label">
            {label}
            {required && ' *'}
          </InputLabel>
          <Select
            labelId="dataset-select-minimal-label"
            value={selectedValue}
            onChange={handleDropdownChange}
            label={label + (required ? ' *' : '')}
            displayEmpty={showEmpty}
          >
            {showEmpty && (
              <MenuItem value="">
                <em>{emptyMessage}</em>
              </MenuItem>
            )}
            {filteredDatasets.map((dataset) => (
              <MenuItem key={dataset.id} value={dataset.id}>
                {dataset.name}
              </MenuItem>
            ))}
          </Select>
          {helperText && <FormHelperText>{helperText}</FormHelperText>}
        </FormControl>
      );
      
    case 'default':
    default:
      return (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1" fontWeight="medium">
              {label}
              {required && ' *'}
            </Typography>
            <Divider sx={{ flex: 1, ml: 2 }} />
          </Box>
          
          {helperText && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {helperText}
            </Typography>
          )}
          
          {filteredDatasets.length === 0 ? (
            <Paper
              elevation={0}
              sx={{
                p: 3,
                textAlign: 'center',
                borderStyle: 'dashed',
                borderWidth: 1,
                borderColor: 'divider',
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.default, 0.5)
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <TableChartIcon sx={{ fontSize: 40, color: 'text.secondary', opacity: 0.5 }} /> 
              </Box>
              
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {emptyMessage}
              </Typography>
              
              <Button
                startIcon={<FileUploadIcon />}
                variant="contained"
                onClick={handleImportClick}
              >
                Import Data
              </Button>
            </Paper>
          ) : (
            <Box>
              <FormControl fullWidth error={error}>
                <Select
                  value={selectedValue}
                  onChange={handleDropdownChange}
                  displayEmpty={showEmpty}
                  sx={{ mb: 2 }}
                >
                  {showEmpty && (
                    <MenuItem value="">
                      <em>{emptyMessage}</em>
                    </MenuItem>
                  )}
                  {filteredDatasets.map((dataset) => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <StorageIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 20 }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography>
                            {dataset.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ({dataset.data.length} rows, {dataset.columns.length} columns)
                          </Typography>
                        </Box>
                        <Tooltip title="Delete dataset">
                          <IconButton
                            size="small"
                            onClick={(e) => handleDeleteDataset(e, dataset.id, dataset.name)}
                            sx={{ ml: 1 }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              {selectedValue && (
                <Box sx={{ mt: 2 }}> {/* Added margin top for spacing */}
                  <Button
                    onClick={() => setDatasetInfoExpanded(!datasetInfoExpanded)}
                    startIcon={datasetInfoExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                    size="small"
                    sx={{ mb: 1, textTransform: 'none' }}
                  >
                    {datasetInfoExpanded ? 'Hide' : 'Show'} Dataset Information
                  </Button>
                  <Collapse in={datasetInfoExpanded} timeout="auto" unmountOnExit>
                    <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.03), borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center' }}>
                          <InfoIcon fontSize="small" sx={{ mr: 0.5, color: 'primary.main' }} />
                          Selected Dataset Details
                        </Typography>
                        
                        {showActions && (
                          <Box>
                            <Button
                            size="small"
                            variant="outlined"
                            startIcon={<TableViewIcon />} 
                            onClick={() => navigate('/data-management/editor')}
                            sx={{ mr: 1 }}
                          >
                            View Data
                          </Button>
                          
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<FilterListIcon />}
                            onClick={() => navigate('/data-management/transform')}
                          >
                            Transform
                          </Button>
                        </Box>
                      )}
                    </Box>
                    
                    {datasets.find(d => d.id === selectedValue)?.columns.map((column, index) => (
                      <Box key={index} sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                        <Chip
                          label={column.type}
                          size="small"
                          color={
                            column.type === 'numeric' ? 'primary' :
                            column.type === 'categorical' ? 'secondary' :
                            column.type === 'date' ? 'info' : 'default'
                          }
                          sx={{ minWidth: 90, mr: 1 }}
                        />
                        <Typography variant="body2">
                          {column.name}
                        </Typography>
                      </Box>
                    ))}
                  </Paper>
                  </Collapse> {/* Added closing tag here */}
                </Box>
              )}
            </Box>
          )}
        </Box>
      );
  }
};

export default DatasetSelector;
