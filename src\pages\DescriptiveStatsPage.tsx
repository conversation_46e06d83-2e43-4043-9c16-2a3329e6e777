import React from 'react';
import { Box, Container } from '@mui/material';
import DescriptiveStatsOptions from '../components/DescriptiveStats/DescriptiveStatsOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';

interface DescriptiveStatsPageProps {
  onNavigate: (path: string) => void;
}

const DescriptiveStatsPage: React.FC<DescriptiveStatsPageProps> = ({ onNavigate }) => {
  // Initialize social meta for descriptive stats page
  useSocialMeta();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <DescriptiveStatsOptions onNavigate={onNavigate} />
      </Box>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible
      />
    </Container>
  );
};

export default DescriptiveStatsPage;
