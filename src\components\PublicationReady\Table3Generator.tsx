import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  CircularProgress,
  FormControlLabel,
  Switch,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  Divider,
  SelectChangeEvent
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Clear as ClearIcon,
  Info as InfoIcon,
  TableChart as TableChartIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import { DataType } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import AddToResultsButton from '../UI/AddToResultsButton';
import { Snackbar } from '@mui/material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`table3-tabpanel-${index}`}
      aria-labelledby={`table3-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface CorrelationResult {
  variable1: string;
  variable2: string;
  correlation: number;
  pValue: number;
  significance: string;
  confidenceInterval?: [number, number];
  sampleSize: number;
}

interface CorrelationMatrix {
  variables: string[];
  correlations: number[][];
  pValues: number[][];
  sampleSizes: number[][];
}

interface Table3Config {
  title: string;
  variables: string[];
  correlationType: 'pearson' | 'spearman' | 'kendall';
  significanceLevel: number;
  includeCI: boolean;
  includeSampleSizes: boolean;
  includeDescriptives: boolean;
  flagSignificant: boolean;
  apa7Format: boolean;
}

const Table3Generator: React.FC = () => {
  const theme = useTheme();
  const { canAccessProFeatures } = useAuth();
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [config, setConfig] = useState<Table3Config>({
    title: 'Correlation Matrix',
    variables: [],
    correlationType: 'pearson',
    significanceLevel: 0.05,
    includeCI: false,
    includeSampleSizes: true,
    includeDescriptives: true,
    flagSignificant: true,
    apa7Format: true
  });
  
  const [correlationMatrix, setCorrelationMatrix] = useState<CorrelationMatrix | null>(null);
  const [availableVariables, setAvailableVariables] = useState<string[]>([]);
  const [descriptiveStats, setDescriptiveStats] = useState<any[]>([]);
  const [interpretation, setInterpretation] = useState<string | null>(null);
  
  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);
  const data = selectedDataset?.data || [];

  useEffect(() => {
    if (selectedDataset && selectedDataset.data.length > 0) {
      // Use the dataset's column definitions to get numeric columns
      const numericColumns = selectedDataset.columns
        .filter(col => col.type === DataType.NUMERIC)
        .map(col => col.name);
      setAvailableVariables(numericColumns);
    } else {
      setAvailableVariables([]);
    }
  }, [selectedDataset]);

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setConfig(prev => ({ ...prev, variables: [] })); // Clear selected variables
    setCorrelationMatrix(null); // Clear results
    setDescriptiveStats([]);
    setInterpretation(null);
    setError(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  const handleConfigChange = (field: keyof Table3Config, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleVariableSelection = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setConfig(prev => ({
      ...prev,
      variables: typeof value === 'string' ? value.split(',') : value
    }));
  };

  // Function to generate contextual interpretation
  const generateInterpretation = (matrix: CorrelationMatrix, descriptives: any[]): string => {
    if (!matrix || matrix.variables.length === 0) return '';

    const correlationType = config.correlationType === 'pearson' ? 'Pearson product-moment' :
                           config.correlationType === 'spearman' ? 'Spearman rank-order' :
                           'Kendall\'s tau';

    let interpretation = `Table 3 presents the ${correlationType.toLowerCase()} correlation matrix for ${matrix.variables.length} variables from a dataset of ${data.length} observations. `;
    interpretation += `The correlation analysis examined the relationships between the selected variables using ${correlationType.toLowerCase()} correlations.\n\n`;

    // Analyze significant correlations
    const significantCorrelations: Array<{var1: string, var2: string, r: number, p: number}> = [];
    const strongCorrelations: Array<{var1: string, var2: string, r: number, p: number}> = [];
    
    for (let i = 0; i < matrix.variables.length; i++) {
      for (let j = i + 1; j < matrix.variables.length; j++) {
        const correlation = matrix.correlations[i][j];
        const pValue = matrix.pValues[i][j];
        
        if (pValue < config.significanceLevel) {
          significantCorrelations.push({
            var1: matrix.variables[i],
            var2: matrix.variables[j],
            r: correlation,
            p: pValue
          });
        }
        
        if (Math.abs(correlation) >= 0.7) {
          strongCorrelations.push({
            var1: matrix.variables[i],
            var2: matrix.variables[j],
            r: correlation,
            p: pValue
          });
        }
      }
    }

    // Report significant correlations
    if (significantCorrelations.length > 0) {
      interpretation += `**Significant Correlations:** ${significantCorrelations.length} significant correlation${significantCorrelations.length > 1 ? 's were' : ' was'} identified at the α = ${config.significanceLevel} level. `;
      
      significantCorrelations.slice(0, 3).forEach((corr, index) => {
        const strength = Math.abs(corr.r) >= 0.7 ? 'strong' : Math.abs(corr.r) >= 0.5 ? 'moderate' : 'weak';
        const direction = corr.r > 0 ? 'positive' : 'negative';
        const pText = corr.p < 0.001 ? 'p < .001' : `p = ${corr.p.toFixed(4)}`;
        
        interpretation += `The correlation between ${corr.var1} and ${corr.var2} was ${strength} and ${direction} (r = ${corr.r.toFixed(2)}, ${pText}). `;
      });
      
      if (significantCorrelations.length > 3) {
        interpretation += `Additional significant correlations are detailed in the matrix above. `;
      }
    } else {
      interpretation += `**No Significant Correlations:** No statistically significant correlations were found at the α = ${config.significanceLevel} level. `;
    }

    // Report strong correlations regardless of significance
    if (strongCorrelations.length > 0) {
      interpretation += `\n\n**Strong Correlations:** ${strongCorrelations.length} correlation${strongCorrelations.length > 1 ? 's showed' : ' showed'} strong magnitude (|r| ≥ .70), indicating substantial shared variance between variables. `;
    }

    // Add descriptive statistics summary if included
    if (config.includeDescriptives && descriptives.length > 0) {
      interpretation += `\n\n**Descriptive Statistics:** `;
      const meanRange = [Math.min(...descriptives.map(d => d.mean)), Math.max(...descriptives.map(d => d.mean))];
      const sdRange = [Math.min(...descriptives.map(d => d.sd)), Math.max(...descriptives.map(d => d.sd))];
      
      interpretation += `Variable means ranged from ${meanRange[0].toFixed(2)} to ${meanRange[1].toFixed(2)}, with standard deviations ranging from ${sdRange[0].toFixed(2)} to ${sdRange[1].toFixed(2)}. `;
    }

    // Add methodological note
    interpretation += `\n\n**Interpretation Note:** ${correlationType} correlations measure the strength and direction of linear relationships between variables. `;
    interpretation += `Correlation coefficients range from -1.00 to +1.00, with values closer to ±1.00 indicating stronger relationships. `;
    interpretation += `These correlations do not imply causation and should be interpreted within the context of the study design and theoretical framework.`;

    return interpretation;
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const calculateCorrelations = () => {
    if (!data || data.length === 0) {
      setError('No data available for analysis.');
      return;
    }

    if (config.variables.length < 2) {
      setError('Please select at least 2 variables for correlation analysis.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const variables = config.variables;
      const n = variables.length;
      const correlations: number[][] = Array(n).fill(null).map(() => Array(n).fill(0));
      const pValues: number[][] = Array(n).fill(null).map(() => Array(n).fill(0));
      const sampleSizes: number[][] = Array(n).fill(null).map(() => Array(n).fill(0));
      
      // Calculate descriptive statistics if requested
      if (config.includeDescriptives) {
        const descriptives = variables.map(variable => {
          const values = data
            .map(row => parseFloat(row[variable]))
            .filter(val => !isNaN(val) && isFinite(val));
          
          const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
          const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
          const sd = Math.sqrt(variance);
          
          return {
            variable,
            n: values.length,
            mean: mean,
            sd: sd,
            min: Math.min(...values),
            max: Math.max(...values)
          };
        });
        setDescriptiveStats(descriptives);
      }

      // Calculate correlations
      for (let i = 0; i < n; i++) {
        for (let j = 0; j < n; j++) {
          if (i === j) {
            correlations[i][j] = 1.0;
            pValues[i][j] = 0;
            sampleSizes[i][j] = data.length;
          } else {
            const result = calculateCorrelation(
              variables[i],
              variables[j],
              config.correlationType
            );
            correlations[i][j] = result.correlation;
            pValues[i][j] = result.pValue;
            sampleSizes[i][j] = result.sampleSize;
          }
        }
      }

      const matrix = {
        variables,
        correlations,
        pValues,
        sampleSizes
      };
      
      setCorrelationMatrix(matrix);
      
      // Generate interpretation
      const interpretationText = generateInterpretation(matrix, descriptiveStats);
      setInterpretation(interpretationText);
      
      // Navigate to results tab
      setActiveTab(1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    } finally {
      setLoading(false);
    }
  };

  const calculateCorrelation = (var1: string, var2: string, type: string) => {
    const pairs = data
      .map(row => [parseFloat(row[var1]), parseFloat(row[var2])])
      .filter(([x, y]) => !isNaN(x) && !isNaN(y) && isFinite(x) && isFinite(y));
    
    const n = pairs.length;
    if (n < 3) {
      throw new Error(`Insufficient data for correlation between ${var1} and ${var2}`);
    }

    let correlation: number;
    
    if (type === 'pearson') {
      correlation = calculatePearsonCorrelation(pairs);
    } else if (type === 'spearman') {
      correlation = calculateSpearmanCorrelation(pairs);
    } else {
      correlation = calculateKendallCorrelation(pairs);
    }

    // Calculate p-value (simplified)
    const tStat = correlation * Math.sqrt((n - 2) / (1 - correlation * correlation));
    const pValue = 2 * (1 - tDistribution(Math.abs(tStat), n - 2));

    return {
      correlation,
      pValue: Math.max(0.001, Math.min(0.999, pValue)), // Bound p-value
      sampleSize: n
    };
  };

  const calculatePearsonCorrelation = (pairs: number[][]) => {
    const n = pairs.length;
    const sumX = pairs.reduce((sum, [x]) => sum + x, 0);
    const sumY = pairs.reduce((sum, [, y]) => sum + y, 0);
    const sumXY = pairs.reduce((sum, [x, y]) => sum + x * y, 0);
    const sumX2 = pairs.reduce((sum, [x]) => sum + x * x, 0);
    const sumY2 = pairs.reduce((sum, [, y]) => sum + y * y, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  };

  const calculateSpearmanCorrelation = (pairs: number[][]) => {
    // Convert to ranks
    const xRanks = getRanks(pairs.map(([x]) => x));
    const yRanks = getRanks(pairs.map(([, y]) => y));
    
    const rankedPairs = xRanks.map((xRank, i) => [xRank, yRanks[i]]);
    return calculatePearsonCorrelation(rankedPairs);
  };

  const calculateKendallCorrelation = (pairs: number[][]) => {
    const n = pairs.length;
    let concordant = 0;
    let discordant = 0;
    
    for (let i = 0; i < n - 1; i++) {
      for (let j = i + 1; j < n; j++) {
        const [x1, y1] = pairs[i];
        const [x2, y2] = pairs[j];
        
        const xDiff = x1 - x2;
        const yDiff = y1 - y2;
        
        if (xDiff * yDiff > 0) concordant++;
        else if (xDiff * yDiff < 0) discordant++;
      }
    }
    
    return (concordant - discordant) / (n * (n - 1) / 2);
  };

  const getRanks = (values: number[]) => {
    const sorted = values.map((val, idx) => ({ val, idx })).sort((a, b) => a.val - b.val);
    const ranks = new Array(values.length);
    
    for (let i = 0; i < sorted.length; i++) {
      ranks[sorted[i].idx] = i + 1;
    }
    
    return ranks;
  };

  const tDistribution = (t: number, df: number) => {
    // Simplified t-distribution CDF approximation
    const x = df / (df + t * t);
    return 0.5 + 0.5 * Math.sign(t) * (1 - Math.pow(x, df / 2));
  };

  const getSignificanceSymbol = (pValue: number) => {
    if (pValue < 0.001) return '***';
    if (pValue < 0.01) return '**';
    if (pValue < 0.05) return '*';
    return '';
  };

  const formatCorrelation = (correlation: number, pValue: number) => {
    const formattedCorr = correlation.toFixed(2);
    const significance = config.flagSignificant ? getSignificanceSymbol(pValue) : '';
    return `${formattedCorr}${significance}`;
  };

  const generateAPATable = () => {
    if (!correlationMatrix) return '';
    
    let table = `Table 3\n${config.title}\n\n`;
    
    // Add descriptive statistics if included
    if (config.includeDescriptives && descriptiveStats.length > 0) {
      table += 'Variable\tM\tSD\t';
      for (let i = 1; i <= correlationMatrix.variables.length; i++) {
        table += `${i}\t`;
      }
      table += '\n';
      
      descriptiveStats.forEach((stat, index) => {
        table += `${index + 1}. ${stat.variable}\t${stat.mean.toFixed(2)}\t${stat.sd.toFixed(2)}\t`;
        for (let j = 0; j < correlationMatrix.variables.length; j++) {
          if (j <= index) {
            const corr = correlationMatrix.correlations[index][j];
            const pVal = correlationMatrix.pValues[index][j];
            table += j === index ? '—\t' : `${formatCorrelation(corr, pVal)}\t`;
          } else {
            table += '\t';
          }
        }
        table += '\n';
      });
    } else {
      // Simple correlation matrix
      table += 'Variable\t';
      for (let i = 1; i <= correlationMatrix.variables.length; i++) {
        table += `${i}\t`;
      }
      table += '\n';
      
      correlationMatrix.variables.forEach((variable, index) => {
        table += `${index + 1}. ${variable}\t`;
        for (let j = 0; j < correlationMatrix.variables.length; j++) {
          if (j <= index) {
            const corr = correlationMatrix.correlations[index][j];
            const pVal = correlationMatrix.pValues[index][j];
            table += j === index ? '—\t' : `${formatCorrelation(corr, pVal)}\t`;
          } else {
            table += '\t';
          }
        }
        table += '\n';
      });
    }
    
    // Add notes
    table += '\nNote. ';
    if (config.correlationType === 'pearson') {
      table += 'Pearson product-moment correlations are displayed. ';
    } else if (config.correlationType === 'spearman') {
      table += 'Spearman rank-order correlations are displayed. ';
    } else {
      table += 'Kendall\'s tau correlations are displayed. ';
    }
    
    if (config.flagSignificant) {
      table += '*p < .05. **p < .01. ***p < .001.';
    }
    
    return table;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const clearAll = () => {
    setConfig({
      title: 'Correlation Matrix',
      variables: [],
      correlationType: 'pearson',
      significanceLevel: 0.05,
      includeCI: false,
      includeSampleSizes: true,
      includeDescriptives: true,
      flagSignificant: true,
      apa7Format: true
    });
    setCorrelationMatrix(null);
    setDescriptiveStats([]);
    setInterpretation(null);
    setError(null);
    setActiveTab(0);
  };

  // Check if user can access this feature
  if (!canAccessProFeatures) {
    return (
      <PublicationReadyGate 
        featureName="Table 3 Generator & Correlation Matrix"
        description="Generate publication-ready correlation matrices with APA formatting, descriptive statistics, and significance testing."
        features={[
          "Pearson, Spearman, and Kendall's tau correlations",
          "APA 7th edition formatting",
          "Descriptive statistics integration",
          "Significance flagging and confidence intervals",
          "Customizable table layouts and export options",
          "Academic write-up generation"
        ]}
      />
    );
  }

  return (
    <Box sx={{ width: '100%', bgcolor: 'background.default', minHeight: '100vh' }}>
      <Paper elevation={1} sx={{ m: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
            <Tab 
              label="Configuration" 
              icon={<TableChartIcon />} 
              iconPosition="start"
            />
            <Tab 
              label="Results" 
              icon={<CalculateIcon />} 
              iconPosition="start"
              disabled={!correlationMatrix}
            />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          <Typography variant="h5" gutterBottom>
            Table 3 Generator & Correlation Matrix
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Generate publication-ready correlation matrices with APA formatting, descriptive statistics, and significance testing.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Table Configuration
                  </Typography>
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="dataset-select-label">Dataset</InputLabel>
                    <Select
                      labelId="dataset-select-label"
                      id="dataset-select"
                      value={selectedDatasetId}
                      label="Dataset"
                      onChange={handleDatasetChange}
                      disabled={datasets.length === 0}
                    >
                      {datasets.length === 0 ? (
                        <MenuItem value="" disabled>
                          No datasets available
                        </MenuItem>
                      ) : (
                        datasets.map(dataset => (
                          <MenuItem key={dataset.id} value={dataset.id}>
                            {dataset.name} ({dataset.data.length} rows)
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    label="Table Title"
                    value={config.title}
                    onChange={(e) => handleConfigChange('title', e.target.value)}
                    margin="normal"
                  />
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Numeric Variables</InputLabel>
                    <Select
                      multiple
                      value={config.variables}
                      onChange={handleVariableSelection}
                      label="Numeric Variables"
                      disabled={availableVariables.length === 0 || !selectedDataset}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {(selected as string[]).map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      {availableVariables.length === 0 ? (
                        <MenuItem value="" disabled>
                          {!selectedDataset ? 'Please select a dataset first' : 'No numeric variables available in selected dataset'}
                        </MenuItem>
                      ) : (
                        availableVariables.map((variable) => (
                          <MenuItem key={variable} value={variable}>
                            {variable}
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Correlation Type</InputLabel>
                    <Select
                      value={config.correlationType}
                      onChange={(e) => handleConfigChange('correlationType', e.target.value)}
                      label="Correlation Type"
                    >
                      <MenuItem value="pearson">Pearson Product-Moment</MenuItem>
                      <MenuItem value="spearman">Spearman Rank-Order</MenuItem>
                      <MenuItem value="kendall">Kendall's Tau</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    label="Significance Level (α)"
                    type="number"
                    value={config.significanceLevel}
                    onChange={(e) => handleConfigChange('significanceLevel', parseFloat(e.target.value))}
                    margin="normal"
                    inputProps={{ min: 0.001, max: 0.1, step: 0.001 }}
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Display Options
                  </Typography>
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.includeDescriptives}
                        onChange={(e) => handleConfigChange('includeDescriptives', e.target.checked)}
                      />
                    }
                    label="Include Descriptive Statistics"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.includeSampleSizes}
                        onChange={(e) => handleConfigChange('includeSampleSizes', e.target.checked)}
                      />
                    }
                    label="Include Sample Sizes"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.includeCI}
                        onChange={(e) => handleConfigChange('includeCI', e.target.checked)}
                      />
                    }
                    label="Include Confidence Intervals"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.flagSignificant}
                        onChange={(e) => handleConfigChange('flagSignificant', e.target.checked)}
                      />
                    }
                    label="Flag Significant Correlations"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.apa7Format}
                        onChange={(e) => handleConfigChange('apa7Format', e.target.checked)}
                      />
                    }
                    label="APA 7th Edition Format"
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              onClick={calculateCorrelations}
              disabled={loading || config.variables.length < 2 || !selectedDataset}
              startIcon={loading ? <CircularProgress size={20} /> : <CalculateIcon />}
            >
              {loading ? 'Calculating...' : 'Generate Table 3'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={clearAll}
              startIcon={<ClearIcon />}
            >
              Clear All
            </Button>
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {correlationMatrix && (
            <>
              <Typography variant="h5" gutterBottom>
                Correlation Matrix Results
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {config.title}
                      </Typography>
                      
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Variable</TableCell>
                              {config.includeDescriptives && (
                                <>
                                  <TableCell align="center">M</TableCell>
                                  <TableCell align="center">SD</TableCell>
                                </>
                              )}
                              {correlationMatrix.variables.map((_, index) => (
                                <TableCell key={index} align="center">
                                  {index + 1}
                                </TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {correlationMatrix.variables.map((variable, rowIndex) => (
                              <TableRow key={rowIndex}>
                                <TableCell>
                                  {rowIndex + 1}. {variable}
                                </TableCell>
                                {config.includeDescriptives && descriptiveStats[rowIndex] && (
                                  <>
                                    <TableCell align="center">
                                      {descriptiveStats[rowIndex].mean.toFixed(2)}
                                    </TableCell>
                                    <TableCell align="center">
                                      {descriptiveStats[rowIndex].sd.toFixed(2)}
                                    </TableCell>
                                  </>
                                )}
                                {correlationMatrix.variables.map((_, colIndex) => (
                                  <TableCell key={colIndex} align="center">
                                    {colIndex <= rowIndex ? (
                                      colIndex === rowIndex ? (
                                        '—'
                                      ) : (
                                        formatCorrelation(
                                          correlationMatrix.correlations[rowIndex][colIndex],
                                          correlationMatrix.pValues[rowIndex][colIndex]
                                        )
                                      )
                                    ) : (
                                      ''
                                    )}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                      
                      <Typography variant="caption" sx={{ mt: 2, display: 'block' }}>
                        <em>Note.</em> {config.correlationType === 'pearson' ? 'Pearson product-moment' : 
                                      config.correlationType === 'spearman' ? 'Spearman rank-order' : 
                                      'Kendall\'s tau'} correlations are displayed.
                        {config.flagSignificant && ' *p < .05. **p < .01. ***p < .001.'}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                

              </Grid>
              
              {/* Interpretation Section */}
              {interpretation && (
                <Paper elevation={2} sx={{ p: 2, mb: 3, mt: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Statistical Interpretation
                  </Typography>
                  <Typography variant="body1" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
                    {interpretation}
                  </Typography>
                </Paper>
              )}
              
              {/* Add to Results Manager Button */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <AddToResultsButton
                  resultData={{
                    title: `${config.title} (${selectedDataset?.name})`,
                    type: 'correlation' as const,
                    component: 'Table3Generator',
                    data: {
                      dataset: selectedDataset?.name || '',
                      variables: config.variables,
                      correlationType: config.correlationType,
                      significanceLevel: config.significanceLevel,
                      correlationMatrix: correlationMatrix,
                      descriptiveStats: descriptiveStats,
                      interpretation: interpretation,
                      timestamp: new Date().toISOString(),
                      totalSampleSize: data.length,
                      configuration: config
                    }
                  }}
                  onSuccess={() => {
                    setSnackbarMessage('Results successfully added to Results Manager!');
                    setSnackbarOpen(true);
                  }}
                  onError={(error) => {
                    setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                    setSnackbarOpen(true);
                  }}
                />
              </Box>
            </>
          )}
        </TabPanel>
      </Paper>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default Table3Generator;