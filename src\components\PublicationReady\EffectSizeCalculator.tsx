import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  SelectChangeEvent,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Tooltip
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import rehypeStringify from 'rehype-stringify';
import rehypeKatex from 'rehype-katex';
import remarkRehype from 'remark-rehype';
import { useData } from '../../context/DataContext';
import VariableSelector from '../UI/VariableSelector';
import { DataType } from '../../types';
import { calculateMean, calculateStandardDeviation } from '../../utils/stats/descriptive';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`effect-size-tabpanel-${index}`}
      aria-labelledby={`effect-size-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface EffectSizeResult {
  type: string;
  value: number;
  interpretation: string;
  confidenceInterval?: [number, number];
  description: string;
  formula: string;
  reference: string;
}

interface CalculationData {
  effectSizeType: string;
  group1Mean?: number;
  group1SD?: number;
  group1N?: number;
  group2Mean?: number;
  group2SD?: number;
  group2N?: number;
  pooledSD?: number;
  correlation?: number;
  chiSquare?: number;
  totalN?: number;
  dfNumerator?: number;
  dfDenominator?: number;
  fStatistic?: number;
  etaSquared?: number;
  partialEtaSquared?: number;
  cramersV?: number;
}

const EFFECT_SIZE_TYPES = [
  { value: 'cohens_d', label: "Cohen's d (Standardized Mean Difference)", category: 'Mean Differences' },
  { value: 'hedges_g', label: "Hedge's g (Corrected Effect Size)", category: 'Mean Differences' },
  { value: 'glass_delta', label: "Glass's Δ (Control Group SD)", category: 'Mean Differences' },
  { value: 'eta_squared', label: 'Eta Squared (η²)', category: 'ANOVA' },
  { value: 'partial_eta_squared', label: 'Partial Eta Squared (ηp²)', category: 'ANOVA' },
  { value: 'omega_squared', label: 'Omega Squared (ω²)', category: 'ANOVA' },
  { value: 'cramers_v', label: "Cramer's V", category: 'Categorical' },
  { value: 'phi_coefficient', label: 'Phi Coefficient (φ)', category: 'Categorical' },
  { value: 'r_squared', label: 'R Squared (R²)', category: 'Correlation/Regression' },
  { value: 'adjusted_r_squared', label: 'Adjusted R Squared', category: 'Correlation/Regression' },
  { value: 'cohens_f', label: "Cohen's f", category: 'ANOVA' },
  { value: 'cohens_w', label: "Cohen's w", category: 'Categorical' }
];

const EffectSizeCalculator: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<CalculationData>({
    effectSizeType: ''
  });
  const [results, setResults] = useState<EffectSizeResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [includeCI, setIncludeCI] = useState<boolean>(true);
  const [confidenceLevel, setConfidenceLevel] = useState<number>(95);

  // Function to render mathematical formulas with KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const file = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$${formula}$`);
      return String(file);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleInputChange = (field: keyof CalculationData, value: any) => {
    setCalculationData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const interpretEffectSize = (type: string, value: number): string => {
    switch (type) {
      case 'cohens_d':
      case 'hedges_g':
      case 'glass_delta':
        if (Math.abs(value) < 0.2) return 'Negligible effect';
        if (Math.abs(value) < 0.5) return 'Small effect';
        if (Math.abs(value) < 0.8) return 'Medium effect';
        return 'Large effect';
      
      case 'eta_squared':
      case 'partial_eta_squared':
      case 'omega_squared':
        if (value < 0.01) return 'Small effect';
        if (value < 0.06) return 'Medium effect';
        return 'Large effect';
      
      case 'cramers_v':
      case 'phi_coefficient':
        if (value < 0.1) return 'Negligible association';
        if (value < 0.3) return 'Small association';
        if (value < 0.5) return 'Medium association';
        return 'Large association';
      
      case 'r_squared':
      case 'adjusted_r_squared':
        if (value < 0.02) return 'Small effect';
        if (value < 0.13) return 'Medium effect';
        return 'Large effect';
      
      case 'cohens_f':
        if (value < 0.1) return 'Small effect';
        if (value < 0.25) return 'Medium effect';
        return 'Large effect';
      
      case 'cohens_w':
        if (value < 0.1) return 'Small effect';
        if (value < 0.3) return 'Medium effect';
        return 'Large effect';
      
      default:
        return 'Effect size calculated';
    }
  };

  const calculateEffectSize = () => {
    if (!calculationData.effectSizeType) {
      setError('Please select an effect size type.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const newResults: EffectSizeResult[] = [];
      const { effectSizeType } = calculationData;

      switch (effectSizeType) {
        case 'cohens_d': {
          const { group1Mean, group1SD, group1N, group2Mean, group2SD, group2N } = calculationData;
          
          if (!group1Mean || !group1SD || !group1N || !group2Mean || !group2SD || !group2N) {
            throw new Error('Please provide all required values for Cohen\'s d calculation.');
          }

          // Calculate pooled standard deviation
          const pooledSD = Math.sqrt(((group1N - 1) * Math.pow(group1SD, 2) + (group2N - 1) * Math.pow(group2SD, 2)) / (group1N + group2N - 2));
          const cohensD = (group1Mean - group2Mean) / pooledSD;

          // Calculate confidence interval (approximate)
          const se = Math.sqrt((group1N + group2N) / (group1N * group2N) + Math.pow(cohensD, 2) / (2 * (group1N + group2N)));
          const criticalValue = 1.96; // 95% CI
          const ci: [number, number] = [cohensD - criticalValue * se, cohensD + criticalValue * se];

          newResults.push({
            type: "Cohen's d",
            value: cohensD,
            interpretation: interpretEffectSize('cohens_d', cohensD),
            confidenceInterval: includeCI ? ci : undefined,
            description: "Standardized mean difference using pooled standard deviation",
            formula: "d = (M₁ - M₂) / SDpooled",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'hedges_g': {
          const { group1Mean, group1SD, group1N, group2Mean, group2SD, group2N } = calculationData;
          
          if (!group1Mean || !group1SD || !group1N || !group2Mean || !group2SD || !group2N) {
            throw new Error('Please provide all required values for Hedge\'s g calculation.');
          }

          // Calculate Cohen's d first
          const pooledSD = Math.sqrt(((group1N - 1) * Math.pow(group1SD, 2) + (group2N - 1) * Math.pow(group2SD, 2)) / (group1N + group2N - 2));
          const cohensD = (group1Mean - group2Mean) / pooledSD;
          
          // Apply small sample correction
          const df = group1N + group2N - 2;
          const correctionFactor = 1 - (3 / (4 * df - 1));
          const hedgesG = cohensD * correctionFactor;

          newResults.push({
            type: "Hedge's g",
            value: hedgesG,
            interpretation: interpretEffectSize('hedges_g', hedgesG),
            description: "Bias-corrected standardized mean difference for small samples",
            formula: "g = d × [1 - 3/(4df - 1)]",
            reference: "Hedges, L. V. (1981). Distribution theory for Glass's estimator of effect size."
          });
          break;
        }

        case 'eta_squared': {
          const { fStatistic, dfNumerator, dfDenominator } = calculationData;
          
          if (!fStatistic || !dfNumerator || !dfDenominator) {
            throw new Error('Please provide F-statistic and degrees of freedom for eta squared calculation.');
          }

          const etaSquared = (dfNumerator * fStatistic) / (dfNumerator * fStatistic + dfDenominator);

          newResults.push({
            type: 'Eta Squared (η²)',
            value: etaSquared,
            interpretation: interpretEffectSize('eta_squared', etaSquared),
            description: "Proportion of total variance explained by the factor",
            formula: "η² = SS_effect / SS_total",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'glass_delta': {
          const { group1Mean, group1SD, group1N, group2Mean, group2SD, group2N } = calculationData;
          
          if (!group1Mean || !group1SD || !group1N || !group2Mean || !group2SD || !group2N) {
            throw new Error('Please provide all required values for Glass\'s Δ calculation.');
          }

          // Use control group (group 2) standard deviation
          const glassDelta = (group1Mean - group2Mean) / group2SD;

          newResults.push({
            type: "Glass's Δ",
            value: glassDelta,
            interpretation: interpretEffectSize('glass_delta', glassDelta),
            description: "Standardized mean difference using control group standard deviation",
            formula: "Δ = (M₁ - M₂) / SD₂",
            reference: "Glass, G. V. (1976). Primary, secondary, and meta-analysis of research."
          });
          break;
        }

        case 'partial_eta_squared': {
          const { fStatistic, dfNumerator, dfDenominator } = calculationData;
          
          if (!fStatistic || !dfNumerator || !dfDenominator) {
            throw new Error('Please provide F-statistic and degrees of freedom for partial eta squared calculation.');
          }

          const partialEtaSquared = (dfNumerator * fStatistic) / (dfNumerator * fStatistic + dfDenominator);

          newResults.push({
            type: 'Partial Eta Squared (ηp²)',
            value: partialEtaSquared,
            interpretation: interpretEffectSize('partial_eta_squared', partialEtaSquared),
            description: "Proportion of variance in the dependent variable explained by the factor, excluding other factors",
            formula: "ηp² = SS_effect / (SS_effect + SS_error)",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'omega_squared': {
          const { fStatistic, dfNumerator, dfDenominator, totalN } = calculationData;
          
          if (!fStatistic || !dfNumerator || !dfDenominator || !totalN) {
            throw new Error('Please provide F-statistic, degrees of freedom, and total N for omega squared calculation.');
          }

          const omegaSquared = (dfNumerator * (fStatistic - 1)) / (dfNumerator * (fStatistic - 1) + totalN);

          newResults.push({
            type: 'Omega Squared (ω²)',
            value: Math.max(0, omegaSquared), // Ensure non-negative
            interpretation: interpretEffectSize('eta_squared', Math.max(0, omegaSquared)), // Use eta_squared interpretation
            description: "Unbiased estimate of population variance explained, less biased than eta squared",
            formula: "ω² = (df₁(F - 1)) / (df₁(F - 1) + N)",
            reference: "Hays, W. L. (1963). Statistics for psychologists."
          });
          break;
        }

        case 'phi_coefficient': {
          const { chiSquare, totalN } = calculationData;
          
          if (!chiSquare || !totalN) {
            throw new Error('Please provide chi-square value and total N for phi coefficient calculation.');
          }

          const phi = Math.sqrt(chiSquare / totalN);

          newResults.push({
            type: 'Phi Coefficient (φ)',
            value: phi,
            interpretation: interpretEffectSize('cramers_v', phi), // Use Cramer's V interpretation
            description: "Measure of association for 2×2 contingency tables",
            formula: "φ = √(χ² / N)",
            reference: "Pearson, K. (1900). Mathematical contributions to the theory of evolution."
          });
          break;
        }

        case 'r_squared': {
          const { correlation } = calculationData;
          
          if (correlation === undefined || correlation === null) {
            throw new Error('Please provide correlation coefficient for R squared calculation.');
          }

          const rSquared = Math.pow(correlation, 2);

          newResults.push({
            type: 'R Squared (R²)',
            value: rSquared,
            interpretation: interpretEffectSize('r_squared', rSquared),
            description: "Proportion of variance in the dependent variable explained by the independent variable(s)",
            formula: "R² = r²",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'adjusted_r_squared': {
          const { correlation, totalN, dfNumerator } = calculationData;
          
          if (correlation === undefined || correlation === null || !totalN || !dfNumerator) {
            throw new Error('Please provide correlation coefficient, total N, and number of predictors for adjusted R squared calculation.');
          }

          const rSquared = Math.pow(correlation, 2);
          const adjustedRSquared = 1 - ((1 - rSquared) * (totalN - 1)) / (totalN - dfNumerator - 1);

          newResults.push({
            type: 'Adjusted R Squared',
            value: Math.max(0, adjustedRSquared), // Ensure non-negative
            interpretation: interpretEffectSize('r_squared', Math.max(0, adjustedRSquared)),
            description: "R squared adjusted for the number of predictors in the model",
            formula: "R²adj = 1 - ((1 - R²)(N - 1)) / (N - k - 1)",
            reference: "Wherry, R. J. (1931). A new formula for predicting the shrinkage of the coefficient of multiple correlation."
          });
          break;
        }

        case 'cohens_f': {
          const { fStatistic, dfNumerator, dfDenominator } = calculationData;
          
          if (!fStatistic || !dfNumerator || !dfDenominator) {
            throw new Error('Please provide F-statistic and degrees of freedom for Cohen\'s f calculation.');
          }

          const etaSquared = (dfNumerator * fStatistic) / (dfNumerator * fStatistic + dfDenominator);
          const cohensF = Math.sqrt(etaSquared / (1 - etaSquared));

          newResults.push({
            type: "Cohen's f",
            value: cohensF,
            interpretation: cohensF < 0.1 ? 'Small effect' : cohensF < 0.25 ? 'Medium effect' : 'Large effect',
            description: "Effect size measure for ANOVA, derived from eta squared",
            formula: "f = √(η² / (1 - η²))",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'cohens_w': {
          const { chiSquare, totalN } = calculationData;
          
          if (!chiSquare || !totalN) {
            throw new Error('Please provide chi-square value and total N for Cohen\'s w calculation.');
          }

          const cohensW = Math.sqrt(chiSquare / totalN);

          newResults.push({
            type: "Cohen's w",
            value: cohensW,
            interpretation: cohensW < 0.1 ? 'Small effect' : cohensW < 0.3 ? 'Medium effect' : 'Large effect',
            description: "Effect size measure for chi-square tests and contingency tables",
            formula: "w = √(χ² / N)",
            reference: "Cohen, J. (1988). Statistical Power Analysis for the Behavioral Sciences."
          });
          break;
        }

        case 'cramers_v': {
          const { chiSquare, totalN, dfNumerator } = calculationData;
          
          if (!chiSquare || !totalN || !dfNumerator) {
            throw new Error('Please provide chi-square value, total N, and degrees of freedom for Cramer\'s V calculation.');
          }

          const cramersV = Math.sqrt(chiSquare / (totalN * dfNumerator));

          newResults.push({
            type: "Cramer's V",
            value: cramersV,
            interpretation: interpretEffectSize('cramers_v', cramersV),
            description: "Measure of association strength for categorical variables",
            formula: "V = √(χ² / (N × df))",
            reference: "Cramér, H. (1946). Mathematical Methods of Statistics."
          });
          break;
        }

        default:
          throw new Error('Selected effect size type is not yet implemented.');
      }

      setResults(newResults);
      // Navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setCalculationData({ effectSizeType: '' });
    setResults([]);
    setError(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (results.length === 0) return '';

    let report = 'Effect Size Analysis Report\n';
    report += '================================\n\n';

    results.forEach((result, index) => {
      report += `${index + 1}. ${result.type}\n`;
      report += `   Value: ${result.value.toFixed(2)}\n`;
      report += `   Interpretation: ${result.interpretation}\n`;
      if (result.confidenceInterval) {
        report += `   95% CI: [${result.confidenceInterval[0].toFixed(2)}, ${result.confidenceInterval[1].toFixed(2)}]\n`;
      }
      report += `   Description: ${result.description}\n`;
      report += `   Formula: ${result.formula}\n`;
      report += `   Reference: ${result.reference}\n\n`;
    });

    return report;
  };

  const renderInputFields = () => {
    const { effectSizeType } = calculationData;

    switch (effectSizeType) {
      case 'cohens_d':
      case 'hedges_g':
      case 'glass_delta':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Group 1</Typography>
              <TextField
                fullWidth
                label="Mean"
                type="number"
                value={calculationData.group1Mean || ''}
                onChange={(e) => handleInputChange('group1Mean', parseFloat(e.target.value))}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Standard Deviation"
                type="number"
                value={calculationData.group1SD || ''}
                onChange={(e) => handleInputChange('group1SD', parseFloat(e.target.value))}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Sample Size (N)"
                type="number"
                value={calculationData.group1N || ''}
                onChange={(e) => handleInputChange('group1N', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Group 2</Typography>
              <TextField
                fullWidth
                label="Mean"
                type="number"
                value={calculationData.group2Mean || ''}
                onChange={(e) => handleInputChange('group2Mean', parseFloat(e.target.value))}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Standard Deviation"
                type="number"
                value={calculationData.group2SD || ''}
                onChange={(e) => handleInputChange('group2SD', parseFloat(e.target.value))}
                margin="normal"
              />
              <TextField
                fullWidth
                label="Sample Size (N)"
                type="number"
                value={calculationData.group2N || ''}
                onChange={(e) => handleInputChange('group2N', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
          </Grid>
        );

      case 'eta_squared':
      case 'partial_eta_squared':
      case 'cohens_f':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="F-statistic"
                type="number"
                value={calculationData.fStatistic || ''}
                onChange={(e) => handleInputChange('fStatistic', parseFloat(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="df Numerator"
                type="number"
                value={calculationData.dfNumerator || ''}
                onChange={(e) => handleInputChange('dfNumerator', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="df Denominator"
                type="number"
                value={calculationData.dfDenominator || ''}
                onChange={(e) => handleInputChange('dfDenominator', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
          </Grid>
        );

      case 'omega_squared':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="F-statistic"
                type="number"
                value={calculationData.fStatistic || ''}
                onChange={(e) => handleInputChange('fStatistic', parseFloat(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="df Numerator"
                type="number"
                value={calculationData.dfNumerator || ''}
                onChange={(e) => handleInputChange('dfNumerator', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="df Denominator"
                type="number"
                value={calculationData.dfDenominator || ''}
                onChange={(e) => handleInputChange('dfDenominator', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Total Sample Size (N)"
                type="number"
                value={calculationData.totalN || ''}
                onChange={(e) => handleInputChange('totalN', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
          </Grid>
        );

      case 'cramers_v':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Chi-square (χ²)"
                type="number"
                value={calculationData.chiSquare || ''}
                onChange={(e) => handleInputChange('chiSquare', parseFloat(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Total Sample Size (N)"
                type="number"
                value={calculationData.totalN || ''}
                onChange={(e) => handleInputChange('totalN', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Degrees of Freedom"
                type="number"
                value={calculationData.dfNumerator || ''}
                onChange={(e) => handleInputChange('dfNumerator', parseInt(e.target.value))}
                margin="normal"
                helperText="min(rows-1, cols-1)"
              />
            </Grid>
          </Grid>
        );

      case 'phi_coefficient':
      case 'cohens_w':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Chi-square (χ²)"
                type="number"
                value={calculationData.chiSquare || ''}
                onChange={(e) => handleInputChange('chiSquare', parseFloat(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Total Sample Size (N)"
                type="number"
                value={calculationData.totalN || ''}
                onChange={(e) => handleInputChange('totalN', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
          </Grid>
        );

      case 'r_squared':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Correlation Coefficient (r)"
                type="number"
                value={calculationData.correlation || ''}
                onChange={(e) => handleInputChange('correlation', parseFloat(e.target.value))}
                margin="normal"
                helperText="Enter the Pearson correlation coefficient"
                inputProps={{ min: -1, max: 1, step: 0.001 }}
              />
            </Grid>
          </Grid>
        );

      case 'adjusted_r_squared':
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Correlation Coefficient (r)"
                type="number"
                value={calculationData.correlation || ''}
                onChange={(e) => handleInputChange('correlation', parseFloat(e.target.value))}
                margin="normal"
                inputProps={{ min: -1, max: 1, step: 0.001 }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Total Sample Size (N)"
                type="number"
                value={calculationData.totalN || ''}
                onChange={(e) => handleInputChange('totalN', parseInt(e.target.value))}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Number of Predictors (k)"
                type="number"
                value={calculationData.dfNumerator || ''}
                onChange={(e) => handleInputChange('dfNumerator', parseInt(e.target.value))}
                margin="normal"
                helperText="Number of independent variables"
              />
            </Grid>
          </Grid>
        );

      default:
        return (
          <Alert severity="info">
            Please select an effect size type to see the input fields.
          </Alert>
        );
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CalculateIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Effect Size Calculator & Reporter
        </Typography>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Calculate and interpret effect sizes for your statistical analyses with confidence intervals and publication-ready interpretations.
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<AssessmentIcon />} />
          <Tab label="Interpretation Guide" icon={<InfoIcon />} />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Effect Size Type</InputLabel>
                <Select
                  value={calculationData.effectSizeType}
                  onChange={(e) => handleInputChange('effectSizeType', e.target.value)}
                  label="Effect Size Type"
                >
                  {EFFECT_SIZE_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body1">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.category}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              {renderInputFields()}
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={includeCI}
                      onChange={(e) => setIncludeCI(e.target.checked)}
                    />
                  }
                  label="Include Confidence Intervals"
                />
                <TextField
                  label="Confidence Level (%)"
                  type="number"
                  value={confidenceLevel}
                  onChange={(e) => setConfidenceLevel(parseInt(e.target.value))}
                  sx={{ width: 150 }}
                  inputProps={{ min: 90, max: 99 }}
                  disabled={!includeCI}
                />
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  onClick={calculateEffectSize}
                  disabled={loading || !calculationData.effectSizeType}
                  startIcon={loading ? <CircularProgress size={20} /> : <CalculateIcon />}
                >
                  {loading ? 'Calculating...' : 'Calculate Effect Size'}
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearAll}
                  startIcon={<ClearIcon />}
                >
                  Clear All
                </Button>
              </Box>
            </Grid>

            {error && (
              <Grid item xs={12}>
                <Alert severity="error">{error}</Alert>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {results.length > 0 ? (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Effect Size Results</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<CopyIcon />}
                  onClick={() => copyToClipboard(generateReport())}
                >
                  Copy Report
                </Button>
              </Box>

              {results.map((result, index) => (
                <Card key={index} sx={{ mb: 2, bgcolor: theme.palette.action.hover }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" color="primary">
                        {result.type}
                      </Typography>
                      <Chip
                        label={result.interpretation}
                        color={Math.abs(result.value) > 0.5 ? 'success' : Math.abs(result.value) > 0.2 ? 'warning' : 'default'}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="h4" sx={{ mb: 1, fontWeight: 'bold' }}>
                      {result.value.toFixed(2)}
                    </Typography>
                    
                    {result.confidenceInterval && (
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        95% CI: [{result.confidenceInterval[0].toFixed(2)}, {result.confidenceInterval[1].toFixed(2)}]
                      </Typography>
                    )}
                    
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {result.description}
                    </Typography>
                    
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5, display: 'block' }}>
                        Formula:
                      </Typography>
                      <Box 
                        dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                        sx={{ 
                          '& .katex': { fontSize: '0.875rem' },
                          '& .katex-display': { margin: '0.5em 0' }
                        }}
                      />
                    </Box>
                    
                    <Divider sx={{ my: 1 }} />
                    
                    <Typography variant="caption" color="text.secondary">
                      Reference: {result.reference}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            <Alert severity="info">
              No results yet. Use the Calculator tab to compute effect sizes.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            Effect Size Interpretation Guidelines
          </Typography>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Cohen's d / Hedge's g / Glass's Δ</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Standardized mean differences for comparing two groups:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="|d| < 0.2: Negligible effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.2 ≤ |d| < 0.5: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.5 ≤ |d| < 0.8: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="|d| ≥ 0.8: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Eta Squared (η²) / Partial Eta Squared (ηp²)</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Proportion of variance explained in ANOVA:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="η² < 0.01: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.01 ≤ η² < 0.06: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="η² ≥ 0.06: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Omega Squared (ω²)</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Unbiased estimate of variance explained in ANOVA:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="ω² < 0.01: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.01 ≤ ω² < 0.06: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="ω² ≥ 0.06: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Cohen's f</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Effect size for ANOVA and multiple regression:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="f < 0.1: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.1 ≤ f < 0.25: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="f ≥ 0.25: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Cramer's V</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Association strength for categorical variables:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="V < 0.1: Negligible association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.1 ≤ V < 0.3: Small association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.3 ≤ V < 0.5: Medium association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="V ≥ 0.5: Large association" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Phi Coefficient (φ)</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Association strength for 2×2 contingency tables:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="|φ| < 0.1: Negligible association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.1 ≤ |φ| < 0.3: Small association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.3 ≤ |φ| < 0.5: Medium association" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="|φ| ≥ 0.5: Large association" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Cohen's w</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Effect size for chi-square tests:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="w < 0.1: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.1 ≤ w < 0.3: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="w ≥ 0.3: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">R Squared (R²) / Adjusted R Squared</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Proportion of variance explained in regression:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="R² < 0.02: Small effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="0.02 ≤ R² < 0.13: Medium effect" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="R² ≥ 0.13: Large effect" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default EffectSizeCalculator;