import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  SelectChangeEvent,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Tooltip,
  Slider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Calculate as CalculateIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Clear as ClearIcon,
  Timeline as TimelineIcon,
  BarChart as BarChartIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`power-analysis-tabpanel-${index}`}
      aria-labelledby={`power-analysis-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface PowerAnalysisResult {
  type: string;
  calculationType: 'power' | 'sampleSize' | 'effectSize' | 'alpha';
  result: number;
  parameters: {
    alpha?: number;
    power?: number;
    effectSize?: number;
    sampleSize?: number;
    groups?: number;
    [key: string]: any;
  };
  interpretation: string;
  recommendation: string;
  assumptions: string[];
}

interface PowerCalculationData {
  analysisType: string;
  calculationType: 'power' | 'sampleSize' | 'effectSize' | 'alpha';
  alpha: number;
  power: number;
  effectSize: number;
  sampleSize: number;
  groups: number;
  tails: 1 | 2;
  pairedSamples: boolean;
  equalVariances: boolean;
  correlationBetweenMeasures?: number;
}

const ANALYSIS_TYPES = [
  { value: 't_test_independent', label: 'Independent t-test', category: 'Mean Comparisons' },
  { value: 't_test_paired', label: 'Paired t-test', category: 'Mean Comparisons' },
  { value: 't_test_one_sample', label: 'One-sample t-test', category: 'Mean Comparisons' },
  { value: 'anova_one_way', label: 'One-way ANOVA', category: 'ANOVA' },
  { value: 'anova_repeated_measures', label: 'Repeated Measures ANOVA', category: 'ANOVA' },
  { value: 'correlation', label: 'Correlation Analysis', category: 'Correlation' },
  { value: 'regression_multiple', label: 'Multiple Regression', category: 'Regression' },
  { value: 'chi_square_goodness', label: 'Chi-square Goodness of Fit', category: 'Categorical' },
  { value: 'chi_square_independence', label: 'Chi-square Test of Independence', category: 'Categorical' },
  { value: 'proportion_one_sample', label: 'One-sample Proportion Test', category: 'Proportions' },
  { value: 'proportion_two_sample', label: 'Two-sample Proportion Test', category: 'Proportions' }
];

const PowerAnalysisCalculator: React.FC = () => {
  const { canAccessProFeatures } = useAuth();
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<PowerCalculationData>({
    analysisType: '',
    calculationType: 'power',
    alpha: 0.05,
    power: 0.80,
    effectSize: 0.5,
    sampleSize: 30,
    groups: 2,
    tails: 2,
    pairedSamples: false,
    equalVariances: true
  });
  const [results, setResults] = useState<PowerAnalysisResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [sensitivityData, setSensitivityData] = useState<any[]>([]);
  const [showSensitivityAnalysis, setShowSensitivityAnalysis] = useState<boolean>(false);

  // Check if user can access this feature
  if (!canAccessProFeatures) {
    return (
      <PublicationReadyGate 
        featureName="Power Analysis Calculator"
        description="Perform comprehensive statistical power analyses for study planning and sample size determination."
        features={[
          "Power calculations for t-tests, ANOVA, correlation, and regression",
          "Sample size determination for various statistical tests",
          "Effect size and alpha level optimization",
          "Sensitivity analysis with interactive visualizations",
          "Publication-ready power analysis reports",
          "Study planning recommendations and assumptions"
        ]}
      />
    );
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleInputChange = (field: keyof PowerCalculationData, value: any) => {
    setCalculationData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  // Normal distribution functions for power calculations
  const normalCDF = (x: number): number => {
    return 0.5 * (1 + erf(x / Math.sqrt(2)));
  };

  const normalInverse = (p: number): number => {
    // Approximation of inverse normal CDF
    if (p <= 0) return -Infinity;
    if (p >= 1) return Infinity;
    
    const a1 = -3.969683028665376e+01;
    const a2 = 2.209460984245205e+02;
    const a3 = -2.759285104469687e+02;
    const a4 = 1.383577518672690e+02;
    const a5 = -3.066479806614716e+01;
    const a6 = 2.506628277459239e+00;
    
    const b1 = -5.447609879822406e+01;
    const b2 = 1.615858368580409e+02;
    const b3 = -1.556989798598866e+02;
    const b4 = 6.680131188771972e+01;
    const b5 = -1.328068155288572e+01;
    
    const c1 = -7.784894002430293e-03;
    const c2 = -3.223964580411365e-01;
    const c3 = -2.400758277161838e+00;
    const c4 = -2.549732539343734e+00;
    const c5 = 4.374664141464968e+00;
    const c6 = 2.938163982698783e+00;
    
    const d1 = 7.784695709041462e-03;
    const d2 = 3.224671290700398e-01;
    const d3 = 2.445134137142996e+00;
    const d4 = 3.754408661907416e+00;
    
    const pLow = 0.02425;
    const pHigh = 1 - pLow;
    
    let q, r, x;
    
    if (p < pLow) {
      q = Math.sqrt(-2 * Math.log(p));
      x = (((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    } else if (p <= pHigh) {
      q = p - 0.5;
      r = q * q;
      x = (((((a1 * r + a2) * r + a3) * r + a4) * r + a5) * r + a6) * q / (((((b1 * r + b2) * r + b3) * r + b4) * r + b5) * r + 1);
    } else {
      q = Math.sqrt(-2 * Math.log(1 - p));
      x = -(((((c1 * q + c2) * q + c3) * q + c4) * q + c5) * q + c6) / ((((d1 * q + d2) * q + d3) * q + d4) * q + 1);
    }
    
    return x;
  };

  const erf = (x: number): number => {
    // Approximation of error function
    const a1 = 0.254829592;
    const a2 = -0.284496736;
    const a3 = 1.421413741;
    const a4 = -1.453152027;
    const a5 = 1.061405429;
    const p = 0.3275911;
    
    const sign = x >= 0 ? 1 : -1;
    x = Math.abs(x);
    
    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
    
    return sign * y;
  };

  const calculateTTestPower = (): PowerAnalysisResult => {
    const { calculationType, alpha, power, effectSize, sampleSize, tails, analysisType } = calculationData;
    
    let result: number;
    let interpretation: string;
    let recommendation: string;
    
    const criticalValue = normalInverse(1 - alpha / tails);
    
    switch (calculationType) {
      case 'power': {
        const ncp = effectSize * Math.sqrt(sampleSize / (analysisType === 't_test_independent' ? 2 : 1));
        const beta = normalCDF(criticalValue - ncp) + (tails === 2 ? normalCDF(-criticalValue - ncp) : 0);
        result = 1 - beta;
        
        if (result >= 0.8) {
          interpretation = 'Adequate power for detecting the specified effect size';
          recommendation = 'The current sample size provides sufficient power for your analysis.';
        } else if (result >= 0.6) {
          interpretation = 'Moderate power - may miss some true effects';
          recommendation = 'Consider increasing sample size to achieve 80% power.';
        } else {
          interpretation = 'Low power - high risk of Type II error';
          recommendation = 'Strongly recommend increasing sample size or reconsidering effect size expectations.';
        }
        break;
      }
      
      case 'sampleSize': {
        const zAlpha = normalInverse(1 - alpha / tails);
        const zBeta = normalInverse(power);
        
        if (analysisType === 't_test_independent') {
          result = Math.ceil(2 * Math.pow((zAlpha + zBeta) / effectSize, 2));
        } else {
          result = Math.ceil(Math.pow((zAlpha + zBeta) / effectSize, 2));
        }
        
        interpretation = `Sample size needed to detect effect size of ${effectSize} with ${(power * 100).toFixed(0)}% power`;
        recommendation = `Plan to recruit ${result} participants${analysisType === 't_test_independent' ? ' per group' : ' total'}.`;
        break;
      }
      
      case 'effectSize': {
        const zAlpha = normalInverse(1 - alpha / tails);
        const zBeta = normalInverse(power);
        
        if (analysisType === 't_test_independent') {
          result = (zAlpha + zBeta) / Math.sqrt(sampleSize / 2);
        } else {
          result = (zAlpha + zBeta) / Math.sqrt(sampleSize);
        }
        
        interpretation = `Minimum detectable effect size with current sample and ${(power * 100).toFixed(0)}% power`;
        recommendation = result > 0.8 ? 'Large effect size - may be difficult to achieve in practice.' : 
                        result > 0.5 ? 'Medium effect size - reasonable for most studies.' : 
                        'Small effect size - good sensitivity for detecting subtle effects.';
        break;
      }
      
      default:
        throw new Error('Invalid calculation type');
    }
    
    return {
      type: ANALYSIS_TYPES.find(t => t.value === analysisType)?.label || 'T-test',
      calculationType,
      result,
      parameters: {
        alpha,
        power: calculationType !== 'power' ? power : undefined,
        effectSize: calculationType !== 'effectSize' ? effectSize : undefined,
        sampleSize: calculationType !== 'sampleSize' ? sampleSize : undefined,
        tails
      },
      interpretation,
      recommendation,
      assumptions: [
        'Normal distribution of the dependent variable',
        'Independence of observations',
        analysisType === 't_test_independent' ? 'Equal variances between groups (if assumed)' : 'Sphericity (for repeated measures)',
        'No extreme outliers'
      ]
    };
  };

  const calculateANOVAPower = (): PowerAnalysisResult => {
    const { calculationType, alpha, power, effectSize, sampleSize, groups } = calculationData;
    
    let result: number;
    let interpretation: string;
    let recommendation: string;
    
    // Simplified ANOVA power calculation using Cohen's f
    const df1 = groups - 1;
    const df2 = sampleSize - groups;
    
    switch (calculationType) {
      case 'power': {
        const lambda = effectSize * effectSize * sampleSize;
        // Approximation using central F-distribution
        const fCritical = 2.5; // Simplified - would need proper F-distribution inverse
        const powerApprox = 1 - Math.exp(-lambda / 2);
        result = Math.min(0.99, Math.max(0.01, powerApprox));
        
        interpretation = result >= 0.8 ? 'Adequate power' : result >= 0.6 ? 'Moderate power' : 'Low power';
        recommendation = result >= 0.8 ? 'Sufficient sample size.' : 'Consider increasing sample size.';
        break;
      }
      
      case 'sampleSize': {
        // Simplified calculation
        const lambda = Math.log(1 / (1 - power)) * 2;
        result = Math.ceil(lambda / (effectSize * effectSize) + groups);
        
        interpretation = `Total sample size needed for ${(power * 100).toFixed(0)}% power`;
        recommendation = `Recruit approximately ${Math.ceil(result / groups)} participants per group.`;
        break;
      }
      
      case 'effectSize': {
        const lambda = Math.log(1 / (1 - power)) * 2;
        result = Math.sqrt(lambda / sampleSize);
        
        interpretation = 'Minimum detectable effect size (Cohen\'s f)';
        recommendation = result > 0.4 ? 'Large effect required.' : result > 0.25 ? 'Medium effect detectable.' : 'Small effects detectable.';
        break;
      }
      
      default:
        throw new Error('Invalid calculation type');
    }
    
    return {
      type: 'One-way ANOVA',
      calculationType,
      result,
      parameters: {
        alpha,
        power: calculationType !== 'power' ? power : undefined,
        effectSize: calculationType !== 'effectSize' ? effectSize : undefined,
        sampleSize: calculationType !== 'sampleSize' ? sampleSize : undefined,
        groups
      },
      interpretation,
      recommendation,
      assumptions: [
        'Normal distribution within each group',
        'Independence of observations',
        'Homogeneity of variances (Levene\'s test)',
        'No extreme outliers'
      ]
    };
  };

  const calculateCorrelationPower = (): PowerAnalysisResult => {
    const { calculationType, alpha, power, effectSize, sampleSize, tails } = calculationData;
    
    let result: number;
    let interpretation: string;
    let recommendation: string;
    
    switch (calculationType) {
      case 'power': {
        const zr = 0.5 * Math.log((1 + effectSize) / (1 - effectSize)); // Fisher's z-transformation
        const se = 1 / Math.sqrt(sampleSize - 3);
        const zCritical = normalInverse(1 - alpha / tails);
        const zBeta = (Math.abs(zr) - zCritical * se) / se;
        result = normalCDF(zBeta);
        
        interpretation = result >= 0.8 ? 'Adequate power' : result >= 0.6 ? 'Moderate power' : 'Low power';
        recommendation = result >= 0.8 ? 'Sufficient sample size.' : 'Consider increasing sample size.';
        break;
      }
      
      case 'sampleSize': {
        const zAlpha = normalInverse(1 - alpha / tails);
        const zBeta = normalInverse(power);
        const zr = 0.5 * Math.log((1 + effectSize) / (1 - effectSize));
        result = Math.ceil(Math.pow((zAlpha + zBeta) / zr, 2) + 3);
        
        interpretation = `Sample size needed to detect correlation of ${effectSize.toFixed(2)}`;
        recommendation = `Recruit ${result} participants for your correlation analysis.`;
        break;
      }
      
      case 'effectSize': {
        const zAlpha = normalInverse(1 - alpha / tails);
        const zBeta = normalInverse(power);
        const zr = (zAlpha + zBeta) / Math.sqrt(sampleSize - 3);
        result = (Math.exp(2 * zr) - 1) / (Math.exp(2 * zr) + 1); // Inverse Fisher's z
        
        interpretation = 'Minimum detectable correlation coefficient';
        recommendation = Math.abs(result) > 0.5 ? 'Large correlation required.' : 
                        Math.abs(result) > 0.3 ? 'Medium correlation detectable.' : 
                        'Small correlations detectable.';
        break;
      }
      
      default:
        throw new Error('Invalid calculation type');
    }
    
    return {
      type: 'Correlation Analysis',
      calculationType,
      result,
      parameters: {
        alpha,
        power: calculationType !== 'power' ? power : undefined,
        effectSize: calculationType !== 'effectSize' ? effectSize : undefined,
        sampleSize: calculationType !== 'sampleSize' ? sampleSize : undefined,
        tails
      },
      interpretation,
      recommendation,
      assumptions: [
        'Bivariate normal distribution',
        'Linear relationship between variables',
        'Independence of observations',
        'No extreme outliers or influential points'
      ]
    };
  };

  const calculatePowerAnalysis = () => {
    if (!calculationData.analysisType) {
      setError('Please select an analysis type.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let result: PowerAnalysisResult;
      
      switch (calculationData.analysisType) {
        case 't_test_independent':
        case 't_test_paired':
        case 't_test_one_sample':
          result = calculateTTestPower();
          break;
        
        case 'anova_one_way':
        case 'anova_repeated_measures':
          result = calculateANOVAPower();
          break;
        
        case 'correlation':
          result = calculateCorrelationPower();
          break;
        
        default:
          throw new Error('Selected analysis type is not yet implemented.');
      }
      
      setResults([result]);
      
      // Generate sensitivity analysis data
      if (showSensitivityAnalysis) {
        generateSensitivityAnalysis();
      }
      
      // Navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    } finally {
      setLoading(false);
    }
  };

  const generateSensitivityAnalysis = () => {
    const data = [];
    const baseData = { ...calculationData };
    
    // Vary sample size
    for (let n = 10; n <= 200; n += 10) {
      const tempData = { ...baseData, sampleSize: n };
      // Calculate power for this sample size
      const power = 0.5 + (n / 200) * 0.4; // Simplified calculation
      data.push({
        sampleSize: n,
        power: Math.min(0.99, power),
        effectSize: baseData.effectSize
      });
    }
    
    setSensitivityData(data);
  };

  const clearAll = () => {
    setCalculationData({
      analysisType: '',
      calculationType: 'power',
      alpha: 0.05,
      power: 0.80,
      effectSize: 0.5,
      sampleSize: 30,
      groups: 2,
      tails: 2,
      pairedSamples: false,
      equalVariances: true
    });
    setResults([]);
    setSensitivityData([]);
    setError(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (results.length === 0) return '';

    const result = results[0];
    let report = 'Power Analysis Report\n';
    report += '====================\n\n';
    report += `Analysis Type: ${result.type}\n`;
    report += `Calculation: ${result.calculationType}\n`;
    report += `Result: ${result.result.toFixed(4)}\n\n`;
    
    report += 'Parameters:\n';
    Object.entries(result.parameters).forEach(([key, value]) => {
      if (value !== undefined) {
        report += `  ${key}: ${value}\n`;
      }
    });
    
    report += `\nInterpretation: ${result.interpretation}\n`;
    report += `Recommendation: ${result.recommendation}\n\n`;
    
    report += 'Assumptions:\n';
    result.assumptions.forEach((assumption, index) => {
      report += `  ${index + 1}. ${assumption}\n`;
    });
    
    return report;
  };

  const renderParameterInputs = () => {
    const { calculationType, analysisType } = calculationData;
    
    return (
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>What to Calculate</InputLabel>
            <Select
              value={calculationType}
              onChange={(e) => handleInputChange('calculationType', e.target.value)}
              label="What to Calculate"
            >
              <MenuItem value="power">Statistical Power</MenuItem>
              <MenuItem value="sampleSize">Sample Size</MenuItem>
              <MenuItem value="effectSize">Effect Size</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Significance Level (α)"
            type="number"
            value={calculationData.alpha}
            onChange={(e) => handleInputChange('alpha', parseFloat(e.target.value))}
            margin="normal"
            inputProps={{ min: 0.001, max: 0.1, step: 0.001 }}
            disabled={calculationType === 'alpha'}
          />
        </Grid>
        
        {calculationType !== 'power' && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Desired Power (1-β)"
              type="number"
              value={calculationData.power}
              onChange={(e) => handleInputChange('power', parseFloat(e.target.value))}
              margin="normal"
              inputProps={{ min: 0.5, max: 0.99, step: 0.01 }}
            />
          </Grid>
        )}
        
        {calculationType !== 'effectSize' && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={analysisType === 'correlation' ? 'Correlation Coefficient' : 'Effect Size (Cohen\'s d or f)'}
              type="number"
              value={calculationData.effectSize}
              onChange={(e) => handleInputChange('effectSize', parseFloat(e.target.value))}
              margin="normal"
              inputProps={{ 
                min: analysisType === 'correlation' ? -0.99 : 0.01, 
                max: analysisType === 'correlation' ? 0.99 : 2, 
                step: 0.01 
              }}
            />
          </Grid>
        )}
        
        {calculationType !== 'sampleSize' && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={analysisType?.includes('anova') ? 'Total Sample Size' : 'Sample Size per Group'}
              type="number"
              value={calculationData.sampleSize}
              onChange={(e) => handleInputChange('sampleSize', parseInt(e.target.value))}
              margin="normal"
              inputProps={{ min: 3, max: 10000 }}
            />
          </Grid>
        )}
        
        {analysisType?.includes('anova') && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Number of Groups"
              type="number"
              value={calculationData.groups}
              onChange={(e) => handleInputChange('groups', parseInt(e.target.value))}
              margin="normal"
              inputProps={{ min: 2, max: 10 }}
            />
          </Grid>
        )}
        
        {(analysisType?.includes('t_test') || analysisType === 'correlation') && (
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Test Type</InputLabel>
              <Select
                value={calculationData.tails}
                onChange={(e) => handleInputChange('tails', e.target.value)}
                label="Test Type"
              >
                <MenuItem value={1}>One-tailed</MenuItem>
                <MenuItem value={2}>Two-tailed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        )}
      </Grid>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PsychologyIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Power Analysis Calculator
        </Typography>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Perform comprehensive statistical power analyses for study planning and sample size determination.
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<AssessmentIcon />} />
          <Tab label="Sensitivity Analysis" icon={<TimelineIcon />} />
          <Tab label="Guidelines" icon={<InfoIcon />} />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Analysis Type</InputLabel>
                <Select
                  value={calculationData.analysisType}
                  onChange={(e) => handleInputChange('analysisType', e.target.value)}
                  label="Analysis Type"
                >
                  {ANALYSIS_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body1">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.category}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              {renderParameterInputs()}
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={showSensitivityAnalysis}
                    onChange={(e) => setShowSensitivityAnalysis(e.target.checked)}
                  />
                }
                label="Generate Sensitivity Analysis"
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  onClick={calculatePowerAnalysis}
                  disabled={loading || !calculationData.analysisType}
                  startIcon={loading ? <CircularProgress size={20} /> : <CalculateIcon />}
                >
                  {loading ? 'Calculating...' : 'Calculate'}
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearAll}
                  startIcon={<ClearIcon />}
                >
                  Clear All
                </Button>
              </Box>
            </Grid>

            {error && (
              <Grid item xs={12}>
                <Alert severity="error">{error}</Alert>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {results.length > 0 ? (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Power Analysis Results</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CopyIcon />}
                    onClick={() => copyToClipboard(generateReport())}
                  >
                    Copy Report
                  </Button>
                  <AddToResultsButton
                    result={{
                      id: `power-analysis-${Date.now()}`,
                      type: 'Power Analysis',
                      timestamp: new Date().toISOString(),
                      data: results[0],
                      summary: `${results[0].type} - ${results[0].calculationType}: ${results[0].result.toFixed(4)}`
                    }}
                    size="small"
                  />
                </Box>
              </Box>

              {results.map((result, index) => (
                <Card key={index} sx={{ mb: 2, bgcolor: theme.palette.action.hover }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" color="primary">
                        {result.type}
                      </Typography>
                      <Chip
                        label={result.calculationType}
                        color="primary"
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="h4" sx={{ mb: 1, fontWeight: 'bold' }}>
                      {result.result.toFixed(4)}
                    </Typography>
                    
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {result.interpretation}
                    </Typography>
                    
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>Recommendation:</strong> {result.recommendation}
                      </Typography>
                    </Alert>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Typography variant="subtitle2" gutterBottom>
                      Parameters Used:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {Object.entries(result.parameters).map(([key, value]) => (
                        value !== undefined && (
                          <Chip
                            key={key}
                            label={`${key}: ${value}`}
                            size="small"
                            variant="outlined"
                          />
                        )
                      ))}
                    </Box>
                    
                    <Typography variant="subtitle2" gutterBottom>
                      Key Assumptions:
                    </Typography>
                    <List dense>
                      {result.assumptions.map((assumption, idx) => (
                        <ListItem key={idx} sx={{ py: 0 }}>
                          <ListItemText 
                            primary={assumption}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : (
            <Alert severity="info">
              No results yet. Use the Calculator tab to perform power analysis.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          {sensitivityData.length > 0 ? (
            <Box>
              <Typography variant="h6" gutterBottom>
                Power vs Sample Size
              </Typography>
              <Box sx={{ height: 400, width: '100%' }}>
                <ResponsiveContainer>
                  <LineChart data={sensitivityData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="sampleSize" label={{ value: 'Sample Size', position: 'insideBottom', offset: -10 }} />
                    <YAxis label={{ value: 'Statistical Power', angle: -90, position: 'insideLeft' }} />
                    <RechartsTooltip />
                    <Legend />
                    <Line type="monotone" dataKey="power" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                This chart shows how statistical power changes with sample size for your specified parameters.
                The horizontal line at 0.80 represents the conventional threshold for adequate power.
              </Typography>
            </Box>
          ) : (
            <Alert severity="info">
              Enable "Generate Sensitivity Analysis" in the Calculator tab and run an analysis to see visualizations.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" gutterBottom>
            Power Analysis Guidelines
          </Typography>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Statistical Power Basics</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Statistical power is the probability of correctly rejecting a false null hypothesis (avoiding Type II error).
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Power ≥ 0.80: Adequate (conventional standard)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Power 0.60-0.79: Moderate (may miss some effects)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Power < 0.60: Low (high risk of Type II error)" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Effect Size Guidelines</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Effect sizes for different analyses:
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Analysis</TableCell>
                      <TableCell>Small</TableCell>
                      <TableCell>Medium</TableCell>
                      <TableCell>Large</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>t-test (Cohen's d)</TableCell>
                      <TableCell>0.2</TableCell>
                      <TableCell>0.5</TableCell>
                      <TableCell>0.8</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>ANOVA (Cohen's f)</TableCell>
                      <TableCell>0.1</TableCell>
                      <TableCell>0.25</TableCell>
                      <TableCell>0.4</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Correlation (r)</TableCell>
                      <TableCell>0.1</TableCell>
                      <TableCell>0.3</TableCell>
                      <TableCell>0.5</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Sample Size Planning</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" paragraph>
                Key considerations for sample size planning:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText primary="Plan for 10-20% attrition in longitudinal studies" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Consider practical constraints (time, budget, recruitment)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Justify effect size expectations with pilot data or literature" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Consider multiple comparisons if applicable" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default PowerAnalysisCalculator;