import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  ScatterPlot as CorrelationIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  Calculate as CalculateIcon,
  TrendingUp as TrendIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`correlation-ci-tabpanel-${index}`}
      aria-labelledby={`correlation-ci-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface CorrelationCIResult {
  r: number;
  n: number;
  confidenceLevel: number;
  method: string;
  lowerBound: number;
  upperBound: number;
  marginOfError: number;
  fisherZ: number;
  fisherZLower: number;
  fisherZUpper: number;
  criticalValue: number;
  standardError: number;
  interpretation: string;
  formula: string;
}

const CorrelationCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [r, setR] = useState<string>('');
  const [n, setN] = useState<string>('');
  const [confidenceLevel, setConfidenceLevel] = useState<number>(95);
  const [method, setMethod] = useState<string>('fisher');
  const [result, setResult] = useState<CorrelationCIResult | null>(null);
  const [error, setError] = useState<string>('');

  // Render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const getZScore = (alpha: number): number => {
    const zScores: { [key: number]: number } = {
      90: 1.645,
      95: 1.96,
      99: 2.576,
      99.9: 3.291
    };
    return zScores[alpha] || 1.96;
  };

  const fisherZTransform = (r: number): number => {
    return 0.5 * Math.log((1 + r) / (1 - r));
  };

  const inverseFisherZ = (z: number): number => {
    return (Math.exp(2 * z) - 1) / (Math.exp(2 * z) + 1);
  };

  const calculateCorrelationCI = useCallback(() => {
    setError('');
    
    const rValue = parseFloat(r);
    const nValue = parseInt(n);
    
    // Input validation
    if (isNaN(rValue) || isNaN(nValue)) {
      setError('Please enter valid numeric values for correlation coefficient and sample size.');
      return;
    }
    
    if (rValue < -1 || rValue > 1) {
      setError('Correlation coefficient must be between -1 and 1.');
      return;
    }
    
    if (nValue < 3) {
      setError('Sample size must be at least 3.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }
    
    const alpha = (100 - confidenceLevel) / 100;
    const zCritical = getZScore(confidenceLevel);
    
    let lowerBound: number;
    let upperBound: number;
    let marginOfError: number;
    let fisherZ: number = 0;
    let fisherZLower: number = 0;
    let fisherZUpper: number = 0;
    let standardError: number;
    
    if (method === 'fisher') {
      // Fisher's z-transformation method (recommended)
      fisherZ = fisherZTransform(rValue);
      standardError = 1 / Math.sqrt(nValue - 3);
      marginOfError = zCritical * standardError;
      
      fisherZLower = fisherZ - marginOfError;
      fisherZUpper = fisherZ + marginOfError;
      
      lowerBound = inverseFisherZ(fisherZLower);
      upperBound = inverseFisherZ(fisherZUpper);
    } else {
      // Simple method (less accurate, especially for large |r|)
      standardError = Math.sqrt((1 - rValue * rValue) / (nValue - 2));
      marginOfError = zCritical * standardError;
      
      lowerBound = Math.max(-1, rValue - marginOfError);
      upperBound = Math.min(1, rValue + marginOfError);
    }
    
    // Interpretation
    let interpretation = '';
    if (lowerBound > 0 && upperBound > 0) {
      interpretation = `We are ${confidenceLevel}% confident that the true population correlation is positive, between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
    } else if (lowerBound < 0 && upperBound < 0) {
      interpretation = `We are ${confidenceLevel}% confident that the true population correlation is negative, between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
    } else {
      interpretation = `We are ${confidenceLevel}% confident that the true population correlation is between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}. The interval includes zero, suggesting the correlation may not be statistically significant.`;
    }
    
    // Formula for Fisher's Z transformation method
    const formula = 'z = \\frac{1}{2}\\ln\\left(\\frac{1+r}{1-r}\\right), \\quad CI: \\left[\\tanh\\left(z - z_{\\alpha/2}\\sqrt{\\frac{1}{n-3}}\\right), \\tanh\\left(z + z_{\\alpha/2}\\sqrt{\\frac{1}{n-3}}\\right)\\right]';
    
    const calculationResult: CorrelationCIResult = {
      r: rValue,
      n: nValue,
      confidenceLevel,
      method,
      lowerBound,
      upperBound,
      marginOfError,
      fisherZ,
      fisherZLower,
      fisherZUpper,
      criticalValue: zCritical,
      standardError,
      interpretation,
      formula
    };
    
    setResult(calculationResult);
    setActiveTab(1); // Switch to Results tab
  }, [r, n, confidenceLevel, method]);
  
  const clearAll = () => {
    setR('');
    setN('');
    setConfidenceLevel(95);
    setMethod('fisher');
    setResult(null);
    setError('');
    setActiveTab(0);
  };
  
  const copyToClipboard = () => {
    if (!result) return;
    
    const text = `Correlation Coefficient Confidence Interval Results:
` +
      `Sample Correlation (r): ${result.r}
` +
      `Sample Size (n): ${result.n}
` +
      `Confidence Level: ${result.confidenceLevel}%
` +
      `Method: ${result.method === 'fisher' ? "Fisher's z-transformation" : 'Simple method'}
` +
      `${result.confidenceLevel}% Confidence Interval: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]
` +
      `Margin of Error: ±${result.marginOfError.toFixed(2)}
` +
      `Standard Error: ${result.standardError.toFixed(2)}
` +
      `\nInterpretation: ${result.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CorrelationIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" component="h1" fontWeight="bold">
          Correlation Coefficient CI
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<TrendIcon />} />
          <Tab label="Guide" icon={<InfoIcon />} />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Correlation Coefficient (r)"
                      value={r}
                      onChange={(e) => setR(e.target.value)}
                      type="number"
                      inputProps={{ step: 0.001, min: -1, max: 1 }}
                      helperText="Enter value between -1 and 1"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Size (n)"
                      value={n}
                      onChange={(e) => setN(e.target.value)}
                      type="number"
                      inputProps={{ step: 1, min: 3 }}
                      helperText="Number of paired observations"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={confidenceLevel}
                        onChange={(e) => setConfidenceLevel(e.target.value as number)}
                        label="Confidence Level"
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                        <MenuItem value={99.9}>99.9%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Method</InputLabel>
                      <Select
                        value={method}
                        onChange={(e) => setMethod(e.target.value as string)}
                        label="Method"
                      >
                        <MenuItem value="fisher">Fisher's z-transformation (Recommended)</MenuItem>
                        <MenuItem value="simple">Simple method</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculateCorrelationCI}
                    disabled={!r || !n}
                    startIcon={<CalculateIcon />}
                  >
                    Calculate Confidence Interval
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  About This Calculator
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This calculator computes confidence intervals for Pearson correlation coefficients. 
                  Fisher's z-transformation is recommended as it provides more accurate intervals, 
                  especially for larger correlation values.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result ? (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Confidence Interval Results
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                        <Typography variant="subtitle2" color="primary">
                          {result.confidenceLevel}% Confidence Interval
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                        <Typography variant="subtitle2" color="secondary">
                          Margin of Error
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          ±{result.marginOfError.toFixed(2)}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Calculation Details
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample r</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.r}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Size</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.n}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Method</Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {result.method === 'fisher' ? "Fisher's z" : 'Simple'}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Standard Error</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.standardError.toFixed(2)}</Typography>
                    </Grid>
                  </Grid>
                  
                  {result.method === 'fisher' && (
                    <>
                      <Divider sx={{ my: 3 }} />
                      <Typography variant="h6" gutterBottom>
                        Fisher's z-transformation Details
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="text.secondary">Fisher's z</Typography>
                          <Typography variant="body1" fontWeight="bold">{result.fisherZ.toFixed(2)}</Typography>
                        </Grid>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="text.secondary">z Lower</Typography>
                          <Typography variant="body1" fontWeight="bold">{result.fisherZLower.toFixed(2)}</Typography>
                        </Grid>
                        <Grid item xs={4}>
                          <Typography variant="body2" color="text.secondary">z Upper</Typography>
                          <Typography variant="body1" fontWeight="bold">{result.fisherZUpper.toFixed(2)}</Typography>
                        </Grid>
                      </Grid>
                    </>
                  )}
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Formula
                  </Typography>
                  <Box 
                    sx={{ 
                      p: 2, 
                      bgcolor: 'grey.50', 
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200',
                      '& .katex': { fontSize: '1.1em' }
                    }}
                    dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                  />
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Interpretation
                  </Typography>
                  <Typography variant="body1">
                    {result.interpretation}
                  </Typography>
                  
                  <Box sx={{ mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={copyToClipboard}
                      startIcon={<CopyIcon />}
                    >
                      Copy Results
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Key Statistics
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Chip label="r" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Correlation" 
                        secondary={result.r.toString()}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="n" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Size" 
                        secondary={result.n.toString()}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="CI" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Confidence Level" 
                        secondary={`${result.confidenceLevel}%`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="SE" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Standard Error" 
                        secondary={result.standardError.toFixed(2)}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        ) : (
          <Alert severity="info">
            No results to display. Please go to the Calculator tab and perform a calculation.
          </Alert>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h5" gutterBottom>
                  Correlation Coefficient Confidence Intervals Guide
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  When to Use
                </Typography>
                <Typography variant="body1" paragraph>
                  Use correlation coefficient confidence intervals when you want to estimate the range of 
                  plausible values for the true population correlation coefficient based on your sample data. 
                  This is particularly useful for:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="Assessing the precision of your correlation estimate" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Determining if the correlation is significantly different from zero" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Comparing correlations between different studies or groups" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Methods Available
                </Typography>
                <Typography variant="body1" paragraph>
                  <strong>Fisher's z-transformation (Recommended):</strong> This method transforms the 
                  correlation coefficient using Fisher's z-transformation, which normalizes the sampling 
                  distribution. It's more accurate, especially for larger correlation values.
                </Typography>
                <Typography variant="body1" paragraph>
                  <strong>Simple method:</strong> Uses the normal approximation directly on the correlation 
                  coefficient. Less accurate for large correlations but simpler to understand.
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  How to Interpret
                </Typography>
                <Typography variant="body1" paragraph>
                  The confidence interval provides a range of values that likely contains the true 
                  population correlation coefficient. For example, a 95% confidence interval means 
                  that if you repeated your study many times, 95% of the intervals would contain 
                  the true population correlation.
                </Typography>
                <Typography variant="body1" paragraph>
                  If the confidence interval includes zero, it suggests that the correlation may not 
                  be statistically significant at the chosen confidence level.
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Assumptions
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="The data follows a bivariate normal distribution" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Observations are independent" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="The relationship is linear" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Sample size is adequate (typically n ≥ 10)" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Reference
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Correlation Strength:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="0.00 - 0.30: Weak" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="0.30 - 0.70: Moderate" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="0.70 - 1.00: Strong" />
                  </ListItem>
                </List>
                
                <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                  <strong>Common Confidence Levels:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="90%: Less stringent" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="95%: Standard" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="99%: More stringent" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default CorrelationCI;