import React from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  <PERSON>,
  Step<PERSON><PERSON><PERSON>,
  StepContent,
  Paper,
  Button,
  Chip,
  LinearProgress,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  PlayArrow as PlayArrowIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'active' | 'pending';
  optional?: boolean;
  estimatedTime?: string;
  tips?: string[];
}

interface WorkflowProgressProps {
  steps: WorkflowStep[];
  currentStep: number;
  onStepClick?: (stepIndex: number) => void;
  showProgress?: boolean;
  orientation?: 'horizontal' | 'vertical';
}

const WorkflowProgress: React.FC<WorkflowProgressProps> = ({
  steps,
  currentStep,
  onStepClick,
  showProgress = true,
  orientation = 'vertical'
}) => {
  const theme = useTheme();

  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const progressPercentage = (completedSteps / steps.length) * 100;

  const getStepIcon = (step: WorkflowStep, index: number) => {
    if (step.status === 'completed') {
      return <CheckCircleIcon color="success" />;
    } else if (step.status === 'active') {
      return <PlayArrowIcon color="primary" />;
    } else {
      return <RadioButtonUncheckedIcon color="disabled" />;
    }
  };

  const getStepColor = (step: WorkflowStep) => {
    switch (step.status) {
      case 'completed':
        return theme.palette.success.main;
      case 'active':
        return theme.palette.primary.main;
      default:
        return theme.palette.grey[400];
    }
  };

  return (
    <Box>
      {showProgress && (
        <Paper 
          elevation={1} 
          sx={{ 
            p: 2, 
            mb: 2, 
            bgcolor: alpha(theme.palette.primary.main, 0.05),
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle2" color="primary">
              Analysis Workflow Progress
            </Typography>
            <Chip 
              label={`${completedSteps}/${steps.length} Complete`}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={progressPercentage} 
            sx={{ 
              height: 8, 
              borderRadius: 4,
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              '& .MuiLinearProgress-bar': {
                borderRadius: 4
              }
            }} 
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            {Math.round(progressPercentage)}% complete
          </Typography>
        </Paper>
      )}

      <Stepper 
        activeStep={currentStep} 
        orientation={orientation}
        sx={{
          '& .MuiStepLabel-root': {
            cursor: onStepClick ? 'pointer' : 'default'
          }
        }}
      >
        {steps.map((step, index) => (
          <Step key={step.id} completed={step.status === 'completed'}>
            <StepLabel
              icon={getStepIcon(step, index)}
              onClick={() => onStepClick && onStepClick(index)}
              sx={{
                '& .MuiStepLabel-label': {
                  color: getStepColor(step),
                  fontWeight: step.status === 'active' ? 'bold' : 'normal'
                }
              }}
            >
              <Box>
                <Typography variant="subtitle2">
                  {step.title}
                  {step.optional && (
                    <Chip 
                      label="Optional" 
                      size="small" 
                      variant="outlined" 
                      sx={{ ml: 1, fontSize: '0.7rem' }} 
                    />
                  )}
                </Typography>
                {step.estimatedTime && (
                  <Typography variant="caption" color="text.secondary">
                    Est. {step.estimatedTime}
                  </Typography>
                )}
              </Box>
            </StepLabel>
            {orientation === 'vertical' && (
              <StepContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {step.description}
                </Typography>
                
                {step.tips && step.tips.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <LightbulbIcon fontSize="small" color="warning" />
                      Tips:
                    </Typography>
                    {step.tips.map((tip, tipIndex) => (
                      <Typography key={tipIndex} variant="caption" color="text.secondary" sx={{ display: 'block', ml: 2 }}>
                        • {tip}
                      </Typography>
                    ))}
                  </Box>
                )}
                
                {step.status === 'active' && (
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<PlayArrowIcon />}
                      onClick={() => onStepClick && onStepClick(index)}
                    >
                      Continue
                    </Button>
                  </Box>
                )}
              </StepContent>
            )}
          </Step>
        ))}
      </Stepper>
    </Box>
  );
};

export default WorkflowProgress;
