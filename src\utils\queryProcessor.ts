/**
 * Enhanced Query Processing Utility for Analysis Assistant
 * Provides intelligent text processing, synonym expansion, and fuzzy matching
 */

// Statistical term synonyms dictionary
const STATISTICAL_SYNONYMS: Record<string, string[]> = {
  'correlation': ['association', 'relationship', 'connection', 'link'],
  'association': ['correlation', 'relationship', 'connection', 'link'],
  'relationship': ['correlation', 'association', 'connection', 'link'],
  'compare': ['comparison', 'contrast', 'difference', 'versus', 'vs'],
  'comparison': ['compare', 'contrast', 'difference', 'versus', 'vs'],
  'difference': ['compare', 'comparison', 'contrast', 'versus', 'vs'],
  'test': ['analysis', 'method', 'procedure', 'technique'],
  'analysis': ['test', 'method', 'procedure', 'technique'],
  'method': ['test', 'analysis', 'procedure', 'technique'],
  'normal': ['gaussian', 'bell curve', 'parametric'],
  'gaussian': ['normal', 'bell curve', 'parametric'],
  'parametric': ['normal', 'gaussian'],
  'nonparametric': ['non-parametric', 'distribution-free', 'rank-based'],
  'categorical': ['nominal', 'ordinal', 'discrete', 'qualitative'],
  'nominal': ['categorical', 'qualitative'],
  'ordinal': ['categorical', 'ranked', 'ordered'],
  'continuous': ['numeric', 'quantitative', 'interval', 'ratio'],
  'numeric': ['continuous', 'quantitative', 'numerical'],
  'quantitative': ['continuous', 'numeric', 'numerical'],
  'group': ['groups', 'category', 'categories', 'factor', 'level'],
  'groups': ['group', 'category', 'categories', 'factor', 'level'],
  'mean': ['average', 'central tendency'],
  'average': ['mean', 'central tendency'],
  'variance': ['variability', 'spread', 'dispersion'],
  'variability': ['variance', 'spread', 'dispersion'],
  'significant': ['significance', 'p-value', 'alpha'],
  'significance': ['significant', 'p-value', 'alpha'],
  'regression': ['prediction', 'modeling', 'linear model'],
  'prediction': ['regression', 'modeling', 'forecasting'],
  'modeling': ['regression', 'prediction', 'model']
};

// Common statistical abbreviations
const STATISTICAL_ABBREVIATIONS: Record<string, string> = {
  'anova': 'analysis of variance',
  'ancova': 'analysis of covariance',
  'manova': 'multivariate analysis of variance',
  'chi2': 'chi-square',
  'χ²': 'chi-square',
  'df': 'degrees of freedom',
  'sd': 'standard deviation',
  'se': 'standard error',
  'ci': 'confidence interval',
  'icc': 'intraclass correlation',
  'roc': 'receiver operating characteristic',
  'auc': 'area under curve',
  'or': 'odds ratio',
  'rr': 'relative risk',
  'hr': 'hazard ratio',
  'glm': 'generalized linear model',
  'lm': 'linear model',
  'mlm': 'multilevel model',
  'sem': 'structural equation modeling',
  'pca': 'principal component analysis',
  'efa': 'exploratory factor analysis',
  'cfa': 'confirmatory factor analysis'
};

// Domain-specific context keywords
const DOMAIN_KEYWORDS: Record<string, string[]> = {
  'medical': ['patient', 'clinical', 'treatment', 'diagnosis', 'symptom', 'disease', 'therapy', 'intervention'],
  'survey': ['questionnaire', 'likert', 'scale', 'response', 'participant', 'respondent', 'item'],
  'experimental': ['control', 'treatment', 'randomized', 'trial', 'experiment', 'intervention', 'baseline'],
  'longitudinal': ['time', 'repeated', 'follow-up', 'baseline', 'change', 'trend', 'over time'],
  'cross-sectional': ['snapshot', 'point in time', 'prevalence', 'current'],
  'business': ['sales', 'revenue', 'customer', 'market', 'profit', 'performance', 'kpi'],
  'psychology': ['behavior', 'cognitive', 'personality', 'attitude', 'perception', 'emotion'],
  'education': ['student', 'grade', 'score', 'achievement', 'learning', 'curriculum', 'assessment']
};

// Variable type inference patterns
const VARIABLE_TYPE_PATTERNS: Record<string, RegExp[]> = {
  'categorical': [
    /\b(gender|sex|race|ethnicity|category|group|type|class)\b/i,
    /\b(yes\/no|true\/false|male\/female)\b/i,
    /\b(low|medium|high)\b/i
  ],
  'continuous': [
    /\b(age|height|weight|temperature|pressure|score|time|duration)\b/i,
    /\b(income|salary|cost|price|amount)\b/i,
    /\b(measurement|value|level|concentration)\b/i
  ],
  'ordinal': [
    /\b(rating|rank|grade|level|stage|degree)\b/i,
    /\b(likert|scale|order|sequence)\b/i,
    /\b(first|second|third|primary|secondary)\b/i
  ]
};

/**
 * Calculate Levenshtein distance for fuzzy string matching
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Calculate fuzzy match score (0-1, where 1 is exact match)
 */
function fuzzyMatchScore(str1: string, str2: string): number {
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1;
  
  const distance = levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
  return 1 - (distance / maxLength);
}

/**
 * Expand query with synonyms
 */
function expandWithSynonyms(query: string): string[] {
  const words = query.toLowerCase().split(/\s+/);
  const expandedTerms = new Set([query]);
  
  words.forEach(word => {
    if (STATISTICAL_SYNONYMS[word]) {
      STATISTICAL_SYNONYMS[word].forEach(synonym => {
        const expandedQuery = query.toLowerCase().replace(new RegExp(`\\b${word}\\b`, 'g'), synonym);
        expandedTerms.add(expandedQuery);
      });
    }
  });
  
  return Array.from(expandedTerms);
}

/**
 * Expand abbreviations in query
 */
function expandAbbreviations(query: string): string {
  let expandedQuery = query.toLowerCase();
  
  Object.entries(STATISTICAL_ABBREVIATIONS).forEach(([abbrev, full]) => {
    const regex = new RegExp(`\\b${abbrev}\\b`, 'gi');
    expandedQuery = expandedQuery.replace(regex, full);
  });
  
  return expandedQuery;
}

/**
 * Detect domain context from query
 */
function detectDomainContext(query: string): string[] {
  const queryLower = query.toLowerCase();
  const detectedDomains: string[] = [];
  
  Object.entries(DOMAIN_KEYWORDS).forEach(([domain, keywords]) => {
    const matchCount = keywords.filter(keyword => 
      queryLower.includes(keyword.toLowerCase())
    ).length;
    
    if (matchCount > 0) {
      detectedDomains.push(domain);
    }
  });
  
  return detectedDomains;
}

/**
 * Infer variable types from query
 */
function inferVariableTypes(query: string): string[] {
  const inferredTypes: string[] = [];
  
  Object.entries(VARIABLE_TYPE_PATTERNS).forEach(([type, patterns]) => {
    const hasMatch = patterns.some(pattern => pattern.test(query));
    if (hasMatch) {
      inferredTypes.push(type);
    }
  });
  
  return inferredTypes;
}

/**
 * Detect statistical intent from query
 */
function detectStatisticalIntent(query: string): string[] {
  const queryLower = query.toLowerCase();
  const intents: string[] = [];
  
  // Comparison intents
  if (/\b(compare|comparison|difference|versus|vs|between)\b/.test(queryLower)) {
    intents.push('comparison');
  }
  
  // Correlation intents
  if (/\b(correlat|associat|relationship|connect|link)\b/.test(queryLower)) {
    intents.push('correlation');
  }
  
  // Distribution intents
  if (/\b(normal|distribution|bell curve|gaussian|skew)\b/.test(queryLower)) {
    intents.push('distribution');
  }
  
  // Descriptive intents
  if (/\b(describe|summary|mean|average|median|mode)\b/.test(queryLower)) {
    intents.push('descriptive');
  }
  
  // Prediction intents
  if (/\b(predict|forecast|model|regression)\b/.test(queryLower)) {
    intents.push('prediction');
  }
  
  // Sample size intents
  if (/\b(sample size|power|effect size|participants needed)\b/.test(queryLower)) {
    intents.push('sample_size');
  }
  
  return intents;
}

/**
 * Enhanced query processing interface
 */
export interface ProcessedQuery {
  original: string;
  normalized: string;
  expandedQueries: string[];
  synonyms: string[];
  domains: string[];
  variableTypes: string[];
  intents: string[];
  confidence: number;
}

/**
 * Main query processing function
 */
export function processQuery(query: string): ProcessedQuery {
  // Normalize query
  const normalized = query.trim().toLowerCase();
  
  // Expand abbreviations
  const withAbbreviations = expandAbbreviations(normalized);
  
  // Expand with synonyms
  const expandedQueries = expandWithSynonyms(withAbbreviations);
  
  // Extract synonyms used
  const synonyms = expandedQueries.filter(q => q !== normalized);
  
  // Detect context
  const domains = detectDomainContext(query);
  const variableTypes = inferVariableTypes(query);
  const intents = detectStatisticalIntent(query);
  
  // Calculate confidence based on detected features
  let confidence = 0.5; // Base confidence
  if (intents.length > 0) confidence += 0.2;
  if (variableTypes.length > 0) confidence += 0.15;
  if (domains.length > 0) confidence += 0.1;
  if (synonyms.length > 0) confidence += 0.05;
  
  confidence = Math.min(confidence, 1.0);
  
  return {
    original: query,
    normalized: withAbbreviations,
    expandedQueries,
    synonyms,
    domains,
    variableTypes,
    intents,
    confidence
  };
}

/**
 * Enhanced fuzzy matching for keywords
 */
export function fuzzyMatchKeywords(query: string, keywords: string[], threshold: number = 0.7): Array<{keyword: string, score: number}> {
  const queryWords = query.toLowerCase().split(/\s+/);
  const matches: Array<{keyword: string, score: number}> = [];
  
  keywords.forEach(keyword => {
    const keywordLower = keyword.toLowerCase();
    
    // Check for exact matches first
    if (query.toLowerCase().includes(keywordLower)) {
      matches.push({ keyword, score: 1.0 });
      return;
    }
    
    // Check fuzzy matches
    queryWords.forEach(word => {
      const score = fuzzyMatchScore(word, keywordLower);
      if (score >= threshold) {
        matches.push({ keyword, score });
      }
    });
  });
  
  // Remove duplicates and sort by score
  const uniqueMatches = matches.reduce((acc, current) => {
    const existing = acc.find(item => item.keyword === current.keyword);
    if (!existing || current.score > existing.score) {
      return [...acc.filter(item => item.keyword !== current.keyword), current];
    }
    return acc;
  }, [] as Array<{keyword: string, score: number}>);
  
  return uniqueMatches.sort((a, b) => b.score - a.score);
}

/**
 * Generate contextual suggestions based on processed query
 */
export function generateContextualSuggestions(processedQuery: ProcessedQuery): string[] {
  const suggestions: string[] = [];
  
  // Intent-based suggestions
  if (processedQuery.intents.includes('comparison')) {
    suggestions.push('Consider checking normality assumptions first');
    suggestions.push('Determine if you have paired or independent groups');
  }
  
  if (processedQuery.intents.includes('correlation')) {
    suggestions.push('Check for linearity and outliers');
    suggestions.push('Consider the scale of your variables');
  }
  
  if (processedQuery.intents.includes('distribution')) {
    suggestions.push('Visual inspection with histograms is recommended');
    suggestions.push('Consider sample size when interpreting normality tests');
  }
  
  // Variable type suggestions
  if (processedQuery.variableTypes.includes('categorical')) {
    suggestions.push('Chi-square tests are appropriate for categorical data');
    suggestions.push('Consider cross-tabulation for exploring relationships');
  }
  
  if (processedQuery.variableTypes.includes('continuous')) {
    suggestions.push('Check for outliers and distribution shape');
    suggestions.push('Consider transformation if data is skewed');
  }
  
  // Domain-specific suggestions
  if (processedQuery.domains.includes('medical')) {
    suggestions.push('Consider clinical significance alongside statistical significance');
    suggestions.push('Account for multiple comparisons if testing multiple outcomes');
  }
  
  if (processedQuery.domains.includes('survey')) {
    suggestions.push('Check internal consistency with Cronbach\'s alpha');
    suggestions.push('Consider ordinal methods for Likert scale data');
  }
  
  return suggestions.slice(0, 3); // Limit to top 3 suggestions
}