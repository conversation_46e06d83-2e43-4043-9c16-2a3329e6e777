import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Slider,
  TextField,
  Button,
  useTheme,
  alpha,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  ToggleButtonGroup,
  ToggleButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Help as HelpIcon,
  ContentCopy as ContentCopyIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import jStat from 'jstat';
import { getZScore } from '../../utils/stats/sampleSize';

const PairedSampleCalculator: React.FC = () => {
  // Function to render mathematical formulas
  const renderFormula = (formula: string): string => {
    const result = unified()
      .use(remarkParse)
      .use(remarkMath)
      .use(remarkRehype)
      .use(rehypeKatex)
      .use(rehypeStringify)
      .processSync(`$${formula}$`);
    return String(result);
  };
  const theme = useTheme();

  // State for Paired-Sample calculator type
  const [pairedSampleCalculatorType, setPairedSampleCalculatorType] = useState<string>('proportion');

  // State for calculator parameters (using twoSampleConfidenceLevel and twoSamplePower for consistency as in original file)
  const [twoSampleConfidenceLevel, setTwoSampleConfidenceLevel] = useState<number>(0.95);
  const [twoSamplePower, setTwoSamplePower] = useState<number>(0.80); // 80% power

  // Paired Samples parameters
  const [p01, setP01] = useState<number>(0.2); // Proportion of discordant pairs (Group 1 success, Group 2 failure)
  const [p10, setP10] = useState<number>(0.1); // Proportion of discordant pairs (Group 1 failure, Group 2 success)
  const [delta, setDelta] = useState<number>(5); // Expected mean difference for paired means
  const [sigmaD, setSigmaD] = useState<number>(10); // Standard deviation of paired differences

  // State for results
  const [pairedRequiredSampleSize, setPairedRequiredSampleSize] = useState<number | null>(null);
  const [pairedPowerCurveData, setPairedPowerCurveData] = useState<any[]>([]);

  // Calculate paired-sample size when parameters change
  useEffect(() => {
    calculatePairedSampleSize();
    generatePairedPowerCurveData();
  }, [twoSampleConfidenceLevel, twoSamplePower, p01, p10, delta, sigmaD, pairedSampleCalculatorType]);

  // Function to calculate required sample size for paired samples
  const calculatePairedSampleSize = () => {
    const zAlpha = getZScore(1 - (1 - twoSampleConfidenceLevel) / 2); // Using twoSampleConfidenceLevel for consistency
    const zBeta = getZScore(twoSamplePower); // Using twoSamplePower for consistency

    let sampleSize: number;

    if (pairedSampleCalculatorType === 'proportion') {
      // Formula for Two Matched Proportions (McNemar's test)
      // n = (Z_alpha/2 * sqrt(p01 + p10) + Z_beta * sqrt(p01 + p10 - (p01 - p10)^2))^2 / (p01 - p10)^2
      const sumP = p01 + p10;
      const diffP = p01 - p10;

      if (diffP === 0) { // Avoid division by zero
        sampleSize = Infinity; 
      } else {
        const numerator = Math.pow(
          zAlpha * Math.sqrt(sumP) + zBeta * Math.sqrt(sumP - Math.pow(diffP, 2)),
          2
        );
        const denominator = Math.pow(diffP, 2);
        sampleSize = Math.ceil(numerator / denominator);
      }
    } else {
      // Formula for Two Paired Means (paired t-test)
      // n = ((Z_alpha/2 + Z_beta)^2 * sigma_d^2) / Delta^2
      if (delta === 0) { // Avoid division by zero
        sampleSize = Infinity;
      } else {
        const numerator = Math.pow(zAlpha + zBeta, 2) * Math.pow(sigmaD, 2);
        const denominator = Math.pow(delta, 2);
        sampleSize = Math.ceil(numerator / denominator);
      }
    }
    setPairedRequiredSampleSize(sampleSize);
  };
  
  // Generate data for paired-sample power curve visualization
  const generatePairedPowerCurveData = () => {
    const data = [];
    const minSampleSize = Math.max(10, Math.floor(pairedRequiredSampleSize ? pairedRequiredSampleSize * 0.5 : 20));
    const maxSampleSize = Math.ceil(pairedRequiredSampleSize ? pairedRequiredSampleSize * 1.5 : 100);
    const step = Math.max(1, Math.floor((maxSampleSize - minSampleSize) / 20));

    for (let n = minSampleSize; n <= maxSampleSize; n += step) {
      let power: number;
      const zAlpha = getZScore(1 - (1 - twoSampleConfidenceLevel) / 2); // Using twoSampleConfidenceLevel for consistency

      if (pairedSampleCalculatorType === 'proportion') {
        const sumP = p01 + p10;
        const diffP = p01 - p10;
        if (sumP === 0) { // Avoid division by zero or sqrt of negative
          power = 0;
        } else {
          const se = Math.sqrt((sumP - Math.pow(diffP, 2)) / n);
          const zBeta = (Math.abs(diffP) - zAlpha * Math.sqrt(sumP / n)) / se;
          power = jStat.normal.cdf(zBeta, 0, 1); // Use jStat.normal.cdf
        }
      } else {
        const effectSize = Math.abs(delta);
        if (sigmaD === 0) { // Avoid division by zero
          power = 1; // If std dev is 0, power is 1 (no variability)
        } else {
          const se = sigmaD / Math.sqrt(n);
          const zBeta = (effectSize - zAlpha * se) / se;
          power = jStat.normal.cdf(zBeta, 0, 1); // Use jStat.normal.cdf
        }
      }
      data.push({
        sampleSize: n,
        power: Math.max(0, Math.min(1, power)),
      });
    }
    setPairedPowerCurveData(data);
  };

  // Handle paired-sample calculator type change
  const handlePairedCalculatorTypeChange = (_event: React.MouseEvent<HTMLElement>, newValue: string) => {
    if (newValue !== null) {
      setPairedSampleCalculatorType(newValue);
      // Set default values based on calculator type
      if (newValue === 'proportion') {
        setP01(0.2);
        setP10(0.1);
      } else if (newValue === 'mean') {
        setDelta(5);
        setSigmaD(10);
      }
    }
  };

  // Handle copying paired-sample results to clipboard
  const handleCopyPairedResults = () => {
    if (pairedRequiredSampleSize) {
      let resultText = '';
      if (pairedSampleCalculatorType === 'proportion') {
        resultText = `Required Sample Size: ${pairedRequiredSampleSize} pairs for comparing two matched proportions (p01=${p01}, p10=${p10}) with ${Math.round(twoSamplePower * 100)}% power at a ${Math.round(twoSampleConfidenceLevel * 100)}% confidence level.`;
      } else {
        resultText = `Required Sample Size: ${pairedRequiredSampleSize} pairs for comparing two paired means (Delta=${delta}, Sigma_d=${sigmaD}) with ${Math.round(twoSamplePower * 100)}% power at a ${Math.round(twoSampleConfidenceLevel * 100)}% confidence level.`;
      }
      navigator.clipboard.writeText(resultText);
    }
  };

  // Handle exporting results as PDF
  const handleExportPDF = () => {
    // For now, just a placeholder
    console.log('Export PDF functionality would go here');
  };

  // Handle reset for paired-sample
  const handlePairedReset = () => {
    setTwoSampleConfidenceLevel(0.95); // Using twoSampleConfidenceLevel for consistency
    setTwoSamplePower(0.80); // Using twoSamplePower for consistency
    setP01(0.2);
    setP10(0.1);
    setDelta(5);
    setSigmaD(10);
  };

  return (
    <>
      {/* Calculator Type Selector */}
      <Box sx={{ mb: 3 }}>
        <ToggleButtonGroup
          value={pairedSampleCalculatorType}
          exclusive
          onChange={handlePairedCalculatorTypeChange}
          aria-label="paired sample calculator type"
          fullWidth
        >
          <ToggleButton
            value="proportion"
            aria-label="two matched proportions"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.primary.main,
                color: theme.palette.primary.contrastText,
                '&:hover': {
                  bgcolor: theme.palette.primary.dark,
                },
              },
            }}
          >
            Two Matched Proportions
          </ToggleButton>
          <ToggleButton
            value="mean"
            aria-label="two paired means"
            sx={{
              '&.Mui-selected': {
                bgcolor: theme.palette.secondary.main,
                color: theme.palette.secondary.contrastText,
                 '&:hover': {
                  bgcolor: theme.palette.secondary.dark,
                },
              },
            }}
          >
            Two Paired Means
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Formulas Section */}
      <Box sx={{ mb: 3 }}>
        <Accordion defaultExpanded>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls="formulas-content"
            id="formulas-header"
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.05),
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.1),
              },
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              📐 Mathematical Formulas
            </Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 3 }}>
            {pairedSampleCalculatorType === 'proportion' ? (
              <Paper elevation={1} sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.02) }}>
                <Typography variant="h6" gutterBottom sx={{ color: theme.palette.info.main, fontWeight: 600 }}>
                  Two Matched Proportions (McNemar's Test)
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Sample Size Formula:
                </Typography>
                <Box sx={{ my: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('n = \\frac{[Z_{\\alpha/2} \\sqrt{p_{01} + p_{10}} + Z_{\\beta} \\sqrt{p_{01} + p_{10} - (p_{01} - p_{10})^2}]^2}{(p_{01} - p_{10})^2}')
                  }} />
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Where:
                </Typography>
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('n') }} style={{ display: 'inline-block' }} /> = required number of pairs
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\alpha/2}') }} style={{ display: 'inline-block' }} /> = critical value for two-tailed test
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\beta}') }} style={{ display: 'inline-block' }} /> = critical value for power (1-β)
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('p_{01}') }} style={{ display: 'inline-block' }} /> = proportion of discordant pairs (Group 1 success, Group 2 failure)
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('p_{10}') }} style={{ display: 'inline-block' }} /> = proportion of discordant pairs (Group 1 failure, Group 2 success)
                  </Typography>
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Effect Size:
                </Typography>
                <Box sx={{ my: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\text{Effect Size} = |p_{01} - p_{10}|')
                  }} />
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Key Assumptions:
                </Typography>
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Paired observations (matched subjects)
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Binary outcomes for each pair
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Independence between pairs
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Large sample approximation (n ≥ 30)
                  </Typography>
                </Box>
              </Paper>
            ) : (
              <Paper elevation={1} sx={{ p: 2, bgcolor: alpha(theme.palette.secondary.main, 0.02) }}>
                <Typography variant="h6" gutterBottom sx={{ color: theme.palette.secondary.main, fontWeight: 600 }}>
                  Two Paired Means (Paired t-test)
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Sample Size Formula:
                </Typography>
                <Box sx={{ my: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('n = \\frac{(Z_{\\alpha/2} + Z_{\\beta})^2 \\sigma_d^2}{\\Delta^2}')
                  }} />
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Where:
                </Typography>
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('n') }} style={{ display: 'inline-block' }} /> = required number of pairs
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\alpha/2}') }} style={{ display: 'inline-block' }} /> = critical value for two-tailed test
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\beta}') }} style={{ display: 'inline-block' }} /> = critical value for power (1-β)
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('\\sigma_d') }} style={{ display: 'inline-block' }} /> = standard deviation of paired differences
                  </Typography>
                  <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <span dangerouslySetInnerHTML={{ __html: renderFormula('\\Delta') }} style={{ display: 'inline-block' }} /> = expected mean difference
                  </Typography>
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Effect Size (Cohen's d):
                </Typography>
                <Box sx={{ my: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('d = \\frac{|\\Delta|}{\\sigma_d}')
                  }} />
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Key Assumptions:
                </Typography>
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Paired observations (matched subjects)
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Differences are normally distributed
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Independence between pairs
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Constant variance of differences
                  </Typography>
                </Box>
                
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, mt: 2 }}>
                  Effect Size Guidelines:
                </Typography>
                <Box sx={{ ml: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Small effect: d = 0.2
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Medium effect: d = 0.5
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Large effect: d = 0.8
                  </Typography>
                </Box>
              </Paper>
            )}
            
            {/* General Information */}
            <Paper elevation={1} sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.02), mt: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ color: theme.palette.warning.main, fontWeight: 600 }}>
                General Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                    Common Z-scores:
                  </Typography>
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      90% confidence: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.05} = 1.645') }} style={{ display: 'inline-block' }} />
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      95% confidence: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.025} = 1.96') }} style={{ display: 'inline-block' }} />
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      99% confidence: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.005} = 2.576') }} style={{ display: 'inline-block' }} />
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      80% power: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.20} = 0.842') }} style={{ display: 'inline-block' }} />
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      90% power: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.10} = 1.282') }} style={{ display: 'inline-block' }} />
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                    Interpretation Guidelines:
                  </Typography>
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Higher power requires larger sample sizes
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Smaller effect sizes require larger samples
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Paired designs are more efficient than independent samples
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      Consider practical significance alongside statistical significance
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </AccordionDetails>
        </Accordion>
      </Box>

      <Grid container spacing={3}>
        {/* Input Parameters */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Input Parameters</Typography>
            
            {/* Confidence Level */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>Confidence Level (α)</Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12}>
                  <Slider
                    value={twoSampleConfidenceLevel * 100} // Using twoSampleConfidenceLevel for consistency
                    onChange={(_, newValue) => setTwoSampleConfidenceLevel((newValue as number) / 100)}
                    step={1}
                    min={80}
                    max={99}
                    marks={[
                      { value: 80, label: '80%' },
                      { value: 90, label: '90%' },
                      { value: 95, label: '95%' },
                      { value: 99, label: '99%' },
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* Power */}
            <Box sx={{ mb: 4 }}>
              <Typography gutterBottom>
                Statistical Power (1-β)
                <Tooltip title="The probability of correctly detecting a true difference between groups.">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12}>
                  <Slider
                    value={twoSamplePower * 100} // Using twoSamplePower for consistency
                    onChange={(_, newValue) => setTwoSamplePower((newValue as number) / 100)}
                    step={5}
                    min={70}
                    max={95}
                    marks={[
                      { value: 70, label: '70%' },
                      { value: 80, label: '80%' },
                      { value: 90, label: '90%' },
                      { value: 95, label: '95%' },
                    ]}
                  />
                </Grid>
              </Grid>
            </Box>
            
            {/* Parameters for Two Matched Proportions */}
            {pairedSampleCalculatorType === 'proportion' && (
              <>
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Proportion of (Group 1 Success, Group 2 Failure) - p₀₁
                    <Tooltip title="The expected proportion of discordant pairs where Group 1 has success and Group 2 has failure.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs>
                      <TextField
                        value={Math.round(p01 * 100)}
                        onChange={(e) => setP01(Number(e.target.value) / 100)}
                        type="number"
                        inputProps={{
                          min: 0,
                          max: 100,
                          step: 1,
                        }}
                        fullWidth
                      />
                    </Grid>
                    <Grid item>
                      <Typography>%</Typography>
                    </Grid>
                  </Grid>
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Proportion of (Group 1 Failure, Group 2 Success) - p₁₀
                    <Tooltip title="The expected proportion of discordant pairs where Group 1 has failure and Group 2 has success.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs>
                      <TextField
                        value={Math.round(p10 * 100)}
                        onChange={(e) => setP10(Number(e.target.value) / 100)}
                        type="number"
                        inputProps={{
                          min: 0,
                          max: 100,
                          step: 1,
                        }}
                        fullWidth
                      />
                    </Grid>
                    <Grid item>
                      <Typography>%</Typography>
                    </Grid>
                  </Grid>
                </Box>
              </>
            )}
            
            {/* Parameters for Two Paired Means */}
            {pairedSampleCalculatorType === 'mean' && (
              <>
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Expected Mean Difference (Δ)
                    <Tooltip title="The expected mean difference between paired observations.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={delta}
                    onChange={(e) => setDelta(Number(e.target.value))}
                    inputProps={{
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
                
                <Box sx={{ mb: 4 }}>
                  <Typography gutterBottom>
                    Standard Deviation of Differences (σd)
                    <Tooltip title="The standard deviation of the paired differences.">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <HelpIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <TextField
                    type="number"
                    value={sigmaD}
                    onChange={(e) => setSigmaD(Number(e.target.value))}
                    inputProps={{
                      min: 0.1,
                      step: 0.1,
                    }}
                    fullWidth
                  />
                </Box>
              </>
            )}
            

            
            {/* Reset Button */}
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handlePairedReset}
              fullWidth
              sx={{ mt: 2 }}
            >
              Reset
            </Button>
          </Paper>
        </Grid>
        
        {/* Results */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Results</Typography>
              <Box>
                <Tooltip title="Copy results">
                  <IconButton onClick={handleCopyPairedResults}>
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Export as PDF">
                  <IconButton onClick={handleExportPDF}>
                    <PictureAsPdfIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            
            {/* Sample Size Result */}
            <Card sx={{ mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>Required Sample Size</Typography>
                <Typography variant="h2" color="primary">
                  {pairedRequiredSampleSize}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  pairs
                </Typography>
              </CardContent>
            </Card>
            
            {/* Power Curve Visualization */}
            <Typography variant="h6" gutterBottom>Power by Sample Size</Typography>
            <Box sx={{ height: 300, mb: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={pairedPowerCurveData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="sampleSize" 
                    label={{ value: 'Sample Size (pairs)', position: 'outerBottom', offset: 15, style: { fontSize: '12px' } }}
                    height={70}
                  />
                  <YAxis 
                    label={{ value: 'Statistical Power', angle: -90, position: 'outside', offset: -60, style: { fontSize: '12px' } }}
                    tickFormatter={(value) => `${Math.round(value * 100)}%`}
                    domain={[0, 1]}
                    width={100}
                  />
                  <RechartsTooltip 
                    formatter={(value: number) => [
                      `${(value * 100).toFixed(1)}%`,
                      'Power'
                    ]}
                    labelFormatter={(value) => `Sample Size: ${value} pairs`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="power" 
                    stroke={theme.palette.primary.main} 
                    activeDot={{ r: 8 }} 
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
            
            {/* Interpretation */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>Interpretation</Typography>
              {pairedSampleCalculatorType === 'proportion' ? (
                <Typography variant="body1">
                  A sample size of {pairedRequiredSampleSize} pairs is needed to detect a difference between matched proportions (p₀₁={p01} and p₁₀={p10}) with {Math.round(twoSamplePower * 100)}% power at a {Math.round(twoSampleConfidenceLevel * 100)}% confidence level.
                </Typography>
              ) : (
                <Typography variant="body1">
                  A sample size of {pairedRequiredSampleSize} pairs is needed to detect a mean difference of {delta} (with a standard deviation of differences of {sigmaD}) with {Math.round(twoSamplePower * 100)}% power at a {Math.round(twoSampleConfidenceLevel * 100)}% confidence level.
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </>
  );
};

export default PairedSampleCalculator;
