# DataStatPro - Comprehensive Statistical Analysis Web Application

DataStatPro is a modern, comprehensive web application for statistical analysis, data visualization, and epidemiological research. Built with React and TypeScript, it provides an intuitive interface for researchers, students, and professionals to perform sophisticated statistical analyses without requiring expensive software licenses.

![DataStatPro Dashboard](docs/assets/screenshots/statistica-dashboard.png)

## 🎯 Project Overview

DataStatPro serves as a complete statistical analysis platform that combines:
- **Modern Web Technologies**: React 18, TypeScript, Material-UI
- **Advanced Statistical Computing**: JStat, Math.js, TensorFlow.js
- **Cloud Integration**: Supabase for authentication and data storage
- **Progressive Web App**: Offline capabilities and mobile-first design
- **Educational Focus**: Built-in tutorials and guided workflows

## 🚀 Core Features

### 👥 User Management & Access Control
- **Multi-tier Authentication**: Guest access, standard accounts, educational tiers, and professional subscriptions
- **Cloud Data Storage**: Secure dataset storage with Supabase integration
- **Admin Dashboard**: Comprehensive user management and system monitoring
- **Subscription Management**: Stripe integration for premium features
- **Educational Licensing**: Special tiers for academic institutions

### 📊 Data Management & Processing
- **Flexible Data Import**: CSV, Excel, Google Sheets integration
- **Advanced Data Editor**: Spreadsheet-like interface with real-time validation
- **Data Transformation Pipeline**: Standardization, normalization, binning, recoding
- **Missing Data Handling**: Sophisticated missing value detection and treatment
- **Variable Management**: Type detection, role assignment, and metadata management
- **Sample Data Generation**: Built-in datasets for learning and testing

### 📈 Statistical Analysis Suite

#### Descriptive Statistics
- **Univariate Analysis**: Central tendency, dispersion, distribution shape
- **Frequency Analysis**: Detailed frequency tables and cross-tabulations
- **Normality Testing**: Shapiro-Wilk, Kolmogorov-Smirnov tests
- **Missing Data Reports**: Comprehensive missing value analysis

#### Inferential Statistics
- **T-Tests**: One-sample, independent samples, paired samples
- **ANOVA**: One-way ANOVA with post-hoc tests (Tukey, Bonferroni)
- **Non-parametric Tests**: Mann-Whitney U, Wilcoxon, Kruskal-Wallis, Friedman
- **Chi-square Tests**: Goodness of fit and independence testing
- **Effect Size Calculations**: Cohen's d, eta-squared, Cramer's V

#### Advanced Analysis (Premium)
- **Survival Analysis**: Kaplan-Meier curves, Cox regression
- **Factor Analysis**: Exploratory (EFA) and Confirmatory (CFA)
- **Reliability Analysis**: Cronbach's alpha, item-total correlations
- **Mediation/Moderation**: Path analysis and interaction effects
- **Cluster Analysis**: K-means, hierarchical clustering
- **Meta-Analysis**: Effect size pooling and forest plots

### 🔗 Correlation & Regression
- **Correlation Analysis**: Pearson, Spearman, Kendall correlations
- **Linear Regression**: Simple and multiple regression with diagnostics
- **Logistic Regression**: Binary and multinomial outcomes
- **Model Diagnostics**: Residual analysis, assumption checking
- **Prediction Intervals**: Confidence and prediction bands

### 📊 Data Visualization
- **Interactive Charts**: Built with Recharts and D3.js
- **Chart Types**: Bar, line, scatter, histogram, box plots, pie charts
- **Advanced Visualizations**: Heatmaps, violin plots, forest plots
- **Publication-Ready**: High-resolution exports, customizable styling
- **Real-time Updates**: Dynamic chart updates with data changes

### 🧮 Specialized Calculators

#### Sample Size & Power Analysis
- **One-sample**: Proportion and mean calculations
- **Two-sample**: Independent and paired comparisons
- **ANOVA**: Multi-group sample size estimation
- **Power Curves**: Visual power analysis

#### Epidemiological Calculator (EpiCalc)
- **Study Design Support**: Cross-sectional, case-control, cohort studies
- **Risk Measures**: Odds ratios, risk ratios, attributable risk
- **Confidence Intervals**: Exact and approximate methods
- **Matched Analysis**: McNemar's test, conditional logistic regression

### 🎓 Educational Features
- **Guided Workflows**: Step-by-step analysis tutorials
- **Statistical Interpretation**: Automated result interpretation
- **Method Selection Guide**: "Which Test" decision tree
- **Video Tutorials**: Integrated YouTube learning content
- **Tip of the Day**: Daily statistical tips and best practices

### 📱 Technical Features
- **Progressive Web App**: Offline functionality, mobile optimization
- **Responsive Design**: Seamless experience across devices
- **Real-time Collaboration**: Shared datasets and results
- **Export Capabilities**: Multiple formats (CSV, Excel, PDF, images)
- **Keyboard Shortcuts**: Power user productivity features

## Technology Stack

- **Frontend Framework**: React v18 with TypeScript
- **UI Components**: Material-UI v5
- **Visualization Libraries**: Recharts, D3.js
- **Statistical Computation**: JStat, Math.js
- **Data Parsing**: PapaParse
- **Build Tool**: Vite

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) directory:

- **[📖 Complete Documentation Index](./docs/README.md)** - Start here for navigation
- **[🏗️ Technical Architecture](./docs/TECHNICAL_ARCHITECTURE.md)** - System design and patterns
- **[🔧 API Reference](./docs/API_REFERENCE.md)** - Complete API documentation
- **[👨‍💻 Development Guide](./docs/DEVELOPMENT_GUIDE.md)** - Development workflows and patterns
- **[👤 User Guide](./docs/USER_GUIDE.md)** - Complete user manual
- **[⚡ Quick Start](./docs/QUICK_START.md)** - Get started in minutes

## 🚀 Getting Started

### Prerequisites
- Node.js 18 or higher
- npm or yarn package manager
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Quick Setup

1. **Clone and Install**:
```bash
git clone [repository-url]
cd DataStatPro
npm install
```

2. **Environment Configuration**:
```bash
cp .env.example .env.local
# Edit .env.local with your Supabase credentials (optional for basic usage)
```

3. **Start Development Server**:
```bash
npm run dev
```

4. **Open Application**:
Navigate to `http://localhost:5173` in your browser

### Production Build

```bash
npm run build    # Build for production
npm run preview  # Preview production build locally
```

### Docker Deployment (Optional)

```bash
docker build -t datastatpro .
docker run -p 3000:3000 datastatpro
```

## Usage

1. **Import Data**: Start by importing your dataset via CSV or generate sample data
2. **Explore Data**: Use descriptive statistics to understand your data's characteristics
3. **Visualize Data**: Create charts to visualize distributions and relationships
4. **Perform Analysis**: Run statistical tests to answer your research questions
5. **Export Results**: Save or export your results for reporting

For detailed instructions on using each feature, please refer to the [User Guide](docs/USER_GUIDE.md).

## Project Structure

```
DataStatPro/
├── docs/                      # Documentation
│   ├── USER_GUIDE.md         # Comprehensive user guide
│   └── TESTING.md            # Testing documentation
├── src/
│   ├── components/
│   │   ├── Auth/              # Authentication components
│   │   ├── Layout/            # Main layout components
│   │   ├── DataManagement/    # Data import, export, editing
│   │   ├── DescriptiveStats/  # Descriptive statistics components
│   │   ├── InferentialStats/  # Statistical inference components
│   │   ├── CorrelationAnalysis/# Correlation and regression tools
│   │   ├── Visualization/     # Visualization components
│   │   ├── PivotAnalysis/     # Pivot table analysis components
│   │   ├── SampleSizeCalculators/ # Sample size calculation tools
│   │   ├── EpiCalc/           # Epidemiological calculators
│   │   ├── ResultsManager/    # Analysis results management
│   │   └── UI/                # Reusable UI components
│   ├── context/               # React context providers
│   ├── pages/                 # Page components
│   ├── types/                 # TypeScript type definitions
│   ├── utils/                 # Utility functions and statistical methods
│   ├── theme.ts               # Theme configuration
│   ├── App.tsx                # Main application component
│   └── main.tsx               # Entry point
├── public/                    # Static assets
├── index.html                 # HTML entry point
└── package.json               # Project configuration
```

## Key Components

### Data Management

The data management module allows users to:

- Import data from CSV files
- Generate sample datasets for practice
- Edit data in a spreadsheet-like interface
- Transform variables using various methods
- Export data in multiple formats

### Statistical Analysis

Statistica provides a comprehensive set of statistical tools:

- **Descriptive Statistics**: Summarize and understand your data's properties
- **Inferential Statistics**: Test hypotheses and make inferences about populations
- **Correlation Analysis**: Examine relationships between variables and create predictive models

### Visualization

The visualization module offers interactive charts that can be customized with:

- Different color schemes
- Custom titles and labels
- Various display options
- Export capabilities

## 🤝 Contributing

We welcome contributions from the community! Please review our documentation before contributing:

1. **Read the [Development Guide](./docs/DEVELOPMENT_GUIDE.md)** for coding standards and patterns
2. **Check the [Technical Architecture](./docs/TECHNICAL_ARCHITECTURE.md)** to understand the system
3. **Follow existing patterns** and maintain consistency with the codebase
4. **Write tests** for new features and bug fixes
5. **Update documentation** when adding new features

### Development Workflow
```bash
# Fork the repository and create a feature branch
git checkout -b feature/your-feature-name

# Make your changes following the development guide
# Write tests and update documentation

# Run tests and linting
npm run test
npm run lint

# Submit a pull request
```

## 🤖 AI Assistant Integration

This project is optimized for AI-assisted development. The comprehensive documentation in the `docs/` directory provides:

- **Complete API references** for accurate code generation
- **Architectural patterns** for consistent implementations
- **Development workflows** for common tasks
- **Type definitions** for TypeScript accuracy

AI assistants should start with the [Documentation Index](./docs/README.md) for optimal context.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- Built with React, TypeScript, and Material-UI
- Statistical computations powered by JStat and Math.js
- Visualizations created with Recharts and D3.js
- Cloud infrastructure provided by Supabase
- Payment processing by Stripe

## 🆘 Support

- **Documentation**: Start with the [docs/](./docs/) directory
- **Issues**: Open an issue on GitHub for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions and community support
- **Email**: Contact our support team for enterprise inquiries

---

**DataStatPro** - *Empowering everyone with accessible, comprehensive statistical analysis tools.*

*🎓 Perfect for students, researchers, educators, and professionals*
*📊 From basic descriptive statistics to advanced survival analysis*
*🌐 Available online with offline PWA capabilities*