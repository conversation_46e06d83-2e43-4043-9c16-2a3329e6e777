import React, { useState, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Paper,
  Divider,
  Stack,
  TextField,
  Slider,
  Switch,
  FormControlLabel,
  Tab,
  Tabs,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Delete,
  Preview,
  Settings,
  Info,
  CheckCircle,
  Error as ErrorIcon,
  Image as ImageIcon,
  CallMerge,
  GridView,
  ViewColumn,
  ViewArray,
  ExpandMore
} from '@mui/icons-material';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface ProcessedImage {
  id: string;
  originalFile: File;
  originalUrl: string;
  processedUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
  originalDimensions?: { width: number; height: number };
  processedDimensions?: { width: number; height: number };
  position?: { x: number; y: number }; // For figure combination
  scale?: number; // For figure combination
}

interface JournalPreset {
  name: string;
  dpi: number;
  format: 'PNG' | 'TIFF' | 'JPEG';
  quality: number;
  description: string;
}

interface CombinationLayout {
  name: string;
  rows: number;
  cols: number;
  description: string;
}

const JOURNAL_PRESETS: JournalPreset[] = [
  { name: 'Nature/Science', dpi: 300, format: 'TIFF', quality: 100, description: 'High-resolution TIFF for top-tier journals' },
  { name: 'PLOS ONE', dpi: 300, format: 'PNG', quality: 100, description: 'PNG format preferred by PLOS journals' },
  { name: 'Elsevier Journals', dpi: 300, format: 'JPEG', quality: 95, description: 'High-quality JPEG for Elsevier publications' },
  { name: 'IEEE Publications', dpi: 600, format: 'TIFF', quality: 100, description: 'Ultra-high resolution for IEEE standards' },
  { name: 'Web/Presentation', dpi: 150, format: 'PNG', quality: 90, description: 'Optimized for web and presentations' },
  { name: 'Print Publication', dpi: 300, format: 'TIFF', quality: 100, description: 'Standard print quality' }
];

const COMBINATION_LAYOUTS: CombinationLayout[] = [
  { name: 'Horizontal (1×2)', rows: 1, cols: 2, description: 'Side by side arrangement' },
  { name: 'Vertical (2×1)', rows: 2, cols: 1, description: 'Stacked vertically' },
  { name: 'Grid (2×2)', rows: 2, cols: 2, description: '2x2 grid layout' },
  { name: 'Triple Horizontal (1×3)', rows: 1, cols: 3, description: 'Three figures in a row' },
  { name: 'Triple Vertical (3×1)', rows: 3, cols: 1, description: 'Three figures stacked' },
  { name: 'Custom', rows: 0, cols: 0, description: 'Custom positioning' }
];

const DPI_OPTIONS = [72, 150, 300, 600, 1200];
const FORMAT_OPTIONS = ['PNG', 'JPEG', 'TIFF'] as const;
type ImageFormat = typeof FORMAT_OPTIONS[number];

export const EnhancedFigureProcessor: React.FC = () => {
  const [images, setImages] = useState<ProcessedImage[]>([]);
  const [selectedDPI, setSelectedDPI] = useState<number>(300);
  const [selectedFormat, setSelectedFormat] = useState<ImageFormat>('PNG');
  const [jpegQuality, setJpegQuality] = useState<number>(95);
  const [showSettings, setShowSettings] = useState(false);
  const [previewImage, setPreviewImage] = useState<ProcessedImage | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [combinationMode, setCombinationMode] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState<CombinationLayout>(COMBINATION_LAYOUTS[0]);
  const [combinedImageUrl, setCombinedImageUrl] = useState<string | null>(null);
  const [spacing, setSpacing] = useState(20);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [showLabels, setShowLabels] = useState(true);
  const [labelStyle, setLabelStyle] = useState({ fontSize: 16, fontFamily: 'Arial', color: '#000000' });
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newImages: ProcessedImage[] = Array.from(files).map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      originalFile: file,
      originalUrl: URL.createObjectURL(file),
      status: 'pending'
    }));

    setImages(prev => [...prev, ...newImages]);
    
    // Load dimensions for each image
    newImages.forEach(loadImageDimensions);
  }, []);

  const loadImageDimensions = (image: ProcessedImage) => {
    const img = new Image();
    img.onload = () => {
      setImages(prev => prev.map(p => 
        p.id === image.id 
          ? { ...p, originalDimensions: { width: img.width, height: img.height } }
          : p
      ));
    };
    img.src = image.originalUrl;
  };

  const convertImageDPI = async (image: ProcessedImage): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) throw new Error('Canvas context not available');

          // Calculate new dimensions based on DPI
          const scaleFactor = selectedDPI / 72; // Assuming original is 72 DPI
          canvas.width = img.width * scaleFactor;
          canvas.height = img.height * scaleFactor;

          // Set canvas DPI
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          
          // Draw scaled image
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

          // Convert to desired format
          let mimeType = 'image/png';
          let quality = 1;
          
          if (selectedFormat === 'JPEG') {
            mimeType = 'image/jpeg';
            quality = jpegQuality / 100;
          } else if (selectedFormat === 'TIFF') {
            // Note: Canvas doesn't natively support TIFF, using PNG as fallback
            mimeType = 'image/png';
          }

          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              resolve(url);
            } else {
              reject(new Error('Failed to convert image'));
            }
          }, mimeType, quality);
        } catch (error) {
          reject(error);
        }
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = image.originalUrl;
    });
  };

  const processImages = async () => {
    const pendingImages = images.filter(img => img.status === 'pending');
    
    for (const image of pendingImages) {
      setImages(prev => prev.map(p => 
        p.id === image.id ? { ...p, status: 'processing' } : p
      ));

      try {
        const processedUrl = await convertImageDPI(image);
        
        // Load processed dimensions
        const img = new Image();
        img.onload = () => {
          setImages(prev => prev.map(p => 
            p.id === image.id 
              ? { 
                  ...p, 
                  processedUrl, 
                  status: 'completed',
                  processedDimensions: { width: img.width, height: img.height }
                }
              : p
          ));
        };
        img.src = processedUrl;
      } catch (error) {
        setImages(prev => prev.map(p => 
          p.id === image.id 
            ? { ...p, status: 'error', error: error instanceof Error ? error.message : 'Unknown error' }
            : p
        ));
      }
    }
  };

  const combineFigures = async () => {
    const completedImages = images.filter(img => img.status === 'completed' && img.processedUrl);
    if (completedImages.length < 2) {
      alert('Please process at least 2 images before combining');
      return;
    }

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context not available');

      // Calculate canvas size based on layout
      let canvasWidth = 0;
      let canvasHeight = 0;
      
      if (selectedLayout.name !== 'Custom') {
        // Calculate max dimensions for grid layout
        const maxWidth = Math.max(...completedImages.map(img => img.processedDimensions?.width || 0));
        const maxHeight = Math.max(...completedImages.map(img => img.processedDimensions?.height || 0));
        
        canvasWidth = (maxWidth * selectedLayout.cols) + (spacing * (selectedLayout.cols + 1));
        canvasHeight = (maxHeight * selectedLayout.rows) + (spacing * (selectedLayout.rows + 1));
      } else {
        // For custom layout, calculate based on positions
        canvasWidth = 1200; // Default size
        canvasHeight = 800;
      }

      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      // Fill background
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // Load and draw images
      const imagePromises = completedImages.map((image, index) => {
        return new Promise<void>((resolve) => {
          const img = new Image();
          img.onload = () => {
            let x, y;
            
            if (selectedLayout.name !== 'Custom') {
              // Calculate position based on grid layout
              const row = Math.floor(index / selectedLayout.cols);
              const col = index % selectedLayout.cols;
              
              const cellWidth = (canvasWidth - spacing * (selectedLayout.cols + 1)) / selectedLayout.cols;
              const cellHeight = (canvasHeight - spacing * (selectedLayout.rows + 1)) / selectedLayout.rows;
              
              x = spacing + (col * (cellWidth + spacing));
              y = spacing + (row * (cellHeight + spacing));
              
              // Center image in cell
              const imgAspect = img.width / img.height;
              const cellAspect = cellWidth / cellHeight;
              
              let drawWidth, drawHeight;
              if (imgAspect > cellAspect) {
                drawWidth = cellWidth;
                drawHeight = cellWidth / imgAspect;
                y += (cellHeight - drawHeight) / 2;
              } else {
                drawHeight = cellHeight;
                drawWidth = cellHeight * imgAspect;
                x += (cellWidth - drawWidth) / 2;
              }
              
              ctx.drawImage(img, x, y, drawWidth, drawHeight);
            } else {
              // Custom positioning
              x = image.position?.x || (index * 200);
              y = image.position?.y || (index * 200);
              const scale = image.scale || 1;
              
              ctx.drawImage(img, x, y, img.width * scale, img.height * scale);
            }
            
            // Add labels if enabled
            if (showLabels) {
              ctx.fillStyle = labelStyle.color;
              ctx.font = `${labelStyle.fontSize}px ${labelStyle.fontFamily}`;
              ctx.fillText(`(${String.fromCharCode(65 + index)})`, x + 5, y + labelStyle.fontSize + 5);
            }
            
            resolve();
          };
          img.src = image.processedUrl!;
        });
      });

      await Promise.all(imagePromises);

      // Convert to blob and create URL
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setCombinedImageUrl(url);
        }
      }, `image/${selectedFormat.toLowerCase()}`, selectedFormat === 'JPEG' ? jpegQuality / 100 : 1);
    } catch (error) {
      console.error('Error combining figures:', error);
      alert('Failed to combine figures');
    }
  };

  const downloadSingle = (image: ProcessedImage) => {
    if (!image.processedUrl) return;
    
    const link = document.createElement('a');
    link.href = image.processedUrl;
    const extension = selectedFormat.toLowerCase();
    link.download = `${image.originalFile.name.split('.')[0]}_${selectedDPI}dpi.${extension}`;
    link.click();
  };

  const downloadAll = async () => {
    const completedImages = images.filter(img => img.processedUrl);
    if (completedImages.length === 0) return;

    const zip = new JSZip();
    
    for (const image of completedImages) {
      try {
        const response = await fetch(image.processedUrl!);
        const blob = await response.blob();
        const extension = selectedFormat.toLowerCase();
        const filename = `${image.originalFile.name.split('.')[0]}_${selectedDPI}dpi.${extension}`;
        zip.file(filename, blob);
      } catch (error) {
        console.error(`Failed to add ${image.originalFile.name} to zip:`, error);
      }
    }

    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, `processed_figures_${selectedDPI}dpi.zip`);
  };

  const downloadCombined = () => {
    if (!combinedImageUrl) return;
    
    const link = document.createElement('a');
    link.href = combinedImageUrl;
    const extension = selectedFormat.toLowerCase();
    link.download = `combined_figure_${selectedDPI}dpi.${extension}`;
    link.click();
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const updated = prev.filter(img => img.id !== id);
      return updated;
    });
  };

  const clearAll = () => {
    images.forEach(img => {
      URL.revokeObjectURL(img.originalUrl);
      if (img.processedUrl) URL.revokeObjectURL(img.processedUrl);
    });
    setImages([]);
    if (combinedImageUrl) {
      URL.revokeObjectURL(combinedImageUrl);
      setCombinedImageUrl(null);
    }
  };

  const applyPreset = (preset: JournalPreset) => {
    setSelectedDPI(preset.dpi);
    setSelectedFormat(preset.format);
    setJpegQuality(preset.quality);
    setShowSettings(false);
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Enhanced Figure Processor
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Process, convert, and combine figures for publication. Supports DPI conversion, format optimization, and multi-figure layouts.
        </Typography>

        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
          <Tab label="Individual Processing" />
          <Tab label="Figure Combination" />
        </Tabs>

        {/* File Upload */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <input
              type="file"
              ref={fileInputRef}
              onChange={(e) => handleFileSelect(e.target.files)}
              accept="image/*"
              multiple
              style={{ display: 'none' }}
            />
            
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 4,
                textAlign: 'center',
                cursor: 'pointer',
                '&:hover': { borderColor: 'primary.main' }
              }}
              onClick={() => fileInputRef.current?.click()}
            >
              <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Upload Images
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Click to select or drag and drop multiple image files
              </Typography>
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Supports PNG, JPEG, TIFF, and other common formats
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {activeTab === 0 && (
          <>
            {/* Processing Settings */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Processing Settings</Typography>
                  <Button
                    startIcon={<Settings />}
                    onClick={() => setShowSettings(true)}
                    variant="outlined"
                    size="small"
                  >
                    Journal Presets
                  </Button>
                </Box>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>DPI</InputLabel>
                      <Select
                        value={selectedDPI}
                        onChange={(e) => setSelectedDPI(Number(e.target.value))}
                        label="DPI"
                      >
                        {DPI_OPTIONS.map((dpi) => (
                          <MenuItem key={dpi} value={dpi}>
                            {dpi} DPI
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Format</InputLabel>
                      <Select
                        value={selectedFormat}
                        onChange={(e) => setSelectedFormat(e.target.value as ImageFormat)}
                        label="Format"
                      >
                        {FORMAT_OPTIONS.map((format) => (
                          <MenuItem key={format} value={format}>
                            {format}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  {selectedFormat === 'JPEG' && (
                    <Grid item xs={12} sm={4}>
                      <Typography gutterBottom>Quality: {jpegQuality}%</Typography>
                      <Slider
                        value={jpegQuality}
                        onChange={(_, value) => setJpegQuality(value as number)}
                        min={10}
                        max={100}
                        step={5}
                        marks
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                  )}
                </Grid>
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={processImages}
                    disabled={images.filter(img => img.status === 'pending').length === 0}
                  >
                    Process Images
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={downloadAll}
                    disabled={images.filter(img => img.status === 'completed').length === 0}
                  >
                    Download All
                  </Button>
                  
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={clearAll}
                    disabled={images.length === 0}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </>
        )}

        {activeTab === 1 && (
          <>
            {/* Figure Combination Settings */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>Figure Combination</Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Layout</InputLabel>
                      <Select
                        value={selectedLayout.name}
                        onChange={(e) => {
                          const layout = COMBINATION_LAYOUTS.find(l => l.name === e.target.value);
                          if (layout) setSelectedLayout(layout);
                        }}
                        label="Layout"
                      >
                        {COMBINATION_LAYOUTS.map((layout) => (
                          <MenuItem key={layout.name} value={layout.name}>
                            {layout.name} - {layout.description}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={3}>
                    <Typography gutterBottom>Spacing: {spacing}px</Typography>
                    <Slider
                      value={spacing}
                      onChange={(_, value) => setSpacing(value as number)}
                      min={0}
                      max={100}
                      step={5}
                      valueLabelDisplay="auto"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={3}>
                    <TextField
                      fullWidth
                      label="Background Color"
                      type="color"
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                    />
                  </Grid>
                </Grid>
                
                <Accordion sx={{ mt: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography>Label Settings</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={showLabels}
                              onChange={(e) => setShowLabels(e.target.checked)}
                            />
                          }
                          label="Show Labels (A, B, C...)"
                        />
                      </Grid>
                      {showLabels && (
                        <>
                          <Grid item xs={4}>
                            <TextField
                              fullWidth
                              label="Font Size"
                              type="number"
                              value={labelStyle.fontSize}
                              onChange={(e) => setLabelStyle(prev => ({ ...prev, fontSize: Number(e.target.value) }))}
                            />
                          </Grid>
                          <Grid item xs={4}>
                            <TextField
                              fullWidth
                              label="Font Family"
                              value={labelStyle.fontFamily}
                              onChange={(e) => setLabelStyle(prev => ({ ...prev, fontFamily: e.target.value }))}
                            />
                          </Grid>
                          <Grid item xs={4}>
                            <TextField
                              fullWidth
                              label="Color"
                              type="color"
                              value={labelStyle.color}
                              onChange={(e) => setLabelStyle(prev => ({ ...prev, color: e.target.value }))}
                            />
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<CallMerge />}
                    onClick={combineFigures}
                    disabled={images.filter(img => img.status === 'completed').length < 2}
                  >
                    Combine Figures
                  </Button>
                  
                  {combinedImageUrl && (
                    <Button
                      variant="outlined"
                      startIcon={<Download />}
                      onClick={downloadCombined}
                    >
                      Download Combined
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
            
            {/* Combined Figure Preview */}
            {combinedImageUrl && (
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Combined Figure Preview</Typography>
                  <Box sx={{ textAlign: 'center', mt: 2 }}>
                    <img 
                      src={combinedImageUrl} 
                      alt="Combined Figure" 
                      style={{ maxWidth: '100%', maxHeight: '400px', border: '1px solid #ddd' }}
                    />
                  </Box>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {/* Image List */}
        {images.length > 0 && (
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Images ({images.length})
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip 
                    label={`Pending: ${images.filter(img => img.status === 'pending').length}`} 
                    color="default" 
                    size="small" 
                  />
                  <Chip 
                    label={`Processing: ${images.filter(img => img.status === 'processing').length}`} 
                    color="warning" 
                    size="small" 
                  />
                  <Chip 
                    label={`Completed: ${images.filter(img => img.status === 'completed').length}`} 
                    color="success" 
                    size="small" 
                  />
                  <Chip 
                    label={`Errors: ${images.filter(img => img.status === 'error').length}`} 
                    color="error" 
                    size="small" 
                  />
                </Box>
              </Box>
              
              <List>
                {images.map((image) => (
                  <ListItem key={image.id} divider>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2">
                            {image.originalFile.name}
                          </Typography>
                          {image.status === 'completed' && <CheckCircle color="success" fontSize="small" />}
                          {image.status === 'error' && <ErrorIcon color="error" fontSize="small" />}
                          {image.status === 'processing' && <LinearProgress sx={{ width: 100, ml: 1 }} />}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Size: {(image.originalFile.size / 1024 / 1024).toFixed(2)} MB
                          </Typography>
                          {image.originalDimensions && (
                            <Typography variant="body2" color="text.secondary">
                              Original: {image.originalDimensions.width} × {image.originalDimensions.height}px
                            </Typography>
                          )}
                          {image.processedDimensions && (
                            <Typography variant="body2" color="text.secondary">
                              Processed: {image.processedDimensions.width} × {image.processedDimensions.height}px
                            </Typography>
                          )}
                          {image.error && (
                            <Alert severity="error" sx={{ mt: 1 }}>
                              {image.error}
                            </Alert>
                          )}
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {image.processedUrl && (
                          <>
                            <Tooltip title="Preview">
                              <IconButton
                                size="small"
                                onClick={() => setPreviewImage(image)}
                              >
                                <Preview />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Download">
                              <IconButton
                                size="small"
                                onClick={() => downloadSingle(image)}
                              >
                                <Download />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                        
                        <Tooltip title="Remove">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => removeImage(image.id)}
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        )}

        {/* Settings Dialog */}
        <Dialog open={showSettings} onClose={() => setShowSettings(false)} maxWidth="md" fullWidth>
          <DialogTitle>Journal Presets & Advanced Settings</DialogTitle>
          <DialogContent>
            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
              Journal-Specific Presets
            </Typography>
            
            <Grid container spacing={2}>
              {JOURNAL_PRESETS.map((preset) => (
                <Grid item xs={12} sm={6} key={preset.name}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      '&:hover': { bgcolor: 'action.hover' }
                    }}
                    onClick={() => applyPreset(preset)}
                  >
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        {preset.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {preset.description}
                      </Typography>
                      <Typography variant="body2">
                        {preset.dpi} DPI • {preset.format} • {preset.quality}% quality
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
            
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                <strong>DPI Guidelines:</strong><br />
                • 72-150 DPI: Web and presentations<br />
                • 300 DPI: Standard print quality<br />
                • 600+ DPI: High-resolution print and archival
              </Typography>
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowSettings(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog 
          open={!!previewImage} 
          onClose={() => setPreviewImage(null)}
          maxWidth="lg"
          fullWidth
        >
          <DialogTitle>
            Preview: {previewImage?.originalFile.name}
          </DialogTitle>
          <DialogContent>
            {previewImage && (
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Box sx={{ flex: 1, minWidth: 300 }}>
                  <Typography variant="subtitle2" gutterBottom>Original</Typography>
                  <img 
                    src={previewImage.originalUrl} 
                    alt="Original" 
                    style={{ width: '100%', height: 'auto', border: '1px solid #ddd' }}
                  />
                  {previewImage.originalDimensions && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {previewImage.originalDimensions.width} × {previewImage.originalDimensions.height}px
                    </Typography>
                  )}
                </Box>
                
                {previewImage.processedUrl && (
                  <Box sx={{ flex: 1, minWidth: 300 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Processed ({selectedDPI} DPI, {selectedFormat})
                    </Typography>
                    <img 
                      src={previewImage.processedUrl} 
                      alt="Processed" 
                      style={{ width: '100%', height: 'auto', border: '1px solid #ddd' }}
                    />
                    {previewImage.processedDimensions && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {previewImage.processedDimensions.width} × {previewImage.processedDimensions.height}px
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewImage(null)}>Close</Button>
            {previewImage?.processedUrl && (
              <Button 
                variant="contained" 
                startIcon={<Download />}
                onClick={() => {
                  if (previewImage) downloadSingle(previewImage);
                  setPreviewImage(null);
                }}
              >
                Download
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </Box>
    </PublicationReadyGate>
  );
};

export default EnhancedFigureProcessor;