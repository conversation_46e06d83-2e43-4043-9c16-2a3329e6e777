# Project Organization Access Control

This document outlines the access control implementation for the project organization features in DataStatPro.

## Overview

Project organization features are restricted to Pro users only, following the existing `canAccessProFeatures` pattern used throughout the application. Guest Access and Standard Account users continue to use the flat results list for backward compatibility.

## Access Control Implementation

### 1. ProjectSelector Component
- **Location**: `src/components/ProjectManager/ProjectSelector.tsx`
- **Access Control**: 
  - Hides the project selector entirely if user is not Pro and only has the default project
  - Shows create button only for Pro users
  - Uses `canAccessProFeatures` from AuthContext

### 2. ProjectManagementDialog Component
- **Location**: `src/components/ProjectManager/ProjectManagementDialog.tsx`
- **Access Control**:
  - Shows informational alert for non-Pro users explaining the feature is for Pro users
  - Hides project creation button for non-Pro users
  - Disables cloud storage features for non-Pro users
  - Only loads cloud projects for Pro users

### 3. AddToResultsButton Component
- **Location**: `src/components/UI/AddToResultsButton.tsx`
- **Access Control**:
  - Shows project selection dialog only for Pro users with multiple projects
  - Non-Pro users directly add to default project without dialog
  - Shows Pro feature information only to Pro users

### 4. ResultsManager Component
- **Location**: `src/components/ResultsManager/ResultsManager.tsx`
- **Access Control**:
  - Shows "Manage Projects" button only for Pro users
  - Shows ProjectSelector only for Pro users
  - Uses project-organized view for Pro users, flat view for non-Pro users
  - Shows result move menu only for Pro users

## User Experience by Account Type

### Guest Access Users
- See flat results list (backward compatible)
- No project organization features visible
- Can still use all Pro features with sample data
- Results automatically go to default project

### Standard Account Users
- See flat results list (backward compatible)
- No project organization features visible
- Results automatically go to default project

### Pro/Educational Account Users
- See project-organized results with expandable folders
- Can create, manage, and delete projects
- Can move results between projects
- Can save projects to cloud storage (up to 2 projects)
- Can load projects from cloud storage
- Full project management interface available

## Technical Implementation

### Authentication Check
All components use the `canAccessProFeatures` boolean from the AuthContext:

```typescript
const { canAccessProFeatures } = useAuth();
```

### Graceful Degradation
- Non-Pro users see the traditional flat results view
- All existing functionality remains unchanged for non-Pro users
- Pro users get enhanced project organization features
- Backward compatibility is maintained for existing results

### Cloud Storage Limits
- Pro users can save up to 2 projects to cloud storage
- Same 2MB size limit per project as datasets
- Uses existing Supabase infrastructure and RLS policies

## Database Schema

### New Tables
- `user_projects`: Stores project metadata
- Storage bucket: `userprojects` for project data files

### Enhanced ResultItem
- Added optional `projectId` field for project association
- Backward compatible - existing results without projectId go to default project

## Security

- Row Level Security (RLS) policies ensure users can only access their own projects
- Cloud storage policies mirror existing dataset storage patterns
- All project operations require authentication
- Pro user verification happens at UI level using existing auth patterns
