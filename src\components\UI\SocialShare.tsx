import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Button,
  Typography,
  Snackbar,
  Alert,
  useTheme,
  alpha,
  Divider,
  Paper
} from '@mui/material';
import {
  Share as ShareIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Email as EmailIcon,
  Link as LinkIcon,
  WhatsApp as WhatsAppIcon,
  Telegram as TelegramIcon,
  Reddit as RedditIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';
import useSocialMeta from '../../hooks/useSocialMeta';

interface SocialShareProps {
  url?: string;
  title?: string;
  description?: string;
  hashtags?: string[];
  via?: string; // Twitter handle
  variant?: 'menu' | 'buttons' | 'compact';
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
  platforms?: SocialPlatform[];
}

type SocialPlatform = 
  | 'facebook'
  | 'twitter'
  | 'linkedin'
  | 'email'
  | 'whatsapp'
  | 'telegram'
  | 'reddit'
  | 'copy';

interface SharePlatform {
  name: string;
  icon: React.ReactNode;
  color: string;
  getUrl: (params: ShareParams) => string;
  action?: (params: ShareParams) => void;
}

interface ShareParams {
  url: string;
  title: string;
  description: string;
  hashtags: string[];
  via?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description,
  hashtags = ['DataStatPro', 'Statistics', 'Research', 'DataAnalysis'],
  via = 'datastatpro',
  variant = 'menu',
  size = 'medium',
  showLabel = false,
  platforms = ['facebook', 'twitter', 'linkedin', 'email', 'whatsapp', 'copy']
}) => {
  // Use social URL from useSocialMeta when no URL is provided
  // This ensures social media crawlers get clean URLs without /app prefix
  const { getCurrentMeta } = useSocialMeta();
  const socialUrl = url || getCurrentMeta().url;
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({ open: false, message: '', severity: 'success' });

  const shareParams: ShareParams = {
    url: encodeURIComponent(socialUrl),
    title: encodeURIComponent(title || 'DataStatPro - Free Statistical Software'),
    description: encodeURIComponent(description || 'Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software.'),
    hashtags,
    via
  };

  const sharePlatforms: Record<SocialPlatform, SharePlatform> = {
    facebook: {
      name: 'Facebook',
      icon: <FacebookIcon />,
      color: '#1877F2',
      getUrl: (params) => 
        `https://www.facebook.com/sharer/sharer.php?u=${params.url}&quote=${params.title}%20-%20${params.description}`
    },
    twitter: {
      name: 'Twitter',
      icon: <TwitterIcon />,
      color: '#1DA1F2',
      getUrl: (params) => {
        const hashtagsStr = params.hashtags.map(tag => `%23${tag}`).join('%20');
        return `https://twitter.com/intent/tweet?url=${params.url}&text=${params.title}%20-%20${params.description}&hashtags=${params.hashtags.join(',')}&via=${params.via}`;
      }
    },
    linkedin: {
      name: 'LinkedIn',
      icon: <LinkedInIcon />,
      color: '#0A66C2',
      getUrl: (params) => 
        `https://www.linkedin.com/sharing/share-offsite/?url=${params.url}&title=${params.title}&summary=${params.description}`
    },
    email: {
      name: 'Email',
      icon: <EmailIcon />,
      color: '#EA4335',
      getUrl: (params) => 
        `mailto:?subject=${params.title}&body=${params.description}%0A%0A${decodeURIComponent(params.url)}`
    },
    whatsapp: {
      name: 'WhatsApp',
      icon: <WhatsAppIcon />,
      color: '#25D366',
      getUrl: (params) => 
        `https://wa.me/?text=${params.title}%20-%20${params.description}%0A${params.url}`
    },
    telegram: {
      name: 'Telegram',
      icon: <TelegramIcon />,
      color: '#0088CC',
      getUrl: (params) => 
        `https://t.me/share/url?url=${params.url}&text=${params.title}%20-%20${params.description}`
    },
    reddit: {
      name: 'Reddit',
      icon: <RedditIcon />,
      color: '#FF4500',
      getUrl: (params) => 
        `https://reddit.com/submit?url=${params.url}&title=${params.title}`
    },
    copy: {
      name: 'Copy Link',
      icon: <ContentCopyIcon />,
      color: theme.palette.text.primary,
      getUrl: () => '',
      action: async (params) => {
        try {
          await navigator.clipboard.writeText(decodeURIComponent(params.url));
          setNotification({
            open: true,
            message: 'Link copied to clipboard!',
            severity: 'success'
          });
        } catch (error) {
          setNotification({
            open: true,
            message: 'Failed to copy link',
            severity: 'error'
          });
        }
      }
    }
  };

  const handleShare = (platform: SocialPlatform) => {
    const sharePlatform = sharePlatforms[platform];
    
    if (sharePlatform.action) {
      sharePlatform.action(shareParams);
    } else {
      const shareUrl = sharePlatform.getUrl(shareParams);
      window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
    }
    
    setAnchorEl(null);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Native Web Share API support
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url
        });
      } catch (error) {
        console.log('Native sharing cancelled or failed');
      }
    }
  };

  const iconSize = size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium';

  if (variant === 'menu') {
    return (
      <>
        <Tooltip title="Share">
          <IconButton
            onClick={handleMenuOpen}
            size={iconSize}
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                color: theme.palette.primary.main,
                backgroundColor: alpha(theme.palette.primary.main, 0.1)
              }
            }}
          >
            <ShareIcon />
          </IconButton>
        </Tooltip>
        
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            sx: {
              minWidth: 200,
              '& .MuiMenuItem-root': {
                py: 1
              }
            }
          }}
        >
          {/* Native share option if available */}
          {navigator.share && (
            <>
              <MenuItem onClick={handleNativeShare}>
                <ListItemIcon>
                  <ShareIcon sx={{ color: theme.palette.primary.main }} />
                </ListItemIcon>
                <ListItemText primary="Share..." />
              </MenuItem>
              <Divider />
            </>
          )}
          
          {platforms.map((platform) => {
            const sharePlatform = sharePlatforms[platform];
            return (
              <MenuItem key={platform} onClick={() => handleShare(platform)}>
                <ListItemIcon>
                  {React.cloneElement(sharePlatform.icon as React.ReactElement, {
                    sx: { color: sharePlatform.color }
                  })}
                </ListItemIcon>
                <ListItemText primary={sharePlatform.name} />
              </MenuItem>
            );
          })}
        </Menu>
      </>
    );
  }

  if (variant === 'buttons') {
    return (
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {platforms.map((platform) => {
          const sharePlatform = sharePlatforms[platform];
          return (
            <Button
              key={platform}
              variant="outlined"
              size={size}
              startIcon={sharePlatform.icon}
              onClick={() => handleShare(platform)}
              sx={{
                borderColor: alpha(sharePlatform.color, 0.3),
                color: sharePlatform.color,
                '&:hover': {
                  borderColor: sharePlatform.color,
                  backgroundColor: alpha(sharePlatform.color, 0.1)
                }
              }}
            >
              {showLabel && sharePlatform.name}
            </Button>
          );
        })}
      </Box>
    );
  }

  if (variant === 'compact') {
    return (
      <Box sx={{ display: 'flex', gap: 0.5 }}>
        {platforms.slice(0, 4).map((platform) => {
          const sharePlatform = sharePlatforms[platform];
          return (
            <Tooltip key={platform} title={`Share on ${sharePlatform.name}`}>
              <IconButton
                size={iconSize}
                onClick={() => handleShare(platform)}
                sx={{
                  color: sharePlatform.color,
                  '&:hover': {
                    backgroundColor: alpha(sharePlatform.color, 0.1)
                  }
                }}
              >
                {sharePlatform.icon}
              </IconButton>
            </Tooltip>
          );
        })}
        
        {platforms.length > 4 && (
          <Tooltip title="More sharing options">
            <IconButton size={iconSize} onClick={handleMenuOpen}>
              <ShareIcon />
            </IconButton>
          </Tooltip>
        )}
        
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {platforms.slice(4).map((platform) => {
            const sharePlatform = sharePlatforms[platform];
            return (
              <MenuItem key={platform} onClick={() => handleShare(platform)}>
                <ListItemIcon>
                  {React.cloneElement(sharePlatform.icon as React.ReactElement, {
                    sx: { color: sharePlatform.color }
                  })}
                </ListItemIcon>
                <ListItemText primary={sharePlatform.name} />
              </MenuItem>
            );
          })}
        </Menu>
      </Box>
    );
  }

  return (
    <>
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleNotificationClose}
          severity={notification.severity}
          variant="filled"
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default SocialShare;