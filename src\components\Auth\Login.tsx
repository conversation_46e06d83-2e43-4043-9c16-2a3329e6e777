import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Link,
  Alert,
  CircularProgress,
  useTheme,
  Snackbar, // Import Snackbar
  IconButton // Import IconButton for Snackbar close action
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close'; // Import Close icon
import { useAuth } from '../../context/AuthContext';
import { Lock as LockIcon, Email as EmailIcon } from '@mui/icons-material';
import GoogleIcon from '@mui/icons-material/Google'; // Import Google icon
import { useNavigate } from 'react-router-dom'; // Import useNavigate

interface LoginProps {
  onRegisterClick: () => void;
  onResetPasswordClick: () => void;
  onGuestLoginClick: () => void; // Now used to navigate to Guest Access tab
}

const Login: React.FC<LoginProps> = ({ 
  onRegisterClick, 
  onResetPasswordClick, 
  onGuestLoginClick // Destructure the prop
}) => {
  const theme = useTheme();
  const { signIn, signInWithGoogle, signInWithMagicLink } = useAuth(); // Add signInWithMagicLink
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openSnackbar, setOpenSnackbar] = useState(false); // State for Snackbar
  const [showMagicLink, setShowMagicLink] = useState(false); // State for magic link mode
  const [magicLinkEmail, setMagicLinkEmail] = useState(''); // State for magic link email
  const [magicLinkLoading, setMagicLinkLoading] = useState(false); // Loading state for magic link
  const [magicLinkSent, setMagicLinkSent] = useState(false); // Success state for magic link
  const navigate = useNavigate(); // Initialize useNavigate

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const { error } = await signIn(email, password);
      if (error) throw error;
      setOpenSnackbar(true); // Show success message
      // Navigation will be handled by AuthContext
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpenSnackbar(false);
  };

  const handleMagicLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setMagicLinkLoading(true);

    try {
      const { error } = await signInWithMagicLink(magicLinkEmail);
      if (error) throw error;
      setMagicLinkSent(true);
    } catch (err: any) {
      setError(err.message || 'Failed to send magic link');
    } finally {
      setMagicLinkLoading(false);
    }
  };

  const toggleMagicLink = () => {
    setShowMagicLink(!showMagicLink);
    setError(null);
    setMagicLinkSent(false);
    setMagicLinkEmail('');
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 400, mx: 'auto', mt: 4 }}>
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <LockIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Sign in to DataStatPro
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Google Sign-In Button - Moved to top */}
      <Button
        fullWidth
        variant="contained"
        color="secondary"
        size="large"
        sx={{ mt: 1, mb: 2 }}
        onClick={signInWithGoogle}
        disabled={loading || magicLinkLoading}
        startIcon={<GoogleIcon />}
      >
        Sign In with Google
      </Button>

      {/* First OR separator - between Google and Magic Link */}
      <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
        <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
        <Typography variant="body2" sx={{ mx: 2, color: 'text.secondary' }}>
          OR
        </Typography>
        <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
      </Box>

      {/* Magic Link Button */}
      <Button
        fullWidth
        variant="outlined"
        color="primary"
        size="large"
        sx={{ mb: 1 }}
        onClick={toggleMagicLink}
        disabled={loading || magicLinkLoading}
        startIcon={<EmailIcon />}
      >
        {showMagicLink ? 'Back to Password Login' : 'Sign In with Magic Link'}
      </Button>
      
      {/* Always visible Magic Link description */}
      {!showMagicLink && (
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            mb: 2, 
            textAlign: 'center', 
            fontSize: '0.875rem',
            fontStyle: 'italic'
          }}
        >
          Passwordless login for registred users - One time secure link to your email
        </Typography>
      )}

      {/* Magic Link Description */}
      {showMagicLink && (
        <Box sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            <strong>What is Magic Link?</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Magic Link is a passwordless authentication method. Simply enter your email address and we'll send you a secure link. Click the link in your email to sign in instantly - no password required!
          </Typography>
        </Box>
      )}

      {/* Magic Link Form */}
      {showMagicLink && (
        <Box>
          {magicLinkSent ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              Magic link sent! Check your email and click the link to sign in.
            </Alert>
          ) : (
            <Box component="form" onSubmit={handleMagicLinkSubmit} sx={{ mb: 2 }}>
              <TextField
                label="Email Address"
                type="email"
                fullWidth
                margin="normal"
                required
                value={magicLinkEmail}
                onChange={(e) => setMagicLinkEmail(e.target.value)}
                disabled={magicLinkLoading}
              />
              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                size="large"
                sx={{ mt: 2 }}
                disabled={magicLinkLoading || !magicLinkEmail.trim()}
              >
                {magicLinkLoading ? <CircularProgress size={24} /> : 'Send Magic Link'}
              </Button>
            </Box>
          )}
        </Box>
      )}

      {/* Second OR separator - between Magic Link and Email/Password form */}
      {!showMagicLink && (
        <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
          <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
          <Typography variant="body2" sx={{ mx: 2, color: 'text.secondary' }}>
            OR
          </Typography>
          <Box sx={{ flexGrow: 1, height: '1px', bgcolor: 'divider' }} />
        </Box>
      )}

      {!showMagicLink && (
        <Box component="form" onSubmit={handleSubmit}>
          <TextField
            label="Email Address"
            type="email"
            fullWidth
            margin="normal"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
          />
          <TextField
            label="Password"
            type="password"
            fullWidth
            margin="normal"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            size="large"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
            disableElevation
          >
            {loading ? <CircularProgress size={24} /> : 'Sign In'}
          </Button>

          <Box sx={{ textAlign: 'center', mt: 2, mb: 2 }}>
            <Typography variant="body2">
              Don't want to create an account?
              <Link
                component="button"
                variant="body2"
                onClick={onGuestLoginClick}
                underline="hover"
                sx={{ ml: 1 }}
              >
                Continue as guest
              </Link>
            </Typography>
          </Box>
        </Box>
      )}

      {showMagicLink && (
        <Box sx={{ textAlign: 'center', mt: 2, mb: 2 }}>
          <Typography variant="body2">
            Don't want to create an account?
            <Link
              component="button"
              variant="body2"
              onClick={onGuestLoginClick}
              underline="hover"
              sx={{ ml: 1 }}
            >
              Continue as guest
            </Link>
          </Typography>
        </Box>
      )}

      {!showMagicLink && (
        <Box display="flex" justifyContent="space-between" mt={1}> 
          <Link
            component="button"
            variant="body2"
            onClick={onResetPasswordClick}
            underline="hover"
          >
            Forgot password?
          </Link>
          <Link
            component="button"
            variant="body2"
            onClick={onRegisterClick}
            underline="hover"
          >
            Don't have an account? Sign up
          </Link>
        </Box>
      )}

      <Snackbar
        open={openSnackbar}
        autoHideDuration={4000} // Hide after 4 seconds
        onClose={handleCloseSnackbar}
        message="You are successfully logged in to DataStatPro app"
        action={
          <IconButton size="small" aria-label="close" color="inherit" onClick={handleCloseSnackbar}>
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      />
    </Paper>
  );
};

export default Login;
