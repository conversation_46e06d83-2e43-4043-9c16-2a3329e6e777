-- Complete Fix for Critical Admin Dashboard Issues Migration
-- This migration addresses all critical issues preventing admin dashboard functionality
-- Date: 2025-07-25

-- Issue 1: Fix get_user_statistics function - Remove REFRESH MATERIALIZED VIEW from STABLE function
-- The main issue is that REFRESH MATERIALIZED VIEW cannot be called from a STABLE function
-- We need to either make it VOL<PERSON><PERSON><PERSON> or remove the REFRESH operation

DROP FUNCTION IF EXISTS public.get_user_statistics();

-- Create a separate VOLATILE function for refreshing the cache
CREATE OR REPLACE FUNCTION public.refresh_user_stats_cache()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
VOLATILE
SET statement_timeout = '30s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Refresh the materialized view
  REFRESH MATERIALIZED VIEW public.user_stats_cache;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail completely
    RAISE WARNING 'Failed to refresh materialized view: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Create the main get_user_statistics function as STABLE (no REFRESH inside)
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '10s'
AS $$
DECLARE
  result JSON;
  cache_age INTERVAL;
  cache_exists BOOLEAN := FALSE;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Check if cache exists and get its age
  SELECT 
    NOW() - last_updated,
    TRUE
  INTO cache_age, cache_exists
  FROM public.user_stats_cache
  LIMIT 1;
  
  -- If cache doesn't exist or is very stale (>1 hour), return calculated stats
  IF NOT cache_exists OR cache_age IS NULL OR cache_age > INTERVAL '1 hour' THEN
    -- Calculate stats directly from profiles table
    SELECT json_build_object(
      'total_users', COUNT(*),
      'admin_users', COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true),
      'pro_users', COUNT(*) FILTER (WHERE accounttype = 'pro'),
      'edu_users', COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')),
      'standard_users', COUNT(*) FILTER (WHERE accounttype = 'standard'),
      'guest_users', COUNT(*) FILTER (WHERE accounttype = 'guest'),
      'users_last_7_days', 0,
      'users_last_30_days', 0,
      'active_users_last_7_days', 0,
      'active_users_last_30_days', 0,
      'cache_updated', NOW(),
      'cache_status', 'calculated_directly'
    ) INTO result
    FROM public.profiles;
  ELSE
    -- Get stats from cache
    SELECT json_build_object(
      'total_users', total_users,
      'admin_users', admin_users,
      'pro_users', pro_users,
      'edu_users', edu_users,
      'standard_users', standard_users,
      'guest_users', guest_users,
      'users_last_7_days', 0,
      'users_last_30_days', 0,
      'active_users_last_7_days', 0,
      'active_users_last_30_days', 0,
      'cache_updated', last_updated,
      'cache_status', 'from_cache'
    ) INTO result
    FROM public.user_stats_cache;
  END IF;

  RETURN result;
END;
$$;

-- Issue 2: Fix get_all_users function - Ensure exact type matching
DROP FUNCTION IF EXISTS public.get_all_users(INTEGER, INTEGER, TEXT);

CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '30s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with explicit type casting to ensure exact matches
  RETURN QUERY
  SELECT 
    p.id,
    COALESCE(p.username, 'No email available')::TEXT as email,
    COALESCE(p.username, '')::TEXT as username,
    COALESCE(p.full_name, '')::TEXT as full_name,
    COALESCE(p.institution, '')::TEXT as institution,
    COALESCE(p.country, '')::TEXT as country,
    COALESCE(p.avatar_url, '')::TEXT as avatar_url,
    COALESCE(p.updated_at, NOW()) as updated_at,
    COALESCE(p.is_admin, false) as is_admin,
    COALESCE(p.accounttype, 'standard')::TEXT as accounttype,
    COALESCE(p.updated_at, NOW()) as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at
  FROM public.profiles p
  WHERE (
    search_term IS NULL OR 
    search_term = '' OR
    COALESCE(p.full_name, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.username, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.institution, '') ILIKE '%' || search_term || '%'
  )
  ORDER BY p.updated_at DESC NULLS LAST
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Issue 3: Create enhanced user statistics function with better error handling
CREATE OR REPLACE FUNCTION public.get_enhanced_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '15s'
AS $$
DECLARE
  result JSON;
  profile_stats JSON;
  dataset_stats JSON;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get profile statistics
  SELECT json_build_object(
    'total_users', COUNT(*),
    'total_profiles', COUNT(*),
    'admin_users', COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true),
    'pro_users', COUNT(*) FILTER (WHERE accounttype = 'pro'),
    'edu_users', COUNT(*) FILTER (WHERE accounttype = 'edu'),
    'edu_pro_users', COUNT(*) FILTER (WHERE accounttype = 'edu_pro'),
    'standard_users', COUNT(*) FILTER (WHERE accounttype = 'standard'),
    'guest_users', COUNT(*) FILTER (WHERE accounttype = 'guest')
  ) INTO profile_stats
  FROM public.profiles;
  
  -- Get dataset statistics with error handling
  BEGIN
    SELECT json_build_object(
      'total_datasets', COUNT(*),
      'users_with_datasets', COUNT(DISTINCT user_id)
    ) INTO dataset_stats
    FROM public.user_datasets
    WHERE user_id IS NOT NULL;
  EXCEPTION
    WHEN OTHERS THEN
      -- If user_datasets table has issues, provide defaults
      dataset_stats := json_build_object(
        'total_datasets', 0,
        'users_with_datasets', 0
      );
  END;
  
  -- Combine all statistics
  SELECT json_build_object(
    'total_users', (profile_stats->>'total_users')::INTEGER,
    'total_profiles', (profile_stats->>'total_profiles')::INTEGER,
    'admin_users', (profile_stats->>'admin_users')::INTEGER,
    'pro_users', (profile_stats->>'pro_users')::INTEGER,
    'edu_users', (profile_stats->>'edu_users')::INTEGER,
    'edu_pro_users', (profile_stats->>'edu_pro_users')::INTEGER,
    'standard_users', (profile_stats->>'standard_users')::INTEGER,
    'guest_users', (profile_stats->>'guest_users')::INTEGER,
    'total_datasets', (dataset_stats->>'total_datasets')::INTEGER,
    'users_with_datasets', (dataset_stats->>'users_with_datasets')::INTEGER,
    'users_last_7_days', 0,
    'users_last_30_days', 0,
    'active_users_last_7_days', 0,
    'active_users_last_30_days', 0,
    'last_updated', NOW()
  ) INTO result;

  RETURN result;
END;
$$;

-- Issue 4: Create a robust admin connection test function
CREATE OR REPLACE FUNCTION public.test_admin_connection()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
DECLARE
  result JSON;
  user_count INTEGER;
  is_admin_user BOOLEAN;
BEGIN
  -- Check if user is admin
  is_admin_user := public.is_user_admin(auth.uid());
  
  IF NOT is_admin_user THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Access denied. Admin privileges required.',
      'user_id', auth.uid(),
      'is_admin', false
    );
  END IF;
  
  -- Test basic query
  SELECT COUNT(*) INTO user_count FROM public.profiles;
  
  RETURN json_build_object(
    'success', true,
    'user_id', auth.uid(),
    'is_admin', true,
    'user_count', user_count,
    'timestamp', NOW()
  );
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_enhanced_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_stats_cache() TO authenticated;
GRANT EXECUTE ON FUNCTION public.test_admin_connection() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;

-- Ensure materialized view exists and is populated
DO $$
BEGIN
  -- Check if materialized view exists
  IF EXISTS (
    SELECT 1 FROM pg_matviews 
    WHERE matviewname = 'user_stats_cache' 
    AND schemaname = 'public'
  ) THEN
    -- Refresh it
    REFRESH MATERIALIZED VIEW public.user_stats_cache;
  ELSE
    -- Create it if it doesn't exist
    CREATE MATERIALIZED VIEW public.user_stats_cache AS
    SELECT 
      COUNT(*) as total_users,
      COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true) as admin_users,
      COUNT(*) FILTER (WHERE accounttype = 'pro') as pro_users,
      COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')) as edu_users,
      COUNT(*) FILTER (WHERE accounttype = 'standard') as standard_users,
      COUNT(*) FILTER (WHERE accounttype = 'guest') as guest_users,
      NOW() as last_updated
    FROM public.profiles;
    
    -- Create index
    CREATE UNIQUE INDEX IF NOT EXISTS idx_user_stats_cache_updated 
    ON public.user_stats_cache(last_updated);
  END IF;
END $$;

-- Add helpful comments
COMMENT ON FUNCTION public.get_user_statistics() IS 'Fixed STABLE function that gets user statistics without REFRESH MATERIALIZED VIEW';
COMMENT ON FUNCTION public.refresh_user_stats_cache() IS 'VOLATILE function specifically for refreshing the materialized view cache';
COMMENT ON FUNCTION public.get_enhanced_user_statistics() IS 'Enhanced statistics function with better error handling and fallbacks';
COMMENT ON FUNCTION public.test_admin_connection() IS 'Test function to verify admin connection and basic functionality';
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Fixed function with exact type casting to prevent type mismatch errors';