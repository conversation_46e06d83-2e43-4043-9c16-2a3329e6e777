import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Container,
  Link
} from '@mui/material';
import { LockReset as LockResetIcon } from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../context/AuthContext'; // Import useAuth to potentially clear guest status
import { useNavigate } from 'react-router-dom';

const UpdatePassword = () => {
  const { logoutGuest } = useAuth(); // Get logoutGuest to clear guest status if needed
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  // Clear guest status when landing on this page from a link
  useEffect(() => {
    logoutGuest(); 
  }, [logoutGuest]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);
    setIsSuccess(false);

    if (password !== confirmPassword) {
      setMessage({ type: 'error', text: 'Passwords do not match.' });
      return;
    }
    if (password.length < 6) {
      setMessage({ type: 'error', text: 'Password must be at least 6 characters long.' });
      return;
    }

    setLoading(true);

    try {
      // Supabase handles the session implicitly from the URL fragment
      const { error } = await supabase.auth.updateUser({ password: password });
      
      if (error) throw error;

      setMessage({ type: 'success', text: 'Password updated successfully! You can now log in with your new password.' });
      setIsSuccess(true);
      // Optionally sign the user out after successful password update
      // await supabase.auth.signOut(); 
    } catch (err: any) {
      setMessage({ type: 'error', text: err.message || 'Failed to update password.' });
      console.error("Password update error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ mt: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
          <LockResetIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
          <Typography variant="h5" component="h1" gutterBottom>
            Update Your Password
          </Typography>
        </Box>

        {message && (
          <Alert severity={message.type} sx={{ mb: 3 }}>
            {message.text}
          </Alert>
        )}

        {!isSuccess ? (
          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              label="New Password"
              type="password"
              fullWidth
              margin="normal"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              helperText="Password must be at least 6 characters."
            />
            <TextField
              label="Confirm New Password"
              type="password"
              fullWidth
              margin="normal"
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={loading}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Update Password'}
            </Button>
          </Box>
        ) : (
           <Box sx={{ textAlign: 'center', mt: 2 }}>
             <Button 
               variant="contained" 
               onClick={() => navigate('/auth/login')}
             >
               Go to Login
             </Button>
           </Box>
        )}
      </Paper>
    </Container>
  );
};

export default UpdatePassword;
