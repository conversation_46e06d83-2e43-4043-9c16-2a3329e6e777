import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  ListItemIcon,
  ListItemText,
  Divider,
  Toolbar,
  Box,
  Typography,
  Collapse,
  ListItemButton,
  useTheme,
  alpha,
  Tooltip,
  Button
} from '@mui/material';
import {
  Storage as StorageIcon,
  Calculate as CalculateIcon,
  Science as ScienceIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Table<PERSON>hart as TableChartIcon,
  Timeline as TimelineIcon,
  Equalizer as EqualizerIcon,
  CompareArrows as CompareArrowsIcon,
  Home as HomeIcon,
  ExpandLess,
  ExpandMore,
  Dashboard as DashboardIcon,
  Bookmarks as BookmarksIcon,
  Settings as SettingsIcon,
  HelpOutline as HelpOutlineIcon,
  Lightbulb as LightbulbIcon,
  Assistant as AssistantIcon,
  AutoAwesome as AutoAwesomeIcon,
  AccountCircle as AccountCircleIcon, // Added Profile Icon
  Insights as InsightsIcon, // Placeholder for Pivot Analysis
  QuestionAnswer as QuestionAnswerIcon, // Icon for Which Test Should I Use?
  Visibility as VisibilityIcon, // Icon for Visualization Guide
  MenuBook as MenuBookIcon, // Icon for Statistical Methods
  PlayCircleOutline as PlayCircleOutlineIcon, // Icon for Video Tutorials
  PivotTableChart as PivotTableChartIcon, // Added for Pivot Charts
  CandlestickChart as CandlestickChartIcon, // Added for Box Plots
  Psychology as PsychologyIcon, // Added for Advanced Analysis
  SmartToy as SmartToyIcon, // Added for AI features
  Security as SecurityIcon, // Added for Admin Dashboard
  Notifications as NotificationsIcon, // Added for Notifications
  GridOn as GridOnIcon, // Icon for Tables group
  Analytics as AnalyticsIcon, // Icon for Analysis group
  Image as ImageIcon // Icon for Figures group
} from '@mui/icons-material';
import VaccinesIcon from '@mui/icons-material/Vaccines'; // Import VaccinesIcon
import { useAuth } from '../../context/AuthContext'; // Import useAuth
import OptimizedImage from '../UI/OptimizedImage';
import AccountTreeIcon from '@mui/icons-material/AccountTree'; // Import AccountTreeIcon for Sankey
import CloudIcon from '@mui/icons-material/Cloud'; // Import CloudIcon for Rain Cloud Plot
import FilterListIcon from '@mui/icons-material/FilterList'; // Import FilterListIcon for Data Filter
import { OverrideStatusBadge } from '../OverrideStatus';

interface SidebarProps {
  open: boolean;
  drawerWidth: number;
  onClose: () => void;
  onMenuItemClick: (path: string) => void;
  activePage?: string;
  activeSubPage?: string;
  isGuest?: boolean; // Add isGuest prop
}

const tips = [
  "Check out our Guided Workflows section for step-by-step analysis assistance. Choose from t-Test or ANOVA workflows for an enhanced experience.",
  "Did you know you can import data directly from Google Sheets?",
  "Explore the Data Visualization section to create various charts and graphs.",
  "The Analysis Assistant can help you choose the right statistical test for your data.",
  "Remember to save your analyses to revisit them later.",
  "Explore our Help & Resources section for FAQs and tutorials.",
  "Did you know you can recode your data under the Data Management section?",
  "Use the Data Editor to clean and prepare your data for analysis.",
  "The Normality Test can help you check if your data follows a normal distribution.",
  "Explore the Correlation Analysis section to understand relationships between variables.",
  "Did you know you can transform your data under the Data Management section?",
  "The Analysis Assistant can help you choose the right statistical test for your data.",
  "Explore the Data using pivot tables for deeper insights.",
  "Did you know you can export your results as a PDF or image?"
];

const Sidebar: React.FC<SidebarProps> = ({
  open,
  drawerWidth,
  onClose,
  onMenuItemClick,
  activePage = '',
  activeSubPage = '',
  isGuest = false // Default isGuest to false
}) => {
  const theme = useTheme();
  const {
    accountType,
    canAccessProFeatures,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    canAccessAdminDashboard
  } = useAuth(); // Get permissions from context

  const [currentTip, setCurrentTip] = useState('');

  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * tips.length);
    setCurrentTip(tips[randomIndex]);
  }, []);

  // State for expanded sections
  // Updated activePage check for dataManagementOpen
  const [dataManagementOpen, setDataManagementOpen] = React.useState(activePage.startsWith('data-management'));
  const [descriptiveStatsOpen, setDescriptiveStatsOpen] = React.useState(activePage === 'stats');
  const [inferentialStatsOpen, setInferentialStatsOpen] = React.useState(activePage.startsWith('inferential-stats')); // State for Inferential Statistics
  const [advancedAnalysisOpen, setAdvancedAnalysisOpen] = React.useState(activePage.startsWith('advanced-analysis')); // State for Advanced Analysis
  const [publicationReadyOpen, setPublicationReadyOpen] = React.useState(activePage.startsWith('publication-ready')); // State for Publication Ready
  const [publicationTablesOpen, setPublicationTablesOpen] = React.useState(false); // State for Publication Ready Tables
  const [publicationAnalysisOpen, setPublicationAnalysisOpen] = React.useState(false); // State for Publication Ready Analysis
  const [publicationFiguresOpen, setPublicationFiguresOpen] = React.useState(false); // State for Publication Ready Figures
  const [correlationAnalysisOpen, setCorrelationAnalysisOpen] = React.useState(activePage.startsWith('correlation-analysis')); // State for Correlation Analysis
  const [visualizationOpen, setVisualizationOpen] = React.useState(activePage === 'charts');
  const [guidedWorkflowsOpen, setGuidedWorkflowsOpen] = React.useState(activePage === 'workflows');
  const [helpResourcesOpen, setHelpResourcesOpen] = React.useState(activePage === 'help'); // State for Help & Resources
  // Removed epiCalcOpen state as Epi Calculator no longer uses dropdown

  // Handle section toggle
  const handleDataManagementClick = () => {
    setDataManagementOpen(!dataManagementOpen);
    // Navigate to the main Data Management page when opening
    if (!dataManagementOpen) {
      handleMenuItemClick('/data-management'); // Updated path
    }
  };

  const handleDescriptiveStatsClick = () => {
    setDescriptiveStatsOpen(!descriptiveStatsOpen);
    if (!descriptiveStatsOpen) {
      handleMenuItemClick('/stats'); // Navigate to the main Descriptive Stats page
    }
  };

  // Handle Inferential Statistics section toggle
  const handleInferentialStatsClick = () => {
    setInferentialStatsOpen(!inferentialStatsOpen);
    // Only navigate if the section is being opened
    if (!inferentialStatsOpen) {
      handleMenuItemClick('/inferential-stats'); // Navigate to the main Inferential Stats page
    }
  };

  // Handle Correlation Analysis section toggle
  const handleCorrelationAnalysisClick = () => {
    setCorrelationAnalysisOpen(!correlationAnalysisOpen);
    // Only navigate if the section is being opened
    if (!correlationAnalysisOpen) {
      handleMenuItemClick('/correlation-analysis'); // Navigate to the main Correlation Analysis page
    }
  };

  const handleVisualizationClick = () => {
    setVisualizationOpen(!visualizationOpen);
    if (!visualizationOpen) {
      handleMenuItemClick('/charts');
    }
  };

  const handleGuidedWorkflowsClick = () => {
    setGuidedWorkflowsOpen(!guidedWorkflowsOpen);
    if (!guidedWorkflowsOpen) {
      handleMenuItemClick('/inference/workflow');
    }
  };

  const handleAdvancedAnalysisClick = () => {
    setAdvancedAnalysisOpen(!advancedAnalysisOpen);
    if (!advancedAnalysisOpen && canAccessAdvancedAnalysis) {
      handleMenuItemClick('/advanced-analysis');
    }
  };

  // Removed handleEpiCalcClick function as Epi Calculator no longer uses dropdown

  // Handle Publication Ready section toggle
  const handlePublicationReadyClick = () => {
    setPublicationReadyOpen(!publicationReadyOpen);
    // Only navigate if the user can access Publication Ready features and the section is being opened
    if (!publicationReadyOpen && canAccessPublicationReady) {
      handleMenuItemClick('/publication-ready'); // Navigate to the new Publication Ready page
    }
  };

  // Handle Publication Ready subsection toggles
  const handlePublicationTablesClick = () => {
    setPublicationTablesOpen(!publicationTablesOpen);
  };

  const handlePublicationAnalysisClick = () => {
    setPublicationAnalysisOpen(!publicationAnalysisOpen);
  };

  const handlePublicationFiguresClick = () => {
    setPublicationFiguresOpen(!publicationFiguresOpen);
  };

  // Handle Help & Resources section toggle
  const handleHelpResourcesClick = () => {
    setHelpResourcesOpen(!helpResourcesOpen);
    if (!helpResourcesOpen) {
      handleMenuItemClick('/help'); // Navigate to the main Help page
    }
  };

  // Check if a menu item is active
  const isActive = (page: string, subPage?: string) => {
    if (page === 'analysis-index') {
      return activePage === page;
    }
    if (page === 'workflows') {
      // Special handling for workflows section
      if (subPage === 'ttest' && activePage === 'inference' && activeSubPage === 'workflow') {
        return true;
      }
      if (subPage === 'anova' && activePage === 'inference' && activeSubPage === 'anovaworkflow') {
        return true;
      }
      return false;
    }

    // Updated isActive check for data management
    if (page === 'data-management') {
       if (subPage) {
         return activePage === page && activeSubPage === subPage;
       }
       return activePage.startsWith('data-management');
    }

    if (page === 'inferential-stats') {
       // Check if the main inferential-stats page or any sub-page is active
       if (subPage) {
         return activePage === page && activeSubPage === subPage;
       }
       return activePage.startsWith('inferential-stats');
    }

    if (page === 'correlation-analysis') {
       return activePage.startsWith('correlation-analysis');
    }

    if (page === 'advanced-analysis') {
       if (subPage) {
         return activePage === page && activeSubPage === subPage;
       }
       return activePage.startsWith('advanced-analysis');
    }

    if (page === 'publication-ready') {
       return activePage.startsWith('publication-ready');
    }

    if (page === 'admin-dashboard') {
       return activePage === 'admin-dashboard';
    }

    if (page === 'publication-ready' && subPage === 'flow-diagram') {
       return activePage === 'publication-ready' && activeSubPage === 'flow-diagram';
    }

    if (page === 'help' && subPage === 'video-tutorials') {
       return activePage === 'video-tutorials'; // Check if the main page is video-tutorials
    }

    if (page === 'advanced' && subPage === 'efa') {
      return activePage === 'advanced' && activeSubPage === 'efa';
    }

    // Removed old data/variables check
    // if (page === 'data' && subPage === 'variables') {
    //   return activePage === 'data' && activeSubPage === 'variables';
    // }

    if (subPage) {
      return activePage === page && activeSubPage === subPage;
    }
    return activePage === page;
  };

  // Ensure consistent navigation path handling
  const handleMenuItemClick = (path: string) => {
    // Remove any hash prefix
    let cleanPath = path.replace(/^\/#\//, '');
    // Do NOT remove /app here, as paths like /samplesize/options are relative to /app/*
    onMenuItemClick(cleanPath);
  };

  // Styles for active items
  const activeItemStyle = {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    color: theme.palette.primary.main,
    fontWeight: 500,
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.12),
    },
    borderRadius: '8px',
  };

  return (
    <Drawer
      variant="persistent"
      open={open}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          borderRight: '1px solid rgba(0, 0, 0, 0.08)',
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.05)',
          backgroundColor: theme.palette.background.paper, // Ensure an opaque background
        },
      }}
    >
      <Toolbar />
      <Box sx={{
        overflow: 'auto',
        mt: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Box sx={{ px: 3, pb: 2, display: 'flex', alignItems: 'center' }}>
          <OptimizedImage
            src="/logo.png"
            alt="Statistica Logo"
            width={36}
            height={36}
            style={{
              marginRight: '12px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
            loading="eager"
          />
          <Box>
            <Typography variant="h6" color="primary" fontWeight="bold" sx={{ lineHeight: 1.2 }}>
              DataStatPro
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
              Advanced Statistical Analysis
            </Typography>
          </Box>
        </Box>

        <Box sx={{ px: 2, pb: 2 }}>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            startIcon={<DashboardIcon />}
            size="large"
            onClick={() => handleMenuItemClick('/home')}
            sx={{
              textAlign: 'left',
              justifyContent: 'flex-start',
              py: 1,
              borderRadius: 2,
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`
            }}
          >
            Dashboard
          </Button>
        </Box>

        {/* Analysis Assistant - Prominent Position */}
        <Box sx={{ px: 2, pb: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            color="primary"
            startIcon={<PsychologyIcon />}
            size="large"
            onClick={() => handleMenuItemClick('/assistant')}
            sx={{
              textAlign: 'left',
              justifyContent: 'flex-start',
              py: 1,
              borderRadius: 2,
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
              borderColor: theme.palette.primary.main,
              backgroundColor: isActive('assistant') ? theme.palette.primary.light + '20' : 'transparent',
              '&:hover': {
                backgroundColor: theme.palette.primary.light + '30',
                borderColor: theme.palette.primary.dark,
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <span>Analysis Assistant</span>
              <Box
                sx={{
                  ml: 1,
                  bgcolor: theme.palette.secondary.main,
                  color: 'white',
                  fontSize: '0.65rem',
                  px: 0.7,
                  borderRadius: 1,
                  display: 'inline-flex',
                  alignItems: 'center',
                  height: 16,
                  fontWeight: 'bold'
                }}
              >
                AI
              </Box>
            </Box>
          </Button>
        </Box>

        {/* Admin Dashboard - Only visible to admin users */}
        {canAccessAdminDashboard && (
          <Box sx={{ px: 2, pb: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              color="error"
              startIcon={<SecurityIcon />}
              size="large"
              onClick={() => handleMenuItemClick('/admin-dashboard')}
              sx={{
                textAlign: 'left',
                justifyContent: 'flex-start',
                py: 1,
                borderRadius: 2,
                boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                borderColor: theme.palette.error.main,
                backgroundColor: isActive('admin-dashboard') ? theme.palette.error.light + '20' : 'transparent',
                '&:hover': {
                  backgroundColor: theme.palette.error.light + '30',
                  borderColor: theme.palette.error.dark,
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <span>Admin Dashboard</span>
                <Box
                  sx={{
                    ml: 1,
                    bgcolor: theme.palette.error.main,
                    color: 'white',
                    fontSize: '0.65rem',
                    px: 0.7,
                    borderRadius: 1,
                    display: 'inline-flex',
                    alignItems: 'center',
                    height: 16,
                    fontWeight: 'bold'
                  }}
                >
                  ADMIN
                </Box>
              </Box>
            </Button>
          </Box>
        )}

        <Divider sx={{ mx: 2, mb: 1 }} />

        <List
          component="nav"
          sx={{
            width: '100%',
            flex: 1, // Allow it to grow
            px: 1
          }}
          dense // Make the list more compact
        >
          {/* Home */}
          <ListItemButton
            onClick={() => handleMenuItemClick('/home')}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('home') ? activeItemStyle : {}),
            }}
          >
            <ListItemIcon>
              <HomeIcon color={isActive('home') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Home"
              primaryTypographyProps={{
                fontWeight: isActive('home') ? 500 : 400
            }}
          />
        </ListItemButton>

          {/* Analysis Index */}
          <ListItemButton
            onClick={() => handleMenuItemClick('/analysis-index')}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('analysis-index') ? activeItemStyle : {}),
            }}
          >
            <ListItemIcon>
              <EqualizerIcon color={isActive('analysis-index') ? "primary" : "inherit"} /> {/* Using EqualizerIcon as a placeholder */}
            </ListItemIcon>
            <ListItemText
              primary="Analysis Index"
              primaryTypographyProps={{
                fontWeight: isActive('analysis-index') ? 500 : 400
              }}
            />
          </ListItemButton>

          {/* Data Management */}
          <ListItemButton
            onClick={handleDataManagementClick}
            sx={{
              mb: 0.5,
              px: 2,
              // Updated isActive check
              ...(isActive('data-management') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <StorageIcon color={isActive('data-management') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Data Management"
              primaryTypographyProps={{
                fontWeight: isActive('data-management') ? 500 : 400
              }}
            />
            {dataManagementOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={dataManagementOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  // Updated isActive check and path
                  ...(isActive('data-management', 'import') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/import')}
              >
                <ListItemText
                  primary="Import Data"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'import') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  // Updated isActive check and path
                  ...(isActive('data-management', 'export') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/export')}
              >
                <ListItemText
                  primary="Export Data"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'export') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  // Updated isActive check and path
                  ...(isActive('data-management', 'editor') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/editor')}
              >
                <ListItemText
                  primary="Data Editor"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'editor') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  // Updated isActive check and path
                  ...(isActive('data-management', 'variables') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/variables')}
              >
                <ListItemText
                  primary="Variable Editor"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'variables') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('data-management', 'datasets') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/datasets')}
              >
                <ListItemText
                  primary="Datasets"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'datasets') ? 500 : 400
                  }}
                />
              </ListItemButton>

              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  // Updated isActive check and path
                  ...(isActive('data-management', 'transform') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/data-management/transform')}
              >
                <ListItemText
                  primary="Transform Data"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('data-management', 'transform') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </Collapse>

          {/* Descriptive Statistics */}
          <ListItemButton
            onClick={handleDescriptiveStatsClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('stats') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <EqualizerIcon color={isActive('stats') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Descriptive Statistics"
              primaryTypographyProps={{
                fontWeight: isActive('stats') ? 500 : 400
              }}
            />
            {descriptiveStatsOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={descriptiveStatsOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('stats', 'descriptives') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/stats/descriptives')}
              >
                <ListItemText
                  primary="Descriptives"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('stats', 'descriptives') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('stats', 'frequencies') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/stats/frequencies')}
              >
                <ListItemText
                  primary="Frequencies"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('stats', 'frequencies') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('stats', 'crosstabs') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/stats/crosstabs')}
              >
                <ListItemText
                  primary="Cross Tabulation"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('stats', 'crosstabs') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('stats', 'normality') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/stats/normality')}
              >
                <ListItemText
                  primary="Normality Test"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('stats', 'normality') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </Collapse>

          {/* Inferential Statistics - New Main Menu Item */}
          <ListItemButton
            onClick={handleInferentialStatsClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('inferential-stats') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <ScienceIcon color={isActive('inferential-stats') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Inferential Statistics"
              primaryTypographyProps={{
                fontWeight: isActive('inferential-stats') ? 500 : 400
              }}
            />
            {inferentialStatsOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={inferentialStatsOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('inferential-stats', 'one-sample-ttest') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inferential-stats/one-sample-ttest')}
              >
                <ListItemText
                  primary="T-Tests"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('inferential-stats', 'one-sample-ttest') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('inferential-stats', 'mann-whitney-u-test') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inferential-stats/mann-whitney-u-test')}
              >
                <ListItemText
                  primary="Non-Parametric Tests"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('inferential-stats', 'mann-whitney-u-test') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('inference', 'anova') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inference/anova')}
              >
                <ListItemText
                  primary="ANOVA"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('inference', 'anova') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('inference', 'assumptions') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inference/assumptions')}
              >
                <ListItemText
                  primary="Assumption Checker"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('inferential-stats', 'assumptions') ? 500 : 400 // Changed 'assumption-checker' to 'assumptions'
                  }}
                />
              </ListItemButton>
              {/* Add other Inferential Statistics sub-items here as needed */}
            </List>
          </Collapse>

          {/* Correlation Analysis */}
          <ListItemButton
            onClick={handleCorrelationAnalysisClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('correlation-analysis') ? activeItemStyle : {}) // Use 'correlation-analysis' for main page
            }}
          >
            <ListItemIcon>
              <CompareArrowsIcon color={isActive('correlation-analysis') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Correlation Analysis"
              primaryTypographyProps={{
                fontWeight: isActive('correlation-analysis') ? 500 : 400
              }}
            />
            {correlationAnalysisOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={correlationAnalysisOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('correlation-analysis', 'pearson') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/correlation-analysis/pearson')}
              >
                <ListItemText
                  primary="Correlation"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('correlation-analysis', 'pearson') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('correlation-analysis', 'regression') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/correlation-analysis/regression')}
              >
                <ListItemText
                  primary="Linear Regression"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('correlation-analysis', 'regression') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('correlation-analysis', 'logistic') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/correlation-analysis/logistic')}
              >
                <ListItemText
                  primary="Logistic Regression"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('correlation-analysis', 'logistic') ? 500 : 400
                  }}
                  />
              </ListItemButton>
              {/* Add other Correlation Analysis sub-items here as needed */}
            </List>
          </Collapse>

          {/* Data Visualization */}
          <ListItemButton
            onClick={handleVisualizationClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('charts') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <ShowChartIcon color={isActive('charts') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary="Data Visualization"
              primaryTypographyProps={{
                fontWeight: isActive('charts') ? 500 : 400
              }}
            />
            {visualizationOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={visualizationOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'bar') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/bar')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <BarChartIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Bar Charts"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'bar') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'pie') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/pie')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <PieChartIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Pie Charts"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'pie') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'histogram') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/histogram')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <EqualizerIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Histograms"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'histogram') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'boxplot') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/boxplot')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <CandlestickChartIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Box Plots"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'boxplot') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'scatter') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/scatter')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <TimelineIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Scatter Plots"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'scatter') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'raincloud') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/raincloud')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <CloudIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Rain Cloud Plots"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'raincloud') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'sankey') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/sankey')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <AccountTreeIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Sankey Diagrams"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'sankey') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('charts', 'errorbar') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/charts/errorbar')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <ShowChartIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Error Bar Charts"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('charts', 'errorbar') ? 500 : 400
                  }}
                />
              </ListItemButton>
              {/* Pivot Table Analysis Link */}
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('pivot', 'analysis') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/pivot/tables')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <PivotTableChartIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Pivot Charts"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('pivot', 'tables') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </Collapse>

          {/* Divider for Pro/Edu Features */}
          <Divider sx={{ my: 2, mx: 2 }}>
            <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 500 }}>
              PRO/EDU FEATURES
            </Typography>
          </Divider>

          {/* Advanced Analysis */}
          <ListItemButton
            onClick={handleAdvancedAnalysisClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('advanced-analysis') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <PsychologyIcon color={isActive('advanced-analysis') ? "primary" : "inherit"} />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span>Advanced Analysis</span>
                  {!canAccessAdvancedAnalysis && (
                    <Box
                      sx={{
                        ml: 1,
                        bgcolor: theme.palette.warning.main,
                        color: 'white',
                        fontSize: '0.65rem',
                        px: 0.7,
                        borderRadius: 1,
                        display: 'inline-flex',
                        alignItems: 'center',
                        height: 16
                      }}
                    >
                      PRO
                    </Box>
                  )}
                </Box>
              }
              primaryTypographyProps={{
                fontWeight: isActive('advanced-analysis') ? 500 : 400
              }}
            />
            {advancedAnalysisOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={advancedAnalysisOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'survival') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/survival')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Survival Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'survival') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'reliability') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/reliability')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Reliability Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'reliability') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'cluster') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/cluster')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Cluster Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'cluster') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'meta-analysis') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/meta-analysis')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Meta Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'meta-analysis') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'variable-tree') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/variable-tree')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Variable Tree Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'variable-tree') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'mediation-moderation') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/mediation')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Mediation/Moderation"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'mediation-moderation') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'efa') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/efa')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Exploratory Factor Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'efa') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('advanced-analysis', 'cfa') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/advanced-analysis/cfa')}
                disabled={!canAccessAdvancedAnalysis}
              >
                <ListItemText
                  primary="Confirmatory Factor Analysis"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('advanced-analysis', 'cfa') ? 500 : 400
                  }}
                />
              </ListItemButton>


            </List>
          </Collapse>

          {/* Publication Ready */}
          <ListItemButton
            onClick={handlePublicationReadyClick}
            sx={{
              mb: 0.5,
              px: 2,
              ...(isActive('publication-ready') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <MenuBookIcon color={isActive('publication-ready') ? "primary" : "inherit"} /> {/* Using MenuBookIcon for now */}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span>Publication Ready</span>
                  {!canAccessPublicationReady && (
                    <Box
                      sx={{
                        ml: 1,
                        bgcolor: theme.palette.warning.main, // Use a distinct color for "Pro" badge
                        color: 'white',
                        fontSize: '0.65rem',
                        px: 0.7,
                        borderRadius: 1,
                        display: 'inline-flex',
                        alignItems: 'center',
                        height: 16
                      }}
                    >
                      PRO
                    </Box>
                  )}
                </Box>
              }
              primaryTypographyProps={{
                fontWeight: isActive('publication-ready') ? 500 : 400
              }}
            />
            {publicationReadyOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={publicationReadyOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              {/* Results Manager - Main Entry Point */}
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('publication-ready', 'results-manager') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/publication-ready/results-manager')}
                disabled={!canAccessPublicationReady}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <BookmarksIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Results Manager"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('publication-ready', 'results-manager') ? 600 : 500
                  }}
                />
              </ListItemButton>



              {/* Tables Group */}
              <ListItemButton
                onClick={handlePublicationTablesClick}
                sx={{
                  pl: 4,
                  mb: 0.5
                }}
                disabled={!canAccessPublicationReady}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <GridOnIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Tables"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                />
                {publicationTablesOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse in={publicationTablesOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding dense sx={{ ml: 1 }}>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'table1') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/table1')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Table 1"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'table1') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'table1a') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/table1a')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Table 1a"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'table1a') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'table1b') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/table1b')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Table 1b"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'table1b') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'table3-generator') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/table3-generator')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Table 3"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'table3-generator') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'smd-table') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/smd-table')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="SMD Table"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'smd-table') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'regression-table') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/regression-table')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Regression Table"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'regression-table') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'posthoc-tests') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/posthoc-tests')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="PostHoc Tests"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'posthoc-tests') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                </List>
              </Collapse>

              {/* Analysis Tools Group */}
              <ListItemButton
                onClick={handlePublicationAnalysisClick}
                sx={{
                  pl: 4,
                  mb: 0.5
                }}
                disabled={!canAccessPublicationReady}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <AnalyticsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Analysis Tools"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                />
                {publicationAnalysisOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse in={publicationAnalysisOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding dense sx={{ ml: 1 }}>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'statistical-methods') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/statistical-methods')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Statistical Methods"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'statistical-methods') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'enhanced-methods-generator') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/enhanced-statistical-methods')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Enhanced Methods Generator"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'enhanced-methods-generator') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'convert-to-apa') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/convert-to-apa')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Convert to APA"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'convert-to-apa') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'citation-reference-manager') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/citation-reference-manager')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Citation & Reference Manager"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'citation-reference-manager') ? 500 : 400
                      }}
                    />
                  </ListItemButton>

                </List>
              </Collapse>

              {/* Figures Group */}
              <ListItemButton
                onClick={handlePublicationFiguresClick}
                sx={{
                  pl: 4,
                  mb: 0.5
                }}
                disabled={!canAccessPublicationReady}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <ImageIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Figures"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: 500
                  }}
                />
                {publicationFiguresOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
              <Collapse in={publicationFiguresOpen} timeout="auto" unmountOnExit>
                <List component="div" disablePadding dense sx={{ ml: 1 }}>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'flow-diagram') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/flow-diagram')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Flow Diagram"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'flow-diagram') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'figure-caption-generator') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/figure-caption-generator')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Figure Caption Generator"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'figure-caption-generator') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                  <ListItemButton
                    sx={{
                      pl: 6,
                      mb: 0.5,
                      ...(isActive('publication-ready', 'enhanced-figure-processor') ? activeItemStyle : {})
                    }}
                    onClick={() => handleMenuItemClick('/publication-ready/enhanced-figure-processor')}
                    disabled={!canAccessPublicationReady}
                  >
                    <ListItemText
                      primary="Enhanced Figure Processor"
                      primaryTypographyProps={{
                        fontSize: '0.8rem',
                        fontWeight: isActive('publication-ready', 'enhanced-figure-processor') ? 500 : 400
                      }}
                    />
                  </ListItemButton>
                </List>
              </Collapse>
            </List>
          </Collapse>

          {/* CALCULATORS Divider */}
          <Divider sx={{ mx: 2, my: 2 }}>
            <Typography variant="caption" sx={{ color: 'text.secondary', fontWeight: 500 }}>
              CALCULATORS
            </Typography>
          </Divider>

          {/* Sample Size Calculator */}
          <ListItemButton
            sx={{
              px: 2,
              ...(isActive('samplesize') ? activeItemStyle : {})
            }}
            onClick={() => handleMenuItemClick('/samplesize')}
          >
            <ListItemIcon>
              <CalculateIcon color={isActive('samplesize') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span>Sample Size Calculator</span>
                  <Box
                    sx={{
                      ml: 1,
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      fontSize: '0.65rem',
                      px: 0.7,
                      borderRadius: 1,
                      display: 'inline-flex',
                      alignItems: 'center',
                      height: 16
                    }}
                  >
                    NEW
                  </Box>
                </Box>
              }
              primaryTypographyProps={{
                fontWeight: isActive('samplesize') ? 500 : 400
              }}
            />
          </ListItemButton>

          {/* Epi Calculator */}
          <ListItemButton
            sx={{
              px: 2,
              ...(isActive('epicalc') ? activeItemStyle : {})
            }}
            onClick={() => handleMenuItemClick('/epicalc')}
          >
            <ListItemIcon>
              <VaccinesIcon color={isActive('epicalc') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span>Epi Calculator</span>
                  <Box
                    sx={{
                      ml: 1,
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      fontSize: '0.65rem',
                      px: 0.7,
                      borderRadius: 1,
                      display: 'inline-flex',
                      alignItems: 'center',
                      height: 16
                    }}
                  >
                    NEW
                  </Box>
                </Box>
              }
              primaryTypographyProps={{
                fontWeight: isActive('epicalc') ? 500 : 400
              }}
            />
          </ListItemButton>

          {/* CI Calculator */}
          <ListItemButton
            sx={{
              px: 2,
              ...(isActive('ci-calculators') ? activeItemStyle : {})
            }}
            onClick={() => handleMenuItemClick('/ci-calculators')}
          >
            <ListItemIcon>
              <CalculateIcon color={isActive('ci-calculators') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <span>CI Calculator</span>
                  <Box
                    sx={{
                      ml: 1,
                      bgcolor: theme.palette.primary.main,
                      color: 'white',
                      fontSize: '0.65rem',
                      px: 0.7,
                      borderRadius: 1,
                      display: 'inline-flex',
                      alignItems: 'center',
                      height: 16
                    }}
                  >
                    NEW
                  </Box>
                </Box>
              }
              primaryTypographyProps={{
                fontWeight: isActive('ci-calculators') ? 500 : 400
              }}
            />
          </ListItemButton>

          {/* Effect Size Calculator */}
          <ListItemButton
            sx={{
              px: 2,
              ...(isActive('effect-size-calculator') ? activeItemStyle : {})
            }}
            onClick={() => handleMenuItemClick('/effect-size-calculator')}
          >
            <ListItemIcon>
              <CalculateIcon color={isActive('effect-size-calculator') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary="Effect Size Calculator"
              primaryTypographyProps={{
                fontWeight: isActive('effect-size-calculator') ? 500 : 400
              }}
            />
          </ListItemButton>
        </List>

        <Divider sx={{ mx: 2, my: 2 }} />

        {/* Quick Links */}
        <List dense sx={{ px: 1 }}>
              {/* Analysis Assistant moved to prominent position above */}
              {/* Results Manager removed */}

          {/* Guided Workflows */}
          <ListItemButton
            onClick={handleGuidedWorkflowsClick}
            sx={{
              px: 2,
              ...(isActive('workflows') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <AutoAwesomeIcon color={isActive('workflows') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary="Guided Workflows"
              primaryTypographyProps={{
                fontWeight: isActive('workflows') ? 500 : 400
              }}
            />
            {guidedWorkflowsOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={guidedWorkflowsOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('workflows', 'ttest') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inference/workflow')}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <span>Guided t-Test</span>
                      <Box
                        sx={{
                          ml: 1,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          fontSize: '0.65rem',
                          px: 0.7,
                          borderRadius: 1,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 16
                        }}
                      >
                        NEW
                      </Box>
                    </Box>
                  }
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('workflows', 'ttest') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('workflows', 'anova') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/inference/anovaworkflow')}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <span>Guided ANOVA</span>
                      <Box
                        sx={{
                          ml: 1,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          fontSize: '0.65rem',
                          px: 0.7,
                          borderRadius: 1,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 16
                        }}
                      >
                        NEW
                      </Box>
                    </Box>
                  }
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('workflows', 'anova') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('workflows', 'correlation-regression') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/correlation-regression-workflow')}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <span>Guided Correlation & Regression</span>
                      <Box
                        sx={{
                          ml: 1,
                          bgcolor: theme.palette.primary.main,
                          color: 'white',
                          fontSize: '0.65rem',
                          px: 0.7,
                          borderRadius: 1,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 16
                        }}
                      >
                        NEW
                      </Box>
                    </Box>
                  }
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('workflows', 'correlation-regression') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </Collapse>

          {/* Knowledgebase */}
          <ListItemButton
            onClick={() => handleMenuItemClick('/knowledge-base')}
            sx={{
              px: 2,
              ...(isActive('knowledge-base') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <MenuBookIcon color={isActive('knowledge-base') ? "primary" : "action"} /> {/* Using MenuBookIcon for Knowledgebase */}
            </ListItemIcon>
            <ListItemText
              primary="Knowledgebase"
              primaryTypographyProps={{
                fontWeight: isActive('knowledge-base') ? 500 : 400
              }}
            />
          </ListItemButton>

          {/* Help & Resources */}
          <ListItemButton
            onClick={() => setHelpResourcesOpen(!helpResourcesOpen)}
            sx={{
              px: 2,
              ...(isActive('help') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <HelpOutlineIcon color={isActive('help') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary="Help & Resources"
              primaryTypographyProps={{
                fontWeight: isActive('help') ? 500 : 400
              }}
            />
            {helpResourcesOpen ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
          <Collapse in={helpResourcesOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding dense sx={{ ml: 2 }}>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('help', 'which-test') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/which-test')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <QuestionAnswerIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Which Test Should I Use?"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('help', 'which-test') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('help', 'visualization-guide') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/visualization-guide')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <VisibilityIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Visualization Guide"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('help', 'visualization-guide') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('help', 'statistical-methods') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/statistical-methods')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <MenuBookIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Statistical Methods"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                  fontWeight: isActive('help', 'statistical-methods') ? 500 : 400
                  }}
                />
              </ListItemButton>

              {/* Video Tutorials Link */}
              <ListItemButton
                sx={{
                  pl: 4,
                  mb: 0.5,
                  ...(isActive('help', 'video-tutorials') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/video-tutorials')}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <PlayCircleOutlineIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Video Tutorials"
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive('help', 'video-tutorials') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </Collapse>
        </List>

        {/* User Profile/Settings Section - Hidden for Guests */}
        {!isGuest && (
          <>
            <Divider sx={{ mx: 2, my: 1 }} />

            {/* Override Status */}
            <Box sx={{ px: 2, py: 1 }}>
              <OverrideStatusBadge variant="chip" showDetails={true} size="small" />
            </Box>

            <List dense sx={{ px: 1 }}>
              <ListItemButton
                sx={{
                  px: 2,
                  ...(isActive('profile') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/profile')}
              >
                <ListItemIcon>
                  <AccountCircleIcon color={isActive('profile') ? "primary" : "action"} />
                </ListItemIcon>
                <ListItemText
                  primary="Profile"
                  primaryTypographyProps={{
                    fontWeight: isActive('profile') ? 500 : 400
                  }}
                />
              </ListItemButton>
              <ListItemButton
                sx={{
                  px: 2,
                  ...(isActive('settings') ? activeItemStyle : {})
                }}
                onClick={() => handleMenuItemClick('/settings')}
              >
                <ListItemIcon>
                  <SettingsIcon color={isActive('settings') ? "primary" : "action"} />
                </ListItemIcon>
                <ListItemText
                  primary="Settings"
                  primaryTypographyProps={{
                    fontWeight: isActive('settings') ? 500 : 400
                  }}
                />
              </ListItemButton>
            </List>
          </>
        )}

        {/* Tips */}
        <Box sx={{
          p: 2,
          mx: 2,
          mb: 2,
          bgcolor: alpha(theme.palette.primary.light, 0.1),
          borderRadius: 2
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <LightbulbIcon color="primary" fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="body2" fontWeight="medium" color="primary.main">
              Tip of the Day
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary" display="block">
            {currentTip}
          </Typography>
        </Box>

        {/* Notification Center */}
        <List dense sx={{ px: 1 }}>
          <ListItemButton
            onClick={() => handleMenuItemClick('/notifications')}
            sx={{
              px: 2,
              ...(isActive('notifications') ? activeItemStyle : {})
            }}
          >
            <ListItemIcon>
              <NotificationsIcon color={isActive('notifications') ? "primary" : "action"} />
            </ListItemIcon>
            <ListItemText
              primary="Notification Center"
              primaryTypographyProps={{
                fontWeight: isActive('notifications') ? 500 : 400
              }}
            />
          </ListItemButton>
        </List>

        {/* Version info at bottom */}
        <Box sx={{
          p: 2,
          borderTop: '1px solid rgba(0, 0, 0, 0.08)',
          backgroundColor: 'rgba(0, 0, 0, 0.02)'
        }}>
          <Typography variant="caption" color="text.secondary" display="block">
            DataStatPro v1.1.7 Beta
          </Typography>
          <Typography variant="caption" color="text.secondary" display="block">
            © 2024 DataStatPro Team
          </Typography>
        </Box>
      </Box>
    </Drawer>
  );
};

export default Sidebar;
