-- Optimize Admin Performance Migration
-- This migration addresses connection pooling and performance issues in admin dashboard
-- Date: 2025-07-25

-- Note: System-level settings must be configured manually in Supabase Dashboard
-- These settings improve connection stability but cannot be set via migration:
-- - max_connections = 200
-- - shared_preload_libraries = 'pg_stat_statements'
-- - default_transaction_isolation = 'read committed'

-- Optimize admin-specific settings (these can be set in migrations)
ALTER ROLE authenticated SET statement_timeout = '30s';
ALTER ROLE authenticated SET idle_in_transaction_session_timeout = '10s';
ALTER ROLE authenticated SET lock_timeout = '5s';

-- Create materialized view for user statistics to reduce load
CREATE MATERIALIZED VIEW IF NOT EXISTS public.user_stats_cache AS
SELECT 
  COUNT(*) as total_users,
  COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true) as admin_users,
  COUNT(*) FILTER (WHERE accounttype = 'pro') as pro_users,
  COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')) as edu_users,
  COUNT(*) FILTER (WHERE accounttype = 'standard') as standard_users,
  COUNT(*) FILTER (WHERE accounttype = 'guest') as guest_users,
  NOW() as last_updated
FROM public.profiles;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_stats_cache_updated ON public.user_stats_cache(last_updated);

-- Create function to refresh stats cache
CREATE OR REPLACE FUNCTION public.refresh_user_stats_cache()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW public.user_stats_cache;
END;
$$;

-- Create optimized get_user_statistics function using cache
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '10s'
AS $$
DECLARE
  result JSON;
  cache_age INTERVAL;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Check if cache needs refresh (older than 5 minutes)
  SELECT NOW() - last_updated INTO cache_age
  FROM public.user_stats_cache
  LIMIT 1;
  
  -- Refresh cache if it's stale or doesn't exist
  IF cache_age IS NULL OR cache_age > INTERVAL '5 minutes' THEN
    PERFORM public.refresh_user_stats_cache();
  END IF;
  
  -- Get stats from cache
  SELECT json_build_object(
    'total_users', total_users,
    'admin_users', admin_users,
    'pro_users', pro_users,
    'edu_users', edu_users,
    'standard_users', standard_users,
    'guest_users', guest_users,
    'users_last_7_days', 0,
    'users_last_30_days', 0,
    'active_users_last_7_days', 0,
    'active_users_last_30_days', 0,
    'cache_updated', last_updated
  ) INTO result
  FROM public.user_stats_cache;

  RETURN result;
END;
$$;

-- Create connection monitoring function
CREATE OR REPLACE FUNCTION public.get_connection_stats()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
  active_connections INTEGER;
  max_connections INTEGER;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get connection statistics
  SELECT COUNT(*) INTO active_connections
  FROM pg_stat_activity
  WHERE state = 'active';
  
  SELECT setting::INTEGER INTO max_connections
  FROM pg_settings
  WHERE name = 'max_connections';
  
  SELECT json_build_object(
    'active_connections', active_connections,
    'max_connections', max_connections,
    'connection_usage_percent', ROUND((active_connections::FLOAT / max_connections::FLOAT) * 100, 2),
    'timestamp', NOW()
  ) INTO result;

  RETURN result;
END;
$$;

-- Create rate limiting for admin functions
CREATE TABLE IF NOT EXISTS public.admin_function_calls (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  function_name TEXT NOT NULL,
  called_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET
);

-- Create index for rate limiting
CREATE INDEX IF NOT EXISTS idx_admin_function_calls_user_time ON public.admin_function_calls(user_id, called_at);
CREATE INDEX IF NOT EXISTS idx_admin_function_calls_cleanup ON public.admin_function_calls(called_at) WHERE called_at < NOW() - INTERVAL '1 hour';

-- Enable RLS on rate limiting table
ALTER TABLE public.admin_function_calls ENABLE ROW LEVEL SECURITY;

-- Create policy for rate limiting table
CREATE POLICY "Admins can manage function call logs" ON public.admin_function_calls
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Create rate limiting function
CREATE OR REPLACE FUNCTION public.check_admin_rate_limit(
  function_name TEXT,
  max_calls INTEGER DEFAULT 10,
  time_window INTERVAL DEFAULT INTERVAL '1 minute'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  call_count INTEGER;
BEGIN
  -- Count recent calls for this user and function
  SELECT COUNT(*) INTO call_count
  FROM public.admin_function_calls
  WHERE user_id = auth.uid()
    AND function_name = check_admin_rate_limit.function_name
    AND called_at > NOW() - time_window;
  
  -- Log this call
  INSERT INTO public.admin_function_calls (user_id, function_name)
  VALUES (auth.uid(), check_admin_rate_limit.function_name);
  
  -- Return whether rate limit is exceeded
  RETURN call_count < max_calls;
END;
$$;

-- Create cleanup function for old rate limiting records
CREATE OR REPLACE FUNCTION public.cleanup_admin_function_calls()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM public.admin_function_calls
  WHERE called_at < NOW() - INTERVAL '1 hour';
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.refresh_user_stats_cache() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_connection_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_admin_rate_limit(TEXT, INTEGER, INTERVAL) TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_admin_function_calls() TO authenticated;

-- Grant access to materialized view
GRANT SELECT ON public.user_stats_cache TO authenticated;
GRANT ALL ON public.admin_function_calls TO authenticated;

-- Add helpful comments
COMMENT ON MATERIALIZED VIEW public.user_stats_cache IS 'Cached user statistics to reduce database load in admin dashboard';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Optimized user statistics using materialized view cache';
COMMENT ON FUNCTION public.get_connection_stats() IS 'Monitor database connection usage for admin dashboard';
COMMENT ON FUNCTION public.check_admin_rate_limit(TEXT, INTEGER, INTERVAL) IS 'Rate limiting for admin functions to prevent connection overload';

-- Initial cache population
SELECT public.refresh_user_stats_cache();
