import React from 'react';
import { Box, Container } from '@mui/material';
import SampleSizeCalculatorsOptions from '../components/SampleSizeCalculators/SampleSizeCalculatorsOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';

interface SampleSizeCalculatorsPageProps {
  onNavigate: (path: string) => void;
}

const SampleSizeCalculatorsPage: React.FC<SampleSizeCalculatorsPageProps> = ({ onNavigate }) => {
  // Initialize social meta for sample size calculators page
  useSocialMeta();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <SampleSizeCalculatorsOptions onNavigate={onNavigate} />
      </Box>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible
      />
    </Container>
  );
};

export default SampleSizeCalculatorsPage;
