-- Admin Subscription Override Functions Migration
-- This migration adds database functions for managing subscription overrides
-- Date: 2025-07-25

-- Function to create a new subscription override
CREATE OR REPLACE FUNCTION public.create_subscription_override(
  target_user_id UUID,
  override_tier_param TEXT,
  duration_months INTEGER DEFAULT 1,
  reason_param TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_tier TEXT;
  override_id UUID;
  end_date_calc TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate override tier
  IF override_tier_param NOT IN ('standard', 'edu', 'edu_pro', 'pro') THEN
    RAISE EXCEPTION 'Invalid override tier: %', override_tier_param;
  END IF;
  
  -- Validate duration
  IF duration_months < 1 OR duration_months > 12 THEN
    RAISE EXCEPTION 'Duration must be between 1 and 12 months';
  END IF;
  
  -- Get current user tier from profiles
  SELECT COALESCE(accounttype, 'standard') INTO current_tier
  FROM public.profiles
  WHERE id = target_user_id;
  
  IF current_tier IS NULL THEN
    RAISE EXCEPTION 'User not found: %', target_user_id;
  END IF;
  
  -- Validate tier upgrade logic
  IF NOT (
    (current_tier = 'standard' AND override_tier_param IN ('edu', 'edu_pro', 'pro')) OR
    (current_tier = 'edu' AND override_tier_param IN ('edu_pro', 'pro')) OR
    (current_tier = 'edu_pro' AND override_tier_param = 'pro')
  ) THEN
    RAISE EXCEPTION 'Invalid tier upgrade from % to %', current_tier, override_tier_param;
  END IF;
  
  -- Check for existing active override
  IF EXISTS (
    SELECT 1 FROM public.subscription_overrides 
    WHERE user_id = target_user_id AND is_active = true
  ) THEN
    RAISE EXCEPTION 'User already has an active subscription override';
  END IF;
  
  -- Calculate end date
  end_date_calc := now() + (duration_months || ' months')::INTERVAL;
  
  -- Create the override
  INSERT INTO public.subscription_overrides (
    user_id, admin_id, original_tier, override_tier, 
    end_date, reason
  ) VALUES (
    target_user_id, auth.uid(), current_tier, override_tier_param,
    end_date_calc, reason_param
  ) RETURNING id INTO override_id;
  
  RETURN override_id;
END;
$$;

-- Function to get user's active override
CREATE OR REPLACE FUNCTION public.get_user_active_override(target_user_id UUID)
RETURNS TABLE (
  id UUID,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  admin_email TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Users can check their own override, admins can check any user's override
  IF target_user_id != auth.uid() AND NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Can only view your own override status.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    au.email as admin_email,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  JOIN auth.users au ON so.admin_id = au.id
  WHERE so.user_id = target_user_id 
    AND so.is_active = true
    AND so.end_date > now()
  ORDER BY so.created_at DESC
  LIMIT 1;
END;
$$;

-- Function to get all active overrides (admin only)
CREATE OR REPLACE FUNCTION public.get_all_active_overrides(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    u.email as user_email,
    p.full_name as user_full_name,
    au.email as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  JOIN auth.users u ON so.user_id = u.id
  JOIN auth.users au ON so.admin_id = au.id
  LEFT JOIN public.profiles p ON so.user_id = p.id
  WHERE so.is_active = true AND so.end_date > now()
  ORDER BY so.end_date ASC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Function to manually expire an override (admin only)
CREATE OR REPLACE FUNCTION public.expire_subscription_override(override_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  UPDATE public.subscription_overrides 
  SET is_active = false, updated_at = now()
  WHERE id = override_id AND is_active = true;
  
  RETURN FOUND;
END;
$$;

-- Function to get override audit trail (admin only)
CREATE OR REPLACE FUNCTION public.get_override_audit_trail(
  target_user_id UUID DEFAULT NULL,
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    u.email as user_email,
    p.full_name as user_full_name,
    au.email as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    so.is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  JOIN auth.users u ON so.user_id = u.id
  JOIN auth.users au ON so.admin_id = au.id
  LEFT JOIN public.profiles p ON so.user_id = p.id
  WHERE (target_user_id IS NULL OR so.user_id = target_user_id)
  ORDER BY so.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.create_subscription_override(UUID, TEXT, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_active_override(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.expire_subscription_override(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.create_subscription_override(UUID, TEXT, INTEGER, TEXT) IS 'Creates a new subscription override for a user. Admin only.';
COMMENT ON FUNCTION public.get_user_active_override(UUID) IS 'Gets the active override for a user. Users can check their own, admins can check any.';
COMMENT ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) IS 'Gets all active overrides with pagination. Admin only.';
COMMENT ON FUNCTION public.expire_subscription_override(UUID) IS 'Manually expires a subscription override. Admin only.';
COMMENT ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) IS 'Gets the complete audit trail of overrides. Admin only.';
