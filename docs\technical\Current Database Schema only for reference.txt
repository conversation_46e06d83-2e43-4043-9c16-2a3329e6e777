-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.admin_function_calls (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  function_name text NOT NULL,
  called_at timestamp with time zone DEFAULT now(),
  ip_address inet,
  CONSTRAINT admin_function_calls_pkey PRIMARY KEY (id),
  CONSTRAINT admin_function_calls_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.events (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid,
  event_type text NOT NULL CHECK (event_type = ANY (ARRAY['app_open'::text, 'login'::text, 'signin'::text, 'sign_in'::text, 'signed_in'::text])),
  timestamp timestamp with time zone NOT NULL DEFAULT now(),
  details jsonb,
  CONSTRAINT events_pkey PRIMARY KEY (id),
  CONSTRAINT events_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  message text NOT NULL,
  type text DEFAULT 'info'::text CHECK (type = ANY (ARRAY['info'::text, 'success'::text, 'warning'::text, 'error'::text])),
  is_active boolean DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  expires_at timestamp with time zone,
  created_by uuid,
  priority integer DEFAULT 0,
  target_audience text DEFAULT 'all'::text CHECK (target_audience = ANY (ARRAY['all'::text, 'pro'::text, 'edu'::text, 'standard'::text, 'guest'::text])),
  CONSTRAINT notifications_pkey PRIMARY KEY (id),
  CONSTRAINT notifications_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  username text,
  avatar_url text,
  updated_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  full_name text,
  institution text,
  country text,
  accounttype character varying DEFAULT 'standard'::character varying CHECK (accounttype::text = ANY (ARRAY['standard'::character varying, 'pro'::character varying, 'edu'::character varying]::text[])),
  stripe_customer_id text,
  edu_subscription_type text CHECK ((edu_subscription_type = ANY (ARRAY['free'::text, 'pro'::text])) OR edu_subscription_type IS NULL),
  is_admin boolean DEFAULT false,
  email text,
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
CREATE TABLE public.subscription_overrides (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  admin_id uuid NOT NULL,
  original_tier text NOT NULL CHECK (original_tier = ANY (ARRAY['guest'::text, 'standard'::text, 'edu'::text, 'edu_pro'::text, 'pro'::text])),
  override_tier text NOT NULL CHECK (override_tier = ANY (ARRAY['standard'::text, 'edu'::text, 'edu_pro'::text, 'pro'::text])),
  start_date timestamp with time zone NOT NULL DEFAULT now(),
  end_date timestamp with time zone NOT NULL,
  reason text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT subscription_overrides_pkey PRIMARY KEY (id),
  CONSTRAINT subscription_overrides_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT subscription_overrides_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES auth.users(id)
);
CREATE TABLE public.subscriptions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  stripe_customer_id text NOT NULL,
  stripe_subscription_id text UNIQUE,
  stripe_price_id text NOT NULL,
  status text NOT NULL,
  current_period_start timestamp with time zone,
  current_period_end timestamp with time zone,
  cancel_at_period_end boolean DEFAULT false,
  billing_cycle text NOT NULL DEFAULT 'monthly'::text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_datasets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  dataset_name character varying NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  file_path character varying,
  CONSTRAINT user_datasets_pkey PRIMARY KEY (id),
  CONSTRAINT user_datasets_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_notification_reads (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  notification_id uuid,
  read_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  CONSTRAINT user_notification_reads_pkey PRIMARY KEY (id),
  CONSTRAINT user_notification_reads_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT user_notification_reads_notification_id_fkey FOREIGN KEY (notification_id) REFERENCES public.notifications(id)
);
CREATE TABLE public.user_projects (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  project_name character varying NOT NULL,
  file_path text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_projects_pkey PRIMARY KEY (id),
  CONSTRAINT user_projects_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_settings (
  user_id uuid NOT NULL,
  notifications_enabled boolean NOT NULL DEFAULT true,
  email_notifications boolean NOT NULL DEFAULT true,
  mobile_notifications boolean NOT NULL DEFAULT false,
  theme_preference text NOT NULL DEFAULT 'system'::text,
  language_preference text NOT NULL DEFAULT 'en'::text,
  data_privacy boolean NOT NULL DEFAULT true,
  data_retention text NOT NULL DEFAULT 'standard'::text,
  two_factor_auth boolean NOT NULL DEFAULT false,
  analytics_consent boolean NOT NULL DEFAULT true,
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);