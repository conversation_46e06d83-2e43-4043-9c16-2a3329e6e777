-- Fix Admin Statistics Calculations Migration
-- This migration fixes the admin dashboard statistics by restoring proper time-based calculations
-- while keeping the materialized view improvements from previous migrations
-- Date: 2025-07-25

-- Drop the current get_user_statistics function that has hardcoded zeros
DROP FUNCTION IF EXISTS public.get_user_statistics();

-- Recreate get_user_statistics with proper time-based calculations using auth.users table
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '15s'
AS $$
DECLARE
  result JSON;
  cache_age INTERVAL;
  cache_exists BOOLEAN := FALSE;
  profile_stats JSON;
  dataset_stats JSON;
  time_stats JSON;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get profile statistics from profiles table
  SELECT json_build_object(
    'total_users', COUNT(*),
    'total_profiles', COUNT(*),
    'admin_users', COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true),
    'standard_users', COUNT(*) FILTER (WHERE COALESCE(accounttype, 'standard') = 'standard'),
    'pro_users', COUNT(*) FILTER (WHERE accounttype = 'pro'),
    'edu_users', COUNT(*) FILTER (WHERE accounttype = 'edu'),
    'edu_pro_users', COUNT(*) FILTER (WHERE accounttype = 'edu_pro'),
    'guest_users', COUNT(*) FILTER (WHERE accounttype = 'guest')
  ) INTO profile_stats
  FROM public.profiles;
  
  -- Get dataset statistics with error handling
  BEGIN
    SELECT json_build_object(
      'total_datasets', COUNT(*),
      'users_with_datasets', COUNT(DISTINCT user_id)
    ) INTO dataset_stats
    FROM public.user_datasets
    WHERE user_id IS NOT NULL;
  EXCEPTION
    WHEN OTHERS THEN
      -- If user_datasets table has issues, provide defaults
      dataset_stats := json_build_object(
        'total_datasets', 0,
        'users_with_datasets', 0
      );
  END;
  
  -- Get time-based statistics from auth.users table
  BEGIN
    SELECT json_build_object(
      'users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '7 days'), 0),
      'users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '30 days'), 0),
      'active_users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '7 days'), 0),
      'active_users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '30 days'), 0)
    ) INTO time_stats;
  EXCEPTION
    WHEN OTHERS THEN
      -- If auth.users access fails, provide defaults
      time_stats := json_build_object(
        'users_last_7_days', 0,
        'users_last_30_days', 0,
        'active_users_last_7_days', 0,
        'active_users_last_30_days', 0
      );
  END;
  
  -- Combine all statistics
  SELECT json_build_object(
    'total_users', (profile_stats->>'total_users')::INTEGER,
    'total_profiles', (profile_stats->>'total_profiles')::INTEGER,
    'admin_users', (profile_stats->>'admin_users')::INTEGER,
    'standard_users', (profile_stats->>'standard_users')::INTEGER,
    'pro_users', (profile_stats->>'pro_users')::INTEGER,
    'edu_users', (profile_stats->>'edu_users')::INTEGER,
    'edu_pro_users', (profile_stats->>'edu_pro_users')::INTEGER,
    'guest_users', (profile_stats->>'guest_users')::INTEGER,
    'total_datasets', (dataset_stats->>'total_datasets')::INTEGER,
    'users_with_datasets', (dataset_stats->>'users_with_datasets')::INTEGER,
    'users_last_7_days', (time_stats->>'users_last_7_days')::INTEGER,
    'users_last_30_days', (time_stats->>'users_last_30_days')::INTEGER,
    'active_users_last_7_days', (time_stats->>'active_users_last_7_days')::INTEGER,
    'active_users_last_30_days', (time_stats->>'active_users_last_30_days')::INTEGER,
    'cache_updated', NOW(),
    'cache_status', 'calculated_with_time_data'
  ) INTO result;

  RETURN result;
END;
$$;

-- Also update the enhanced statistics function to include proper time calculations
DROP FUNCTION IF EXISTS public.get_enhanced_user_statistics();

CREATE OR REPLACE FUNCTION public.get_enhanced_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '15s'
AS $$
DECLARE
  result JSON;
  profile_stats JSON;
  dataset_stats JSON;
  time_stats JSON;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get profile statistics
  SELECT json_build_object(
    'total_users', COUNT(*),
    'total_profiles', COUNT(*),
    'admin_users', COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true),
    'pro_users', COUNT(*) FILTER (WHERE accounttype = 'pro'),
    'edu_users', COUNT(*) FILTER (WHERE accounttype = 'edu'),
    'edu_pro_users', COUNT(*) FILTER (WHERE accounttype = 'edu_pro'),
    'standard_users', COUNT(*) FILTER (WHERE accounttype = 'standard'),
    'guest_users', COUNT(*) FILTER (WHERE accounttype = 'guest')
  ) INTO profile_stats
  FROM public.profiles;
  
  -- Get dataset statistics with error handling
  BEGIN
    SELECT json_build_object(
      'total_datasets', COUNT(*),
      'users_with_datasets', COUNT(DISTINCT user_id)
    ) INTO dataset_stats
    FROM public.user_datasets
    WHERE user_id IS NOT NULL;
  EXCEPTION
    WHEN OTHERS THEN
      dataset_stats := json_build_object(
        'total_datasets', 0,
        'users_with_datasets', 0
      );
  END;
  
  -- Get time-based statistics from auth.users table
  BEGIN
    SELECT json_build_object(
      'users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '7 days'), 0),
      'users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '30 days'), 0),
      'active_users_last_7_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '7 days'), 0),
      'active_users_last_30_days', COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '30 days'), 0)
    ) INTO time_stats;
  EXCEPTION
    WHEN OTHERS THEN
      time_stats := json_build_object(
        'users_last_7_days', 0,
        'users_last_30_days', 0,
        'active_users_last_7_days', 0,
        'active_users_last_30_days', 0
      );
  END;
  
  -- Combine all statistics
  SELECT json_build_object(
    'total_users', (profile_stats->>'total_users')::INTEGER,
    'total_profiles', (profile_stats->>'total_profiles')::INTEGER,
    'admin_users', (profile_stats->>'admin_users')::INTEGER,
    'pro_users', (profile_stats->>'pro_users')::INTEGER,
    'edu_users', (profile_stats->>'edu_users')::INTEGER,
    'edu_pro_users', (profile_stats->>'edu_pro_users')::INTEGER,
    'standard_users', (profile_stats->>'standard_users')::INTEGER,
    'guest_users', (profile_stats->>'guest_users')::INTEGER,
    'total_datasets', (dataset_stats->>'total_datasets')::INTEGER,
    'users_with_datasets', (dataset_stats->>'users_with_datasets')::INTEGER,
    'users_last_7_days', (time_stats->>'users_last_7_days')::INTEGER,
    'users_last_30_days', (time_stats->>'users_last_30_days')::INTEGER,
    'active_users_last_7_days', (time_stats->>'active_users_last_7_days')::INTEGER,
    'active_users_last_30_days', (time_stats->>'active_users_last_30_days')::INTEGER,
    'last_updated', NOW()
  ) INTO result;

  RETURN result;
END;
$$;

-- Update the materialized view to include time-based statistics
DROP MATERIALIZED VIEW IF EXISTS public.user_stats_cache CASCADE;

CREATE MATERIALIZED VIEW public.user_stats_cache AS
SELECT 
  COUNT(*) as total_users,
  COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true) as admin_users,
  COUNT(*) FILTER (WHERE accounttype = 'pro') as pro_users,
  COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')) as edu_users,
  COUNT(*) FILTER (WHERE accounttype = 'standard') as standard_users,
  COUNT(*) FILTER (WHERE accounttype = 'guest') as guest_users,
  -- Add time-based statistics to the materialized view
  COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '7 days'), 0) as users_last_7_days,
  COALESCE((SELECT COUNT(*) FROM auth.users WHERE created_at >= NOW() - INTERVAL '30 days'), 0) as users_last_30_days,
  COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '7 days'), 0) as active_users_last_7_days,
  COALESCE((SELECT COUNT(*) FROM auth.users WHERE last_sign_in_at >= NOW() - INTERVAL '30 days'), 0) as active_users_last_30_days,
  NOW() as last_updated
FROM public.profiles;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_user_stats_cache_updated ON public.user_stats_cache(last_updated);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_enhanced_user_statistics() TO authenticated;
GRANT SELECT ON public.user_stats_cache TO authenticated;

-- Refresh the materialized view with new data
REFRESH MATERIALIZED VIEW public.user_stats_cache;

-- Add helpful comments
COMMENT ON FUNCTION public.get_user_statistics() IS 'Fixed function with proper time-based calculations using auth.users table for accurate statistics';
COMMENT ON FUNCTION public.get_enhanced_user_statistics() IS 'Enhanced statistics function with proper time-based calculations and error handling';
COMMENT ON MATERIALIZED VIEW public.user_stats_cache IS 'Materialized view with proper time-based statistics from auth.users table';

-- Success message
SELECT 'Admin statistics functions updated successfully with proper time-based calculations.' as status;