import { supabase } from './supabaseClient';

/**
 * Test script to verify admin database functions are working correctly
 * This helps debug the SQL ambiguity issues
 */

export const testAdminFunctions = async () => {
  console.log('🧪 Testing admin database functions...');
  
  try {
    // Test 1: Check if is_user_admin function exists and works
    console.log('\n1️⃣ Testing is_user_admin function...');
    const { data: user } = await supabase.auth.getUser();
    
    if (!user.user) {
      console.log('❌ No authenticated user found');
      return;
    }
    
    const { data: isAdminResult, error: isAdminError } = await supabase.rpc('is_user_admin', {
      user_id: user.user.id
    });
    
    if (isAdminError) {
      console.error('❌ is_user_admin error:', isAdminError);
    } else {
      console.log('✅ is_user_admin result:', isAdminResult);
    }
    
    // Test 2: Test get_all_users function
    console.log('\n2️⃣ Testing get_all_users function...');
    const { data: usersResult, error: usersError } = await supabase.rpc('get_all_users', {
      page_size: 5,
      page_offset: 0,
      search_term: null
    });
    
    if (usersError) {
      console.error('❌ get_all_users error:', usersError);
      console.error('Error details:', {
        message: usersError.message,
        details: usersError.details,
        hint: usersError.hint,
        code: usersError.code
      });
    } else {
      console.log('✅ get_all_users result:', usersResult);
      console.log('📊 Number of users returned:', usersResult?.length || 0);
    }
    
    // Test 3: Test get_admin_users function
    console.log('\n3️⃣ Testing get_admin_users function...');
    const { data: adminUsersResult, error: adminUsersError } = await supabase.rpc('get_admin_users');
    
    if (adminUsersError) {
      console.error('❌ get_admin_users error:', adminUsersError);
    } else {
      console.log('✅ get_admin_users result:', adminUsersResult);
      console.log('👑 Number of admin users:', adminUsersResult?.length || 0);
    }
    
    // Test 4: Test get_user_statistics function
    console.log('\n4️⃣ Testing get_user_statistics function...');
    const { data: statsResult, error: statsError } = await supabase.rpc('get_user_statistics');
    
    if (statsError) {
      console.error('❌ get_user_statistics error:', statsError);
    } else {
      console.log('✅ get_user_statistics result:', statsResult);
    }
    
    console.log('\n🎉 Admin functions test completed!');
    
  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
};

/**
 * Test specifically the get_all_users function with different parameters
 */
export const testGetAllUsersDetailed = async () => {
  console.log('🔍 Detailed testing of get_all_users function...');
  
  try {
    // Test with different page sizes
    const testCases = [
      { page_size: 1, page_offset: 0, search_term: null },
      { page_size: 5, page_offset: 0, search_term: null },
      { page_size: 10, page_offset: 0, search_term: '' },
      { page_size: 5, page_offset: 0, search_term: 'test' }
    ];
    
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\nTest case ${i + 1}:`, testCase);
      
      const { data, error } = await supabase.rpc('get_all_users', testCase);
      
      if (error) {
        console.error(`❌ Test case ${i + 1} failed:`, error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
      } else {
        console.log(`✅ Test case ${i + 1} passed. Results:`, data?.length || 0, 'users');
        if (data && data.length > 0) {
          console.log('Sample user:', {
            id: data[0].id,
            email: data[0].email,
            username: data[0].username,
            is_admin: data[0].is_admin,
            accounttype: data[0].accounttype
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during detailed testing:', error);
  }
};

/**
 * Quick function to run all tests
 */
export const runAllAdminTests = async () => {
  await testAdminFunctions();
  await testGetAllUsersDetailed();
};
