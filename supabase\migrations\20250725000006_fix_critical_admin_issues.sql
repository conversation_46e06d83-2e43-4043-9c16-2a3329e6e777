-- Fix Critical Admin Dashboard Issues Migration
-- This migration addresses the three critical issues after recent migrations
-- Date: 2025-07-25

-- Issue 1: Fix User Statistics Materialized View
-- Drop and recreate the materialized view to ensure correct data
DROP MATERIALIZED VIEW IF EXISTS public.user_stats_cache;

-- Create materialized view with proper user count
CREATE MATERIALIZED VIEW public.user_stats_cache AS
SELECT 
  COUNT(*) as total_users,
  COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true) as admin_users,
  COUNT(*) FILTER (WHERE accounttype = 'pro') as pro_users,
  COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')) as edu_users,
  COUNT(*) FILTER (WHERE accounttype = 'standard') as standard_users,
  COUNT(*) FILTER (WHERE accounttype = 'guest') as guest_users,
  NOW() as last_updated
FROM public.profiles;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_user_stats_cache_updated ON public.user_stats_cache(last_updated);

-- Issue 2: Fix get_all_users function return type mismatch
-- Drop the problematic function and recreate with exact TypeScript interface match
DROP FUNCTION IF EXISTS public.get_all_users(INTEGER, INTEGER, TEXT);

CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '30s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with proper field mapping that matches TypeScript interface
  RETURN QUERY
  SELECT 
    p.id,
    -- Use username as email if email column doesn't exist, or use actual email if it does
    COALESCE(p.username, 'No email available')::TEXT as email,
    p.username,
    p.full_name,
    p.institution,
    p.country,
    p.avatar_url,
    p.updated_at,
    COALESCE(p.is_admin, false) as is_admin,
    COALESCE(p.accounttype, 'standard') as accounttype,
    COALESCE(p.updated_at, NOW()) as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at
  FROM public.profiles p
  WHERE (
    search_term IS NULL OR 
    search_term = '' OR
    COALESCE(p.full_name, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.username, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.institution, '') ILIKE '%' || search_term || '%'
  )
  ORDER BY p.updated_at DESC NULLS LAST
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Issue 3: Fix subscription override functions
-- Ensure all override functions work correctly after recent changes
DROP FUNCTION IF EXISTS public.get_all_active_overrides(INTEGER, INTEGER);
DROP FUNCTION IF EXISTS public.get_override_audit_trail(UUID, INTEGER, INTEGER);
DROP FUNCTION IF EXISTS public.get_expiring_overrides(INTEGER);

-- Recreate get_all_active_overrides with proper structure
CREATE OR REPLACE FUNCTION public.get_all_active_overrides(
  page_size INTEGER DEFAULT 100,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    COALESCE(up.username, 'Unknown User') as user_email,
    COALESCE(up.full_name, 'Unknown User') as user_full_name,
    COALESCE(ap.username, 'Unknown Admin') as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    GREATEST(0, EXTRACT(DAY FROM (so.end_date - NOW()))::INTEGER) as days_remaining,
    (so.end_date > NOW() AND so.start_date <= NOW()) as is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE so.end_date > NOW() AND so.start_date <= NOW()
  ORDER BY so.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Recreate get_override_audit_trail
CREATE OR REPLACE FUNCTION public.get_override_audit_trail(
  target_user_id UUID DEFAULT NULL,
  page_size INTEGER DEFAULT 100,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    COALESCE(up.username, 'Unknown User') as user_email,
    COALESCE(up.full_name, 'Unknown User') as user_full_name,
    COALESCE(ap.username, 'Unknown Admin') as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    GREATEST(0, EXTRACT(DAY FROM (so.end_date - NOW()))::INTEGER) as days_remaining,
    (so.end_date > NOW() AND so.start_date <= NOW()) as is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE (target_user_id IS NULL OR so.user_id = target_user_id)
  ORDER BY so.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Recreate get_expiring_overrides
CREATE OR REPLACE FUNCTION public.get_expiring_overrides(
  days_ahead INTEGER DEFAULT 7
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    COALESCE(up.username, 'Unknown User') as user_email,
    COALESCE(up.full_name, 'Unknown User') as user_full_name,
    COALESCE(ap.username, 'Unknown Admin') as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    GREATEST(0, EXTRACT(DAY FROM (so.end_date - NOW()))::INTEGER) as days_remaining,
    (so.end_date > NOW() AND so.start_date <= NOW()) as is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE so.end_date > NOW() 
    AND so.end_date <= NOW() + INTERVAL '1 day' * days_ahead
    AND so.start_date <= NOW()
  ORDER BY so.end_date ASC;
END;
$$;

-- Fix get_user_statistics to use the refreshed materialized view
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '10s'
AS $$
DECLARE
  result JSON;
  cache_age INTERVAL;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Check if cache needs refresh (older than 5 minutes)
  SELECT NOW() - last_updated INTO cache_age
  FROM public.user_stats_cache
  LIMIT 1;
  
  -- Refresh cache if it's stale or doesn't exist
  IF cache_age IS NULL OR cache_age > INTERVAL '5 minutes' THEN
    REFRESH MATERIALIZED VIEW public.user_stats_cache;
  END IF;
  
  -- Get stats from cache
  SELECT json_build_object(
    'total_users', total_users,
    'admin_users', admin_users,
    'pro_users', pro_users,
    'edu_users', edu_users,
    'standard_users', standard_users,
    'guest_users', guest_users,
    'users_last_7_days', 0,
    'users_last_30_days', 0,
    'active_users_last_7_days', 0,
    'active_users_last_30_days', 0,
    'cache_updated', last_updated
  ) INTO result
  FROM public.user_stats_cache;

  RETURN result;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_expiring_overrides(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;
GRANT SELECT ON public.user_stats_cache TO authenticated;

-- Initial cache population with correct data
REFRESH MATERIALIZED VIEW public.user_stats_cache;

-- Add helpful comments
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Fixed function that returns user data matching TypeScript interface exactly';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Fixed function using properly refreshed materialized view for accurate user counts';
COMMENT ON MATERIALIZED VIEW public.user_stats_cache IS 'Refreshed materialized view with correct user statistics';
