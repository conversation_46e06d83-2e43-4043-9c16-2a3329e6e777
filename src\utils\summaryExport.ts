// Summary export utilities for different formats

import { GeneratedSummary } from '../components/ResultsManager/ResultsSummaryGenerator';
import { formatNumber, formatPValue, formatConfidenceInterval } from '../context/ResultsContext';

export interface ExportOptions {
  format: 'markdown' | 'html' | 'docx' | 'pdf' | 'txt' | 'json';
  includeMetadata: boolean;
  includeTimestamp: boolean;
  customStyling?: string;
  pageSize?: 'A4' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
}

export interface ExportResult {
  content: string | Blob;
  filename: string;
  mimeType: string;
  success: boolean;
  error?: string;
}

// Default export options
const DEFAULT_EXPORT_OPTIONS: ExportOptions = {
  format: 'markdown',
  includeMetadata: true,
  includeTimestamp: true,
  pageSize: 'A4',
  orientation: 'portrait'
};

// Export class for handling different formats
export class SummaryExporter {
  private summary: GeneratedSummary;
  private options: ExportOptions;

  constructor(summary: GeneratedSummary, options: Partial<ExportOptions> = {}) {
    this.summary = summary;
    this.options = { ...DEFAULT_EXPORT_OPTIONS, ...options };
  }

  async export(): Promise<ExportResult> {
    try {
      switch (this.options.format) {
        case 'markdown':
          return this.exportMarkdown();
        case 'html':
          return this.exportHTML();
        case 'txt':
          return this.exportText();
        case 'json':
          return this.exportJSON();
        case 'docx':
          return this.exportDocx();
        case 'pdf':
          return this.exportPDF();
        default:
          throw new Error(`Unsupported export format: ${this.options.format}`);
      }
    } catch (error) {
      return {
        content: '',
        filename: '',
        mimeType: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown export error'
      };
    }
  }

  private exportMarkdown(): ExportResult {
    const content = this.generateMarkdownContent();
    const filename = this.generateFilename('md');
    
    return {
      content,
      filename,
      mimeType: 'text/markdown',
      success: true
    };
  }

  private exportHTML(): ExportResult {
    const content = this.generateHTMLContent();
    const filename = this.generateFilename('html');
    
    return {
      content,
      filename,
      mimeType: 'text/html',
      success: true
    };
  }

  private exportText(): ExportResult {
    const content = this.generateTextContent();
    const filename = this.generateFilename('txt');
    
    return {
      content,
      filename,
      mimeType: 'text/plain',
      success: true
    };
  }

  private exportJSON(): ExportResult {
    const content = JSON.stringify(this.summary, null, 2);
    const filename = this.generateFilename('json');
    
    return {
      content,
      filename,
      mimeType: 'application/json',
      success: true
    };
  }

  private exportDocx(): ExportResult {
    // For now, return a text version
    // In a full implementation, you'd use a library like docx
    const content = new Blob([this.generateTextContent()], { type: 'text/plain' });
    const filename = this.generateFilename('docx');
    
    return {
      content,
      filename,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      success: true
    };
  }

  private exportPDF(): ExportResult {
    // For now, return HTML that can be printed to PDF
    // In a full implementation, you'd use a library like jsPDF or puppeteer
    const htmlContent = this.generateHTMLContent();
    const content = new Blob([htmlContent], { type: 'text/html' });
    const filename = this.generateFilename('pdf');
    
    return {
      content,
      filename,
      mimeType: 'application/pdf',
      success: true
    };
  }

  private generateMarkdownContent(): string {
    let content = '';
    
    // Title
    content += `# ${this.summary.title}\n\n`;
    
    // Metadata
    if (this.options.includeMetadata) {
      content += this.generateMetadataSection();
    }
    
    // Executive Summary
    content += `## Executive Summary\n\n${this.summary.executiveSummary}\n\n`;
    
    // Key Findings
    content += `## Key Findings\n\n`;
    this.summary.keyFindings.forEach(finding => {
      content += `- ${finding}\n`;
    });
    content += '\n';
    
    // Statistical Summary
    content += `## Statistical Summary\n\n${this.summary.statisticalSummary}\n\n`;
    
    // Methods
    content += `## Methods\n\n${this.summary.methodsSummary}\n\n`;
    
    // Limitations
    content += `## Limitations\n\n`;
    this.summary.limitations.forEach(limitation => {
      content += `- ${limitation}\n`;
    });
    content += '\n';
    
    // Recommendations
    content += `## Recommendations\n\n`;
    this.summary.recommendations.forEach(recommendation => {
      content += `- ${recommendation}\n`;
    });
    
    return content;
  }

  private generateHTMLContent(): string {
    const markdownContent = this.generateMarkdownContent();
    
    // Basic markdown to HTML conversion
    let htmlContent = markdownContent
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>');
    
    // Wrap list items
    htmlContent = htmlContent.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
    
    // Add CSS styling
    const css = this.generateCSS();
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.summary.title}</title>
    <style>${css}</style>
</head>
<body>
    <div class="summary-container">
        <p>${htmlContent}</p>
    </div>
</body>
</html>`;
  }

  private generateTextContent(): string {
    let content = '';
    
    // Title
    content += `${this.summary.title}\n`;
    content += '='.repeat(this.summary.title.length) + '\n\n';
    
    // Metadata
    if (this.options.includeMetadata) {
      content += this.generateMetadataSection();
    }
    
    // Executive Summary
    content += `EXECUTIVE SUMMARY\n`;
    content += '-'.repeat(17) + '\n';
    content += `${this.summary.executiveSummary}\n\n`;
    
    // Key Findings
    content += `KEY FINDINGS\n`;
    content += '-'.repeat(12) + '\n';
    this.summary.keyFindings.forEach((finding, index) => {
      content += `${index + 1}. ${finding}\n`;
    });
    content += '\n';
    
    // Statistical Summary
    content += `STATISTICAL SUMMARY\n`;
    content += '-'.repeat(19) + '\n';
    content += `${this.summary.statisticalSummary}\n\n`;
    
    // Methods
    content += `METHODS\n`;
    content += '-'.repeat(7) + '\n';
    content += `${this.summary.methodsSummary}\n\n`;
    
    // Limitations
    content += `LIMITATIONS\n`;
    content += '-'.repeat(11) + '\n';
    this.summary.limitations.forEach((limitation, index) => {
      content += `${index + 1}. ${limitation}\n`;
    });
    content += '\n';
    
    // Recommendations
    content += `RECOMMENDATIONS\n`;
    content += '-'.repeat(15) + '\n';
    this.summary.recommendations.forEach((recommendation, index) => {
      content += `${index + 1}. ${recommendation}\n`;
    });
    
    return content;
  }

  private generateMetadataSection(): string {
    let metadata = '';
    
    if (this.options.includeTimestamp) {
      metadata += `**Generated:** ${this.summary.timestamp.toLocaleString()}\n`;
    }
    
    metadata += `**Word Count:** ${this.summary.wordCount}\n`;
    metadata += `**Export Format:** ${this.options.format.toUpperCase()}\n\n`;
    
    return metadata;
  }

  private generateCSS(): string {
    return `
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #fff;
      }
      
      .summary-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 30px;
      }
      
      h1 {
        color: #1976d2;
        border-bottom: 3px solid #1976d2;
        padding-bottom: 10px;
        margin-bottom: 30px;
      }
      
      h2 {
        color: #424242;
        margin-top: 30px;
        margin-bottom: 15px;
        border-left: 4px solid #1976d2;
        padding-left: 15px;
      }
      
      h3 {
        color: #666;
        margin-top: 20px;
        margin-bottom: 10px;
      }
      
      ul {
        padding-left: 20px;
      }
      
      li {
        margin-bottom: 8px;
      }
      
      p {
        margin-bottom: 15px;
        text-align: justify;
      }
      
      .metadata {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-size: 0.9em;
      }
      
      @media print {
        body {
          margin: 0;
          padding: 0;
        }
        
        .summary-container {
          box-shadow: none;
          padding: 20px;
        }
      }
    `;
  }

  private generateFilename(extension: string): string {
    const timestamp = this.options.includeTimestamp 
      ? `_${this.summary.timestamp.toISOString().split('T')[0]}`
      : '';
    
    const baseFilename = this.summary.title
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .toLowerCase();
    
    return `${baseFilename}${timestamp}.${extension}`;
  }
}

// Utility functions for quick exports
export async function exportSummaryAsMarkdown(summary: GeneratedSummary): Promise<void> {
  const exporter = new SummaryExporter(summary, { format: 'markdown' });
  const result = await exporter.export();
  
  if (result.success) {
    downloadFile(result.content as string, result.filename, result.mimeType);
  } else {
    throw new Error(result.error);
  }
}

export async function exportSummaryAsHTML(summary: GeneratedSummary): Promise<void> {
  const exporter = new SummaryExporter(summary, { format: 'html' });
  const result = await exporter.export();
  
  if (result.success) {
    downloadFile(result.content as string, result.filename, result.mimeType);
  } else {
    throw new Error(result.error);
  }
}

export async function exportSummaryAsText(summary: GeneratedSummary): Promise<void> {
  const exporter = new SummaryExporter(summary, { format: 'txt' });
  const result = await exporter.export();
  
  if (result.success) {
    downloadFile(result.content as string, result.filename, result.mimeType);
  } else {
    throw new Error(result.error);
  }
}

export async function exportSummaryAsJSON(summary: GeneratedSummary): Promise<void> {
  const exporter = new SummaryExporter(summary, { format: 'json' });
  const result = await exporter.export();
  
  if (result.success) {
    downloadFile(result.content as string, result.filename, result.mimeType);
  } else {
    throw new Error(result.error);
  }
}

// Helper function to trigger file download
function downloadFile(content: string | Blob, filename: string, mimeType: string): void {
  const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

// Integration with existing Results Manager export system
export function addSummaryToResults(
  summary: GeneratedSummary,
  addResult: (result: any) => void
): void {
  const resultItem = {
    id: `summary_${Date.now()}`,
    title: summary.title,
    type: 'Summary',
    timestamp: summary.timestamp,
    component: 'SummaryDisplay',
    data: {
      summary: summary.fullText,
      executiveSummary: summary.executiveSummary,
      keyFindings: summary.keyFindings,
      statisticalSummary: summary.statisticalSummary,
      methodsSummary: summary.methodsSummary,
      limitations: summary.limitations,
      recommendations: summary.recommendations,
      wordCount: summary.wordCount,
      generatedAt: summary.timestamp.toISOString()
    },
    selected: false
  };
  
  addResult(resultItem);
}

// Email sharing functionality
export function shareSummaryByEmail(
  summary: GeneratedSummary,
  recipientEmail?: string
): void {
  const subject = encodeURIComponent(summary.title);
  const body = encodeURIComponent(
    `Please find the analysis summary below:\n\n${summary.fullText}`
  );
  
  const mailtoLink = `mailto:${recipientEmail || ''}?subject=${subject}&body=${body}`;
  window.open(mailtoLink);
}

// Copy to clipboard functionality
export async function copySummaryToClipboard(
  summary: GeneratedSummary,
  format: 'markdown' | 'text' | 'html' = 'text'
): Promise<boolean> {
  try {
    let content: string;
    
    switch (format) {
      case 'markdown':
        const markdownExporter = new SummaryExporter(summary, { format: 'markdown' });
        const markdownResult = await markdownExporter.export();
        content = markdownResult.content as string;
        break;
      case 'html':
        const htmlExporter = new SummaryExporter(summary, { format: 'html' });
        const htmlResult = await htmlExporter.export();
        content = htmlResult.content as string;
        break;
      default:
        content = summary.fullText;
    }
    
    await navigator.clipboard.writeText(content);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}