import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
  Collapse,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  Launch as LaunchIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  // Import category icons
  ShowChart as DescriptiveIcon,
  Assessment as InferentialIcon,
  CompareArrows as CorrelationIcon,
  School as AdvancedIcon,
  BarChart as VisualizationIcon,
  Storage as DataManagementIcon,
  TableChart as PublicationIcon,
  Calculate as SampleSizeIcon,
  Science as EpiCalcIcon,
  TrendingUp as CICalculatorIcon,
} from '@mui/icons-material';
import { TextField } from '@mui/material';
import { useAuth } from '../context/AuthContext'; // Import useAuth
import useSocialMeta from '../hooks/useSocialMeta';
import SocialShareWidget from '../components/UI/SocialShareWidget';

// Import analysis options from each source file
import { descriptiveStatsOptions } from '../components/DescriptiveStats/DescriptiveStatsOptions';
import { inferentialStatsOptions } from '../components/InferentialStats/InferentialStatsOptions';
import { correlationAnalysisOptions } from '../components/CorrelationAnalysis/CorrelationAnalysisOptions';
import { advancedAnalysisOptions } from '../components/AdvancedAnalysisAliases/AdvancedAnalysisOptions';
import { epiCalcOptions } from '../components/EpiCalc/EpiCalcOptions';
import { sampleSizeCalculatorOptions } from '../components/SampleSizeCalculators/SampleSizeCalculatorsOptions';
import { ciCalculatorOptions } from '../components/CICalculators/CICalculatorsOptions';
import { dataVisualizationOptions } from '../components/Visualization/DataVisualizationOptions';
import { dataManagementOptions } from '../components/DataManagement/DataManagementOptions';
import { publicationReadyOptions } from '../components/PublicationReady/PublicationReadyOptions';

// Define a common interface for all analysis options
interface AnalysisOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: string; // Use a broader type for category
  color: string;
}

// Define category structure that matches sidebar navigation
interface CategoryGroup {
  name: string;
  icon: React.ReactNode;
  color: string;
  options: AnalysisOption[];
}

interface AnalysisIndexPageProps {
  onNavigate: (path: string) => void;
}

const AnalysisIndexPage: React.FC<AnalysisIndexPageProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { canAccessProFeatures } = useAuth(); // Get accountType from context
  
  // Initialize social meta for analysis index page with enhanced SEO
  useSocialMeta({
    title: 'Statistical Analysis Tools - DataStatPro | Complete Statistical Software Suite',
    description: 'Professional statistical analysis platform with 50+ tools: descriptive statistics, inferential tests, ANOVA, regression, correlation analysis, data visualization, and publication-ready outputs. Free alternative to SPSS, R, and SAS.',
    keywords: 'statistical analysis software, data analysis tools, statistics calculator, SPSS alternative, R alternative, SAS alternative, statistical tests, t-test, ANOVA, regression analysis, correlation matrix, chi-square test, data visualization, publication ready tables, APA format, research statistics, biostatistics, epidemiology calculator, sample size calculator, power analysis, meta-analysis, survival analysis',
    url: window.location.href,
    image: '/images/analysis-index-preview.png'
  });

  // Create hierarchical category groups that match sidebar navigation
  const categoryGroups: CategoryGroup[] = useMemo(() => {
    const groups: CategoryGroup[] = [
      {
        name: 'Data Management',
        icon: <DataManagementIcon />,
        color: '#795548', // Brown
        options: dataManagementOptions,
      },
      {
        name: 'Descriptive Statistics',
        icon: <DescriptiveIcon />,
        color: '#4CAF50', // Green
        options: descriptiveStatsOptions,
      },
      {
        name: 'Inferential Statistics',
        icon: <InferentialIcon />,
        color: '#2196F3', // Blue
        options: inferentialStatsOptions,
      },
      {
        name: 'Correlation Analysis',
        icon: <CorrelationIcon />,
        color: '#FF9800', // Orange
        options: correlationAnalysisOptions,
      },
      {
        name: 'Advanced Analysis',
        icon: <AdvancedIcon />,
        color: '#9C27B0', // Purple
        options: advancedAnalysisOptions,
      },
      {
        name: 'Data Visualization',
        icon: <VisualizationIcon />,
        color: '#00BCD4', // Cyan
        options: dataVisualizationOptions,
      },
      {
        name: 'Sample Size Calculator',
        icon: <SampleSizeIcon />,
        color: '#FF5722', // Deep Orange
        options: sampleSizeCalculatorOptions,
      },
      {
        name: 'Epi Calculator',
        icon: <EpiCalcIcon />,
        color: '#607D8B', // Blue Grey
        options: epiCalcOptions,
      },
      {
        name: 'CI Calculator',
        icon: <CICalculatorIcon />,
        color: '#E91E63', // Pink
        options: ciCalculatorOptions,
      },
      {
        name: 'Publication Ready',
        icon: <PublicationIcon />,
        color: '#3F51B5', // Indigo
        options: publicationReadyOptions,
      },
    ];

    // Filter groups based on search query
    if (searchQuery.trim()) {
      return groups.map(group => ({
        ...group,
        options: group.options.filter(option =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.shortDescription.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.detailedDescription.toLowerCase().includes(searchQuery.toLowerCase())
        )
      })).filter(group => group.options.length > 0);
    }

    return groups;
  }, [searchQuery]);

  // State for collapsible sections
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Toggle section expansion
  const toggleSection = (sectionName: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionName)) {
      newExpanded.delete(sectionName);
    } else {
      newExpanded.add(sectionName);
    }
    setExpandedSections(newExpanded);
  };

  // Initialize all sections as collapsed by default
  React.useEffect(() => {
    setExpandedSections(new Set());
  }, [categoryGroups]);

  // Auto-expand sections that contain search results
  React.useEffect(() => {
    if (searchQuery.trim()) {
      const sectionsWithResults = new Set<string>();
      categoryGroups.forEach(group => {
        const hasMatchingOptions = group.options.some(option =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.shortDescription.toLowerCase().includes(searchQuery.toLowerCase()) ||
          option.detailedDescription.toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (hasMatchingOptions) {
          sectionsWithResults.add(group.name);
        }
      });
      setExpandedSections(sectionsWithResults);
    } else {
      // When search is cleared, collapse all sections
      setExpandedSections(new Set());
    }
  }, [searchQuery, categoryGroups]);

  // Generate structured data for search engines and AI
  const generateStructuredData = () => {
    const baseUrl = window.location.origin;
    
    return {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "DataStatPro Statistical Analysis Suite",
      "description": "Comprehensive statistical analysis platform with 50+ professional tools for researchers, students, and data analysts. Includes descriptive statistics, inferential tests, regression analysis, data visualization, and publication-ready outputs.",
      "applicationCategory": "StatisticalSoftware",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "featureList": [
        "Descriptive Statistics Analysis",
        "Inferential Statistical Tests",
        "T-Tests (One-sample, Independent, Paired)",
        "ANOVA (One-way, Two-way, Repeated Measures)",
        "Non-parametric Tests (Mann-Whitney, Wilcoxon, Kruskal-Wallis)",
        "Correlation Analysis (Pearson, Spearman, Kendall)",
        "Linear and Logistic Regression",
        "Chi-Square Tests",
        "Data Visualization (Bar Charts, Histograms, Box Plots, Scatter Plots)",
        "Publication-Ready Tables (APA Format)",
        "Sample Size Calculators",
        "Power Analysis",
        "Meta-Analysis Tools",
        "Survival Analysis",
        "Factor Analysis",
        "Reliability Analysis",
        "Data Import/Export",
        "Missing Data Handling"
      ],
      "audience": {
        "@type": "Audience",
        "audienceType": ["Researchers", "Students", "Data Analysts", "Statisticians", "Healthcare Professionals"]
      },
      "educationalUse": "Research and Education",
      "isAccessibleForFree": true,
      "url": `${baseUrl}/analysis-index`,
      "sameAs": [
        `${baseUrl}`,
        `${baseUrl}/statistics`,
        `${baseUrl}/data-analysis`
      ],
      "potentialAction": {
        "@type": "UseAction",
        "target": `${baseUrl}/analysis-index`,
        "description": "Access comprehensive statistical analysis tools"
      }
    };
  };

  return (
    <>
      <Helmet>
        {/* Enhanced meta tags for AI and search engines */}
        <title>Statistical Analysis Tools | DataStatPro - Complete Statistical Software Suite</title>
        <meta name="description" content="Professional statistical analysis platform with 50+ tools: descriptive statistics, inferential tests, ANOVA, regression, correlation analysis, data visualization, and publication-ready outputs. Free alternative to SPSS, R, and SAS." />
        <meta name="keywords" content="statistical analysis software, data analysis tools, statistics calculator, SPSS alternative, R alternative, SAS alternative, statistical tests, t-test, ANOVA, regression analysis, correlation matrix, chi-square test, data visualization, publication ready tables, APA format, research statistics, biostatistics, epidemiology calculator, sample size calculator, power analysis, meta-analysis, survival analysis" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Statistical analysis and data science tools for research and education" />
        <meta name="ai:category" content="Statistics, Data Analysis, Research Tools" />
        <meta name="ai:audience" content="Researchers, Students, Data Analysts, Healthcare Professionals" />
        <meta name="ai:features" content="Descriptive Statistics, Inferential Tests, Regression Analysis, Data Visualization, Publication Tools" />
        
        {/* Structured data for AI understanding */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* Additional structured data for educational content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Course",
            "name": "Statistical Analysis Methods",
            "description": "Learn statistical analysis through hands-on tools and guided workflows",
            "provider": {
              "@type": "Organization",
              "name": "DataStatPro"
            },
            "educationalLevel": "Beginner to Advanced",
            "teaches": [
              "Descriptive Statistics",
              "Hypothesis Testing",
              "Regression Analysis",
              "Data Visualization",
              "Research Methods"
            ]
          })}
        </script>
        
        {/* FAQ structured data for common statistical questions */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What statistical tests should I use for my data?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "The choice of statistical test depends on your data type and research question. For comparing means between two groups, use t-tests. For three or more groups, use ANOVA. For categorical data, use chi-square tests. For relationships between variables, use correlation or regression analysis."
                }
              },
              {
                "@type": "Question",
                "name": "How do I choose between parametric and non-parametric tests?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Use parametric tests (t-tests, ANOVA) when your data is normally distributed. Use non-parametric tests (Mann-Whitney, Kruskal-Wallis, Wilcoxon) when data is not normally distributed or when dealing with ordinal data."
                }
              },
              {
                "@type": "Question",
                "name": "What sample size do I need for my study?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Sample size depends on effect size, power (typically 80%), and significance level (typically 0.05). Use our sample size calculators for specific study designs including one-sample, two-sample, and ANOVA designs."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Statistical Analysis Tools - Complete Research Suite
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Professional statistical analysis platform with 50+ tools for descriptive statistics, 
          inferential tests, regression analysis, data visualization, and publication-ready outputs. 
          Free alternative to SPSS, R, and SAS for researchers, students, and data analysts.
        </Typography>
        <Typography variant="body1" color="text.secondary">
          <strong>Key Features:</strong> T-Tests • ANOVA • Regression Analysis • Correlation Matrix • 
          Chi-Square Tests • Data Visualization • Publication Tables • Sample Size Calculators • 
          Power Analysis • Meta-Analysis • Survival Analysis
        </Typography>
      </Paper>

      {/* Search Bar */}
      <Box sx={{ mb: 2 }}>
        <TextField
          label="Search Statistical Tests & Analysis Tools"
          variant="outlined"
          fullWidth
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Try: 't-test', 'ANOVA', 'correlation', 'regression', 'chi-square', 'sample size'"
          InputProps={{
            startAdornment: <SearchIcon color="action" />,
          }}
          aria-label="Search statistical analysis tools and methods"
          helperText="Search by test name, statistical method, or research question"
        />
      </Box>

      {/* Hierarchical Category Sections */}
      {categoryGroups.map((group) => (
        <Box key={group.name} sx={{ mb: 4 }}>
          {/* Category Header */}
          <Paper
            elevation={1}
            sx={{
              p: 2,
              mb: 2,
              backgroundColor: alpha(group.color, 0.05),
              borderLeft: `4px solid ${group.color}`,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                backgroundColor: alpha(group.color, 0.1),
                transform: 'translateX(4px)',
              }
            }}
            onClick={() => toggleSection(group.name)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: group.color,
                    width: 40,
                    height: 40,
                  }}
                >
                  {group.icon}
                </Avatar>
                <Box>
                  <Typography 
                  variant="h5" 
                  fontWeight="bold" 
                  color={group.color}
                  itemProp="name"
                >
                  {group.name}
                </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {group.options.length} analysis{group.options.length !== 1 ? 'es' : ''} available
                  </Typography>
                </Box>
              </Box>
              <IconButton size="small" sx={{ color: group.color }}>
                {expandedSections.has(group.name) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Box>
          </Paper>

          {/* Category Options Grid */}
          <Collapse in={expandedSections.has(group.name)} timeout="auto" unmountOnExit>
            <Grid container spacing={3}>
              {group.options.map((option) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography 
                      variant="h6" 
                      fontWeight="bold"
                      itemProp="name"
                    >
                      {option.name}
                    </Typography>
                    {((option.path.startsWith('advanced-analysis') || option.path.startsWith('publication-ready')) && !canAccessProFeatures) && (
                      <Box
                        sx={{
                          ml: 1,
                          bgcolor: theme.palette.warning.main,
                          color: 'white',
                          fontSize: '0.65rem',
                          px: 0.7,
                          borderRadius: 1,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 16
                        }}
                      >
                        PRO
                      </Box>
                    )}
                  </Box>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  disabled={
                    (option.path.startsWith('advanced-analysis') || option.path.startsWith('publication-ready')) &&
                    !canAccessProFeatures
                  }
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Launch ${option.name} analysis tool`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
              ))}
            </Grid>
          </Collapse>
        </Box>
      ))}

      {/* Optional: Add a help section similar to other pages if needed */}
      {/*
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              ... help text based on categories ...
            </Typography>
          </Box>
        </Box>
      </Paper>
      */}
      
      {/* Social Share Widget */}
      <SocialShareWidget 
        variant="floating"
        position="bottom-right"
        platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
      />
    </Container>
    </>
  );
};

export default AnalysisIndexPage;
