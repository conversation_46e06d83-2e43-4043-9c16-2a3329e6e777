import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  useTheme,
  alpha,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  CalculateOutlined as CalculateIcon,
  HelpOutline as HelpIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Functions as FunctionsIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import { StatsCard } from '../UI';
import { chiSquareTest as calculateChiSquare } from '../../utils/stats/non-parametric';
import jStat from 'jstat'; // Import jStat for accurate p-value calculation

// Cross-Sectional Calculator class with statistical methods
class CrossSectionalCalculatorUtils {
  // Prevalence with Wilson Score CI
  static prevalence(a: number, c: number, total: number): { prevalence: number; ci: [number, number] } {
    const p = (a + c) / total;
    const z = 1.96; // 95% CI
    const denominator = 1 + (z*z)/total;
    const centre = p + (z*z)/(2*total);
    const spread = z * Math.sqrt((p*(1-p))/total + (z*z)/(4*total*total));
    return {
      prevalence: p,
      ci: [(centre - spread)/denominator, (centre + spread)/denominator] as [number, number]
    };
  }

  // Prevalence Ratio with Log-Normal CI
  static prevalenceRatio(a: number, b: number, c: number, d: number): { pr: number; ci: [number, number] } {
    const p1 = a / (a + b);
    const p0 = c / (c + d);
    const pr = p1 / p0;
    const se = Math.sqrt((1/a - 1/(a+b)) + (1/c - 1/(c+d)));
    return {
      pr: pr,
      ci: [Math.exp(Math.log(pr) - 1.96*se), Math.exp(Math.log(pr) + 1.96*se)] as [number, number]
    };
  }

  // Prevalence Odds Ratio with Woolf's CI
  static prevalenceOddsRatio(a: number, b: number, c: number, d: number): { por: number; ci: [number, number] } {
    const por = (a * d) / (b * c);
    const se = Math.sqrt(1/a + 1/b + 1/c + 1/d);
    return {
      por: por,
      ci: [Math.exp(Math.log(por) - 1.96*se), Math.exp(Math.log(por) + 1.96*se)] as [number, number]
    };
  }

  // Uncorrected Chi-square Test
  static uncorrectedChiSquareTest(a: number, b: number, c: number, d: number) {
    const contingencyTable = [[a, b], [c, d]];
    return calculateChiSquare(contingencyTable);
  }

  // Chi-square Test with Yates' Correction
  static chiSquareTestYates(a: number, b: number, c: number, d: number) {
    const n = a + b + c + d;
    const expected = [
      ((a + c) * (a + b)) / n,
      ((b + d) * (a + b)) / n,
      ((a + c) * (c + d)) / n,
      ((b + d) * (c + d)) / n
    ];
    
    const chi2 = Math.pow(Math.abs(a - expected[0]) - 0.5, 2) / expected[0] +
                Math.pow(Math.abs(b - expected[1]) - 0.5, 2) / expected[1] +
                Math.pow(Math.abs(c - expected[2]) - 0.5, 2) / expected[2] +
                Math.pow(Math.abs(d - expected[3]) - 0.5, 2) / expected[3];
    
    // For Yates' corrected chi-square, df is always 1 for a 2x2 table
    // The p-value calculation should ideally use a proper chi-square CDF from a library
    // For now, we'll use the imported chiSquareTest's p-value calculation for consistency
    // with the library's approach, but apply it to the Yates' corrected chi2.
    // For Yates' corrected chi-square, df is always 1 for a 2x2 table
    return { chi2: chi2, p: this.chiSquarePValue(chi2, 1) };
  }

  // Helper function to calculate p-value from chi-square statistic
  static chiSquarePValue(chi2: number, df: number): number | null {
    if (df <= 0) {
      return null; // p-value not meaningful for df <= 0
    }
    // Use jStat for accurate p-value calculation from chi-square distribution
    return 1 - jStat.chisquare.cdf(chi2, df);
  }

  // Fisher's Exact Test (2x2)
  static fisherExact(a: number, b: number, c: number, d: number) {
    // This is a simplified implementation
    // For a more accurate calculation, you would use a proper statistical library
    const factorial = (n: number): number => {
      if (n === 0 || n === 1) return 1;
      let result = 1;
      for (let i = 2; i <= n; i++) result *= i;
      return result;
    };

    const hypergeometric = (a: number, b: number, c: number, d: number) => {
      return (factorial(a+b) * factorial(c+d) * factorial(a+c) * factorial(b+d)) /
             (factorial(a) * factorial(b) * factorial(c) * factorial(d) * factorial(a+b+c+d));
    };
    
    const original = hypergeometric(a, b, c, d);
    let p = 0;
    
    // This is a simplified approach - a full implementation would be more complex
    // and would handle larger numbers better
    for(let x = 0; x <= Math.min(a + b, a + c); x++) {
      const y = a + b - x;
      const z = a + c - x;
      const w = b + d - y;
      if (y >= 0 && z >= 0 && w >= 0) {
        const current = hypergeometric(x, y, z, w);
        if(current <= original) p += current;
      }
    }
    
    return p;
  }
}

const CrossSectionalCalculator: React.FC = () => {
  const theme = useTheme();

  // State for 2x2 table values
  const [cellValues, setCellValues] = useState({
    a: 0,
    b: 0,
    c: 0,
    d: 0
  });

  // Function to render mathematical formulas with KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // State for calculation results
  const [results, setResults] = useState<{
    prevalence?: { prevalence: number; ci: [number, number] };
    prevalenceRatio?: { pr: number; ci: [number, number] };
    prevalenceOddsRatio?: { por: number; ci: [number, number] };
    chiSquareYates?: { chi2: number; p: number | null }; // Revert to number | null
    chiSquareUncorrected?: { chiSquare: number; df: number; pValue: number; cramersV: number; hasLowExpectedFrequencies?: boolean; lowFrequencyCells?: Array<{row: number, col: number, expected: number}>; };
    fisherExact?: number | null;
  }>({});

  // Handle input changes
  const handleInputChange = (cell: 'a' | 'b' | 'c' | 'd', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setCellValues(prev => ({
      ...prev,
      [cell]: isNaN(numValue) ? 0 : numValue
    }));
  };

  // Calculate results
  const calculateResults = () => {
    const { a, b, c, d } = cellValues;
    const total = a + b + c + d;

    if (total === 0) return;

    try {
      const prevalenceResult = CrossSectionalCalculatorUtils.prevalence(a, c, total);
      const prevalenceRatioResult = CrossSectionalCalculatorUtils.prevalenceRatio(a, b, c, d);
      const prevalenceOddsRatioResult = CrossSectionalCalculatorUtils.prevalenceOddsRatio(a, b, c, d);
      const chiSquareYatesResult = CrossSectionalCalculatorUtils.chiSquareTestYates(a, b, c, d);
      const chiSquareUncorrectedResult = CrossSectionalCalculatorUtils.uncorrectedChiSquareTest(a, b, c, d);
      
      // Only calculate Fisher's exact test for small sample sizes
      let fisherExactResult = null;
      if (total < 100) {
        fisherExactResult = CrossSectionalCalculatorUtils.fisherExact(a, b, c, d);
      }

      setResults({
        prevalence: prevalenceResult,
        prevalenceRatio: prevalenceRatioResult,
        prevalenceOddsRatio: prevalenceOddsRatioResult,
        chiSquareYates: chiSquareYatesResult,
        chiSquareUncorrected: chiSquareUncorrectedResult,
        fisherExact: fisherExactResult
      });
    } catch (error) {
      console.error('Error calculating results:', error);
    }
  };

  // Reset form
  const resetForm = () => {
    setCellValues({ a: 0, b: 0, c: 0, d: 0 });
    setResults({});
  };

  // Format number with specified decimal places
  const formatNumber = (num: number, decimals: number = 3) => {
    return num.toFixed(decimals);
  };

  // Format confidence interval
  const formatCI = (ci: [number, number], decimals: number = 3) => {
    return `${formatNumber(ci[0], decimals)} - ${formatNumber(ci[1], decimals)}`;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Cross-Sectional Study Calculator</Typography>
      <Typography variant="body1" paragraph>
        Calculate measures of association for cross-sectional studies, including prevalence ratios and odds ratios.
      </Typography>
      
      {/* Formulas Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="formulas-content"
          id="formulas-header"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FunctionsIcon color="primary" />
            <Typography variant="h6">Mathematical Formulas</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            {/* Prevalence */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Prevalence
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('P = \\frac{a + c}{n} = \\frac{\\text{Total cases}}{\\text{Total sample size}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Wilson Score 95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = \\frac{p + \\frac{z^2}{2n} \\pm z\\sqrt{\\frac{p(1-p)}{n} + \\frac{z^2}{4n^2}}}{1 + \\frac{z^2}{n}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: p = sample proportion, z = 1.96 (for 95% CI), n = sample size
                </Typography>
              </Paper>
            </Grid>

            {/* Prevalence Ratio */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Prevalence Ratio (PR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('PR = \\frac{P_1}{P_0} = \\frac{\\frac{a}{a+b}}{\\frac{c}{c+d}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Log-Normal 95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = PR \\times \\exp(\\pm 1.96 \\times SE_{\\ln(PR)})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(PR)} = \\sqrt{\\frac{1}{a} - \\frac{1}{a+b} + \\frac{1}{c} - \\frac{1}{c+d}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: P₁ = prevalence in exposed group, P₀ = prevalence in unexposed group
                </Typography>
              </Paper>
            </Grid>

            {/* Prevalence Odds Ratio */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Prevalence Odds Ratio (POR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('POR = \\frac{\\text{Odds}_1}{\\text{Odds}_0} = \\frac{\\frac{a}{b}}{\\frac{c}{d}} = \\frac{ad}{bc}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Woolf\'s 95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = POR \\times \\exp(\\pm 1.96 \\times SE_{\\ln(POR)})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(POR)} = \\sqrt{\\frac{1}{a} + \\frac{1}{b} + \\frac{1}{c} + \\frac{1}{d}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: a, b, c, d are the cell counts in the 2×2 table
                </Typography>
              </Paper>
            </Grid>

            {/* Chi-Square Tests */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Chi-Square Tests
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Uncorrected Chi-Square:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2 = \\frac{n(ad - bc)^2}{(a+b)(c+d)(a+c)(b+d)}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Yates\' Corrected Chi-Square:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2_{Yates} = \\frac{n(|ad - bc| - \\frac{n}{2})^2}{(a+b)(c+d)(a+c)(b+d)}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Both tests have 1 degree of freedom. Yates\' correction is more conservative for small samples.
                </Typography>
              </Paper>
            </Grid>

            {/* Fisher's Exact Test */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Fisher\'s Exact Test
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Hypergeometric Probability:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('P(X = a) = \\frac{\\binom{a+b}{a} \\binom{c+d}{c}}{\\binom{n}{a+c}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  The exact p-value is calculated by summing probabilities for all tables with the same marginal totals that are as extreme or more extreme than the observed table. Recommended for small sample sizes (n &lt; 100).
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>2×2 Contingency Table</Typography>
        <Typography variant="body2" paragraph>
          Enter the values for your 2×2 table to calculate epidemiological measures.
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={7}>
            <TableContainer component={Paper} variant="outlined">
              <Table aria-label="2x2 contingency table">
                <TableHead>
                  <TableRow>
                    <TableCell></TableCell>
                    <TableCell align="center">Disease+</TableCell>
                    <TableCell align="center">Disease-</TableCell>
                    <TableCell align="center">Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row">Exposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.a || ''}
                        onChange={(e) => handleInputChange('a', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.b || ''}
                        onChange={(e) => handleInputChange('b', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.b}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Unexposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.c || ''}
                        onChange={(e) => handleInputChange('c', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.d || ''}
                        onChange={(e) => handleInputChange('d', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.c + cellValues.d}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Total</TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.c}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.b + cellValues.d}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.b + cellValues.c + cellValues.d}</Typography>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CalculateIcon />}
                onClick={calculateResults}
                disabled={Object.values(cellValues).every(val => val === 0)}
              >
                Calculate
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetForm}
              >
                Reset
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Enter the values in the 2×2 table and click Calculate to compute epidemiological measures.
            </Typography>
          </Grid>
        </Grid>

        {/* Results Section */}
        {Object.keys(results).length > 0 && (
          <Box sx={{ mt: 4 }}>
            <Divider sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>Results</Typography>
            
            <Grid container spacing={3}>
              {/* Prevalence */}
              {results.prevalence && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Prevalence"
                    value={`${formatNumber(results.prevalence.prevalence * 100)}%`}
                    description={`95% CI: ${formatNumber(results.prevalence.ci[0] * 100)}% - ${formatNumber(results.prevalence.ci[1] * 100)}%`}
                    color="primary"
                    variant="outlined"
                  />
                </Grid>
              )}
              
              {/* Prevalence Ratio */}
              {results.prevalenceRatio && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Prevalence Ratio"
                    value={formatNumber(results.prevalenceRatio.pr)}
                    description={`95% CI: ${formatCI(results.prevalenceRatio.ci)}`}
                    color="secondary"
                    variant="outlined"
                    tooltip="Ratio of prevalence in exposed vs. unexposed groups"
                  />
                </Grid>
              )}
              
              {/* Prevalence Odds Ratio */}
              {results.prevalenceOddsRatio && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Prevalence Odds Ratio"
                    value={formatNumber(results.prevalenceOddsRatio.por)}
                    description={`95% CI: ${formatCI(results.prevalenceOddsRatio.ci)}`}
                    color="info"
                    variant="outlined"
                    tooltip="Odds ratio of disease in exposed vs. unexposed groups"
                  />
                </Grid>
              )}
              
              {/* Chi-Square Test (Uncorrected) */}
              {results.chiSquareUncorrected && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Chi-Square Test (Uncorrected)"
                    value={formatNumber(results.chiSquareUncorrected.chiSquare)}
                    description={`p-value: ${formatNumber(results.chiSquareUncorrected.pValue)} (df=${results.chiSquareUncorrected.df})`}
                    color="warning"
                    variant="outlined"
                    tooltip="Uncorrected Chi-square test for 2×2 tables"
                  />
                </Grid>
              )}

              {/* Chi-Square Test (Yates) */}
              {results.chiSquareYates && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Chi-Square Test (Yates)"
                    value={formatNumber(results.chiSquareYates.chi2)}
                    description={results.chiSquareYates.p !== null ? 
                      `p-value: ${formatNumber(results.chiSquareYates.p)}` : 
                      'p-value: Not calculated'}
                    color="warning"
                    variant="outlined"
                    tooltip="Chi-square test with Yates' correction for 2×2 tables"
                  />
                </Grid>
              )}
              
              {/* Fisher's Exact Test */}
              {results.fisherExact !== null && results.fisherExact !== undefined && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Fisher's Exact Test"
                    value={`p = ${formatNumber(results.fisherExact)}`}
                    description={`Significance at α = 0.05: ${results.fisherExact < 0.05 ? 'Yes' : 'No'}`}
                    color="success"
                    variant="outlined"
                    tooltip="Fisher's exact test for small sample sizes"
                  />
                </Grid>
              )}
            </Grid>
            
            {/* Interpretation */}
            <Paper 
              variant="outlined" 
              sx={{ 
                mt: 3, 
                p: 2, 
                backgroundColor: alpha(theme.palette.info.main, 0.05),
                borderColor: alpha(theme.palette.info.main, 0.2)
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Interpretation Guidelines:
              </Typography>
              <Typography variant="body2">
                • <strong>Prevalence Ratio = 1:</strong> No association between exposure and disease<br />
                • <strong>Prevalence Ratio &gt; 1:</strong> Positive association (exposure may increase disease prevalence)<br />
                • <strong>Prevalence Ratio &lt; 1:</strong> Negative association (exposure may decrease disease prevalence)<br />
                • <strong>Statistical significance:</strong> If the 95% confidence interval does not include 1, or p-value &lt; 0.05
              </Typography>
            </Paper>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default CrossSectionalCalculator;
