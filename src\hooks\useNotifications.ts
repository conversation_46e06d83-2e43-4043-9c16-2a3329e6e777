import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../context/AuthContext';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_active: boolean;
  created_at: string;
  expires_at: string | null;
  priority: number;
  target_audience: 'all' | 'pro' | 'edu' | 'standard' | 'guest';
  is_read?: boolean; // Computed field based on user_notification_reads
}

export const useNotifications = () => {
  const { user, accountType, isGuest } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Determine user's audience type for filtering
  const getUserAudience = useCallback(() => {
    if (isGuest) return 'guest';
    if (!user) return 'guest';
    return accountType || 'standard';
  }, [user, accountType, isGuest]);

  // Fetch notifications from Supabase
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Skip notification fetching when on admin dashboard to prevent conflicts
      const isOnAdminDashboard = location.pathname.includes('/admin-dashboard');
      if (isOnAdminDashboard) {
        console.log('⚠️ Skipping notification fetch on admin dashboard');
        setNotifications([]);
        setLoading(false);
        return;
      }

      const userAudience = getUserAudience();

      // Fetch active notifications that haven't expired
      const { data: notificationsData, error: notificationsError } = await supabase
        .from('notifications')
        .select('*')
        .eq('is_active', true)
        .or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`)
        .in('target_audience', ['all', userAudience])
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      if (notificationsError) {
        console.error('Error fetching notifications:', notificationsError);
        setError('Failed to load notifications');
        return;
      }

      if (!notificationsData) {
        setNotifications([]);
        return;
      }

      // If user is authenticated, fetch read status
      if (user) {
        const notificationIds = notificationsData.map(n => n.id);
        
        const { data: readData, error: readError } = await supabase
          .from('user_notification_reads')
          .select('notification_id')
          .eq('user_id', user.id)
          .in('notification_id', notificationIds);

        if (readError) {
          console.error('Error fetching read status:', readError);
        }

        const readNotificationIds = new Set(readData?.map(r => r.notification_id) || []);
        
        const notificationsWithReadStatus = notificationsData.map(notification => ({
          ...notification,
          is_read: readNotificationIds.has(notification.id)
        }));

        setNotifications(notificationsWithReadStatus);
      } else {
        // For guests, all notifications are unread
        const notificationsWithReadStatus = notificationsData.map(notification => ({
          ...notification,
          is_read: false
        }));
        
        setNotifications(notificationsWithReadStatus);
      }
    } catch (err) {
      console.error('Error in fetchNotifications:', err);
      setError('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [user, getUserAudience]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!user) return; // Guests can't mark as read

    try {
      const { error } = await supabase
        .from('user_notification_reads')
        .upsert({
          user_id: user.id,
          notification_id: notificationId
        });

      if (error) {
        console.error('Error marking notification as read:', error);
        return;
      }

      // Update local state immediately for better UX
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );
    } catch (err) {
      console.error('Error in markAsRead:', err);
    }
  }, [user]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!user || notifications.length === 0) return;

    const unreadNotifications = notifications.filter(n => !n.is_read);
    if (unreadNotifications.length === 0) return;

    try {
      const readRecords = unreadNotifications.map(notification => ({
        user_id: user.id,
        notification_id: notification.id
      }));

      const { error } = await supabase
        .from('user_notification_reads')
        .upsert(readRecords);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return;
      }

      // Update local state immediately for better UX
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );
    } catch (err) {
      console.error('Error in markAllAsRead:', err);
    }
  }, [user, notifications]);

  // Get unread count
  const unreadCount = notifications.filter(n => !n.is_read).length;

  // Fetch notifications when user or account type changes
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Reset notifications state when user logs out
  useEffect(() => {
    if (!user && !isGuest) {
      setNotifications([]);
      setLoading(false);
      setError(null);
    }
  }, [user, isGuest]);

  // Timeout to prevent infinite loading states
  useEffect(() => {
    if (loading) {
      const timeout = setTimeout(() => {
        console.warn('Notification loading timeout - forcing loading to false');
        setLoading(false);
      }, 10000); // 10 second timeout

      return () => clearTimeout(timeout);
    }
  }, [loading]);

  // Set up real-time subscription for new notifications
  useEffect(() => {
    // Don't set up subscription if user is not available
    if (!user && !isGuest) {
      return;
    }

    const userAudience = getUserAudience();

    const subscription = supabase
      .channel(`notifications-${user?.id || 'guest'}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `target_audience=in.(all,${userAudience})`
        },
        (payload) => {
          console.log('Notification change detected:', payload);
          // Only refetch for INSERT/UPDATE/DELETE events, not for read status changes
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE' || payload.eventType === 'DELETE') {
            fetchNotifications();
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up notification subscription');
      subscription.unsubscribe();
    };
  }, [fetchNotifications, getUserAudience, user, isGuest]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refetch: fetchNotifications
  };
};
