import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  alpha,
  AppBar,
  Toolbar,
  Avatar,
  IconButton
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Star as StarIcon,
  School as SchoolIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Security as SecurityIcon,
  Devices as DevicesIcon,
  Support as SupportIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import FeatureComparisonTable from '../components/UI/FeatureComparisonTable';

interface PricingTier {
  id: string;
  name: string;
  price: string;
  period: string;
  monthlyPrice?: string;
  annualPrice?: string;
  annualSavings?: string;
  billingOptions?: ('monthly' | 'annual')[];
  description: string;
  features: Array<{
    name: string;
    included: boolean;
    description?: string;
  }>;
  highlighted?: boolean;
  icon: React.ReactNode;
  buttonText: string;
  buttonVariant: 'contained' | 'outlined';
  badge?: string;
  color: string;
  emailRequirement?: string;
  upgradeOption?: {
    name: string;
    price: string;
    description: string;
    features: string[];
  };
}

const PricingPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  useEffect(() => {
    const timer = setTimeout(() => setVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const pricingTiers: PricingTier[] = [
    {
      id: 'guest',
      name: 'Guest Access',
      price: 'Free',
      period: 'Forever',
      description: 'Perfect for exploring and learning statistical analysis',
      icon: <PersonIcon sx={{ fontSize: 32 }} />,
      color: '#4caf50',
      buttonText: 'Start Exploring',
      buttonVariant: 'outlined',
      features: [
        { name: 'Full app exploration', included: true, description: 'Access all features and interface' },
        { name: 'Pro features preview', included: true, description: 'See advanced analysis capabilities' },
        { name: 'Built-in sample datasets', included: true, description: 'Practice with curated datasets' },
        { name: 'Teaching & demonstration', included: true, description: 'Perfect for educational use' },
        { name: 'Personal data import', included: false, description: 'Cannot upload your own data' },
        { name: 'Data persistence', included: false, description: 'Data resets on page refresh' },
        { name: 'Cloud storage', included: false },
        { name: 'Pro analysis features', included: false }
      ]
    },
    {
      id: 'standard',
      name: 'Standard Account',
      price: 'Free',
      period: 'Currently',
      description: 'Full local analysis capabilities with personal data',
      icon: <BusinessIcon sx={{ fontSize: 32 }} />,
      color: '#2196f3',
      buttonText: 'Create Account',
      buttonVariant: 'outlined',
      badge: 'Most Popular',
      features: [
        { name: 'All Guest Access features', included: true },
        { name: 'Personal data import', included: true, description: 'Upload CSV, Excel, and other formats' },
        { name: 'Local data storage', included: true, description: 'Data saved in your browser' },
        { name: 'Full analysis suite', included: true, description: 'Complete statistical toolkit' },
        { name: 'Export capabilities', included: true, description: 'Save results and visualizations' },
        { name: 'Pro analysis features', included: false, description: 'Advanced statistical methods' },
        { name: 'Cloud synchronization', included: false },
        { name: 'Multi-device access', included: false }
      ]
    },
    {
      id: 'pro',
      name: 'Pro Account',
      price: billingCycle === 'monthly' ? '$10' : '$96',
      period: billingCycle === 'monthly' ? 'per month' : 'per year',
      monthlyPrice: '$10',
      annualPrice: '$96',
      annualSavings: '20%',
      billingOptions: ['monthly', 'annual'],
      description: 'Professional analysis with cloud features and advanced tools',
      icon: <StarIcon sx={{ fontSize: 32 }} />,
      color: '#ff9800',
      buttonText: 'Get Started',
      buttonVariant: 'contained',
      highlighted: true,
      features: [
        { name: 'All Standard features', included: true },
        { name: 'Advanced Analysis', included: true, description: 'Advanced statistical methods' },
        { name: 'Publication Ready', included: true, description: 'APA tables, methods text, figures' },
        { name: 'Cloud data storage', included: true, description: 'Secure cloud backup' },
        { name: 'Multi-device sync', included: true, description: 'Access from anywhere' },
        { name: 'Priority support', included: true, description: 'Faster response times' },
        { name: 'Collaboration tools', included: true, description: 'Share projects with team' },
        { name: 'API access', included: true, description: 'Integrate with other tools' }
      ]
    },
    {
      id: 'edu',
      name: 'Educational Account',
      price: 'Free',
      period: 'for .edu emails',
      emailRequirement: 'Educational email required (.edu)',
      description: 'Advanced Analysis included free for educational users',
      icon: <SchoolIcon sx={{ fontSize: 32 }} />,
      color: '#9c27b0',
      buttonText: 'Create Educational Account',
      buttonVariant: 'outlined',
      badge: 'Advanced Analysis Free',
      features: [
        { name: 'All Standard Account features', included: true },
        { name: 'Advanced Analysis', included: true, description: 'FREE for .edu users' },
        { name: 'Advanced statistical tests', included: true, description: 'ANOVA, Regression, etc.' },
        { name: 'Interactive visualizations', included: true, description: 'Professional charts' },
        { name: 'Publication Ready', included: false, description: 'Upgrade to Pro for $10/month' },
        { name: 'Cloud Storage', included: false, description: 'Upgrade to Pro for $10/month' },
        { name: 'Multi-device sync', included: false, description: 'Upgrade to Pro for $10/month' }
      ],
      upgradeOption: {
        name: 'Educational Pro',
        price: '$10/month',
        description: 'Same price as regular Pro - no educational discount',
        features: [
          'Keep all current features',
          'Add Publication Ready tools',
          'Add Cloud Storage',
          'Priority support'
        ]
      }
    }
  ];

  const handleGetStarted = (tierId: string) => {
    switch (tierId) {
      case 'guest':
        navigate('/app');
        break;
      case 'standard':
        navigate('/app#/auth/login');
        break;
      case 'pro':
        window.open('mailto:<EMAIL>?subject=Early Access Request - DataStatPro Pro Account&body=Hi DataStatPro Team,%0D%0A%0D%0AI would like to request early access to Pro account features during the development phase.%0D%0A%0D%0AMy use case:%0D%0A[Please describe how you plan to use DataStatPro]%0D%0A%0D%0ASpecific features I\'m interested in:%0D%0A- Advanced statistical methods%0D%0A- Cloud data storage%0D%0A- Multi-device synchronization%0D%0A- [Add other features you\'re interested in]%0D%0A%0D%0AThank you for considering my request.%0D%0A%0D%0ABest regards');
        break;
      case 'edu':
        // Educational accounts are free - direct to registration
        navigate('/app#/auth/login');
        break;
      default:
        navigate('/app');
    }
  };

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Navigation Header */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
        }}
        elevation={0}
      >
        <Toolbar sx={{
          minHeight: { xs: 56, sm: 64 },
          px: { xs: 1, sm: 2 }
        }}>
          <IconButton
            color="inherit"
            aria-label="go back"
            edge="start"
            onClick={() => navigate(-1)}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            <ArrowBackIcon />
          </IconButton>

          {/* Logo and Title */}
          <Box
            onClick={() => navigate('/')}
            sx={{
              display: 'flex',
              alignItems: 'center',
              mr: 1,
              cursor: 'pointer',
              textDecoration: 'none',
              color: 'inherit'
            }}
          >
            <Avatar
              src="/logo.png"
              alt="DataStatPro"
              sx={{
                width: { xs: 28, sm: 32 },
                height: { xs: 28, sm: 32 },
                mr: 1.5
              }}
            />
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              noWrap
              component="div"
              sx={{
                fontWeight: 'bold',
                letterSpacing: '0.5px'
              }}
            >
              DataStatPro
            </Typography>
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          {/* Home Button */}
          <Button
            color="inherit"
            startIcon={<HomeIcon />}
            onClick={() => navigate('/')}
            sx={{
              ml: 1,
              display: { xs: 'none', sm: 'flex' }
            }}
          >
            Home
          </Button>

          {/* App Button */}
          <Button
            color="inherit"
            onClick={() => navigate('/app/dashboard')}
            sx={{
              ml: 1,
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.2)',
              }
            }}
          >
            Launch App
          </Button>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ pt: { xs: 7, sm: 8 }, pb: { xs: 4, md: 8 } }}>
        <Container maxWidth="lg">
        {/* Header Section */}
        <Fade in={visible} timeout={800}>
          <Box textAlign="center" mb={6}>
            <Typography
              variant="h2"
              component="h1"
              fontWeight="bold"
              sx={{
                mb: 2,
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Choose Your Plan
            </Typography>
            <Typography
              variant="h5"
              color="text.secondary"
              sx={{ mb: 4, maxWidth: '600px', mx: 'auto', lineHeight: 1.6 }}
            >
              Select the perfect plan for your statistical analysis needs
            </Typography>
            
            {/* Development Notice */}
            <Alert
              severity="info"
              sx={{
                maxWidth: '800px',
                mx: 'auto',
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontSize: '1rem'
                }
              }}
            >
              <Typography variant="body1" fontWeight="medium">
                🚧 Development Phase Notice
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                DataStatPro is currently in active development. Payment processing is not yet available.
                All features are accessible for testing and evaluation purposes.
              </Typography>
            </Alert>



            {/* Billing Cycle Toggle */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: 'background.paper',
                  borderRadius: 3,
                  p: 0.5,
                  border: 1,
                  borderColor: 'divider',
                  boxShadow: 1
                }}
              >
                <Button
                  variant={billingCycle === 'monthly' ? 'contained' : 'text'}
                  onClick={() => setBillingCycle('monthly')}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    textTransform: 'none',
                    fontWeight: billingCycle === 'monthly' ? 'bold' : 'normal'
                  }}
                >
                  Monthly
                </Button>
                <Button
                  variant={billingCycle === 'annual' ? 'contained' : 'text'}
                  onClick={() => setBillingCycle('annual')}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    textTransform: 'none',
                    fontWeight: billingCycle === 'annual' ? 'bold' : 'normal',
                    position: 'relative'
                  }}
                >
                  Annual
                  <Chip
                    label="Save 20%"
                    size="small"
                    color="success"
                    sx={{
                      position: 'absolute',
                      top: -8,
                      right: -8,
                      fontSize: '0.7rem',
                      height: 18
                    }}
                  />
                </Button>
              </Box>
            </Box>
          </Box>
        </Fade>

        {/* Pricing Cards */}
        <Grid container spacing={4} justifyContent="center">
          {pricingTiers.map((tier, index) => (
            <Grid item xs={12} sm={6} lg={3} key={tier.id}>
              <Slide direction="up" in={visible} timeout={800 + index * 200}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    borderRadius: 3,
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    ...(tier.highlighted && {
                      border: `2px solid ${tier.color}`,
                      transform: 'scale(1.05)',
                      boxShadow: `0 12px 40px ${alpha(tier.color, 0.2)}`,
                    }),
                    '&:hover': {
                      transform: tier.highlighted ? 'scale(1.05)' : 'translateY(-8px)',
                      boxShadow: `0 16px 50px ${alpha(tier.color, 0.15)}`,
                    }
                  }}
                >
                  {/* Badge */}
                  {tier.badge && (
                    <Chip
                      label={tier.badge}
                      sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        bgcolor: tier.color,
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '0.75rem'
                      }}
                    />
                  )}

                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    {/* Header */}
                    <Box textAlign="center" mb={3}>
                      <Box
                        sx={{
                          width: 64,
                          height: 64,
                          borderRadius: '50%',
                          bgcolor: alpha(tier.color, 0.1),
                          color: tier.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 2
                        }}
                      >
                        {tier.icon}
                      </Box>
                      <Typography variant="h5" fontWeight="bold" gutterBottom>
                        {tier.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {tier.description}
                      </Typography>
                      <Box>
                        <Typography
                          variant="h3"
                          component="span"
                          fontWeight="bold"
                          color={tier.color}
                        >
                          {tier.price}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" component="span">
                          {tier.period && ` ${tier.period}`}
                        </Typography>
                        {tier.billingOptions && billingCycle === 'annual' && tier.annualSavings && (
                          <Box sx={{ mt: 1 }}>
                            <Chip
                              label={`Save ${tier.annualSavings}`}
                              size="small"
                              color="success"
                              sx={{ fontSize: '0.75rem' }}
                            />
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                              ${(parseFloat(tier.annualPrice?.replace('$', '') || '0') / 12).toFixed(0)}/month when billed annually
                            </Typography>
                          </Box>
                        )}
                        {tier.emailRequirement && (
                          <Typography variant="caption" color="warning.main" sx={{ display: 'block', mt: 1, fontStyle: 'italic' }}>
                            {tier.emailRequirement}
                          </Typography>
                        )}
                      </Box>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    {/* Features List */}
                    <List dense sx={{ p: 0 }}>
                      {tier.features.map((feature, featureIndex) => (
                        <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            {feature.included ? (
                              <CheckIcon sx={{ color: 'success.main', fontSize: 20 }} />
                            ) : (
                              <CancelIcon sx={{ color: 'text.disabled', fontSize: 20 }} />
                            )}
                          </ListItemIcon>
                          <ListItemText
                            primary={feature.name}
                            secondary={feature.description}
                            primaryTypographyProps={{
                              variant: 'body2',
                              color: feature.included ? 'text.primary' : 'text.disabled'
                            }}
                            secondaryTypographyProps={{
                              variant: 'caption',
                              sx: { fontSize: '0.7rem' }
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>

                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant={tier.buttonVariant}
                      fullWidth
                      size="large"
                      onClick={() => handleGetStarted(tier.id)}
                      sx={{
                        py: 1.5,
                        fontWeight: 'bold',
                        borderRadius: 2,
                        ...(tier.buttonVariant === 'contained' && {
                          bgcolor: tier.color,
                          '&:hover': {
                            bgcolor: alpha(tier.color, 0.8),
                          }
                        }),
                        ...(tier.buttonVariant === 'outlined' && {
                          borderColor: tier.color,
                          color: tier.color,
                          '&:hover': {
                            bgcolor: alpha(tier.color, 0.1),
                            borderColor: tier.color,
                          }
                        })
                      }}
                    >
                      {tier.buttonText}
                    </Button>
                  </CardActions>
                </Card>
              </Slide>
            </Grid>
          ))}
        </Grid>

        {/* Feature Comparison Table */}
        <Fade in={visible} timeout={1000}>
          <Box mt={8}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              Compare Features Across All Plans
            </Typography>
            <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
              See exactly what's included with each account type
            </Typography>
            <FeatureComparisonTable showDescriptions={true} />
          </Box>
        </Fade>

        {/* Additional Information */}
        <Fade in={visible} timeout={1200}>
          <Box mt={8} textAlign="center">
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Why Choose DataStatPro?
            </Typography>
            <Grid container spacing={4} sx={{ mt: 2 }}>
              {[
                {
                  icon: <SecurityIcon sx={{ fontSize: 40, color: '#4caf50' }} />,
                  title: 'Secure & Private',
                  description: 'Your data is protected with enterprise-grade security'
                },
                {
                  icon: <DevicesIcon sx={{ fontSize: 40, color: '#2196f3' }} />,
                  title: 'Cross-Platform',
                  description: 'Works seamlessly across all devices and browsers'
                },
                {
                  icon: <SupportIcon sx={{ fontSize: 40, color: '#ff9800' }} />,
                  title: '24/7 Support',
                  description: 'Get help whenever you need it from our expert team'
                }
              ].map((benefit, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <Box textAlign="center">
                    {benefit.icon}
                    <Typography variant="h6" fontWeight="bold" sx={{ mt: 2, mb: 1 }}>
                      {benefit.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {benefit.description}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Fade>

        {/* FAQ Section */}
        <Fade in={visible} timeout={1400}>
          <Box mt={8}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              Frequently Asked Questions
            </Typography>
            <Grid container spacing={3} sx={{ mt: 2 }}>
              {[
                {
                  question: 'How can I get early access to Pro or Educational features?',
                  answer: 'During the development phase, you can request early access to Pro or Educational features <NAME_EMAIL>. Include details about your use case and which specific features you\'d like to evaluate. We\'ll review your request and provide access for testing purposes.'
                },
                {
                  question: 'When will payment processing be available?',
                  answer: 'Payment processing will be implemented in the next phase of development. All users can currently access the full application for testing and evaluation.'
                },
                {
                  question: 'What happens to my data during development?',
                  answer: 'During the development phase, data is stored locally in your browser. Cloud storage will be available with Pro accounts once payment processing is implemented.'
                },
                {
                  question: 'Can I upgrade or downgrade my plan later?',
                  answer: 'Yes, once payment processing is available, you can change your plan at any time. Changes will be reflected in your next billing cycle.'
                },
                {
                  question: 'Is there a free trial for Pro features?',
                  answer: 'Currently, all features are accessible for testing. Once payment processing is implemented, we will offer a free trial period for Pro accounts.'
                }
              ].map((faq, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Card sx={{ height: '100%', p: 2 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {faq.question}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {faq.answer}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Fade>
        </Container>
      </Box>
    </Box>
  );
};

export default PricingPage;
