# DataStatPro Development Guide

## Overview

This guide provides practical instructions for developing and extending DataStatPro. It's designed to help developers and AI assistants understand common development patterns, workflows, and best practices.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Git
- VS Code (recommended) with TypeScript and React extensions
- Supabase account (for cloud features)

### Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd DataStatPro

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your Supabase credentials

# Start development server
npm run dev
```

### Environment Variables
```bash
# .env.local
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_key
VITE_APP_VERSION=1.0.0
```

## 🏗️ Development Workflows

### Adding a New Statistical Test

#### 1. Create the Statistical Function
```typescript
// utils/stats/newTest.ts
export interface NewTestResult extends StatisticalTestResult {
  specificStatistic: number;
  additionalMetric: number;
}

export function performNewTest(
  data: number[],
  options: NewTestOptions = {}
): NewTestResult {
  // Implement statistical calculation
  const statistic = calculateStatistic(data);
  const pValue = calculatePValue(statistic);
  
  return {
    testName: 'New Statistical Test',
    statistic,
    pValue,
    interpretation: generateInterpretation(pValue),
    specificStatistic: statistic,
    additionalMetric: calculateAdditional(data)
  };
}
```

#### 2. Create the UI Component
```typescript
// components/InferentialStats/NewTest/NewTestComponent.tsx
import React, { useState } from 'react';
import { useData } from '../../../contexts/DataContext';
import { performNewTest } from '../../../utils/stats/newTest';

interface NewTestComponentProps {
  onResultsChange: (results: any) => void;
}

export const NewTestComponent: React.FC<NewTestComponentProps> = ({ onResultsChange }) => {
  const { activeDataset } = useData();
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const [results, setResults] = useState<NewTestResult | null>(null);
  
  const handleAnalysis = () => {
    if (!activeDataset || !selectedColumn) return;
    
    const columnData = getColumnData(activeDataset, selectedColumn);
    const numericData = columnData.filter(v => typeof v === 'number') as number[];
    
    const testResults = performNewTest(numericData);
    setResults(testResults);
    onResultsChange(testResults);
  };
  
  return (
    <Card>
      <CardContent>
        <Typography variant="h6">New Statistical Test</Typography>
        
        {/* Column Selection */}
        <FormControl fullWidth margin="normal">
          <InputLabel>Select Variable</InputLabel>
          <Select
            value={selectedColumn}
            onChange={(e) => setSelectedColumn(e.target.value)}
          >
            {activeDataset?.columns
              .filter(col => col.type === DataType.NUMERIC)
              .map(col => (
                <MenuItem key={col.id} value={col.id}>
                  {col.name}
                </MenuItem>
              ))
            }
          </Select>
        </FormControl>
        
        {/* Analysis Button */}
        <Button
          variant="contained"
          onClick={handleAnalysis}
          disabled={!selectedColumn}
          fullWidth
          sx={{ mt: 2 }}
        >
          Run Analysis
        </Button>
        
        {/* Results Display */}
        {results && (
          <Box mt={3}>
            <Typography variant="h6">Results</Typography>
            <ResultsTable results={results} />
            <InterpretationText interpretation={results.interpretation} />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};
```

#### 3. Add Route Configuration
```typescript
// routes/statisticsRoutes.ts
import { NewTestComponent } from '../components/InferentialStats/NewTest/NewTestComponent';

export const statisticsRoutes = [
  // ... existing routes
  {
    path: '/statistics/new-test',
    element: <NewTestComponent />,
    title: 'New Statistical Test',
    description: 'Perform new statistical analysis',
    category: 'Inferential Statistics',
    requiresAuth: false,
    requiresPro: false
  }
];
```

#### 4. Update Navigation
```typescript
// components/Layout/Sidebar.tsx
const statisticsMenuItems = [
  // ... existing items
  {
    title: 'New Test',
    path: '/statistics/new-test',
    icon: <CalculateIcon />,
    description: 'Perform new statistical test'
  }
];
```

### Adding a New Visualization Type

#### 1. Create Chart Component
```typescript
// components/Visualization/Charts/NewChartType.tsx
import React from 'react';
import { ResponsiveContainer, ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

interface NewChartTypeProps {
  data: any[];
  config: ChartConfig;
  width?: number;
  height?: number;
}

export const NewChartType: React.FC<NewChartTypeProps> = ({ data, config, width = 600, height = 400 }) => {
  return (
    <ResponsiveContainer width={width} height={height}>
      <ComposedChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey={config.xAxis.dataKey} />
        <YAxis />
        <Tooltip />
        <Legend />
        {config.series.map((series, index) => (
          <Bar
            key={index}
            dataKey={series.dataKey}
            fill={series.color}
            name={series.name}
          />
        ))}
      </ComposedChart>
    </ResponsiveContainer>
  );
};
```

#### 2. Register Chart Type
```typescript
// utils/visualization/chartRegistry.ts
import { NewChartType } from '../../components/Visualization/Charts/NewChartType';

export const chartComponents = {
  // ... existing charts
  newChart: NewChartType
};

export const chartTypes = [
  // ... existing types
  {
    id: 'newChart',
    name: 'New Chart Type',
    component: NewChartType,
    description: 'Description of new chart type',
    dataRequirements: {
      minColumns: 2,
      supportedTypes: [DataType.NUMERIC, DataType.CATEGORICAL]
    }
  }
];
```

### Adding a New Data Import Format

#### 1. Create Parser Function
```typescript
// utils/parsers/newFormatParser.ts
export interface NewFormatParseOptions {
  encoding?: string;
  customOption?: boolean;
}

export async function parseNewFormat(
  file: File,
  options: NewFormatParseOptions = {}
): Promise<{ columns: Column[]; rows: DataRow[] }> {
  const text = await file.text();
  
  // Implement parsing logic
  const parsedData = customParseLogic(text, options);
  
  return {
    columns: parsedData.columns,
    rows: parsedData.rows
  };
}
```

#### 2. Register Parser
```typescript
// utils/dataImport.ts
import { parseNewFormat } from './parsers/newFormatParser';

export const supportedFormats = {
  // ... existing formats
  '.newext': {
    name: 'New Format',
    parser: parseNewFormat,
    options: {
      encoding: 'utf-8',
      customOption: false
    }
  }
};
```

## 🧪 Testing Guidelines

### Unit Testing Statistical Functions
```typescript
// __tests__/stats/newTest.test.ts
import { performNewTest } from '../../utils/stats/newTest';

describe('New Statistical Test', () => {
  test('should calculate correct statistic', () => {
    const data = [1, 2, 3, 4, 5];
    const result = performNewTest(data);
    
    expect(result.statistic).toBeCloseTo(expectedValue, 6);
    expect(result.pValue).toBeLessThan(1);
    expect(result.pValue).toBeGreaterThan(0);
  });
  
  test('should handle edge cases', () => {
    const emptyData: number[] = [];
    expect(() => performNewTest(emptyData)).toThrow();
    
    const singleValue = [1];
    expect(() => performNewTest(singleValue)).toThrow();
  });
});
```

### Component Testing
```typescript
// __tests__/components/NewTestComponent.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { NewTestComponent } from '../../components/InferentialStats/NewTest/NewTestComponent';
import { DataProvider } from '../../contexts/DataContext';

const mockDataset = {
  id: 'test',
  name: 'Test Dataset',
  columns: [
    { id: 'col1', name: 'Variable 1', type: DataType.NUMERIC }
  ],
  rows: [{ col1: 1 }, { col1: 2 }, { col1: 3 }]
};

test('renders new test component', () => {
  render(
    <DataProvider value={{ activeDataset: mockDataset }}>
      <NewTestComponent onResultsChange={jest.fn()} />
    </DataProvider>
  );
  
  expect(screen.getByText('New Statistical Test')).toBeInTheDocument();
  expect(screen.getByLabelText('Select Variable')).toBeInTheDocument();
});
```

## 🎨 UI/UX Guidelines

### Component Structure
```typescript
// Standard component structure
interface ComponentProps {
  // Props interface
}

export const Component: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // State management
  const [state, setState] = useState();
  
  // Context usage
  const { contextValue } = useContext();
  
  // Effect hooks
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  // Event handlers
  const handleEvent = useCallback(() => {
    // Handler logic
  }, [dependencies]);
  
  // Render
  return (
    <Container>
      {/* Component JSX */}
    </Container>
  );
};
```

### Styling Patterns
```typescript
// Use Material-UI styling
const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
    marginBottom: theme.spacing(1)
  },
  header: {
    borderBottom: `1px solid ${theme.palette.divider}`,
    paddingBottom: theme.spacing(1)
  }
}));

// Or use sx prop for simple styling
<Box sx={{ p: 2, mb: 1, borderRadius: 1 }}>
  Content
</Box>
```

### Responsive Design
```typescript
// Use Material-UI breakpoints
<Grid container spacing={2}>
  <Grid item xs={12} md={6} lg={4}>
    <Card>Content</Card>
  </Grid>
</Grid>

// Conditional rendering based on screen size
const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down('md'));

return (
  <>
    {isMobile ? <MobileComponent /> : <DesktopComponent />}
  </>
);
```

## 🔧 Common Development Tasks

### Adding a New Context
```typescript
// contexts/NewContext.tsx
interface NewContextType {
  value: string;
  setValue: (value: string) => void;
}

const NewContext = createContext<NewContextType | undefined>(undefined);

export const NewProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [value, setValue] = useState<string>('');
  
  return (
    <NewContext.Provider value={{ value, setValue }}>
      {children}
    </NewContext.Provider>
  );
};

export const useNew = () => {
  const context = useContext(NewContext);
  if (!context) {
    throw new Error('useNew must be used within NewProvider');
  }
  return context;
};
```

### Creating Custom Hooks
```typescript
// hooks/useCustomHook.ts
export const useCustomHook = (dependency: string) => {
  const [state, setState] = useState();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const result = await someAsyncOperation(dependency);
        setState(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [dependency]);
  
  return { state, loading, error, refetch: () => fetchData() };
};
```

### Error Handling Patterns
```typescript
// utils/errorHandling.ts
export class AppError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export const handleError = (error: unknown): AppError => {
  if (error instanceof AppError) {
    return error;
  }
  
  if (error instanceof Error) {
    return new AppError(ErrorCode.UNKNOWN_ERROR, error.message);
  }
  
  return new AppError(ErrorCode.UNKNOWN_ERROR, 'An unknown error occurred');
};

// Usage in components
try {
  await riskyOperation();
} catch (error) {
  const appError = handleError(error);
  showNotification(appError.message, 'error');
}
```

## 📊 Data Flow Patterns

### Context to Component Data Flow
```typescript
// 1. Context provides data
const { datasets, activeDataset } = useData();

// 2. Component processes data
const processedData = useMemo(() => {
  if (!activeDataset) return [];
  return activeDataset.rows.map(row => processRow(row));
}, [activeDataset]);

// 3. Component renders data
return (
  <DataTable data={processedData} />
);
```

### Service Layer Integration
```typescript
// Service call pattern
const handleSaveDataset = async () => {
  try {
    setLoading(true);
    await DatasetService.saveDataset(dataset);
    showNotification('Dataset saved successfully', 'success');
  } catch (error) {
    const appError = handleError(error);
    showNotification(appError.message, 'error');
  } finally {
    setLoading(false);
  }
};
```

## 🚀 Performance Optimization

### Memoization Patterns
```typescript
// Expensive calculations
const expensiveValue = useMemo(() => {
  return performExpensiveCalculation(data);
}, [data]);

// Callback memoization
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);

// Component memoization
export const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <ComplexVisualization data={data} />;
}, (prevProps, nextProps) => {
  return prevProps.data === nextProps.data;
});
```

### Virtual Scrolling for Large Datasets
```typescript
// Use react-window for large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <DataRow data={items[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={items.length}
      itemSize={50}
    >
      {Row}
    </List>
  );
};
```

## 🔐 Security Best Practices

### Input Validation
```typescript
// Validate user inputs
const validateNumericInput = (value: string): number | null => {
  const num = parseFloat(value);
  if (isNaN(num) || !isFinite(num)) {
    return null;
  }
  return num;
};

// Sanitize data before processing
const sanitizeData = (data: DataValue[]): DataValue[] => {
  return data.map(value => {
    if (typeof value === 'string') {
      return value.trim().slice(0, 1000); // Limit string length
    }
    return value;
  });
};
```

### Authentication Checks
```typescript
// Protect sensitive operations
const ProtectedComponent = ({ children }) => {
  const { user, isAdmin } = useAuth();
  
  if (!user) {
    return <LoginPrompt />;
  }
  
  if (!isAdmin) {
    return <AccessDenied />;
  }
  
  return <>{children}</>;
};
```

## 📱 PWA Development

### Service Worker Updates
```typescript
// Handle service worker updates
const handleServiceWorkerUpdate = () => {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      window.location.reload();
    });
  }
};

// Check for updates
const checkForUpdates = async () => {
  const registration = await navigator.serviceWorker.getRegistration();
  if (registration) {
    registration.update();
  }
};
```

### Offline Functionality
```typescript
// Detect online/offline status
const useOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  return isOnline;
};
```

## 🐛 Debugging Tips

### React DevTools
- Install React Developer Tools browser extension
- Use Profiler to identify performance bottlenecks
- Inspect component props and state

### Console Debugging
```typescript
// Conditional logging
const DEBUG = process.env.NODE_ENV === 'development';

const debugLog = (message: string, data?: any) => {
  if (DEBUG) {
    console.log(`[DEBUG] ${message}`, data);
  }
};

// Performance timing
const startTime = performance.now();
// ... expensive operation
const endTime = performance.now();
debugLog(`Operation took ${endTime - startTime} milliseconds`);
```

### Error Boundaries
```typescript
// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Log to error reporting service
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    
    return this.props.children;
  }
}
```

This development guide provides practical patterns and examples for extending DataStatPro. Follow these patterns for consistency and maintainability.