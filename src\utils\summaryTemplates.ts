// Summary generation templates and utilities

export interface SummaryTemplate {
  id: string;
  name: string;
  description: string;
  sections: SummarySection[];
  style: 'executive' | 'detailed' | 'apa' | 'custom';
  targetAudience: 'academic' | 'clinical' | 'general';
}

export interface SummarySection {
  id: string;
  title: string;
  required: boolean;
  template: string;
  variables: string[];
  formatting: SectionFormatting;
}

export interface SectionFormatting {
  bulletPoints: boolean;
  numbered: boolean;
  paragraphs: boolean;
  tables: boolean;
  maxLength?: number;
}

export interface StatisticalData {
  effectSizes: Array<{type: string, value: number, ci?: [number, number]}>;
  pValues: Array<{test: string, value: number}>;
  confidenceIntervals: Array<{parameter: string, ci: [number, number]}>;
  modelFit: Array<{model: string, metric: string, value: number}>;
  sampleSizes: number[];
  testTypes: string[];
}

// Template variables that can be used in templates
export const TEMPLATE_VARIABLES = {
  TOTAL_TESTS: '{{totalTests}}',
  SIGNIFICANT_TESTS: '{{significantTests}}',
  SIGNIFICANCE_RATE: '{{significanceRate}}',
  TOTAL_SAMPLE_SIZE: '{{totalSampleSize}}',
  UNIQUE_TEST_TYPES: '{{uniqueTestTypes}}',
  MEAN_EFFECT_SIZE: '{{meanEffectSize}}',
  EFFECT_SIZE_RANGE: '{{effectSizeRange}}',
  LARGEST_EFFECT: '{{largestEffect}}',
  MOST_SIGNIFICANT: '{{mostSignificant}}',
  TEST_SUMMARY: '{{testSummary}}',
  KEY_FINDINGS: '{{keyFindings}}',
  LIMITATIONS: '{{limitations}}',
  RECOMMENDATIONS: '{{recommendations}}'
};

// Predefined summary templates
export const SUMMARY_TEMPLATES: SummaryTemplate[] = [
  {
    id: 'executive',
    name: 'Executive Summary',
    description: 'Concise overview for decision-makers',
    style: 'executive',
    targetAudience: 'general',
    sections: [
      {
        id: 'overview',
        title: 'Overview',
        required: true,
        template: 'This analysis encompasses {{totalTests}} statistical test(s) with a combined sample size of {{totalSampleSize}} observations. {{significantTests}} of {{totalTests}} tests ({{significanceRate}}%) yielded statistically significant results.',
        variables: ['totalTests', 'totalSampleSize', 'significantTests', 'significanceRate'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: false }
      },
      {
        id: 'key_results',
        title: 'Key Results',
        required: true,
        template: '{{keyFindings}}',
        variables: ['keyFindings'],
        formatting: { bulletPoints: true, numbered: false, paragraphs: false, tables: false, maxLength: 300 }
      },
      {
        id: 'implications',
        title: 'Implications',
        required: false,
        template: 'The observed effects suggest {{implications}}. {{recommendations}}',
        variables: ['implications', 'recommendations'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: false }
      }
    ]
  },
  {
    id: 'detailed',
    name: 'Detailed Analysis',
    description: 'Comprehensive statistical report',
    style: 'detailed',
    targetAudience: 'academic',
    sections: [
      {
        id: 'summary',
        title: 'Statistical Summary',
        required: true,
        template: '{{testSummary}}',
        variables: ['testSummary'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: true }
      },
      {
        id: 'effect_sizes',
        title: 'Effect Sizes',
        required: true,
        template: 'Effect sizes ranged from {{effectSizeRange}} with a mean absolute effect of {{meanEffectSize}}. The largest effect was observed for {{largestEffect}}.',
        variables: ['effectSizeRange', 'meanEffectSize', 'largestEffect'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: true }
      },
      {
        id: 'significance',
        title: 'Statistical Significance',
        required: true,
        template: '{{significantTests}} of {{totalTests}} tests achieved statistical significance (p < 0.05). The most significant result was {{mostSignificant}}.',
        variables: ['significantTests', 'totalTests', 'mostSignificant'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: false }
      },
      {
        id: 'limitations',
        title: 'Limitations',
        required: true,
        template: '{{limitations}}',
        variables: ['limitations'],
        formatting: { bulletPoints: true, numbered: false, paragraphs: false, tables: false }
      }
    ]
  },
  {
    id: 'apa',
    name: 'APA Style',
    description: 'Publication-ready APA format',
    style: 'apa',
    targetAudience: 'academic',
    sections: [
      {
        id: 'results',
        title: 'Results',
        required: true,
        template: 'Statistical analyses were conducted on {{totalSampleSize}} observations using {{uniqueTestTypes}} different analytical approaches. {{testSummary}}',
        variables: ['totalSampleSize', 'uniqueTestTypes', 'testSummary'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: false }
      },
      {
        id: 'findings',
        title: 'Findings',
        required: true,
        template: '{{keyFindings}}',
        variables: ['keyFindings'],
        formatting: { bulletPoints: false, numbered: false, paragraphs: true, tables: false }
      }
    ]
  }
];

// Utility functions for template processing
export class SummaryTemplateEngine {
  static processTemplate(template: string, data: Record<string, any>): string {
    let processed = template;
    
    Object.entries(data).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      const replacement = Array.isArray(value) ? value.join(', ') : String(value);
      processed = processed.replace(new RegExp(placeholder, 'g'), replacement);
    });
    
    return processed;
  }

  static generateTemplateData(stats: StatisticalData): Record<string, any> {
    const totalTests = stats.pValues.length;
    const significantTests = stats.pValues.filter(p => p.value < 0.05).length;
    const significanceRate = totalTests > 0 ? ((significantTests / totalTests) * 100).toFixed(1) : '0';
    const totalSampleSize = stats.sampleSizes.reduce((sum, n) => sum + n, 0);
    const uniqueTestTypes = [...new Set(stats.testTypes)].length;
    
    const effectSizeValues = stats.effectSizes.map(es => Math.abs(es.value));
    const meanEffectSize = effectSizeValues.length > 0 
      ? (effectSizeValues.reduce((sum, val) => sum + val, 0) / effectSizeValues.length).toFixed(3)
      : 'N/A';
    
    const effectSizeRange = effectSizeValues.length > 0
      ? `${Math.min(...effectSizeValues).toFixed(3)} to ${Math.max(...effectSizeValues).toFixed(3)}`
      : 'N/A';
    
    const largestEffect = stats.effectSizes.length > 0
      ? stats.effectSizes.reduce((max, current) => 
          Math.abs(current.value) > Math.abs(max.value) ? current : max
        )
      : null;
    
    const mostSignificant = stats.pValues.length > 0
      ? stats.pValues.reduce((min, current) => current.value < min.value ? current : min)
      : null;
    
    // Generate test summary
    const testTypeCounts = stats.testTypes.reduce((acc: Record<string, number>, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    
    const testSummary = Object.entries(testTypeCounts)
      .map(([type, count]) => `${type} (n=${count})`)
      .join(', ');
    
    return {
      totalTests,
      significantTests,
      significanceRate,
      totalSampleSize: totalSampleSize.toLocaleString(),
      uniqueTestTypes,
      meanEffectSize,
      effectSizeRange,
      largestEffect: largestEffect ? `${largestEffect.type}: ${largestEffect.value.toFixed(3)}` : 'N/A',
      mostSignificant: mostSignificant ? `${mostSignificant.test} (p = ${mostSignificant.value.toFixed(4)})` : 'N/A',
      testSummary
    };
  }

  static formatSection(content: string, formatting: SectionFormatting): string {
    let formatted = content;
    
    if (formatting.maxLength && formatted.length > formatting.maxLength) {
      formatted = formatted.substring(0, formatting.maxLength - 3) + '...';
    }
    
    if (formatting.bulletPoints && !formatted.includes('•')) {
      const sentences = formatted.split('. ').filter(s => s.trim());
      formatted = sentences.map(s => `• ${s.trim()}`).join('\n');
    }
    
    if (formatting.numbered) {
      const sentences = formatted.split('. ').filter(s => s.trim());
      formatted = sentences.map((s, i) => `${i + 1}. ${s.trim()}`).join('\n');
    }
    
    return formatted;
  }

  static generateSummary(
    templateId: string, 
    stats: StatisticalData, 
    customData?: Record<string, any>
  ): string {
    const template = SUMMARY_TEMPLATES.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Template with id '${templateId}' not found`);
    }
    
    const templateData = { ...this.generateTemplateData(stats), ...customData };
    
    const sections = template.sections.map(section => {
      const content = this.processTemplate(section.template, templateData);
      const formatted = this.formatSection(content, section.formatting);
      return `## ${section.title}\n${formatted}`;
    });
    
    return sections.join('\n\n');
  }
}

// Effect size interpretation helpers
export const EFFECT_SIZE_INTERPRETATIONS = {
  cohensD: {
    small: 0.2,
    medium: 0.5,
    large: 0.8
  },
  correlation: {
    small: 0.1,
    medium: 0.3,
    large: 0.5
  },
  etaSquared: {
    small: 0.01,
    medium: 0.06,
    large: 0.14
  }
};

export function interpretEffectSize(value: number, type: string): string {
  const absValue = Math.abs(value);
  const interpretation = EFFECT_SIZE_INTERPRETATIONS[type as keyof typeof EFFECT_SIZE_INTERPRETATIONS];
  
  if (!interpretation) {
    return 'unknown magnitude';
  }
  
  if (absValue < interpretation.small) {
    return 'negligible';
  } else if (absValue < interpretation.medium) {
    return 'small';
  } else if (absValue < interpretation.large) {
    return 'medium';
  } else {
    return 'large';
  }
}

// Statistical significance helpers
export function formatPValue(p: number): string {
  if (p < 0.001) {
    return 'p < 0.001';
  } else if (p < 0.01) {
    return `p = ${p.toFixed(3)}`;
  } else {
    return `p = ${p.toFixed(2)}`;
  }
}

export function formatConfidenceInterval(ci: [number, number], decimals: number = 3): string {
  return `95% CI [${ci[0].toFixed(decimals)}, ${ci[1].toFixed(decimals)}]`;
}

// Export utilities
export function generateMarkdownSummary(summary: any): string {
  return `# ${summary.title}

${summary.fullText}`;
}

export function generateHTMLSummary(summary: any): string {
  const markdown = generateMarkdownSummary(summary);
  // In a real implementation, you'd use a markdown-to-HTML converter
  return `<div class="summary">${markdown.replace(/\n/g, '<br>')}</div>`;
}

export function generateWordDocument(summary: any): Blob {
  // This would require a library like docx or similar
  // For now, return a simple text blob
  const content = generateMarkdownSummary(summary);
  return new Blob([content], { type: 'text/plain' });
}