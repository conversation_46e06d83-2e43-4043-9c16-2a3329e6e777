import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  useTheme,
  alpha,
  TextField as MuiTextField,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON> as <PERSON>iButton,
  Chip,
  FormGroup,
  Checkbox,
  Select,
  MenuItem,
  InputLabel,
  FormHelperText
} from '@mui/material';
import {
  Science as ScienceIcon,
  Compare as CompareIcon,
  BarChart as Bar<PERSON>hartIcon,
  Functions as FunctionsIcon,
  FlashOn as FlashOnIcon,
  ArrowBackIos as ArrowBackIosIcon,
  Help as HelpIcon,
  Timeline as TimelineIcon,
  ShowChart as ShowChartIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  GridOn as GridOnIcon,
  B<PERSON>bleChart as BubbleChartIcon
} from '@mui/icons-material';
import { useData } from '../context/DataContext';
import { DataType } from '../types';
import { 
  <PERSON><PERSON>, 
  GuidedWorkflow, 
  DatasetSelector, 
  VariableSelector, 
  EnhancedAnalysisResultCard,
  DataFlowDiagram,
  ContextualHelp
} from '../components/UI';
import {
  calculatePearsonCorrelation,
  calculateSpearmanCorrelation,
  calculateKendallCorrelation,
  calculateCorrelationMatrix,
  multipleLinearRegression,
  multipleLogisticRegression,
  isNormallyDistributed
} from '../utils/stats';
import jStat from 'jstat';

// Analysis types for correlation and regression
type AnalysisType = 'correlation' | 'simple-regression' | 'multiple-regression' | 'logistic-regression';
type CorrelationType = 'pearson' | 'spearman' | 'kendall';

// Guided Correlation/Regression Analysis Workflow Page
const CorrelationRegressionWorkflowPage: React.FC = () => {
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  
  // Analysis parameters state
  const [selectedDataset, setSelectedDataset] = useState<string>(currentDataset?.id || '');
  const [analysisType, setAnalysisType] = useState<AnalysisType>('correlation');
  const [correlationType, setCorrelationType] = useState<CorrelationType>('pearson');
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [independentVariables, setIndependentVariables] = useState<string[]>([]);
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [significanceLevel, setSignificanceLevel] = useState<number>(0.05);
  const [confidenceLevel, setConfidenceLevel] = useState<number>(0.95);
  const [includeIntercept, setIncludeIntercept] = useState<boolean>(true);
  const [checkAssumptions, setCheckAssumptions] = useState<boolean>(true);
  
  // Categorical variable handling state
  const [selectedCategoricalBaseCategories, setSelectedCategoricalBaseCategories] = useState<{ [key: string]: string }>({});
  
  // Categorical dependent variable mapping state (for logistic regression)
  const [dependentVariableCategories, setDependentVariableCategories] = useState<string[]>([]);
  const [selectedPositiveCategory, setSelectedPositiveCategory] = useState<string>('');
  
  // Results state
  const [results, setResults] = useState<any | null>(null);
  
  // Get the current dataset
  const dataset = datasets.find(d => d.id === selectedDataset);
  
  // Get numeric columns for analysis
  const numericColumns = dataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];
  
  // Get categorical columns for logistic regression
  const categoricalColumns = dataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL
  ) || [];
  
  // Check if we have the required variables for the selected analysis type
  const hasRequiredVariables = () => {
    if (!dataset) return false;
    
    switch (analysisType) {
      case 'correlation':
        return selectedVariables.length >= 2;
      case 'simple-regression':
        return !!dependentVariable && independentVariables.length === 1;
      case 'multiple-regression':
        return !!dependentVariable && independentVariables.length >= 2;
      case 'logistic-regression':
        return !!dependentVariable && independentVariables.length >= 1;
      default:
        return false;
    }
  };
  
  // Helper function to get values for a variable (handles both column.id and column.name)
  const getVariableValues = (variableIdentifier: string): number[] => {
    if (!dataset || !variableIdentifier) return [];
    
    // Find the column by ID first, then fall back to name
    const column = dataset.columns.find(col => col.id === variableIdentifier) || 
                   dataset.columns.find(col => col.name === variableIdentifier);
    
    if (!column) {
      console.warn(`Column not found for identifier: ${variableIdentifier}`);
      return [];
    }
    
    // Use column.name to access data from rows
    const columnName = column.name;
    
    return dataset.data
      .map(row => {
        const value = row[columnName];
        const numValue = Number(value);
        return numValue;
      })
      .filter(val => !isNaN(val) && isFinite(val));
  };
  
  // Helper function to get column name from identifier
  const getColumnName = (variableIdentifier: string): string => {
    if (!dataset || !variableIdentifier) return variableIdentifier;
    
    const column = dataset.columns.find(col => col.id === variableIdentifier) || 
                   dataset.columns.find(col => col.name === variableIdentifier);
    
    return column?.name || variableIdentifier;
  };
  
  // Helper function to get unique categories for a categorical variable
  const getUniqueCategories = (variableIdentifier: string): string[] => {
    if (!dataset || !variableIdentifier) return [];
    
    const column = dataset.columns.find(col => col.id === variableIdentifier) || 
                   dataset.columns.find(col => col.name === variableIdentifier);
    
    if (!column || column.type !== DataType.CATEGORICAL) return [];
    
    // Use column.name to access data from rows
    const columnName = column.name;
    const uniqueValues = [...new Set(dataset.data.map(row => String(row[columnName])).filter(val => val && val.trim() !== ''))];
    return uniqueValues.sort();
  };
  
  // Handle baseline category change for a categorical variable
  const handleBaseCategoryChange = (variableId: string, baseCategory: string) => {
    setSelectedCategoricalBaseCategories(prev => ({
      ...prev,
      [variableId]: baseCategory,
    }));
    setResults(null); // Clear results when baseline categories change
  };
  
  // Handle dependent variable change (with categorical detection for logistic regression)
  const handleDependentVariableChange = (value: string) => {
    setDependentVariable(value);
    setResults(null); // Clear results when dependent variable changes
    
    // If logistic regression and categorical variable, set up category mapping
    if (analysisType === 'logistic-regression' && value && dataset) {
      // VariableSelector passes column.name, so look up by name first, then by ID as fallback
      const column = dataset.columns.find(col => col.name === value) || dataset.columns.find(col => col.id === value);
      if (column && column.type === DataType.CATEGORICAL) {
        const categories = getUniqueCategories(value);
        if (categories.length === 2) {
          setDependentVariableCategories(categories);
          setSelectedPositiveCategory(categories[1]); // Default to second category as positive
        } else {
          setDependentVariableCategories([]);
          setSelectedPositiveCategory('');
        }
      } else {
        setDependentVariableCategories([]);
        setSelectedPositiveCategory('');
      }
    } else {
      setDependentVariableCategories([]);
      setSelectedPositiveCategory('');
    }
  };
  
  // Get categorical values for a variable (for dummy variable creation)
  const getCategoricalValues = (variableIdentifier: string): string[] => {
    if (!dataset || !variableIdentifier) return [];
    
    const column = dataset.columns.find(col => col.id === variableIdentifier) || 
                   dataset.columns.find(col => col.name === variableIdentifier);
    
    if (!column) return [];
    
    // Use column.name to access data from rows
    const columnName = column.name;
    return dataset.data.map(row => String(row[columnName] || ''));
  };
  
  // Create dummy variables for categorical variables
  const createDummyVariables = (variableId: string, baseCategory: string): { [key: string]: number[] } => {
    const categories = getUniqueCategories(variableId);
    const values = getCategoricalValues(variableId);
    const columnName = getColumnName(variableId);
    const dummyVariables: { [key: string]: number[] } = {};
    
    // Create dummy variables for all categories except the base
    categories.filter(cat => cat !== baseCategory).forEach(category => {
      const dummyName = `${columnName}_${category}`;
      dummyVariables[dummyName] = values.map(val => val === category ? 1 : 0);
    });
    
    return dummyVariables;
  };
  
  // Prepare data matrix with dummy variables for regression
  const prepareRegressionData = (independentVars: string[]): { 
    independentMatrix: number[][], 
    variableNames: string[],
    dependentValues: number[]
  } => {
    if (!dataset) {
      throw new Error('Dataset is required for regression analysis');
    }
    
    // Get dependent variable column info - VariableSelector passes column.name, so try name first, then ID as fallback
    let depColumn = dataset.columns.find(col => col.name === dependentVariable);
    if (!depColumn) {
      // Fallback: try to find by ID
      depColumn = dataset.columns.find(col => col.id === dependentVariable);
    }
    
    if (!depColumn) {
      throw new Error(`Dependent variable column not found: '${dependentVariable}'`);
    }
    
    // For linear regression, dependent variable must be numeric
    if ((analysisType === 'simple-regression' || analysisType === 'multiple-regression') && depColumn.type !== DataType.NUMERIC) {
      throw new Error(`Linear regression requires a numeric dependent variable. '${depColumn.name}' is categorical.`);
    }
    
    // Create a list of valid row indices (rows with valid dependent variable values)
    const validRowIndices: number[] = [];
    const depColumnName = depColumn.name;
    
    dataset.data.forEach((row, index) => {
      const depValue = row[depColumnName];
      let isValidDep = false;
      
      if (depColumn.type === DataType.NUMERIC) {
        const numValue = Number(depValue);
        isValidDep = !isNaN(numValue) && isFinite(numValue);
      } else if (depColumn.type === DataType.CATEGORICAL) {
        // Only allow categorical dependent variables for logistic regression
        if (analysisType === 'logistic-regression') {
          isValidDep = depValue != null && String(depValue).trim() !== '';
        }
      }
      
      if (isValidDep) {
        // Check if all independent variables also have valid values in this row
        let allIndepValid = true;
        for (const varId of independentVars) {
          const indepColumn = dataset.columns.find(col => col.name === varId || col.id === varId);
          if (!indepColumn) {
            allIndepValid = false;
            break;
          }
          
          const indepValue = row[indepColumn.name];
          if (indepColumn.type === DataType.NUMERIC) {
            const numValue = Number(indepValue);
            if (isNaN(numValue) || !isFinite(numValue)) {
              allIndepValid = false;
              break;
            }
          } else if (indepColumn.type === DataType.CATEGORICAL) {
            if (indepValue == null || String(indepValue).trim() === '') {
              allIndepValid = false;
              break;
            }
          }
        }
        
        if (allIndepValid) {
          validRowIndices.push(index);
        }
      }
    });
    
    if (validRowIndices.length === 0) {
      throw new Error('No valid observations found with complete data for all variables');
    }
    
    // Extract dependent values using valid row indices
    const dependentValues: number[] = [];
    if (depColumn.type === DataType.NUMERIC) {
      validRowIndices.forEach(index => {
        const value = Number(dataset.data[index][depColumnName]);
        dependentValues.push(value);
      });
    } else {
      // This should only happen for logistic regression with categorical dependent variables
      // Return empty array as this will be handled separately in runLogisticRegressionAnalysis
      throw new Error('Categorical dependent variables should be handled separately in logistic regression');
    }
    
    // Extract independent variables using valid row indices
    const independentMatrix: number[][] = [];
    const variableNames: string[] = [];
    
    independentVars.forEach(varId => {
      const column = dataset.columns.find(col => col.name === varId || col.id === varId);
      if (!column) return;
      
      if (column.type === DataType.NUMERIC) {
        // Add numeric variable directly using valid row indices
        const values: number[] = [];
        validRowIndices.forEach(index => {
          const value = Number(dataset.data[index][column.name]);
          values.push(value);
        });
        independentMatrix.push(values);
        variableNames.push(getColumnName(varId));
      } else if (column.type === DataType.CATEGORICAL) {
        // Add dummy variables for categorical variable using valid row indices
        const baseCategory = selectedCategoricalBaseCategories[varId];
        if (baseCategory) {
          const categories = getUniqueCategories(varId);
          const columnName = getColumnName(varId);
          
          // Create dummy variables for all categories except the base
          categories.filter(cat => cat !== baseCategory).forEach(category => {
            const dummyName = `${columnName}_${category}`;
            const dummyValues: number[] = [];
            
            validRowIndices.forEach(index => {
              const value = String(dataset.data[index][column.name]);
              dummyValues.push(value === category ? 1 : 0);
            });
            
            independentMatrix.push(dummyValues);
            variableNames.push(dummyName);
          });
        }
      }
    });
    
    return { independentMatrix, variableNames, dependentValues };
  };
  
  // Validate data before analysis
  const validateAnalysisData = (): { isValid: boolean; error?: string } => {
    if (!dataset || !hasRequiredVariables()) {
      return { isValid: false, error: 'Missing required variables or dataset' };
    }
    
    // Check for sufficient data
    if (dataset.data.length < 3) {
      return { isValid: false, error: 'Insufficient data: At least 3 observations required' };
    }
    
    // Validate variables based on analysis type
    if (analysisType === 'correlation') {
      // Check if selected variables have sufficient valid data
      for (const varId of selectedVariables) {
        const values = getVariableValues(varId);
        if (values.length < 3) {
          const columnName = getColumnName(varId);
          return { isValid: false, error: `Variable '${columnName}' has insufficient valid numeric data` };
        }
      }
    } else {
      // Check dependent variable
      const depValues = getVariableValues(dependentVariable);
      if (depValues.length < 3) {
        const depName = getColumnName(dependentVariable);
        return { isValid: false, error: `Dependent variable '${depName}' has insufficient valid data` };
      }
      
      // Check baseline category selection for categorical independent variables
      for (const varId of independentVariables) {
        const column = dataset.columns.find(col => col.name === varId || col.id === varId);
        if (column && column.type === DataType.CATEGORICAL) {
          if (!selectedCategoricalBaseCategories[varId]) {
            return { 
              isValid: false, 
              error: `Please select a baseline category for categorical variable '${getColumnName(varId)}'` 
            };
          }
        }
      }
      
      // Check independent variables
      for (const varId of independentVariables) {
        const column = dataset.columns.find(col => col.name === varId || col.id === varId);
        if (column && column.type === DataType.NUMERIC) {
          const values = getVariableValues(varId);
          if (values.length < 3) {
            const columnName = getColumnName(varId);
            return { isValid: false, error: `Independent variable '${columnName}' has insufficient valid data` };
          }
        } else if (column && column.type === DataType.CATEGORICAL) {
          const categories = getUniqueCategories(varId);
          if (categories.length < 2) {
            const columnName = getColumnName(varId);
            return { isValid: false, error: `Categorical variable '${columnName}' must have at least 2 categories` };
          }
        }
      }
      
      // For regression, check if we have enough observations relative to predictors
      // Count total predictors including dummy variables
      let totalPredictors = 0;
      for (const varId of independentVariables) {
        const column = dataset.columns.find(col => col.name === varId || col.id === varId);
        if (column && column.type === DataType.NUMERIC) {
          totalPredictors += 1;
        } else if (column && column.type === DataType.CATEGORICAL) {
          const categories = getUniqueCategories(varId);
          totalPredictors += Math.max(0, categories.length - 1); // k-1 dummy variables
        }
      }
      
      const minObservations = totalPredictors * 5; // Rule of thumb: 5 observations per predictor
      if (depValues.length < minObservations) {
        return { 
          isValid: false, 
          error: `Insufficient observations: Need at least ${minObservations} valid observations for ${totalPredictors} predictors (including dummy variables)` 
        };
      }
    }
    
    return { isValid: true };
  };
  
  // Run the analysis
  const runAnalysis = () => {
    // Validate data first
    const validation = validateAnalysisData();
    if (!validation.isValid) {
      setResults({
        error: validation.error,
        analysisType,
        timestamp: new Date()
      });
      return;
    }
    
    try {
      let analysisResults;
      
      switch (analysisType) {
        case 'correlation':
          analysisResults = runCorrelationAnalysis();
          break;
        case 'simple-regression':
        case 'multiple-regression':
          analysisResults = runRegressionAnalysis();
          break;
        case 'logistic-regression':
          analysisResults = runLogisticRegressionAnalysis();
          break;
      }
      
      setResults({
        ...analysisResults,
        analysisType,
        timestamp: new Date(),
        significanceLevel,
        confidenceLevel
      });
      
    } catch (error) {
      console.error('Error running analysis:', error);
      setResults({
        error: error instanceof Error ? error.message : 'An unexpected error occurred during analysis',
        analysisType,
        timestamp: new Date()
      });
    }
  };
  
  // Run correlation analysis
  const runCorrelationAnalysis = () => {
    // Prepare variables data using proper column names
    const variablesData: { [key: string]: number[] } = {};
    const variableNames: string[] = [];
    const rawVariablesData: number[][] = [];
    
    selectedVariables.forEach(varId => {
      const columnName = getColumnName(varId);
      const values = getVariableValues(varId);
      variablesData[columnName] = values;
      variableNames.push(columnName);
      rawVariablesData.push(values);
    });
    
    // Validate data consistency - ensure all variables have the same length
    const minLength = Math.min(...rawVariablesData.map(arr => arr.length));
    if (minLength < 3) {
      throw new Error('Insufficient valid data points for correlation analysis');
    }
    
    // Trim all arrays to the same length and update variablesData
    selectedVariables.forEach((varId, index) => {
      const columnName = getColumnName(varId);
      variablesData[columnName] = rawVariablesData[index].slice(0, minLength);
    });
    
    // Check for zero variance in any variable
    for (let i = 0; i < variableNames.length; i++) {
      const values = variablesData[variableNames[i]];
      const variance = values.reduce((sum, val, _, arr) => {
        const mean = arr.reduce((s, v) => s + v, 0) / arr.length;
        return sum + Math.pow(val - mean, 2);
      }, 0) / (values.length - 1);
      
      if (variance === 0 || !isFinite(variance)) {
        throw new Error(`Variable '${variableNames[i]}' has zero variance - cannot calculate correlations`);
      }
    }
    
    // Calculate correlation matrix using the appropriate method
    const correlationResult = calculateCorrelationMatrix(
      variablesData,
      correlationType as 'pearson' | 'spearman' | 'kendall'
    );
    
    // Format results for display using proper variable names
    const correlationMatrix: any = {};
    const pValueMatrix: any = {};
    const significanceMatrix: any = {};
    
    variableNames.forEach(var1 => {
      correlationMatrix[var1] = {};
      pValueMatrix[var1] = {};
      significanceMatrix[var1] = {};
      
      variableNames.forEach(var2 => {
        const result = correlationResult.matrix[var1][var2];
        correlationMatrix[var1][var2] = result.r;
        pValueMatrix[var1][var2] = result.pValue;
        
        // Add significance indicators
        significanceMatrix[var1][var2] = 
          result.pValue < 0.001 ? '***' :
          result.pValue < 0.01 ? '**' :
          result.pValue < 0.05 ? '*' :
          result.pValue < 0.1 ? '.' : '';
      });
    });
    
    // Calculate summary statistics using proper variable names
    const allCorrelations = [];
    const significantCorrelations = [];
    
    for (let i = 0; i < variableNames.length; i++) {
      for (let j = i + 1; j < variableNames.length; j++) {
        const var1 = variableNames[i];
        const var2 = variableNames[j];
        const result = correlationResult.matrix[var1][var2];
        
        allCorrelations.push({
          var1,
          var2,
          correlation: result.r,
          pValue: result.pValue,
          n: result.n
        });
        
        if (result.pValue < 0.05) {
          significantCorrelations.push({
            var1,
            var2,
            correlation: result.r,
            pValue: result.pValue,
            n: result.n
          });
        }
      }
    }
    
    // Create correlation matrix table using proper variable names
    const tableColumns = ['Variable', ...variableNames];
    const tableRows = variableNames.map(var1 => {
      const row = [var1];
      variableNames.forEach(var2 => {
        const correlation = correlationMatrix[var1][var2];
        const significance = significanceMatrix[var1][var2];
        row.push(`${correlation.toFixed(3)}${significance}`);
      });
      return row;
    });

    return {
      correlationMatrix,
      pValueMatrix,
      significanceMatrix,
      variables: variableNames,
      sampleSize: minLength,
      correlationType,
      table: {
        columns: tableColumns,
        rows: tableRows
      },
      summary: {
        totalCorrelations: allCorrelations.length,
        significantCorrelations: significantCorrelations.length,
        strongestCorrelation: allCorrelations.length > 0 ? allCorrelations.reduce((max, curr) => 
          Math.abs(curr.correlation) > Math.abs(max.correlation) ? curr : max
        ) : null,
        averageCorrelation: allCorrelations.length > 0 ? 
          allCorrelations.reduce((sum, curr) => sum + Math.abs(curr.correlation), 0) / allCorrelations.length : 0
      },
      allCorrelations,
      significantCorrelations
    };
  };
  
  // Run regression analysis
  const runRegressionAnalysis = () => {
    // Use the new prepareRegressionData function that handles categorical variables
    const { independentMatrix, variableNames, dependentValues } = prepareRegressionData(independentVariables);
    
    // Get proper column names for display
    const dependentVariableName = getColumnName(dependentVariable);
    const independentVariableNames = variableNames; // Now includes dummy variable names
    
    // Validate data consistency
    const minLength = Math.min(dependentValues.length, ...independentMatrix.map(arr => arr.length));
    if (minLength < 3) {
      throw new Error('Insufficient valid data points for regression analysis');
    }
    
    // Ensure all arrays have the same length by trimming to minimum
    const trimmedDependentValues = dependentValues.slice(0, minLength);
    const trimmedIndependentMatrix = independentMatrix.map(arr => arr.slice(0, minLength));
    
    // Check for zero variance in dependent variable
    const depVariance = trimmedDependentValues.reduce((sum, val, _, arr) => {
      const mean = arr.reduce((s, v) => s + v, 0) / arr.length;
      return sum + Math.pow(val - mean, 2);
    }, 0) / (trimmedDependentValues.length - 1);
    
    if (depVariance === 0 || !isFinite(depVariance)) {
      throw new Error(`Dependent variable '${dependentVariableName}' has zero variance - cannot perform regression`);
    }
    
    // Check for zero variance in independent variables
    for (let i = 0; i < trimmedIndependentMatrix.length; i++) {
      const values = trimmedIndependentMatrix[i];
      const variance = values.reduce((sum, val, _, arr) => {
        const mean = arr.reduce((s, v) => s + v, 0) / arr.length;
        return sum + Math.pow(val - mean, 2);
      }, 0) / (values.length - 1);
      
      if (variance === 0 || !isFinite(variance)) {
        throw new Error(`Independent variable '${independentVariableNames[i]}' has zero variance - cannot perform regression`);
      }
    }
    
    // Transpose matrix for regression function using trimmed data
    const transposedMatrix: number[][] = [];
    for (let i = 0; i < trimmedDependentValues.length; i++) {
      transposedMatrix[i] = trimmedIndependentMatrix.map(col => col[i]);
    }
    
    const regressionResult = multipleLinearRegression(
      transposedMatrix,
      trimmedDependentValues
    );
    
    // Calculate additional statistics
    const n = trimmedDependentValues.length;
    const k = independentVariables.length;
    
    // Calculate adjusted R²
    const adjustedRSquared = 1 - ((1 - regressionResult.rSquared) * (n - 1)) / (n - k - 1);
    
    // F-statistic and p-value are already calculated in the regression function
    const fStatistic = (regressionResult.rSquared / k) / ((1 - regressionResult.rSquared) / (n - k - 1));
    const fPValue = regressionResult.pValue; // Use the p-value from the regression result
    
    // Format coefficients with confidence intervals using proper variable names
    const formattedCoefficients = regressionResult.coefficients.map((coef, index) => {
      const tCritical = 1.96; // Approximate for 95% CI
      const margin = tCritical * regressionResult.stdErrors[index];
      
      return {
        variable: independentVariableNames[index],
        coefficient: coef,
        standardError: regressionResult.stdErrors[index],
        tValue: coef / regressionResult.stdErrors[index],
        pValue: regressionResult.pValues[index],
        confidenceInterval: {
          lower: coef - margin,
          upper: coef + margin
        },
        significance: regressionResult.pValues[index] < 0.001 ? '***' : 
                     regressionResult.pValues[index] < 0.01 ? '**' : 
                     regressionResult.pValues[index] < 0.05 ? '*' : 
                     regressionResult.pValues[index] < 0.1 ? '.' : ''
      };
    });
    
    // Check assumptions if requested
    let assumptions = [];
    if (checkAssumptions) {
      // Normality of residuals
      const residuals = regressionResult.residuals || [];
      if (residuals.length > 0) {
        const normalityTest = isNormallyDistributed(residuals);
        assumptions.push({
          name: 'Normality of residuals',
          status: normalityTest.isNormal ? 'passed' : 'failed',
          message: normalityTest.isNormal 
            ? 'Residuals appear normally distributed'
            : 'Residuals may not be normally distributed'
        });
      }
      
      // Linearity assumption
      assumptions.push({
        name: 'Linearity',
        status: 'warning',
        message: 'Check scatterplots for linear relationships'
      });
      
      // Independence assumption
      assumptions.push({
        name: 'Independence of observations',
        status: 'passed',
        message: 'Assumed based on study design'
      });
      
      // Homoscedasticity
      assumptions.push({
        name: 'Homoscedasticity',
        status: 'warning',
        message: 'Check residual plots for constant variance'
      });
    }
    
    // Create table for regression coefficients
    const table = {
      columns: ['Variable', 'Coefficient', 'Std. Error', 't-value', 'p-value', 'Sig.'],
      rows: formattedCoefficients.map(coef => [
        coef.variable,
        coef.coefficient.toFixed(4),
        coef.standardError.toFixed(4),
        coef.tValue.toFixed(3),
        coef.pValue < 0.001 ? '<0.001' : coef.pValue.toFixed(3),
        coef.significance
      ])
    };

    return {
       ...regressionResult,
       adjustedRSquared,
       fStatistic,
       fPValue,
       coefficients: formattedCoefficients,
       dependentVariable: dependentVariableName,
       independentVariables: independentVariableNames,
       sampleSize: n,
       degreesOfFreedom: {
         regression: k,
         residual: n - k - 1,
         total: n - 1
       },
       assumptions,
       isSimpleRegression: independentVariables.length === 1,
       table
     };
  };
  
  // Run logistic regression analysis
  const runLogisticRegressionAnalysis = () => {
    console.log('🔍 Starting logistic regression analysis');
    
    // Get proper column names for display
    const dependentVariableName = getColumnName(dependentVariable);
    console.log('📊 Dependent variable name:', dependentVariableName);
    
    // For categorical dependent variables, use string values from the dataset
    if (!dataset || !dependentVariable) {
      throw new Error('Dataset and dependent variable are required');
    }
    
    const column = dataset.columns.find(col => col.name === dependentVariable || col.id === dependentVariable);
    if (!column) {
      throw new Error('Dependent variable column not found');
    }
    
    console.log('🎯 Column type:', column.type);
    console.log('🎯 Selected positive category:', selectedPositiveCategory);
    console.log('📋 Dependent variable categories:', dependentVariableCategories);
    console.log('🔢 Independent variables:', independentVariables);
    
    let dependentValues: (string | number)[];
    let uniqueValues: (string | number)[];
    let binaryDependent: (0 | 1)[];
    let independentMatrix: number[][];
    let variableNames: string[];
    
    if (column.type === DataType.CATEGORICAL) {
      // For categorical variables, use string values and user-selected mapping
      if (!selectedPositiveCategory || dependentVariableCategories.length !== 2) {
        throw new Error('Please select which category should be mapped to 1 (positive outcome)');
      }
      
      uniqueValues = dependentVariableCategories;
      
      // Use the prepareRegressionData function to get consistent data
      const { independentMatrix: tempMatrix, variableNames: tempNames, dependentValues: tempDepValues } = prepareRegressionData(independentVariables);
      
      // For categorical dependent variables, we need to re-extract the dependent values using the same valid indices
      // Get the valid row indices that prepareRegressionData used
      const validRowIndices: number[] = [];
      const depColumnName = column.name;
      console.log('🔍 Extracting valid row indices for dependent column:', depColumnName);
      console.log('📊 Total dataset rows:', dataset.data.length);
      
      dataset.data.forEach((row, index) => {
        const depValue = row[depColumnName];
        const isValidDep = depValue != null && String(depValue).trim() !== '' && dependentVariableCategories.includes(String(depValue));
        
        if (isValidDep) {
          // Check if all independent variables also have valid values in this row
          let allIndepValid = true;
          for (const varId of independentVariables) {
            const indepColumn = dataset.columns.find(col => col.name === varId || col.id === varId);
            if (!indepColumn) {
              allIndepValid = false;
              break;
            }
            
            const indepValue = row[indepColumn.name];
            if (indepColumn.type === DataType.NUMERIC) {
              const numValue = Number(indepValue);
              if (isNaN(numValue) || !isFinite(numValue)) {
                allIndepValid = false;
                break;
              }
            } else if (indepColumn.type === DataType.CATEGORICAL) {
              if (indepValue == null || String(indepValue).trim() === '') {
                allIndepValid = false;
                break;
              }
            }
          }
          
          if (allIndepValid) {
            validRowIndices.push(index);
          }
        }
      });
      
      console.log('✅ Valid row indices found:', validRowIndices.length, 'out of', dataset.data.length);
      console.log('📋 First 10 valid indices:', validRowIndices.slice(0, 10));
      
      // Extract categorical dependent values using the same valid indices
      dependentValues = validRowIndices.map(index => String(dataset.data[index][depColumnName]));
      console.log('📊 Dependent values extracted:', dependentValues.length);
      console.log('📋 First 10 dependent values:', dependentValues.slice(0, 10));
      
      // Map based on user selection: selectedPositiveCategory -> 1, other -> 0
      binaryDependent = dependentValues.map(val => 
        String(val) === selectedPositiveCategory ? 1 : 0
      ) as (0 | 1)[];
      
      console.log('🎯 Binary dependent mapping completed:', binaryDependent.length);
      console.log('📊 Binary values distribution:', {
        zeros: binaryDependent.filter(v => v === 0).length,
        ones: binaryDependent.filter(v => v === 1).length
      });
      console.log('📋 First 10 binary values:', binaryDependent.slice(0, 10));
      
      // Use the consistent independent matrix
      independentMatrix = tempMatrix;
      variableNames = tempNames;
      
      console.log('📊 Independent matrix dimensions:', independentMatrix.length, 'x', independentMatrix[0]?.length || 0);
      console.log('📋 Variable names:', variableNames);
    } else {
      // For numeric variables, use the prepareRegressionData function for consistency
      const { independentMatrix: tempMatrix, variableNames: tempNames, dependentValues: tempDepValues } = prepareRegressionData(independentVariables);
      
      dependentValues = tempDepValues;
      uniqueValues = [...new Set(dependentValues)];
      
      if (uniqueValues.length !== 2) {
        throw new Error('Logistic regression requires a binary dependent variable');
      }
      
      // For numeric binary variables, also allow user selection if categories are available
      if (selectedPositiveCategory && dependentVariableCategories.length === 2) {
        // Use user-selected mapping for numeric variables
        binaryDependent = dependentValues.map(val => 
          String(val) === selectedPositiveCategory ? 1 : 0
        ) as (0 | 1)[];
      } else {
        // Default behavior: first unique value -> 0, second -> 1
        binaryDependent = dependentValues.map(val => 
          val === uniqueValues[0] ? 0 : 1
        ) as (0 | 1)[];
      }
      
      // Use the consistent independent matrix
      independentMatrix = tempMatrix;
      variableNames = tempNames;
    }
    
    // Variables are now consistently prepared
    const independentVariableNames = variableNames; // Now includes dummy variable names
    
    // Validate data consistency
    const minLength = Math.min(binaryDependent.length, ...independentMatrix.map(arr => arr.length));
    console.log('🔍 Data consistency validation:');
    console.log('📊 Binary dependent length:', binaryDependent.length);
    console.log('📊 Independent matrix lengths:', independentMatrix.map(arr => arr.length));
    console.log('📊 Minimum length:', minLength);
    
    if (minLength < 10) { // Logistic regression needs more data points
      console.error('❌ Insufficient data error - minLength:', minLength);
      throw new Error('Insufficient valid data points for logistic regression analysis (minimum 10 required)');
    }
    
    // Ensure all arrays have the same length by trimming to minimum
    const trimmedBinaryDependent = binaryDependent.slice(0, minLength);
    const trimmedIndependentMatrix = independentMatrix.map(arr => arr.slice(0, minLength));
    
    // Check for separation issues (all 0s or all 1s in dependent variable)
    const uniqueBinary = [...new Set(trimmedBinaryDependent)];
    if (uniqueBinary.length < 2) {
      throw new Error('Complete separation detected - all observations have the same outcome');
    }
    
    // Check for sufficient variation in each group
    const count0 = trimmedBinaryDependent.filter(val => val === 0).length;
    const count1 = trimmedBinaryDependent.filter(val => val === 1).length;
    if (count0 < 5 || count1 < 5) {
      throw new Error('Insufficient observations in each outcome group (minimum 5 per group required)');
    }
    
    // Transpose matrix for logistic regression function using trimmed data
    const transposedMatrix: number[][] = [];
    for (let i = 0; i < trimmedBinaryDependent.length; i++) {
      transposedMatrix[i] = trimmedIndependentMatrix.map(col => col[i]);
    }
    
    // Run the actual logistic regression
    const logisticResult = multipleLogisticRegression(
      transposedMatrix,
      trimmedBinaryDependent
    );
    
    // Format coefficients with odds ratios using proper variable names
    const coefficients = logisticResult.coefficients.map((coef, index) => ({
      variable: independentVariableNames[index],
      coefficient: coef,
      standardError: logisticResult.stdErrors[index],
      zValue: coef / logisticResult.stdErrors[index],
      pValue: logisticResult.pValues[index],
      oddsRatio: Math.exp(coef),
      confidenceInterval: {
        lower: Math.exp(coef - 1.96 * logisticResult.stdErrors[index]),
        upper: Math.exp(coef + 1.96 * logisticResult.stdErrors[index])
      }
    }));
    
    // Create table for logistic regression coefficients
    const table = {
      columns: ['Variable', 'Coefficient', 'Std. Error', 'z-value', 'p-value', 'Odds Ratio', '95% CI Lower', '95% CI Upper'],
      rows: coefficients.map(coef => [
        coef.variable,
        coef.coefficient.toFixed(4),
        coef.standardError.toFixed(4),
        coef.zValue.toFixed(3),
        coef.pValue < 0.001 ? '<0.001' : coef.pValue.toFixed(3),
        coef.oddsRatio.toFixed(3),
        coef.confidenceInterval.lower.toFixed(3),
        coef.confidenceInterval.upper.toFixed(3)
      ])
    };

    return {
      ...logisticResult,
      coefficients,
      dependentVariable: dependentVariableName,
      independentVariables: independentVariableNames,
      categories: uniqueValues,
      sampleSize: trimmedBinaryDependent.length,
      intercept: logisticResult.intercept,
      interceptStdError: logisticResult.interceptStdError,
      interceptPValue: logisticResult.interceptPValue,
      table
    };
  };
  
  // Helper function to format p-value
  const formatPValue = (p: number): string => {
    if (p < 0.001) return 'p < 0.001';
    return `p = ${p.toFixed(3)}`;
  };
  
  // Define workflow steps
  const workflowSteps = [
    {
      id: 'select-dataset',
      title: 'Select Dataset',
      description: 'Choose the dataset for your correlation/regression analysis',
      content: (
        <DatasetSelector
          value={selectedDataset}
          onChange={(value) => setSelectedDataset(value)}
          variant="card"
          showEmpty={false}
          required
          minRows={4}
          helperText="Select a dataset with sufficient data for the analysis"
        />
      ),
      validation: () => selectedDataset ? true : 'Please select a dataset',
    },
    {
      id: 'select-analysis-type',
      title: 'Choose Analysis Type',
      description: 'Select the type of correlation or regression analysis',
      content: (
        <Box>
          <Typography variant="body2" color="text.secondary" paragraph>
            Choose the appropriate analysis based on your research question:
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: analysisType === 'correlation' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnalysisType('correlation')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <GridOnIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={analysisType === 'correlation' ? 'bold' : 'normal'}>
                    Correlation Analysis
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Examine relationships between multiple variables
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> How are height, weight, and age related?
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: analysisType === 'simple-regression' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnalysisType('simple-regression')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ShowChartIcon color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={analysisType === 'simple-regression' ? 'bold' : 'normal'}>
                    Simple Linear Regression
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Predict one variable from another variable
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Predict sales from advertising spend
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: analysisType === 'multiple-regression' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnalysisType('multiple-regression')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TrendingUpIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={analysisType === 'multiple-regression' ? 'bold' : 'normal'}>
                    Multiple Linear Regression
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Predict one variable from multiple predictors
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Predict house price from size, location, age
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  border: analysisType === 'logistic-regression' ? `2px solid ${theme.palette.primary.main}` : undefined,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: theme.shadows[3],
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  }
                }}
                onClick={() => setAnalysisType('logistic-regression')}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <BubbleChartIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="subtitle1" fontWeight={analysisType === 'logistic-regression' ? 'bold' : 'normal'}>
                    Binary Logistic Regression
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Predict binary outcomes (yes/no, success/failure)
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Example:</strong> Predict loan approval from income, credit score
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      ),
      validation: () => analysisType ? true : 'Please select an analysis type',
      helpContent: (
        <Box>
          <Typography variant="h6" gutterBottom>
            Choosing the Right Analysis
          </Typography>
          
          <Typography variant="body1" paragraph>
            The type of analysis depends on your research question and data structure:
          </Typography>
          
          <Typography variant="subtitle1" gutterBottom>
            Correlation Analysis
          </Typography>
          <Typography variant="body1" paragraph>
            Use when you want to explore relationships between multiple variables without assuming causation.
          </Typography>
          
          <Typography variant="subtitle1" gutterBottom>
            Linear Regression
          </Typography>
          <Typography variant="body1" paragraph>
            Use when you want to predict a continuous outcome variable from one or more predictor variables.
          </Typography>
          
          <Typography variant="subtitle1" gutterBottom>
            Logistic Regression
          </Typography>
          <Typography variant="body1" paragraph>
            Use when your outcome variable is binary (two categories) and you want to predict the probability of an event.
          </Typography>
        </Box>
      ),
    },
    {
      id: 'select-variables',
      title: 'Select Variables',
      description: 'Choose the variables for your analysis',
      content: (
        <Box>
          {!dataset ? (
            <Alert severity="warning">
              <AlertTitle>No Dataset Selected</AlertTitle>
              Please go back and select a dataset first.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {analysisType === 'correlation' && (
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Select Variables for Correlation Analysis
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Choose at least 2 numeric variables to analyze their relationships.
                  </Typography>
                  
                  <FormGroup>
                    {numericColumns.map((column) => (
                      <FormControlLabel
                        key={column.id}
                        control={
                          <Checkbox
                            checked={selectedVariables.includes(column.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedVariables([...selectedVariables, column.id]);
                              } else {
                                setSelectedVariables(selectedVariables.filter(v => v !== column.id));
                              }
                            }}
                          />
                        }
                        label={column.name}
                      />
                    ))}
                  </FormGroup>
                  
                  {selectedVariables.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Selected variables ({selectedVariables.length}):
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {selectedVariables.map((varId) => {
                          const column = numericColumns.find(col => col.id === varId);
                          return (
                            <Chip
                              key={varId}
                              label={column?.name || varId}
                              onDelete={() => setSelectedVariables(selectedVariables.filter(v => v !== varId))}
                              color="primary"
                              variant="outlined"
                            />
                          );
                        })}
                      </Box>
                    </Box>
                  )}
                  
                  {analysisType === 'correlation' && (
                    <Box sx={{ mt: 3 }}>
                      <FormControl component="fieldset">
                        <FormLabel component="legend">Correlation Type</FormLabel>
                        <RadioGroup
                          value={correlationType}
                          onChange={(e) => setCorrelationType(e.target.value as CorrelationType)}
                          row
                        >
                          <FormControlLabel 
                            value="pearson" 
                            control={<Radio size="small" />} 
                            label="Pearson (Linear)" 
                          />
                          <FormControlLabel 
                            value="spearman" 
                            control={<Radio size="small" />} 
                            label="Spearman (Rank)" 
                          />
                          <FormControlLabel 
                            value="kendall" 
                            control={<Radio size="small" />} 
                            label="Kendall (Rank)" 
                          />
                        </RadioGroup>
                      </FormControl>
                    </Box>
                  )}
                </Grid>
              )}
              
              {(analysisType === 'simple-regression' || analysisType === 'multiple-regression' || analysisType === 'logistic-regression') && (
                <>
                  <Grid item xs={12}>
                    <VariableSelector
                      label={analysisType === 'logistic-regression' ? "Dependent Variable (Binary)" : "Dependent Variable (Numeric)"}
                      helperText={analysisType === 'logistic-regression' ? "Select the binary outcome variable" : "Select the continuous variable you want to predict"}
                      value={dependentVariable}
                      onChange={(value) => handleDependentVariableChange(value as string)}
                      datasetId={selectedDataset}
                      required
                      allowedTypes={analysisType === 'logistic-regression' ? [DataType.CATEGORICAL, DataType.NUMERIC] : [DataType.NUMERIC]}
                      variant="autocomplete"
                      placeholder={analysisType === 'logistic-regression' ? "Select a binary variable" : "Select a numeric variable"}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom>
                      Independent Variables (Predictors)
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Select {analysisType === 'simple-regression' ? 'one' : 'one or more'} predictor variable{analysisType === 'simple-regression' ? '' : 's'}.
                    </Typography>
                    
                    <FormGroup>
                      {numericColumns.concat(categoricalColumns).map((column) => (
                        <FormControlLabel
                          key={column.id}
                          control={
                            <Checkbox
                              checked={independentVariables.includes(column.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  if (analysisType === 'simple-regression' && independentVariables.length >= 1) {
                                    // Replace the existing variable for simple regression
                                    setIndependentVariables([column.id]);
                                  } else {
                                    setIndependentVariables([...independentVariables, column.id]);
                                  }
                                } else {
                                  setIndependentVariables(independentVariables.filter(v => v !== column.id));
                                }
                              }}
                              disabled={column.name === dependentVariable || column.id === dependentVariable}
                            />
                          }
                          label={`${column.name} (${column.type})`}
                        />
                      ))}
                    </FormGroup>
                    
                    {independentVariables.length > 0 && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" gutterBottom>
                          Selected predictors ({independentVariables.length}):
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {independentVariables.map((varId) => {
                            const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
                            return (
                              <Chip
                                key={varId}
                                label={column?.name || varId}
                                onDelete={() => setIndependentVariables(independentVariables.filter(v => v !== varId))}
                                color="secondary"
                                variant="outlined"
                              />
                            );
                          })}
                        </Box>
                      </Box>
                    )}
                    
                    {/* Baseline Category Selection for Categorical Variables */}
                    {independentVariables.some(varId => {
                      const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
                      return column?.type === DataType.CATEGORICAL;
                    }) && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Baseline Category Selection
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          For categorical variables, select the baseline (reference) category. Other categories will be compared against this baseline.
                        </Typography>
                        
                        <Grid container spacing={2}>
                          {independentVariables
                            .filter(varId => {
                              const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
                              return column?.type === DataType.CATEGORICAL;
                            })
                            .map((varId) => {
                              const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
                              const categories = getUniqueCategories(varId);
                              
                              return (
                                <Grid item xs={12} sm={6} key={varId}>
                                  <FormControl fullWidth size="small">
                                    <InputLabel>{column?.name} - Baseline Category</InputLabel>
                                    <Select
                                      value={selectedCategoricalBaseCategories[varId] || ''}
                                      onChange={(e) => handleBaseCategoryChange(varId, e.target.value as string)}
                                      label={`${column?.name} - Baseline Category`}
                                    >
                                      {categories.map((category) => (
                                        <MenuItem key={category} value={category}>
                                          {category}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                    <FormHelperText>
                                      {categories.length - 1} dummy variable{categories.length - 1 !== 1 ? 's' : ''} will be created
                                    </FormHelperText>
                                  </FormControl>
                                </Grid>
                              );
                            })
                          }
                        </Grid>
                      </Box>
                    )}
                    
                    {/* Dependent Variable Mapping for Logistic Regression (both categorical and numeric binary) */}
                    {analysisType === 'logistic-regression' && dependentVariableCategories.length === 2 && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Dependent Variable Category Mapping
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          Select which category should be mapped to 1 (positive outcome). The other category will be mapped to 0 (negative outcome).
                        </Typography>
                        
                        <FormControl component="fieldset">
                          <RadioGroup
                            value={selectedPositiveCategory}
                            onChange={(e) => setSelectedPositiveCategory(e.target.value)}
                          >
                            {dependentVariableCategories.map((category) => (
                              <FormControlLabel
                                key={category}
                                value={category}
                                control={<Radio />}
                                label={`Map "${category}" to 1 (and "${dependentVariableCategories.find(c => c !== category)}" to 0)`}
                              />
                            ))}
                          </RadioGroup>
                        </FormControl>
                        
                        {selectedPositiveCategory && (
                          <Alert severity="info" sx={{ mt: 2 }}>
                            <Typography variant="body2">
                              <strong>Mapping:</strong> "{selectedPositiveCategory}" → 1 (positive), "{dependentVariableCategories.find(c => c !== selectedPositiveCategory)}" → 0 (negative)
                            </Typography>
                          </Alert>
                        )}
                      </Box>
                    )}
                  </Grid>
                </>
              )}
            </Grid>
          )}
        </Box>
      ),
      validation: () => {
        if (!dataset) return 'Please select a dataset first';
        
        if (analysisType === 'correlation') {
          if (selectedVariables.length < 2) return 'Please select at least 2 variables for correlation analysis';
        } else {
          if (!dependentVariable) return 'Please select a dependent variable';
          if (independentVariables.length === 0) return 'Please select at least one independent variable';
          if (analysisType === 'simple-regression' && independentVariables.length > 1) {
            return 'Simple regression requires exactly one independent variable';
          }
          if (analysisType === 'multiple-regression' && independentVariables.length < 2) {
            return 'Multiple regression requires at least two independent variables';
          }
          if (independentVariables.includes(dependentVariable)) {
            return 'Dependent variable cannot be used as an independent variable';
          }
          
          // Check baseline category selection for categorical variables
          const categoricalIndependentVars = independentVariables.filter(varId => {
            const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
            return column?.type === DataType.CATEGORICAL;
          });
          
          for (const varId of categoricalIndependentVars) {
            if (!selectedCategoricalBaseCategories[varId]) {
              const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
              return `Please select a baseline category for ${column?.name || varId}`;
            }
            
            // Check if the variable has at least 2 categories
            const categories = getUniqueCategories(varId);
            if (categories.length < 2) {
              const column = numericColumns.concat(categoricalColumns).find(col => col.id === varId);
              return `${column?.name || varId} must have at least 2 categories for regression analysis`;
            }
          }
        }
        
        return true;
      },
    },
    {
      id: 'check-assumptions',
      title: 'Check Assumptions',
      description: 'Review the assumptions for your selected analysis',
      content: (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Statistical Assumptions
          </Typography>
          
          {analysisType === 'correlation' && (
            <Box>
              <Typography variant="body1" paragraph>
                For correlation analysis, the following assumptions should be considered:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="info.main">
                      {correlationType === 'pearson' ? 'Pearson Correlation' : correlationType === 'spearman' ? 'Spearman Correlation' : 'Kendall Correlation'}
                    </Typography>
                    <Typography variant="body2">
                      {correlationType === 'pearson' 
                        ? 'Assumes linear relationships and normally distributed variables'
                        : 'Non-parametric method suitable for monotonic relationships and ordinal data'
                      }
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="warning.main">
                      Data Quality
                    </Typography>
                    <Typography variant="body2">
                      Check for outliers and missing values that might affect correlation coefficients
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
          
          {(analysisType === 'simple-regression' || analysisType === 'multiple-regression') && (
            <Box>
              <Typography variant="body1" paragraph>
                Linear regression assumes:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="success.main">
                      Linearity
                    </Typography>
                    <Typography variant="body2">
                      The relationship between variables should be linear
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="info.main">
                      Independence
                    </Typography>
                    <Typography variant="body2">
                      Observations should be independent of each other
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="warning.main">
                      Homoscedasticity
                    </Typography>
                    <Typography variant="body2">
                      Residuals should have constant variance
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.error.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="error.main">
                      Normality of Residuals
                    </Typography>
                    <Typography variant="body2">
                      Residuals should be approximately normally distributed
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
          
          {analysisType === 'logistic-regression' && (
            <Box>
              <Typography variant="body1" paragraph>
                Logistic regression assumes:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="primary.main">
                      Binary Outcome
                    </Typography>
                    <Typography variant="body2">
                      Dependent variable should have exactly two categories
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.secondary.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="secondary.main">
                      Independence
                    </Typography>
                    <Typography variant="body2">
                      Observations should be independent
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="info.main">
                      No Multicollinearity
                    </Typography>
                    <Typography variant="body2">
                      Independent variables should not be highly correlated
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1) }}>
                    <Typography variant="subtitle2" gutterBottom color="warning.main">
                      Large Sample Size
                    </Typography>
                    <Typography variant="body2">
                      Requires adequate sample size for stable results
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
          
          <Box sx={{ mt: 3 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={checkAssumptions}
                  onChange={(e) => setCheckAssumptions(e.target.checked)}
                />
              }
              label="Perform automatic assumption checks where possible"
            />
          </Box>
        </Box>
      ),
      validation: () => true, // Always valid, just informational
    },
    {
      id: 'set-options',
      title: 'Set Analysis Options',
      description: 'Configure additional options for the analysis',
      content: (
        <Box>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <MuiTextField
                label="Significance Level (α)"
                type="number"
                value={significanceLevel}
                onChange={(e) => setSignificanceLevel(parseFloat(e.target.value))}
                inputProps={{ min: 0.001, max: 0.999, step: 0.01 }}
                fullWidth
                size="small"
                helperText="Typical values: 0.05, 0.01, or 0.001"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <MuiTextField
                label="Confidence Level"
                type="number"
                value={confidenceLevel}
                onChange={(e) => setConfidenceLevel(parseFloat(e.target.value))}
                inputProps={{ min: 0.5, max: 0.999, step: 0.01 }}
                fullWidth
                size="small"
                helperText="For confidence intervals (e.g., 0.95 for 95%)"
              />
            </Grid>
            
            {(analysisType === 'simple-regression' || analysisType === 'multiple-regression') && (
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={includeIntercept}
                      onChange={(e) => setIncludeIntercept(e.target.checked)}
                    />
                  }
                  label="Include intercept in regression model"
                />
              </Grid>
            )}
          </Grid>
          
          <ContextualHelp
            title="About Analysis Options"
            items={[
              {
                title: "Significance Level (α)",
                content: "The probability of rejecting the null hypothesis when it is true. Lower values make the test more conservative.",
                type: "info"
              },
              {
                title: "Confidence Level",
                content: "The level of confidence for confidence intervals. A 95% confidence level means we are 95% confident the true parameter lies within the interval.",
                type: "info"
              },
              {
                title: "Intercept",
                content: "The intercept represents the expected value of the dependent variable when all independent variables equal zero.",
                type: "tip"
              }
            ]}
            initiallyExpanded={true}
            variant="panel"
            showIcons={true}
            width="100%"
          />
        </Box>
      ),
      validation: () => {
        if (isNaN(significanceLevel) || significanceLevel <= 0 || significanceLevel >= 1) {
          return 'Significance level must be between 0 and 1';
        }
        if (isNaN(confidenceLevel) || confidenceLevel <= 0.5 || confidenceLevel >= 1) {
          return 'Confidence level must be between 0.5 and 1';
        }
        return true;
      },
    },
    {
      id: 'review-analysis',
      title: 'Review & Run Analysis',
      description: 'Review your selections and run the analysis',
      content: (
        <Box>
          <Paper sx={{ p: 2, mb: 3, backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
            <Typography variant="subtitle1" gutterBottom>
              Analysis Summary
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2">
                  <strong>Analysis Type:</strong> {
                    analysisType === 'correlation' ? 'Correlation Analysis' :
                    analysisType === 'simple-regression' ? 'Simple Linear Regression' :
                    analysisType === 'multiple-regression' ? 'Multiple Linear Regression' :
                    'Binary Logistic Regression'
                  }
                </Typography>
                <Typography variant="body2">
                  <strong>Dataset:</strong> {dataset?.name || 'None selected'}
                </Typography>
                {analysisType === 'correlation' && (
                  <>
                    <Typography variant="body2">
                      <strong>Correlation Type:</strong> {correlationType}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Variables:</strong> {selectedVariables.length} selected
                    </Typography>
                  </>
                )}
              </Grid>
              
              <Grid item xs={12} sm={6}>
                {analysisType !== 'correlation' && (
                  <>
                    <Typography variant="body2">
                      <strong>Dependent Variable:</strong> {dependentVariable || 'None selected'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Independent Variables:</strong> {independentVariables.length} selected
                    </Typography>
                  </>
                )}
                
                <Typography variant="body2">
                  <strong>Significance Level:</strong> {significanceLevel}
                </Typography>
                
                <Typography variant="body2">
                  <strong>Confidence Level:</strong> {confidenceLevel}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
          
          <DataFlowDiagram
            title="Analysis Process"
            steps={[
              {
                id: 'data',
                label: 'Dataset',
                description: dataset?.name || 'No dataset',
                state: dataset ? 'completed' : 'error'
              },
              {
                id: 'variables',
                label: 'Variables',
                description: analysisType === 'correlation' 
                  ? `${selectedVariables.length} variables` 
                  : `${independentVariables.length + (dependentVariable ? 1 : 0)} variables`,
                state: hasRequiredVariables() ? 'completed' : 'error'
              },
              {
                id: 'analysis',
                label: 'Analysis',
                description: analysisType === 'correlation' ? 'Correlation' : 'Regression',
                state: 'active'
              },
              {
                id: 'results',
                label: 'Results',
                description: 'Statistical output',
                state: undefined
              }
            ]}
            connections={[
              { from: 'data', to: 'variables', direction: 'horizontal' },
              { from: 'variables', to: 'analysis', direction: 'horizontal' },
              { from: 'analysis', to: 'results', direction: 'horizontal' }
            ]}
            variant="process"
            interactive={false}
          />
          
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              gradient
              rounded
              startIcon={<FlashOnIcon />}
              onClick={runAnalysis}
              disabled={!hasRequiredVariables()}
              size="large"
            >
              Run {analysisType === 'correlation' ? 'Correlation' : 'Regression'} Analysis
            </Button>
          </Box>

          {/* Results section */}
          <Box sx={{ mt: 3 }}>
            {!results ? (
              <Alert severity="info">
                <AlertTitle>Analysis Results</AlertTitle>
                Results will appear here after clicking "Run Analysis".
              </Alert>
            ) : results.error ? (
              <Alert severity="error">
                <AlertTitle>Analysis Error</AlertTitle>
                {results.error}
              </Alert>
            ) : (
              <EnhancedAnalysisResultCard
                title={`${
                  analysisType === 'correlation' ? 'Correlation Analysis' :
                  analysisType === 'simple-regression' ? 'Simple Linear Regression' :
                  analysisType === 'multiple-regression' ? 'Multiple Linear Regression' :
                  'Binary Logistic Regression'
                } Results`}
                description={
                  analysisType === 'correlation' 
                    ? `Correlation analysis of ${selectedVariables.length} variables using ${correlationType} correlation`
                    : analysisType === 'logistic-regression'
                    ? `Predicting ${dependentVariable} from ${independentVariables.length} predictor${independentVariables.length > 1 ? 's' : ''}`
                    : `Predicting ${dependentVariable} from ${independentVariables.length} predictor${independentVariables.length > 1 ? 's' : ''}`
                }
                timestamp={results.timestamp}
                pValue={analysisType === 'correlation' ? undefined : results.pValue}
                significance={significanceLevel}
                stats={
                  analysisType === 'correlation' 
                    ? [
                        { label: 'Variables', value: results.variables?.length || 0 },
                        { label: 'Sample Size', value: results.sampleSize },
                        { label: 'Correlation Type', value: results.correlationType }
                      ]
                    : analysisType === 'logistic-regression'
                    ? [
                        { label: 'Sample Size', value: results.sampleSize },
                        { label: 'Pseudo R²', value: results.pseudoRSquared?.toFixed(3) },
                        { label: 'AIC', value: results.aic?.toFixed(2) },
                        { label: 'Log-Likelihood', value: results.logLikelihood?.toFixed(2) },
                        { label: 'Accuracy', value: results.accuracy?.toFixed(3) },
                        { label: 'AUC', value: results.auc?.toFixed(3) }
                      ]
                    : [
                        { label: 'R²', value: results.rSquared?.toFixed(3) },
                        { label: 'Adjusted R²', value: results.adjustedRSquared?.toFixed(3) },
                        { label: 'F-statistic', value: results.fStatistic?.toFixed(2) },
                        { label: 'Sample Size', value: results.sampleSize }
                      ]
                }
                table={results.table}
                chart={
                  <Typography variant="body2" color="text.secondary" sx={{ p: 3, textAlign: 'center' }}>
                    Chart visualization will be implemented based on analysis type.
                  </Typography>
                }
                interpretations={[
                  analysisType === 'correlation'
                    ? `Correlation analysis completed for ${results.variables?.length || 0} variables using ${results.correlationType} correlation. Check the correlation matrix for significant relationships.`
                    : analysisType === 'logistic-regression'
                    ? `Logistic regression model predicting ${results.dependentVariable} from ${results.independentVariables?.length || 0} predictor(s). Pseudo R² = ${results.pseudoRSquared?.toFixed(3)}.`
                    : `${results.isSimpleRegression ? 'Simple' : 'Multiple'} linear regression model explains ${(results.rSquared * 100)?.toFixed(1)}% of the variance in ${results.dependentVariable}.`
                ]}
                assumptions={results.assumptions || []}
                footnotes={[
                  `The significance level (α) was set at ${significanceLevel}.`,
                  `The confidence level was set at ${confidenceLevel}.`
                ]}
                variant="default"
              />
            )}
          </Box>
        </Box>
      ),
      validation: () => {
        if (!hasRequiredVariables()) {
          return 'Please complete all previous steps before running the analysis';
        }
        return true;
      },
    },
  ];
  
  // Render Page
  return (
    <Box sx={{ p: 3, maxWidth: 1200, margin: '0 auto' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <MuiButton
          startIcon={<ArrowBackIosIcon />}
          sx={{ mr: 2 }}
          component="a"
          href="#/"
        >
          Back to Home
        </MuiButton>
        
        <Typography variant="h5" component="h1">
          Correlation & Regression Analysis Workflow
        </Typography>
      </Box>
      
      <Typography variant="body1" paragraph color="text.secondary">
        This guided workflow will help you perform correlation and regression analysis on your data. 
        Follow the steps below to select your data, choose the appropriate analysis type, and interpret the results.
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <GuidedWorkflow
          steps={workflowSteps}
          title="Correlation & Regression Analysis"
          description="Follow these steps to conduct correlation or regression analysis on your data"
          variant="vertical"
          saveProgress={true}
          persistenceKey="correlation-regression-workflow"
          enableBookmarking={true}
          showStepNavigation={true}
          allowSkipSteps={false}
        />
      </Paper>
    </Box>
  );
};

export default CorrelationRegressionWorkflowPage;