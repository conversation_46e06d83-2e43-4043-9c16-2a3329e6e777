import { createClient, SupabaseClient, Session } from '@supabase/supabase-js';

// Initialize the Supabase client with enhanced session management
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase credentials are missing. Authentication features will not work properly.');
}

// Enhanced Supabase client with automatic token refresh and session management
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session in localStorage
    persistSession: true,
    // Detect session in URL (for OAuth flows)
    detectSessionInUrl: true,
    // Storage key for session persistence
    storageKey: 'datastatpro-auth-token',
    // Custom storage implementation with error handling
    storage: {
      getItem: (key: string) => {
        try {
          return localStorage.getItem(key);
        } catch (error) {
          console.warn('Failed to get item from localStorage:', error);
          return null;
        }
      },
      setItem: (key: string, value: string) => {
        try {
          localStorage.setItem(key, value);
        } catch (error) {
          console.warn('Failed to set item in localStorage:', error);
        }
      },
      removeItem: (key: string) => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.warn('Failed to remove item from localStorage:', error);
        }
      }
    }
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'datastatpro-web'
    }
  },
  // Realtime configuration for better connection management
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Session validation and heartbeat monitoring
class SessionManager {
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = Date.now();
  private sessionValidationCallbacks: Array<(isValid: boolean) => void> = [];
  private isMonitoring: boolean = false;

  constructor(private client: SupabaseClient) {
    this.setupAuthStateListener();
  }

  /**
   * Sets up auth state change listener for session management
   */
  private setupAuthStateListener(): void {
    this.client.auth.onAuthStateChange((event, session) => {
      console.log('🔐 Auth state changed:', event, session ? 'Session active' : 'No session');
      
      if (event === 'SIGNED_IN' && session) {
        this.startHeartbeatMonitoring();
        this.notifySessionValidation(true);
      } else if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        if (event === 'SIGNED_OUT') {
          this.stopHeartbeatMonitoring();
          this.notifySessionValidation(false);
        } else if (event === 'TOKEN_REFRESHED') {
          console.log('🔄 Token refreshed successfully');
          this.lastHeartbeat = Date.now();
          this.notifySessionValidation(true);
        }
      }
    });
  }

  /**
   * Validates current session before API calls
   */
  async validateSession(): Promise<{ isValid: boolean; session: Session | null; error?: string }> {
    try {
      const { data: { session }, error } = await this.client.auth.getSession();
      
      if (error) {
        console.warn('❌ Session validation error:', error.message);
        return { isValid: false, session: null, error: error.message };
      }

      if (!session) {
        console.warn('⚠️ No active session found');
        return { isValid: false, session: null, error: 'No active session' };
      }

      // Check if session is expired
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = session.expires_at || 0;
      
      if (expiresAt <= now) {
        console.warn('⏰ Session has expired');
        return { isValid: false, session: null, error: 'Session expired' };
      }

      // Check if session expires soon (within 5 minutes)
      const timeUntilExpiry = expiresAt - now;
      if (timeUntilExpiry < 300) { // 5 minutes
        console.log('🔄 Session expires soon, attempting refresh...');
        const { data: { session: refreshedSession }, error: refreshError } = await this.client.auth.refreshSession();
        
        if (refreshError) {
          console.warn('❌ Session refresh failed:', refreshError.message);
          return { isValid: false, session: null, error: refreshError.message };
        }
        
        return { isValid: true, session: refreshedSession, error: undefined };
      }

      console.log('✅ Session is valid');
      return { isValid: true, session, error: undefined };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('💥 Session validation failed:', errorMessage);
      return { isValid: false, session: null, error: errorMessage };
    }
  }

  /**
   * Starts heartbeat monitoring to keep session alive
   */
  startHeartbeatMonitoring(): void {
    if (this.isMonitoring) {
      console.log('💓 Heartbeat monitoring already active');
      return;
    }

    console.log('💓 Starting session heartbeat monitoring');
    this.isMonitoring = true;
    this.lastHeartbeat = Date.now();

    // Check session health every 30 seconds
    this.heartbeatInterval = setInterval(async () => {
      try {
        const { isValid, error } = await this.validateSession();
        
        if (!isValid) {
          console.warn('💔 Heartbeat detected invalid session:', error);
          this.notifySessionValidation(false);
          
          // Try to refresh the session
          const { error: refreshError } = await this.client.auth.refreshSession();
          if (refreshError) {
            console.error('💔 Heartbeat session refresh failed:', refreshError.message);
            this.stopHeartbeatMonitoring();
          }
        } else {
          this.lastHeartbeat = Date.now();
          console.log('💓 Heartbeat: Session healthy');
        }
      } catch (error) {
        console.error('💔 Heartbeat monitoring error:', error);
      }
    }, 30000); // 30 seconds
  }

  /**
   * Stops heartbeat monitoring
   */
  stopHeartbeatMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    this.isMonitoring = false;
    console.log('💔 Stopped session heartbeat monitoring');
  }

  /**
   * Adds a callback for session validation events
   */
  onSessionValidation(callback: (isValid: boolean) => void): () => void {
    this.sessionValidationCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.sessionValidationCallbacks.indexOf(callback);
      if (index > -1) {
        this.sessionValidationCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Notifies all session validation callbacks
   */
  private notifySessionValidation(isValid: boolean): void {
    this.sessionValidationCallbacks.forEach(callback => {
      try {
        callback(isValid);
      } catch (error) {
        console.error('Session validation callback error:', error);
      }
    });
  }

  /**
   * Gets session monitoring status
   */
  getMonitoringStatus(): { isMonitoring: boolean; lastHeartbeat: number; timeSinceLastHeartbeat: number } {
    return {
      isMonitoring: this.isMonitoring,
      lastHeartbeat: this.lastHeartbeat,
      timeSinceLastHeartbeat: Date.now() - this.lastHeartbeat
    };
  }

  /**
   * Forces a session refresh
   */
  async forceSessionRefresh(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 Forcing session refresh...');
      const { error } = await this.client.auth.refreshSession();
      
      if (error) {
        console.error('❌ Force refresh failed:', error.message);
        return { success: false, error: error.message };
      }
      
      console.log('✅ Force refresh successful');
      this.lastHeartbeat = Date.now();
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('💥 Force refresh error:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }
}

// Create session manager instance
export const sessionManager = new SessionManager(supabase);

// Enhanced API wrapper with session validation
export const createValidatedApiCall = <T extends any[], R>(
  apiCall: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    // Validate session before making API call
    const { isValid, error } = await sessionManager.validateSession();
    
    if (!isValid) {
      throw new Error(`Session validation failed: ${error}`);
    }
    
    try {
      return await apiCall(...args);
    } catch (apiError) {
      // If API call fails due to auth issues, try to refresh session and retry once
      if (apiError instanceof Error && 
          (apiError.message.includes('JWT') || 
           apiError.message.includes('expired') || 
           apiError.message.includes('unauthorized'))) {
        
        console.log('🔄 API call failed due to auth issue, attempting session refresh...');
        const { success } = await sessionManager.forceSessionRefresh();
        
        if (success) {
          console.log('🔄 Retrying API call after session refresh...');
          return await apiCall(...args);
        }
      }
      
      throw apiError;
    }
  };
};