// Shared Pyodide Service
// Provides a centralized Pyodide instance for all statistical analysis services

class PyodideService {
  private static instance: PyodideService;
  private pyodide: any = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;
  private initializationAttempts = 0;
  private maxRetries = 3;

  private constructor() {}

  static getInstance(): PyodideService {
    if (!PyodideService.instance) {
      PyodideService.instance = new PyodideService();
    }
    return PyodideService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.doInitialize();
    await this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    while (this.initializationAttempts < this.maxRetries && !this.isInitialized) {
      try {
        this.initializationAttempts++;
        console.log(`Attempting to initialize Pyodide (attempt ${this.initializationAttempts}/${this.maxRetries})`);

        // Check if Pyodide is already loaded
        if (typeof window !== 'undefined' && (window as any).loadPyodide) {
          console.log('Pyodide loader already available');
        } else {
          // Load Pyodide from CDN with timeout
          await this.loadPyodideScript();
        }

        // Initialize Pyodide with timeout
        console.log('Initializing Pyodide...');
        this.pyodide = await Promise.race([
          (window as any).loadPyodide({
            indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/'
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Pyodide initialization timeout')), 30000)
          )
        ]);

        // Install required packages
        console.log('Installing Python packages...');
        await Promise.race([
          this.pyodide.loadPackage(['numpy', 'scipy', 'pandas']),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Package installation timeout')), 20000)
          )
        ]);

        console.log('Pyodide initialized successfully');
        this.isInitialized = true;
        return;

      } catch (error) {
        console.error(`Pyodide initialization attempt ${this.initializationAttempts} failed:`, error);
        
        if (this.initializationAttempts >= this.maxRetries) {
          throw new Error(
            `Failed to initialize Python environment after ${this.maxRetries} attempts. ` +
            'This may be due to network connectivity issues or CDN unavailability. ' +
            'Please check your internet connection and try again.'
          );
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  private async loadPyodideScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js';
      
      const timeout = setTimeout(() => {
        document.head.removeChild(script);
        reject(new Error('Pyodide script loading timeout'));
      }, 15000);

      script.onload = () => {
        clearTimeout(timeout);
        console.log('Pyodide script loaded successfully');
        resolve();
      };

      script.onerror = () => {
        clearTimeout(timeout);
        document.head.removeChild(script);
        reject(new Error('Failed to load Pyodide script from CDN'));
      };

      document.head.appendChild(script);
    });
  }

  getPyodide(): any {
    if (!this.isInitialized || !this.pyodide) {
      throw new Error('Pyodide not initialized. Call initialize() first.');
    }
    return this.pyodide;
  }

  isReady(): boolean {
    return this.isInitialized && this.pyodide !== null;
  }

  reset(): void {
    this.pyodide = null;
    this.isInitialized = false;
    this.initializationPromise = null;
    this.initializationAttempts = 0;
  }
}

export const pyodideService = PyodideService.getInstance();
export default pyodideService;