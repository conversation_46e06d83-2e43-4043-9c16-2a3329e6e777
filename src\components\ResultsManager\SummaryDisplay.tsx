import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  <PERSON>ack,
  Divider,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  ExpandMore,
  ContentCopy,
  Share,
  Download,
  Email,
  Description,
  Code,
  Html,
  TextFields,
  DataObject
} from '@mui/icons-material';
import { GeneratedSummary } from './ResultsSummaryGenerator';
import {
  copySummaryToClipboard,
  shareSummaryByEmail,
  exportSummaryAsMarkdown,
  exportSummaryAsHTML,
  exportSummaryAsText,
  exportSummaryAsJSON
} from '../../utils/summaryExport';

interface SummaryDisplayProps {
  data: {
    summary: string;
    executiveSummary: string;
    keyFindings: string[];
    statisticalSummary: string;
    methodsSummary: string;
    limitations: string[];
    recommendations: string[];
    wordCount: number;
    generatedAt: string;
  };
  title: string;
  timestamp: Date;
}

export const SummaryDisplay: React.FC<SummaryDisplayProps> = ({
  data,
  title,
  timestamp
}) => {
  const [shareMenuAnchor, setShareMenuAnchor] = useState<null | HTMLElement>(null);
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [copySuccess, setCopySuccess] = useState(false);

  // Reconstruct GeneratedSummary object for export functions
  const generatedSummary: GeneratedSummary = {
    title,
    executiveSummary: data.executiveSummary,
    keyFindings: data.keyFindings,
    statisticalSummary: data.statisticalSummary,
    methodsSummary: data.methodsSummary,
    limitations: data.limitations,
    recommendations: data.recommendations,
    fullText: data.summary,
    wordCount: data.wordCount,
    timestamp: new Date(data.generatedAt)
  };

  const handleCopy = async (format: 'text' | 'markdown' | 'html' = 'text') => {
    const success = await copySummaryToClipboard(generatedSummary, format);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const handleShare = (method: 'email' | 'copy') => {
    if (method === 'email') {
      shareSummaryByEmail(generatedSummary);
    } else {
      handleCopy('text');
    }
    setShareMenuAnchor(null);
  };

  const handleExport = async (format: 'markdown' | 'html' | 'text' | 'json') => {
    try {
      switch (format) {
        case 'markdown':
          await exportSummaryAsMarkdown(generatedSummary);
          break;
        case 'html':
          await exportSummaryAsHTML(generatedSummary);
          break;
        case 'text':
          await exportSummaryAsText(generatedSummary);
          break;
        case 'json':
          await exportSummaryAsJSON(generatedSummary);
          break;
      }
    } catch (error) {
      console.error('Export failed:', error);
    }
    setExportMenuAnchor(null);
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card variant="outlined">
      <CardContent>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box flex={1}>
            <Typography variant="h6" component="h3" gutterBottom>
              {title}
            </Typography>
            <Stack direction="row" spacing={1} alignItems="center" mb={1}>
              <Chip 
                label="Summary" 
                size="small" 
                color="primary" 
                variant="outlined"
              />
              <Typography variant="body2" color="text.secondary">
                {data.wordCount} words
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Generated {formatTimestamp(new Date(data.generatedAt))}
              </Typography>
            </Stack>
          </Box>
          
          <Stack direction="row" spacing={1}>
            <Tooltip title={copySuccess ? 'Copied!' : 'Copy to clipboard'}>
              <IconButton 
                onClick={() => handleCopy('text')}
                color={copySuccess ? 'success' : 'default'}
                size="small"
              >
                <ContentCopy />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Share">
              <IconButton 
                onClick={(e) => setShareMenuAnchor(e.currentTarget)}
                size="small"
              >
                <Share />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Export">
              <IconButton 
                onClick={(e) => setExportMenuAnchor(e.currentTarget)}
                size="small"
              >
                <Download />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Executive Summary */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Executive Summary
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1" paragraph>
              {data.executiveSummary}
            </Typography>
          </AccordionDetails>
        </Accordion>

        {/* Key Findings */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Key Findings ({data.keyFindings.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={1}>
              {data.keyFindings.map((finding, index) => (
                <Box key={index} display="flex" alignItems="flex-start">
                  <Typography variant="body2" color="primary" sx={{ mr: 1, mt: 0.5 }}>
                    •
                  </Typography>
                  <Typography variant="body2" flex={1}>
                    {finding}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* Statistical Summary */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Statistical Summary
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              {data.statisticalSummary}
            </Typography>
          </AccordionDetails>
        </Accordion>

        {/* Methods */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Methods
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body1">
              {data.methodsSummary}
            </Typography>
          </AccordionDetails>
        </Accordion>

        {/* Limitations */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Limitations ({data.limitations.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={1}>
              {data.limitations.map((limitation, index) => (
                <Box key={index} display="flex" alignItems="flex-start">
                  <Typography variant="body2" color="warning.main" sx={{ mr: 1, mt: 0.5 }}>
                    •
                  </Typography>
                  <Typography variant="body2" flex={1}>
                    {limitation}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* Recommendations */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Recommendations ({data.recommendations.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={1}>
              {data.recommendations.map((recommendation, index) => (
                <Box key={index} display="flex" alignItems="flex-start">
                  <Typography variant="body2" color="success.main" sx={{ mr: 1, mt: 0.5 }}>
                    •
                  </Typography>
                  <Typography variant="body2" flex={1}>
                    {recommendation}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* Full Text Preview */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1" fontWeight="medium">
              Full Text Preview
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box 
              component="pre" 
              sx={{ 
                whiteSpace: 'pre-wrap', 
                fontFamily: 'monospace', 
                fontSize: '0.875rem',
                backgroundColor: 'grey.50',
                p: 2,
                borderRadius: 1,
                overflow: 'auto',
                maxHeight: 400
              }}
            >
              {data.summary}
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Share Menu */}
        <Menu
          anchorEl={shareMenuAnchor}
          open={Boolean(shareMenuAnchor)}
          onClose={() => setShareMenuAnchor(null)}
        >
          <MenuItem onClick={() => handleShare('copy')}>
            <ListItemIcon>
              <ContentCopy fontSize="small" />
            </ListItemIcon>
            <ListItemText>Copy to Clipboard</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleShare('email')}>
            <ListItemIcon>
              <Email fontSize="small" />
            </ListItemIcon>
            <ListItemText>Share via Email</ListItemText>
          </MenuItem>
        </Menu>

        {/* Export Menu */}
        <Menu
          anchorEl={exportMenuAnchor}
          open={Boolean(exportMenuAnchor)}
          onClose={() => setExportMenuAnchor(null)}
        >
          <MenuItem onClick={() => handleExport('markdown')}>
            <ListItemIcon>
              <Description fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as Markdown</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleExport('html')}>
            <ListItemIcon>
              <Html fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as HTML</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleExport('text')}>
            <ListItemIcon>
              <TextFields fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as Text</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleExport('json')}>
            <ListItemIcon>
              <DataObject fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
};

export default SummaryDisplay;