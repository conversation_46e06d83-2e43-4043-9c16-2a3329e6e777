import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { routeRegistry } from '../routing/routeConfig';

/**
 * Enhanced hash redirect mapping for backward compatibility
 * Maps old hash-based URLs to new path-based URLs
 */
const hashRedirectMap: Record<string, string> = {
  // Core pages
  '#app/home': '/home',
  '#app/dashboard': '/dashboard',
  '#app/analysis-index': '/analysis-index',
  '#app/assistant': '/assistant',
  
  // Data management
  '#app/data-management': '/data-management',
  '#app/data-management/import': '/data-management/import',
  '#app/data-management/export': '/data-management/export',
  '#app/data-management/transform': '/data-management/transform',
  '#app/data-management/merge': '/data-management/merge',
  
  // Statistics
  '#app/stats': '/stats',
  '#app/stats/descriptive': '/stats/descriptive',
  '#app/stats/frequencies': '/stats/frequencies',
  '#app/stats/crosstabs': '/stats/crosstabs',
  '#app/inferential-stats': '/inferential-stats',
  '#app/inferential-stats/t-tests': '/inferential-stats/t-tests',
  '#app/inferential-stats/anova': '/inferential-stats/anova',
  '#app/inferential-stats/non-parametric': '/inferential-stats/non-parametric',
  
  // Correlation and regression
  '#app/correlation-analysis': '/correlation-analysis',
  '#app/correlation-analysis/pearson': '/correlation-analysis/pearson',
  '#app/correlation-analysis/spearman': '/correlation-analysis/spearman',
  '#app/correlation-analysis/regression': '/correlation-analysis/regression',
  
  // Advanced analysis
  '#app/advanced-analysis': '/advanced-analysis',
  '#app/advanced-analysis/factor-analysis': '/advanced-analysis/factor-analysis',
  '#app/advanced-analysis/cluster-analysis': '/advanced-analysis/cluster-analysis',
  '#app/advanced-analysis/survival-analysis': '/advanced-analysis/survival-analysis',
  '#app/advanced-analysis/meta-analysis': '/advanced-analysis/meta-analysis',
  
  // Visualization
  '#app/charts': '/charts',
  '#app/charts/bar': '/charts/bar',
  '#app/charts/pie': '/charts/pie',
  '#app/charts/histogram': '/charts/histogram',
  '#app/charts/boxplot': '/charts/boxplot',
  '#app/charts/scatter': '/charts/scatter',
  '#app/charts/raincloud': '/charts/raincloud',
  '#app/charts/sankey': '/charts/sankey',
  '#app/charts/errorbar': '/charts/errorbar',
  
  // Publication ready
  '#app/publication-ready': '/publication-ready',
  '#app/publication-ready/table1': '/publication-ready/table1',
  '#app/publication-ready/table2': '/publication-ready/table2',
  '#app/publication-ready/flow-diagram': '/publication-ready/flow-diagram',
  '#app/publication-ready/regression-table': '/publication-ready/regression-table',
  
  // Calculators
  '#app/epicalc': '/epicalc',
  '#app/epicalc/case-control': '/epicalc/case-control',
  '#app/epicalc/cohort': '/epicalc/cohort',
  '#app/epicalc/cross-sectional': '/epicalc/cross-sectional',
  '#app/samplesize': '/samplesize',
  '#app/samplesize/options': '/samplesize/options',
  '#app/samplesize/one-sample': '/samplesize/one-sample',
  '#app/samplesize/two-sample': '/samplesize/two-sample',
  
  // Knowledge base
  '#app/knowledge-base': '/knowledge-base',
  '#app/which-test': '/which-test',
  '#app/visualization-guide': '/visualization-guide',
  '#app/statistical-methods': '/statistical-methods',
  
  // Authentication
  '#app/auth': '/auth',
  '#app/user-profile': '/user-profile',
  '#app/settings': '/settings',
  '#app/update-password': '/update-password',
  
  // Legacy patterns
  '#/app/': '/home',
  '#/': '/home',
  '#app': '/home',
  '#': '/home'
};

/**
 * Generate dynamic hash mappings from route registry
 */
function generateDynamicHashMappings(): Record<string, string> {
  const dynamicMappings: Record<string, string> = {};
  
  try {
    const allRoutes = routeRegistry.getAllRoutes();
    
    allRoutes.forEach(route => {
      if (route.path) {
        // Add standard #app/ prefix mapping
        dynamicMappings[`#app/${route.path}`] = `/${route.path}`;
        
        // Add alternative hash patterns
        dynamicMappings[`#/${route.path}`] = `/${route.path}`;
        dynamicMappings[`#${route.path}`] = `/${route.path}`;
      }
    });
  } catch (error) {
    console.warn('Failed to generate dynamic hash mappings:', error);
  }
  
  return dynamicMappings;
}

/**
 * Enhanced hash redirect hook with comprehensive mapping
 */
export function useHashRedirect() {
  const navigate = useNavigate();
  
  useEffect(() => {
    const hash = window.location.hash;
    
    if (!hash) return;
    
    // Don't redirect if we're already in the /app context
    if (window.location.pathname.startsWith('/app')) {
      return;
    }
    
    // Combine static and dynamic mappings
    const dynamicMappings = generateDynamicHashMappings();
    const allMappings = { ...hashRedirectMap, ...dynamicMappings };
    
    // Check for exact hash match
    if (allMappings[hash]) {
      const newPath = allMappings[hash];
      console.log(`Hash redirect: ${hash} -> ${newPath}`);
      navigate(newPath, { replace: true });
      clearHash();
      return;
    }
    
    // Handle #app/ prefix patterns
    if (hash.startsWith('#app/')) {
      const hashPath = hash.substring(5); // Remove '#app/'
      const newPath = `/${hashPath}`;
      console.log(`Hash redirect (app prefix): ${hash} -> ${newPath}`);
      navigate(newPath, { replace: true });
      clearHash();
      return;
    }
    
    // Handle #/ prefix patterns
    if (hash.startsWith('#/')) {
      const hashPath = hash.substring(2); // Remove '#/'
      // Only redirect if there's actually a path after #/
      if (hashPath) {
        const newPath = `/${hashPath}`;
        console.log(`Hash redirect (slash prefix): ${hash} -> ${newPath}`);
        navigate(newPath, { replace: true });
        clearHash();
        return;
      }
      // If it's just '#/', clear the hash and let the root path work naturally
      clearHash();
      return;
    }
    
    // Handle single # patterns
    if (hash.startsWith('#') && hash.length > 1) {
      const hashPath = hash.substring(1); // Remove '#'
      // Only redirect if it looks like a route (contains letters/numbers)
      if (/^[a-zA-Z0-9\-\/]+$/.test(hashPath)) {
        const newPath = `/${hashPath}`;
        console.log(`Hash redirect (single hash): ${hash} -> ${newPath}`);
        navigate(newPath, { replace: true });
        clearHash();
        return;
      }
    }
    
    // If hash doesn't match any pattern but exists, log for debugging
    if (hash && hash !== '#') {
      console.log(`Unhandled hash pattern: ${hash}`);
    }
  }, [navigate]);
}

/**
 * Clear hash from URL without triggering navigation
 */
function clearHash() {
  if (window.location.hash) {
    const newUrl = window.location.pathname + window.location.search;
    window.history.replaceState(null, '', newUrl);
  }
}

/**
 * Get redirect path for a given hash (for testing/debugging)
 */
export function getRedirectPath(hash: string): string | null {
  const dynamicMappings = generateDynamicHashMappings();
  const allMappings = { ...hashRedirectMap, ...dynamicMappings };
  
  if (allMappings[hash]) {
    return allMappings[hash];
  }
  
  if (hash.startsWith('#app/')) {
    return `/${hash.substring(5)}`;
  }
  
  if (hash.startsWith('#/')) {
    const hashPath = hash.substring(2);
    return hashPath ? `/${hashPath}` : '/home';
  }
  
  if (hash.startsWith('#') && hash.length > 1) {
    const hashPath = hash.substring(1);
    if (/^[a-zA-Z0-9\-\/]+$/.test(hashPath)) {
      return `/${hashPath}`;
    }
  }
  
  return null;
}