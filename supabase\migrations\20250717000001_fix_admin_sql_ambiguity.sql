-- Fix Admin SQL Ambiguity Migration
-- This migration fixes the "column reference 'is_admin' is ambiguous" error
-- by ensuring the is_user_admin helper function exists and is used consistently
-- Date: 2025-07-17

-- Create or replace the is_user_admin helper function to avoid ambiguity
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id AND COALESCE(is_admin, false) = true
  );
END;
$$;

-- Fix get_all_users function to use the helper function
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function (using helper function to avoid ambiguity)
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with explicit type casting to match expected interface
  RETURN QUERY
  SELECT 
    p.id::UUID, 
    COALESCE(p.username, 'No email available')::TEXT as email,
    p.username::TEXT, 
    p.full_name::TEXT, 
    p.institution::TEXT, 
    p.country::TEXT, 
    p.avatar_url::TEXT, 
    p.updated_at::TIMESTAMP WITH TIME ZONE, 
    COALESCE(p.is_admin, false)::BOOLEAN as is_admin,
    COALESCE(p.accounttype, 'standard')::TEXT as accounttype,
    COALESCE(p.updated_at, NOW())::TIMESTAMP WITH TIME ZONE as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at
  FROM public.profiles p
  WHERE (search_term IS NULL OR 
         COALESCE(p.full_name, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.username, '') ILIKE '%' || COALESCE(search_term, '') || '%' OR
         COALESCE(p.institution, '') ILIKE '%' || COALESCE(search_term, '') || '%')
  ORDER BY COALESCE(p.updated_at, NOW()) DESC
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Fix get_admin_users function to use the helper function
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
  id UUID,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function (using helper function to avoid ambiguity)
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT p.id, p.username, p.full_name, p.institution, p.country, p.avatar_url, p.updated_at, p.is_admin
  FROM public.profiles p
  WHERE COALESCE(p.is_admin, false) = true
  ORDER BY p.full_name, p.username;
END;
$$;

-- Fix update_user_admin_status function to use the helper function
CREATE OR REPLACE FUNCTION public.update_user_admin_status(
  target_user_id UUID,
  new_admin_status BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function (using helper function to avoid ambiguity)
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Prevent users from removing their own admin status
  IF target_user_id = auth.uid() AND new_admin_status = false THEN
    RAISE EXCEPTION 'Cannot remove your own admin privileges.';
  END IF;
  
  UPDATE public.profiles 
  SET is_admin = new_admin_status, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Fix update_user_account_type function to use the helper function
CREATE OR REPLACE FUNCTION public.update_user_account_type(
  target_user_id UUID,
  new_account_type TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function (using helper function to avoid ambiguity)
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate account type
  IF new_account_type NOT IN ('standard', 'pro', 'edu', 'edu_pro') THEN
    RAISE EXCEPTION 'Invalid account type. Must be one of: standard, pro, edu, edu_pro';
  END IF;
  
  UPDATE public.profiles 
  SET accounttype = new_account_type, updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_users() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_admin_status(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_account_type(UUID, TEXT) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.is_user_admin(UUID) IS 'Helper function to check admin status, avoiding SQL ambiguity issues. Fixed 2025-07-17.';
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Returns paginated list of all users with search. Fixed SQL ambiguity 2025-07-17.';

-- Success message
SELECT 'Admin SQL ambiguity issues fixed successfully. User Management should now work properly.' as status;
