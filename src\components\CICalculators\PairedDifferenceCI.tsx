import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  Tabs,
  Tab,
  useTheme,
  Chip,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  SwapHoriz as PairedIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import 'katex/dist/katex.min.css';
import katex from 'katex';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`paired-difference-ci-tabpanel-${index}`}
      aria-labelledby={`paired-difference-ci-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface PairedDifferenceCIResult {
  method: string;
  lowerBound: number;
  upperBound: number;
  confidenceLevel: number;
  meanDifference: number;
  standardError: number;
  degreesOfFreedom: number;
  tStatistic: number;
  interpretation: string;
  formula: string;
}

interface CalculationData {
  meanDifference: number;
  standardDeviation: number;
  sampleSize: number;
  confidenceLevel: number;
}

const PairedDifferenceCI: React.FC = () => {
  const theme = useTheme();
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<CalculationData>({
    meanDifference: 0,
    standardDeviation: 0,
    sampleSize: 0,
    confidenceLevel: 95
  });
  const [result, setResult] = useState<PairedDifferenceCIResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const renderFormula = (formula: string): string => {
    try {
      return katex.renderToString(formula, {
        displayMode: true,
        throwOnError: false,
        strict: false
      });
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const getTScore = (df: number, confidenceLevel: number): number => {
    // Simplified t-values for common confidence levels and degrees of freedom
    const tTable: { [key: number]: { [key: number]: number } } = {
      90: { 1: 6.314, 2: 2.920, 5: 2.015, 10: 1.812, 20: 1.725, 30: 1.697, 50: 1.676, 100: 1.660, 1000: 1.645 },
      95: { 1: 12.706, 2: 4.303, 5: 2.571, 10: 2.228, 20: 2.086, 30: 2.042, 50: 2.009, 100: 1.984, 1000: 1.962 },
      99: { 1: 63.657, 2: 9.925, 5: 4.032, 10: 3.169, 20: 2.845, 30: 2.750, 50: 2.678, 100: 2.626, 1000: 2.581 },
      99.9: { 1: 636.619, 2: 31.598, 5: 8.610, 10: 4.587, 20: 3.850, 30: 3.646, 50: 3.460, 100: 3.390, 1000: 3.291 }
    };
    
    const tValues = tTable[confidenceLevel];
    if (!tValues) return 1.96; // Default to z-score
    
    // Find closest df
    const dfKeys = Object.keys(tValues).map(Number).sort((a, b) => a - b);
    let closestDf = dfKeys[dfKeys.length - 1]; // Default to largest
    
    for (const key of dfKeys) {
      if (df <= key) {
        closestDf = key;
        break;
      }
    }
    
    return tValues[closestDf] || 1.96;
  };

  const calculatePairedDifferenceCI = () => {
    const { meanDifference, standardDeviation, sampleSize, confidenceLevel } = calculationData;
    
    if (sampleSize <= 0) {
      setError('Sample size must be greater than 0.');
      return;
    }
    
    if (standardDeviation <= 0) {
      setError('Standard deviation must be greater than 0.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const degreesOfFreedom = sampleSize - 1;
      const standardError = standardDeviation / Math.sqrt(sampleSize);
      const tCritical = getTScore(degreesOfFreedom, confidenceLevel);
      const margin = tCritical * standardError;
      
      const lowerBound = meanDifference - margin;
      const upperBound = meanDifference + margin;
      
      const tStatistic = meanDifference / standardError;
      
      let interpretation = `We are ${confidenceLevel}% confident that the true mean difference (μd) lies between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
      
      if (lowerBound > 0) {
        interpretation += ' Since the entire interval is positive, we can conclude that there is a significant positive difference.';
      } else if (upperBound < 0) {
        interpretation += ' Since the entire interval is negative, we can conclude that there is a significant negative difference.';
      } else {
        interpretation += ' Since the interval contains 0, we cannot conclude there is a significant difference.';
      }
      
      const formula = '\\bar{d} \\pm t_{\\alpha/2,df} \\cdot \\frac{s_d}{\\sqrt{n}}';
      
      setResult({
        method: `Paired t-test (df = ${degreesOfFreedom})`,
        lowerBound,
        upperBound,
        confidenceLevel,
        meanDifference,
        standardError,
        degreesOfFreedom,
        tStatistic,
        interpretation,
        formula
      });
      
      // Automatically navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError('An error occurred during calculation. Please check your inputs.');
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setCalculationData({
      meanDifference: 0,
      standardDeviation: 0,
      sampleSize: 0,
      confidenceLevel: 95
    });
    setResult(null);
    setError(null);
    setActiveTab(0);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (!result) return '';
    
    return `Paired Difference Confidence Interval Analysis\n\n` +
           `Mean Difference (d̄): ${result.meanDifference}\n` +
           `Standard Deviation: ${calculationData.standardDeviation}\n` +
           `Sample Size: ${calculationData.sampleSize}\n` +
           `Confidence Level: ${result.confidenceLevel}%\n` +
           `Method: ${result.method}\n` +
           `Standard Error: ${result.standardError.toFixed(2)}\n` +
           `t-statistic: ${result.tStatistic.toFixed(2)}\n` +
           `Confidence Interval: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n\n` +
           `Interpretation: ${result.interpretation}`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <PairedIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Paired Difference Confidence Interval
        </Typography>
      </Box>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Calculate confidence intervals for the mean of paired differences (before/after, matched pairs).
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!result} />
          <Tab label="Guide" />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <InfoIcon sx={{ mr: 1 }} />
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mean Difference (d̄)"
                      value={calculationData.meanDifference}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, meanDifference: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ step: 'any' }}
                      sx={{ mb: 2 }}
                      helperText="Average of all paired differences"
                    />
                    
                    <TextField
                      fullWidth
                      label="Standard Deviation of Differences (sd)"
                      value={calculationData.standardDeviation}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, standardDeviation: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 0, step: 'any' }}
                      sx={{ mb: 2 }}
                      helperText="Standard deviation of the paired differences"
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Number of Pairs (n)"
                      value={calculationData.sampleSize}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, sampleSize: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 1, step: 1 }}
                      sx={{ mb: 2 }}
                      helperText="Number of paired observations"
                    />
                    
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={calculationData.confidenceLevel}
                        label="Confidence Level"
                        onChange={(e) => setCalculationData(prev => ({ ...prev, confidenceLevel: Number(e.target.value) }))}
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                        <MenuItem value={99.9}>99.9%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                
                {calculationData.sampleSize > 0 && calculationData.standardDeviation > 0 && (
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'info.50', borderRadius: 1 }}>
                    <Typography variant="body2" color="info.main">
                      <strong>Standard Error:</strong> {(calculationData.standardDeviation / Math.sqrt(calculationData.sampleSize)).toFixed(2)}
                    </Typography>
                    <Typography variant="body2" color="info.main">
                      <strong>Degrees of Freedom:</strong> {calculationData.sampleSize - 1}
                    </Typography>
                  </Box>
                )}
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculatePairedDifferenceCI}
                    startIcon={<CalculateIcon />}
                    disabled={loading}
                    sx={{ flex: 1 }}
                  >
                    {loading ? 'Calculating...' : 'Calculate CI'}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                    startIcon={<ClearIcon />}
                    sx={{ flex: 1 }}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Interval Results
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                    <Typography variant="subtitle2" color="primary">
                      {result.confidenceLevel}% Confidence Interval
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                    <Typography variant="subtitle2" color="secondary">
                      Mean Difference (d̄)
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.meanDifference.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'info.50' }}>
                    <Typography variant="subtitle2" color="info.main">
                      Standard Error
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.standardError.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>t-statistic:</strong> {result.tStatistic.toFixed(2)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Degrees of Freedom:</strong> {result.degreesOfFreedom}
                  </Typography>
                </Grid>
              </Grid>
              
              <Typography variant="subtitle2" gutterBottom>
                Method: {result.method}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle2" gutterBottom>
                Formula
              </Typography>
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'grey.200',
                  mb: 2,
                  '& .katex-display': {
                    margin: '0.5em 0'
                  }
                }}
                dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
              />
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle2" gutterBottom>
                Interpretation
              </Typography>
              <Typography variant="body2" sx={{ mt: 2 }}>
                {result.interpretation}
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<CopyIcon />}
                onClick={() => copyToClipboard(generateReport())}
                sx={{ mt: 2 }}
              >
                Copy Results
              </Button>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Paired Difference Confidence Interval Guide
            </Typography>
            
            <Typography variant="body1" paragraph>
              A paired difference confidence interval estimates the true mean difference when the same subjects 
              are measured twice or when subjects are matched in pairs.
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              When to Use:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• Before/after studies (e.g., weight before and after treatment)</li>
              <li>• Matched pairs design (e.g., twins, siblings)</li>
              <li>• Repeated measures on the same subjects</li>
              <li>• When you want to control for individual differences</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              How to Calculate Differences:
            </Typography>
            <Typography variant="body2" component="ol" sx={{ pl: 2 }}>
              <li>1. For each pair, calculate: difference = measurement1 - measurement2</li>
              <li>2. Calculate the mean of all differences (d̄)</li>
              <li>3. Calculate the standard deviation of the differences (sd)</li>
              <li>4. Use these values in the calculator</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Interpretation:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• If the interval contains 0: No significant difference between paired measurements</li>
              <li>• If the interval is entirely positive: Significant increase from first to second measurement</li>
              <li>• If the interval is entirely negative: Significant decrease from first to second measurement</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Assumptions:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• The differences should be approximately normally distributed</li>
              <li>• Pairs should be independent of each other</li>
              <li>• Each pair should be measured under similar conditions</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Advantages of Paired Design:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• Controls for individual differences between subjects</li>
              <li>• Often requires smaller sample sizes than independent groups</li>
              <li>• More powerful for detecting differences when they exist</li>
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>
    </Box>
  );
};

export default PairedDifferenceCI;