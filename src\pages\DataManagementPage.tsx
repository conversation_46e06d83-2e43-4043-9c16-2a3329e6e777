import React from 'react';
import { Box, Container } from '@mui/material';
import DataManagementOptions from '../components/DataManagement/DataManagementOptions'; // Import DataManagementOptions
import useSocialMeta from '../hooks/useSocialMeta';
import SocialShareWidget from '../components/UI/SocialShareWidget';

interface DataManagementPageProps {
  onNavigate: (path: string) => void; // Add onNavigate prop
}

const DataManagementPage: React.FC<DataManagementPageProps> = ({ onNavigate }) => {
  // Initialize social meta for data management page
  useSocialMeta();
  
  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <DataManagementOptions onNavigate={onNavigate} /> {/* Render DataManagementOptions */}
      </Box>
      
      {/* Social Share Widget */}
      <SocialShareWidget 
        variant="floating"
        position="bottom-right"
        platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
      />
    </Container>
  );
};

export default DataManagementPage;
