import React, { useState } from 'react';
import {
  Box,
  Fab,
  Tooltip,
  Collapse,
  Paper,
  Typography,
  IconButton,
  useTheme,
  alpha,
  Zoom,
  Slide
} from '@mui/material';
import {
  Share as ShareIcon,
  Close as CloseIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import SocialShare from './SocialShare';
import useSocialMeta, { SocialMetaOptions } from '../../hooks/useSocialMeta';

interface SocialShareWidgetProps {
  variant?: 'floating' | 'inline' | 'card';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  customMeta?: SocialMetaOptions;
  title?: string;
  description?: string;
  showTitle?: boolean;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  platforms?: Array<'facebook' | 'twitter' | 'linkedin' | 'email' | 'whatsapp' | 'telegram' | 'reddit' | 'copy'>;
}

const SocialShareWidget: React.FC<SocialShareWidgetProps> = ({
  variant = 'floating',
  position = 'bottom-right',
  customMeta,
  title,
  description,
  showTitle = true,
  collapsible = false,
  defaultExpanded = false,
  platforms = ['facebook', 'twitter', 'linkedin', 'email', 'copy']
}) => {
  const theme = useTheme();
  const { getCurrentMeta } = useSocialMeta(customMeta);
  const [expanded, setExpanded] = useState(defaultExpanded);
  const [visible, setVisible] = useState(false);

  const currentMeta = getCurrentMeta();
  const shareTitle = title || currentMeta.title || 'DataStatPro';
  const shareDescription = description || currentMeta.description || 'Free Statistical Software for Research & Education';

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: theme.zIndex.speedDial,
    };

    switch (position) {
      case 'bottom-right':
        return { ...baseStyles, bottom: 24, right: 24 };
      case 'bottom-left':
        return { ...baseStyles, bottom: 24, left: 24 };
      case 'top-right':
        return { ...baseStyles, top: 24, right: 24 };
      case 'top-left':
        return { ...baseStyles, top: 24, left: 24 };
      default:
        return { ...baseStyles, bottom: 24, right: 24 };
    }
  };

  const handleToggle = () => {
    if (variant === 'floating') {
      setVisible(!visible);
    } else {
      setExpanded(!expanded);
    }
  };

  if (variant === 'floating') {
    return (
      <Box sx={getPositionStyles()}>
        {/* Main FAB */}
        <Tooltip title={visible ? 'Close sharing options' : 'Share this page'}>
          <Fab
            color="primary"
            onClick={handleToggle}
            sx={{
              transition: 'all 0.3s ease',
              transform: visible ? 'rotate(45deg)' : 'rotate(0deg)',
            }}
          >
            {visible ? <CloseIcon /> : <ShareIcon />}
          </Fab>
        </Tooltip>

        {/* Sharing options */}
        <Collapse in={visible} timeout={300}>
          <Paper
            elevation={8}
            sx={{
              position: 'absolute',
              bottom: 70,
              right: 0,
              p: 2,
              minWidth: 200,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.divider, 0.2)}`
            }}
          >
            {showTitle && (
              <Typography
                variant="subtitle2"
                sx={{
                  mb: 1.5,
                  fontWeight: 600,
                  color: theme.palette.text.primary
                }}
              >
                Share this page
              </Typography>
            )}
            
            <SocialShare
              variant="compact"
              title={shareTitle}
              description={shareDescription}
              url={currentMeta.url}
              platforms={platforms}
              size="medium"
            />
          </Paper>
        </Collapse>
      </Box>
    );
  }

  if (variant === 'card') {
    return (
      <Paper
        elevation={2}
        sx={{
          p: 2,
          borderRadius: 2,
          background: alpha(theme.palette.background.paper, 0.8),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: collapsible ? 1 : 2 }}>
          {showTitle && (
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: theme.palette.text.primary,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <ShareIcon color="primary" />
              Share
            </Typography>
          )}
          
          {collapsible && (
            <IconButton onClick={handleToggle} size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>

        <Collapse in={!collapsible || expanded}>
          <Box sx={{ mt: showTitle ? 1 : 0 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 2 }}
            >
              Share this page with others
            </Typography>
            
            <SocialShare
              variant="buttons"
              title={shareTitle}
              description={shareDescription}
              url={currentMeta.url}
              platforms={platforms}
              size="small"
              showLabel={true}
            />
          </Box>
        </Collapse>
      </Paper>
    );
  }

  // Inline variant
  return (
    <Box
      sx={{
        p: 2,
        borderRadius: 1,
        backgroundColor: alpha(theme.palette.background.default, 0.5),
        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: collapsible ? 1 : 0 }}>
        {showTitle && (
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              color: theme.palette.text.primary,
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <ShareIcon fontSize="small" color="primary" />
            Share this page
          </Typography>
        )}
        
        {collapsible && (
          <IconButton onClick={handleToggle} size="small">
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        )}
      </Box>

      <Collapse in={!collapsible || expanded}>
        <Box sx={{ mt: showTitle ? 1.5 : 0 }}>
          <SocialShare
            variant="compact"
            title={shareTitle}
            description={shareDescription}
            url={currentMeta.url}
            platforms={platforms}
            size="medium"
          />
        </Box>
      </Collapse>
    </Box>
  );
};

export default SocialShareWidget;