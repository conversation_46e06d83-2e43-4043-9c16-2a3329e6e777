import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Typography,
  Box,
  Chip,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Alert,
  SelectChangeEvent
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { 
  Column, 
  CompositeScoreConfig, 
  CategoryMapping,
  DataType 
} from '../../types';
import { extractUniqueCategories } from '../../utils/dataUtilities';

interface CompositeScoreDialogProps {
  open: boolean;
  onClose: () => void;
  onApply: (config: CompositeScoreConfig) => void;
  columns: Column[];
  data: any[];
}

const CompositeScoreDialog: React.FC<CompositeScoreDialogProps> = ({
  open,
  onClose,
  onApply,
  columns,
  data
}) => {
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);
  const [categoryMappings, setCategoryMappings] = useState<CategoryMapping[]>([]);
  const [aggregationMethod, setAggregationMethod] = useState<'sum' | 'mean' | 'count' | 'std' | 'min' | 'max'>('sum');
  const [newVariableName, setNewVariableName] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  // Get available categorical and ordinal columns
  const availableColumns = columns.filter(col => 
    col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL
  );

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedVariables([]);
      setCategoryMappings([]);
      setAggregationMethod('sum');
      setNewVariableName('');
      setErrors([]);
    }
  }, [open]);

  // Update category mappings when selected variables change
  useEffect(() => {
    const newMappings: CategoryMapping[] = [];
    
    selectedVariables.forEach(variableName => {
      const column = columns.find(col => col.name === variableName);
      if (column) {
        const uniqueCategories = extractUniqueCategories(data, [variableName]);
        const existingMapping = categoryMappings.find(m => m.variableName === variableName);
        
        if (existingMapping) {
          // Update existing mapping with new categories if any
          const updatedCategoryValues = { ...existingMapping.categoryValues };
          uniqueCategories.forEach(category => {
            if (!(category in updatedCategoryValues)) {
              updatedCategoryValues[category] = 0;
            }
          });
          newMappings.push({
            ...existingMapping,
            categoryValues: updatedCategoryValues
          });
        } else {
          // Create new mapping
          const categoryValues: Record<string, number> = {};
          uniqueCategories.forEach(category => {
            categoryValues[category] = 0;
          });
          newMappings.push({
            variableName,
            categoryValues
          });
        }
      }
    });
    
    setCategoryMappings(newMappings);
  }, [selectedVariables, columns, data]);

  const handleVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? value.split(',') : value);
  };

  const handleCategoryValueChange = (variableName: string, category: string, value: string) => {
    const numericValue = parseFloat(value) || 0;
    setCategoryMappings(prev => 
      prev.map(mapping => 
        mapping.variableName === variableName
          ? {
              ...mapping,
              categoryValues: {
                ...mapping.categoryValues,
                [category]: numericValue
              }
            }
          : mapping
      )
    );
  };

  const validateForm = (): string[] => {
    const validationErrors: string[] = [];

    if (selectedVariables.length === 0) {
      validationErrors.push('Please select at least one variable.');
    }

    if (!newVariableName.trim()) {
      validationErrors.push('Please enter a name for the new variable.');
    }

    // Check if variable name already exists
    if (newVariableName.trim() && columns.some(col => col.name === newVariableName.trim())) {
      validationErrors.push('A variable with this name already exists.');
    }

    // Check if all categories have numeric values assigned
    categoryMappings.forEach(mapping => {
      const hasUnassignedCategories = Object.values(mapping.categoryValues).some(value => 
        value === null || value === undefined || isNaN(value)
      );
      if (hasUnassignedCategories) {
        validationErrors.push(`Please assign numeric values to all categories in ${mapping.variableName}.`);
      }
    });

    return validationErrors;
  };

  const handleApply = () => {
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    const config: CompositeScoreConfig = {
      selectedVariables,
      categoryMappings,
      aggregationMethod,
      newVariableName: newVariableName.trim()
    };

    onApply(config);
    onClose();
  };

  const handleClose = () => {
    setErrors([]);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Create Composite Score</DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Variable Selection */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Select Variables</InputLabel>
                <Select
                  multiple
                  value={selectedVariables}
                  onChange={handleVariableChange}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {availableColumns.map((column) => (
                    <MenuItem key={column.name} value={column.name}>
                      {column.name} ({column.type})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Category Mappings */}
            {categoryMappings.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Category Mappings
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Assign numeric values to each category. These values will be used to calculate the composite score.
                </Typography>
                
                {categoryMappings.map((mapping) => (
                  <Paper key={mapping.variableName} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      {mapping.variableName}
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Category</TableCell>
                            <TableCell>Numeric Value</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {Object.entries(mapping.categoryValues).map(([category, value]) => (
                            <TableRow key={category}>
                              <TableCell>{category}</TableCell>
                              <TableCell>
                                <TextField
                                  type="number"
                                  value={value}
                                  onChange={(e) => handleCategoryValueChange(
                                    mapping.variableName, 
                                    category, 
                                    e.target.value
                                  )}
                                  size="small"
                                  fullWidth
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Paper>
                ))}
              </Grid>
            )}

            {/* Aggregation Method */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Aggregation Method</InputLabel>
                <Select
                  value={aggregationMethod}
                  onChange={(e) => setAggregationMethod(e.target.value as any)}
                >
                  <MenuItem value="sum">Sum</MenuItem>
                  <MenuItem value="mean">Mean</MenuItem>
                  <MenuItem value="count">Count</MenuItem>
                  <MenuItem value="std">Standard Deviation</MenuItem>
                  <MenuItem value="min">Minimum</MenuItem>
                  <MenuItem value="max">Maximum</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* New Variable Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="New Variable Name"
                value={newVariableName}
                onChange={(e) => setNewVariableName(e.target.value)}
                placeholder="Enter name for composite score"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleApply} variant="contained">
          Create Composite Score
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CompositeScoreDialog;