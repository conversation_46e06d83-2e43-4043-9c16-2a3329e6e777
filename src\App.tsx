import React, { useState, useEffect, ReactNode, Suspense, lazy, useCallback } from 'react';
import { useAuth } from './context/AuthContext';
import { useData } from './context/DataContext';
import { Box, Snackbar, Alert, useMediaQuery, useTheme, CircularProgress, Button, Typography } from '@mui/material';
import { UpdateNotification } from './components/PWA'; // Import enhanced PWA components
import AppHeader from './components/Layout/AppHeader';
import AuthAppHeader from './components/Layout/AuthAppHeader';
import Sidebar from './components/Layout/Sidebar';
import MainContent from './components/Layout/MainContent';
import { Routes, Route, useNavigate, Navigate, useLocation } from 'react-router-dom';
import { initializeRoutes, routeRegistry } from './routing/routeConfig';
import { useTheme as useThemeContext } from './context/ThemeContext';
import { useResults } from './context/ResultsContext';
import { useSocialMeta } from './hooks/useSocialMeta';
import { useNotifications } from './hooks/useNotifications';
import { useDataLoadEvent } from './hooks/useDataLoadEvent';
import { useHashRedirect } from './hooks/useHashRedirect';
import { useRouteInitialization } from './hooks/useRouteInitialization';
import { useResponsiveLayout } from './hooks/useResponsiveLayout';
import { useAuthenticationSuccess } from './hooks/useAuthenticationSuccess';
import { useDatasetConflictDialog } from './hooks/useDatasetConflictDialog';
import { useDevTools } from './hooks/useDevTools';
import { useUpdateNotification } from './hooks/useUpdateNotification';
import { useDataLoadNotification } from './hooks/useDataLoadNotification';
import { useAuthView } from './hooks/useAuthView';
import { useNavigationHandlers } from './hooks/useNavigationHandlers';
import { useNotificationHandlers } from './hooks/useNotificationHandlers';
import { useDataLoadEventHandlers } from './hooks/useDataLoadEventHandlers';
import ResponsiveTester from './components/DevTools/ResponsiveTester';
import DatasetNameConflictDialog from './components/DataManagement/DatasetNameConflictDialog';
import { useDatasetNameConflict } from './hooks/useDatasetNameConflict';
import { AuthView } from './components/Auth/AuthContainer';


// Determine if we should show dev tools
const isDevelopment = process.env.NODE_ENV === 'development';

// AuthView is now imported from AuthContainer.tsx

function App() {
  const { user, isGuest, logoutGuest, loading: authLoading, showSignupSuccessMessage, clearSignupSuccessMessage } = useAuth();
  const [open, setOpen] = useState(true);
  const [routesInitialized, setRoutesInitialized] = useState(false);
  // PWA update system is now handled by the UpdateNotification component
    const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
    actions?: ReactNode;
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const [loading, setLoading] = useState(false);
  const { dataLoadEventInfo, clearDataLoadEvent, currentDataset } = useData();

  // Initialize routes before any navigation logic
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const drawerWidth = isMobile ? 260 : isTablet ? 280 : 300;


  // Auto-close sidebar on mobile
  useEffect(() => {
    if (isMobile) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [isMobile]);

  const navigate = useNavigate();
  const location = useLocation();
  
  // Enhanced hash redirect logic for backward compatibility
  useHashRedirect();
  
  const navigateToPage = React.useCallback((path: string) => {
    // Ensure all internal app navigation maintains the /app prefix
    let cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    // If the path doesn't start with /app, prepend it
    if (!cleanPath.startsWith('/app/')) {
      cleanPath = `/app${cleanPath}`;
    }
    
    navigate(cleanPath);
  }, [navigate]);

  // Memoized auth success callback
  const handleAuthSuccess = useCallback(() => {
    navigateToPage('dashboard');
  }, [navigateToPage]);

  // Show notification (memoized with useCallback)
  const showNotification = useCallback((
    message: string,
    severity: 'success' | 'info' | 'warning' | 'error' = 'info',
    actions?: ReactNode,
    autoHideDuration = 6000
  ) => {
    setNotification({
      open: true,
      message,
      severity,
      actions
    });
  }, []); // No dependencies, as setNotification is a stable setter

  // Handle notification close
  const handleNotificationClose = React.useCallback((event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setNotification(prev => ({ ...prev, open: false }));
    // Always attempt to clear the data load event when the notification related to it is closed.
    // clearDataLoadEvent itself should handle if there's nothing to clear.
    clearDataLoadEvent();
  }, [clearDataLoadEvent]); // Dependency only on clearDataLoadEvent

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  const handleMenuItemClick = (path: string) => {
    navigateToPage(path);
  };

  useEffect(() => {
    initializeRoutes();
    setRoutesInitialized(true);
  }, []);

  // Note: Authentication and route access control is now handled by AppRouter and route guards

  // Effect to show signup success message
  useEffect(() => {
    if (showSignupSuccessMessage) {
      showNotification(
        "You have successfully confirmed your registration! Enjoy using DataStatPro.",
        "success"
      );
      clearSignupSuccessMessage(); // Clear the flag after showing the message
    }
  }, [showSignupSuccessMessage, showNotification, clearSignupSuccessMessage]);

  // Effect to handle data load notifications
  useEffect(() => {
    if (dataLoadEventInfo) {
      const actions = (
        <Box sx={{ mt: 1 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              navigateToPage('data-management/editor'); // Assuming 'data-management/editor' shows the current dataset
              handleNotificationClose(); // This will also call clearDataLoadEvent
            }}
            sx={{ mr: 1, color: 'common.white', borderColor: 'common.white' }}
          >
            View/Edit Data
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              navigateToPage('analysis-index'); // Changed from 'assistant' to 'stats'
              handleNotificationClose(); // This will also call clearDataLoadEvent
            }}
            sx={{ color: 'common.white', borderColor: 'common.white' }}
          >
            Start Analysis
          </Button>
        </Box>
      );
      showNotification(
        `Data '${dataLoadEventInfo.datasetName}' loaded successfully!`,
        'success',
        actions // Remove the hardcoded duration argument
      );
    }
  }, [dataLoadEventInfo, navigateToPage, handleNotificationClose]);

  const renderRoutes = (routes: any[]) => {
    return routes.map((route, index) => {
      const RouteComponent = route.component;
      
      // Routes that need the onNavigate prop based on path
      const routesNeedingOnNavigate = [
        'dashboard', 
        'home', 
        'analysis-index',
        'data-management',
        'stats',
        'inferential-stats',
        'correlation-analysis',
        'advanced-analysis',
        'publication-ready',
        'charts',
        'epicalc',
        'samplesize',
        'knowledge-base',
        'ci-calculators',
        'assistant',
        'analysis-assistant'
      ];
      const shouldPassOnNavigate = routesNeedingOnNavigate.some(path => route.path && route.path.startsWith(path));
       
       const props = {
         ...(route.props || {}),
         ...(shouldPassOnNavigate ? { onNavigate: navigateToPage } : {})
       };
       
       return (
         <Route
           key={route.path || index}
           path={route.path}
           element={RouteComponent ? <RouteComponent {...props} /> : null}
         >
           {route.children && renderRoutes(route.children)}
         </Route>
       );
     });
   };

  const pathname = location.pathname;
  const parts = pathname.split('/').filter(Boolean);
  const activePage = parts[0] || 'home';
  const activeSubPage = parts[1] || '';
  const renderContent = () => (
    <Suspense fallback={<CircularProgress />}>
      <Routes>
        {renderRoutes(routeRegistry.getAllRoutes())}
        <Route path="*" element={<Navigate to="dashboard" replace />} />
      </Routes>
    </Suspense>
  );
        

  // Show loading screen while authentication is being restored or routes are being initialized
  if (authLoading || !routesInitialized) {
    const loadingMessage = authLoading
      ? 'Restoring session...'
      : !routesInitialized
        ? 'Initializing routes...'
        : 'Loading DataStatPro...';

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: 'background.default'
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          {loadingMessage}
        </Typography>
      </Box>
    );
  }

   return (
     <>
       {/* Render AppHeader outside the main flex container */}
       {user ? (
           <AuthAppHeader
             title="DataStatPro"
             onMenuClick={handleDrawerToggle}
             onNavigate={navigateToPage} // Pass the new navigation handler
           />
         ) : (
           <AppHeader
             title="DataStatPro"
             onMenuClick={handleDrawerToggle}
             onNavigate={navigateToPage}
             isGuest={isGuest} // Pass isGuest state
             logoutGuest={logoutGuest} // Pass logoutGuest function
          />
         )}
         {/* Create a new flex container specifically for Sidebar and MainContent */}
         <Box sx={{ position: 'relative', height: 'calc(100vh - 64px)' /* Adjust height for header */ }}>
         <Sidebar
            open={open}
            drawerWidth={drawerWidth}
            onClose={handleDrawerToggle}
            onMenuItemClick={handleMenuItemClick}
            activePage={activePage}
            activeSubPage={activeSubPage}
            isGuest={isGuest} // Pass guest status to Sidebar
          />
          <MainContent drawerWidth={drawerWidth} open={open}>
            <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%' }}><CircularProgress /></Box>}>
              {renderContent()}
            </Suspense>
          </MainContent>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={notification.actions ? 3000 : 6000} // 3 seconds for data load msg (has actions), 6 seconds otherwise
          onClose={handleNotificationClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleNotificationClose}
            severity={notification.severity}
            elevation={6}
            variant="filled"
            sx={{ width: '100%' }} // Ensure alert takes full width of snackbar
          >
            {notification.message}
            {notification.actions && <Box sx={{ pt: 1 }}>{notification.actions}</Box>}
          </Alert>
        </Snackbar>

        {/* Enhanced PWA Update Notification */}
        <UpdateNotification anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} />

        {/* Responsive Design Tester (only shown in development) */}
        {isDevelopment && <ResponsiveTester />}

        {/* Dataset Name Conflict Dialog (rendered globally) */}
        <DatasetNameConflictDialog />
        </Box>
    </>
  );
}

export default App;
