# DataStatPro Hostinger Configuration
# This .htaccess file configures the server for optimal PWA and SPA functionality

# Enable HTTPS redirect (if not already handled by <PERSON><PERSON>)
RewriteEngine On

# Remove www subdomain to prevent blank page issues
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [R=301,L]

# Social Media Crawler Detection and URL Rewriting
# Detect major social media crawlers and serve proper meta tags

# Handle knowledge base tutorials specifically for crawlers (must come first)
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/knowledge-base/(.+)$
RewriteRule ^app/knowledge-base/(.+)$ /social-meta.php?tutorial=$1 [L,QSA]

# Handle main knowledge-base page for crawlers
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/knowledge-base/?$
RewriteRule ^app/knowledge-base/?$ /social-meta.php?path=knowledge-base [L,QSA]

# Handle other app routes for crawlers
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/(.*)$
RewriteRule ^app/(.*)$ /social-meta.php?path=$1 [L,QSA]

RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers for PWA
<IfModule mod_headers.c>
    # Content Security Policy - Allow Supabase and required resources
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://ghzibvkqmdlpyaidfbah.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: blob: https:; connect-src 'self' https://ghzibvkqmdlpyaidfbah.supabase.co wss://ghzibvkqmdlpyaidfbah.supabase.co; worker-src 'self' blob:; manifest-src 'self';"
    
    # X-Frame-Options
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # X-Content-Type-Options
    Header always set X-Content-Type-Options "nosniff"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # CORS headers for API requests
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# MIME Types for PWA files
<IfModule mod_mime.c>
    # Web App Manifest - Critical for PWA functionality
    AddType application/manifest+json .webmanifest
    AddType application/manifest+json manifest.json
    AddType application/json .json

    # Service Worker - Critical for PWA functionality
    AddType application/javascript .js
    AddType text/javascript .js
    AddType application/javascript sw.js
    AddType application/javascript workbox-*.js

    # Web Fonts
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2

    # Images
    AddType image/webp .webp
    AddType image/svg+xml .svg
</IfModule>

# Cache Control for PWA assets
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Service Worker - No cache (always fresh)
    <FilesMatch "\.(js)$">
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # Manifest - Short cache
    <FilesMatch "\.(webmanifest|json)$">
        ExpiresDefault "access plus 1 hour"
    </FilesMatch>
    
    # Static assets - Long cache
    <FilesMatch "\.(css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2)$">
        ExpiresDefault "access plus 1 month"
    </FilesMatch>
    
    # HTML - No cache for SPA routing
    <FilesMatch "\.(html|htm)$">
        ExpiresDefault "access plus 0 seconds"
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </FilesMatch>
</IfModule>

# SPA Routing - Redirect all requests to index.html
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Angular and React Router
    # Skip real files and directories
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # Skip API endpoints and service worker
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteCond %{REQUEST_URI} !^/sw\.js
    RewriteCond %{REQUEST_URI} !^/workbox-
    RewriteCond %{REQUEST_URI} !^/manifest\.json
    RewriteCond %{REQUEST_URI} !^/robots\.txt
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml
    
    # Skip asset files
    RewriteCond %{REQUEST_URI} !\.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot|json|xml|txt)$
    
    # Redirect to index.html
    RewriteRule . /index.html [L]
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|htaccess|htpasswd)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Service Worker specific headers
<FilesMatch "sw\.js$">
    Header set Service-Worker-Allowed "/"
    Header set Cache-Control "no-cache, no-store, must-revalidate"
</FilesMatch>

# Manifest file headers
<FilesMatch "manifest\.json$">
    Header set Content-Type "application/manifest+json"
</FilesMatch>
