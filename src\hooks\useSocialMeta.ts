import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface SocialMetaOptions {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image';
  keywords?: string[];
}

interface PageMetaConfig {
  [key: string]: SocialMetaOptions;
}

// Path-based route mappings for social sharing (updated for clean URLs)
const pathRouteMapping: { [key: string]: string } = {
  '/home': '/',
  '/analysis-index': '/dashboard',
  '/assistant': '/assistant',
  '/data-management': '/data-management',
  '/stats': '/stats',
  '/inferential-stats': '/inferential-stats',
  '/correlation-analysis': '/correlation-analysis',
  '/advanced-analysis': '/advanced-analysis',
  '/publication-ready': '/publication-ready',
  '/charts': '/charts',
  '/samplesize': '/samplesize',
  '/epicalc': '/epicalc',
  '/knowledge-base': '/knowledge-base',
  // Tutorial-specific routes
  '/knowledge-base/logistic-regression': '/knowledge-base/logistic-regression',
  '/knowledge-base/t-tests-and-alternatives': '/knowledge-base/t-tests-and-alternatives',
  '/knowledge-base/anova-tests-and-alternatives': '/knowledge-base/anova-tests-and-alternatives',
  '/knowledge-base/correlation-linear-regression': '/knowledge-base/correlation-linear-regression',
  '/knowledge-base/sample-size-power-analysis': '/knowledge-base/sample-size-power-analysis',
  '/knowledge-base/numerical-descriptives': '/knowledge-base/numerical-descriptives',
  '/knowledge-base/categorical-descriptives': '/knowledge-base/categorical-descriptives',
  '/knowledge-base/epidemiological-calculators': '/knowledge-base/epidemiological-calculators',
  '/knowledge-base/exploratory-factor-analysis': '/knowledge-base/exploratory-factor-analysis',
  '/knowledge-base/confirmatory-factor-analysis': '/knowledge-base/confirmatory-factor-analysis',
  '/knowledge-base/survival-analysis': '/knowledge-base/survival-analysis',
  '/knowledge-base/reliability-analysis': '/knowledge-base/reliability-analysis',
  '/knowledge-base/mediation-moderation': '/knowledge-base/mediation-moderation',
  '/knowledge-base/cluster-analysis': '/knowledge-base/cluster-analysis',
  '/knowledge-base/meta-analysis': '/knowledge-base/meta-analysis',
  '/knowledge-base/variable-tree-analysis': '/knowledge-base/variable-tree-analysis',
  '/knowledge-base/data-visualization': '/knowledge-base/data-visualization',
  '/knowledge-base/correlation-regression': '/knowledge-base/correlation-regression'
};

// Legacy hash-based route mappings for backward compatibility
const legacyHashRouteMapping: { [key: string]: string } = {
  '#home': '/',
  '#analysis-index': '/dashboard',
  '#assistant': '/assistant',
  '#data-management': '/data-management',
  '#stats': '/stats',
  '#inferential-stats': '/inferential-stats',
  '#correlation-analysis': '/correlation-analysis',
  '#advanced-analysis': '/advanced-analysis',
  '#publication-ready': '/publication-ready',
  '#charts': '/charts',
  '#samplesize/options': '/samplesize',
  '#epicalc': '/epicalc',
  '#knowledge-base': '/knowledge-base',
  // Tutorial-specific legacy routes
  '#knowledge-base/logistic-regression': '/knowledge-base/logistic-regression',
  '#knowledge-base/t-tests-and-alternatives': '/knowledge-base/t-tests-and-alternatives',
  '#knowledge-base/anova-tests-and-alternatives': '/knowledge-base/anova-tests-and-alternatives',
  '#knowledge-base/correlation-linear-regression': '/knowledge-base/correlation-linear-regression',
  '#knowledge-base/sample-size-power-analysis': '/knowledge-base/sample-size-power-analysis',
  '#knowledge-base/numerical-descriptives': '/knowledge-base/numerical-descriptives',
  '#knowledge-base/categorical-descriptives': '/knowledge-base/categorical-descriptives',
  '#knowledge-base/epidemiological-calculators': '/knowledge-base/epidemiological-calculators',
  '#knowledge-base/exploratory-factor-analysis': '/knowledge-base/exploratory-factor-analysis',
  '#knowledge-base/confirmatory-factor-analysis': '/knowledge-base/confirmatory-factor-analysis',
  '#knowledge-base/survival-analysis': '/knowledge-base/survival-analysis',
  '#knowledge-base/reliability-analysis': '/knowledge-base/reliability-analysis',
  '#knowledge-base/mediation-moderation': '/knowledge-base/mediation-moderation',
  '#knowledge-base/cluster-analysis': '/knowledge-base/cluster-analysis',
  '#knowledge-base/meta-analysis': '/knowledge-base/meta-analysis',
  '#knowledge-base/variable-tree-analysis': '/knowledge-base/variable-tree-analysis',
  '#knowledge-base/data-visualization': '/knowledge-base/data-visualization',
  '#knowledge-base/correlation-regression': '/knowledge-base/correlation-regression'
};

// Default meta configuration for different pages
const defaultPageMeta: PageMetaConfig = {
  '/': {
    title: 'DataStatPro - Free Statistical Software for Research & Education',
    description: 'Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software with AI-powered analysis assistant.',
    keywords: ['statistical software', 'data analysis', 'research tools', 'SPSS alternative', 'free statistics']
  },
  '/dashboard': {
    title: 'Analysis Dashboard - DataStatPro',
    description: 'Access comprehensive statistical analysis tools and data visualization features. Start your research with our powerful analytics dashboard.',
    keywords: ['statistical dashboard', 'data analysis', 'research tools', 'analytics platform']
  },
  '/data-management': {
    title: 'Data Management Tools - DataStatPro',
    description: 'Import, clean, transform, and manage your research data with powerful data management tools. Prepare your data for analysis.',
    keywords: ['data management', 'data cleaning', 'data import', 'data transformation']
  },
  '/stats/descriptives': {
    title: 'Descriptive Statistics - DataStatPro',
    description: 'Calculate means, medians, standard deviations, and other descriptive statistics for your data.',
    keywords: ['descriptive statistics', 'mean', 'median', 'standard deviation', 'data summary']
  },
  '/inference/ttest': {
    title: 'T-Tests - DataStatPro',
    description: 'Perform one-sample, independent samples, and paired t-tests with detailed statistical output.',
    keywords: ['t-test', 'statistical inference', 'hypothesis testing', 'parametric tests']
  },
  '/inference/anova': {
    title: 'ANOVA - DataStatPro',
    description: 'Conduct one-way, two-way, and repeated measures ANOVA with post-hoc tests.',
    keywords: ['ANOVA', 'analysis of variance', 'statistical testing', 'group comparisons']
  },
  '/charts': {
    title: 'Data Visualization & Charts - DataStatPro',
    description: 'Create professional charts, graphs, and visualizations for your data. Interactive plotting tools for research and presentations.',
    keywords: ['data visualization', 'charts', 'graphs', 'plotting tools', 'data graphics']
  },
  '/data-visualization': {
    title: 'Data Visualization - DataStatPro',
    description: 'Create beautiful, publication-ready charts and graphs for your research data.',
    keywords: ['data visualization', 'charts', 'graphs', 'plotting', 'publication ready']
  },
  '/correlation': {
    title: 'Correlation Analysis - DataStatPro',
    description: 'Analyze relationships between variables with correlation matrices and regression analysis.',
    keywords: ['correlation', 'regression', 'relationship analysis', 'statistical modeling']
  },
  '/advanced': {
    title: 'Advanced Analysis - DataStatPro',
    description: 'Access advanced statistical methods including factor analysis, survival analysis, and more.',
    keywords: ['advanced statistics', 'factor analysis', 'survival analysis', 'multivariate analysis']
  },
  '/advanced-analysis': {
    title: 'Advanced Statistical Analysis - DataStatPro',
    description: 'Perform sophisticated statistical analyses including multivariate analysis, time series, and advanced modeling techniques.',
    keywords: ['advanced statistics', 'multivariate analysis', 'statistical modeling', 'complex analysis']
  },
  '/publication-ready': {
    title: 'Publication-Ready Results - DataStatPro',
    description: 'Generate publication-quality tables, figures, and reports for your research papers and presentations.',
    keywords: ['publication ready', 'research reports', 'academic publishing', 'statistical tables']
  },
  '/epicalc': {
    title: 'Epidemiological Calculators - DataStatPro',
    description: 'Calculate epidemiological measures including odds ratios, relative risks, confidence intervals, and disease prevalence.',
    keywords: ['epidemiology', 'odds ratio', 'relative risk', 'disease prevalence', 'public health']
  },
  '/sample-size': {
    title: 'Sample Size Calculators - DataStatPro',
    description: 'Determine appropriate sample sizes for your research studies and statistical tests.',
    keywords: ['sample size', 'power analysis', 'study design', 'statistical power']
  },
  '/samplesize': {
    title: 'Sample Size Calculators - DataStatPro',
    description: 'Calculate required sample sizes for research studies. Free online tools for one-sample, two-sample, paired-sample, and multi-group study designs.',
    keywords: ['sample size calculator', 'power analysis', 'study design', 'research planning', 'statistical power', 'effect size']
  },
  '/video-tutorials': {
    title: 'Video Tutorials - DataStatPro',
    description: 'Learn statistical analysis with our comprehensive video tutorial library.',
    keywords: ['tutorials', 'statistical education', 'learning', 'video guides']
  },
  '/assistant': {
    title: 'AI Statistical Assistant - DataStatPro',
    description: 'Get AI-powered help with statistical analysis, research design, and data interpretation. Your intelligent research companion.',
    keywords: ['AI assistant', 'statistical help', 'research guidance', 'data analysis support']
  },
  '/statistical-methods': {
    title: 'Statistical Methods Guide - DataStatPro',
    description: 'Comprehensive guide to statistical methods including t-tests, ANOVA, correlation, regression, and data visualization techniques.',
    keywords: ['statistical methods', 'statistical guide', 't-tests', 'ANOVA', 'correlation', 'regression', 'data analysis methods']
  },
  '/stats': {
    title: 'Descriptive Statistics - DataStatPro',
    description: 'Calculate descriptive statistics including means, medians, frequencies, cross-tabulations, and normality tests. Free online statistical analysis tools for researchers and students.',
    keywords: ['descriptive statistics', 'mean median mode', 'frequency tables', 'cross tabulation', 'normality test', 'statistical summary']
  },
  '/inferential-stats': {
    title: 'Inferential Statistics - DataStatPro',
    description: 'Perform hypothesis testing with t-tests, ANOVA, non-parametric tests, and statistical inference. Free online tools for research and statistical analysis.',
    keywords: ['inferential statistics', 'hypothesis testing', 't-test', 'ANOVA', 'non-parametric tests', 'statistical inference', 'p-values']
  },
  '/correlation-analysis': {
    title: 'Correlation Analysis - DataStatPro',
    description: 'Analyze relationships between variables with Pearson correlation, Spearman correlation, linear regression, and logistic regression. Free statistical correlation tools.',
    keywords: ['correlation analysis', 'Pearson correlation', 'Spearman correlation', 'linear regression', 'logistic regression', 'relationship analysis']
  },
  '/which-test': {
    title: 'Which Test Should I Use? - DataStatPro',
    description: 'Get personalized recommendations for the best statistical test for your research question.',
    keywords: ['statistical test selection', 'research guidance', 'test recommendations']
  },
  // Tutorial-specific meta configurations
  '/knowledge-base/logistic-regression': {
    title: 'Logistic Regression Tutorial | DataStatPro Knowledge Base',
    description: 'A comprehensive guide covering the basics of logistic regression, how to use the component, computational details, formula explanations, and interpretation of results.',
    keywords: ['logistic regression', 'statistics', 'data analysis', 'tutorial', 'regression analysis', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/t-tests-and-alternatives': {
    title: 'T-Tests and Alternatives | DataStatPro Knowledge Base',
    description: 'Detailed coverage of one-sample, independent samples, and paired t-tests, plus non-parametric alternatives including Mann-Whitney U, Wilcoxon signed-rank, and Sign tests.',
    keywords: ['t-tests', 'statistics', 'inferential statistics', 'Mann-Whitney U', 'Wilcoxon', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/anova-tests-and-alternatives': {
    title: 'ANOVA Tests and Alternatives | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to Analysis of Variance (ANOVA) tests and their non-parametric alternatives for comparing multiple groups.',
    keywords: ['ANOVA', 'analysis of variance', 'statistics', 'inferential statistics', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/correlation-linear-regression': {
    title: 'Correlation and Linear Regression | DataStatPro Knowledge Base',
    description: 'Explore relationships between variables using correlation analysis and linear regression modeling techniques.',
    keywords: ['correlation', 'linear regression', 'statistics', 'regression analysis', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/sample-size-power-analysis': {
    title: 'Sample Size and Power Analysis | DataStatPro Knowledge Base',
    description: 'Determine appropriate sample sizes and conduct power analysis for your research studies using statistical calculations.',
    keywords: ['sample size', 'power analysis', 'study design', 'statistics', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/numerical-descriptives': {
    title: 'Numerical Descriptives and Distributions | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to descriptive statistics for numerical data including measures of central tendency, variability, and distribution shape.',
    keywords: ['descriptive statistics', 'numerical data', 'central tendency', 'variability', 'distribution', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/categorical-descriptives': {
    title: 'Categorical Descriptives and Association | DataStatPro Knowledge Base',
    description: 'Complete guide to categorical data analysis including frequency tables, cross-tabulations, chi-square tests, and association measures.',
    keywords: ['categorical data', 'frequency tables', 'cross-tabulation', 'chi-square', 'association measures', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/epidemiological-calculators': {
    title: 'Epidemiological Calculators and Study Design | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to epidemiological methods including case-control studies, cohort studies, odds ratios, and relative risk calculations.',
    keywords: ['epidemiology', 'case-control', 'cohort study', 'odds ratio', 'relative risk', 'study design', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/exploratory-factor-analysis': {
    title: 'Exploratory Factor Analysis (EFA) | DataStatPro Knowledge Base',
    description: 'Complete guide to exploratory factor analysis including factor extraction methods, rotation techniques, and interpretation guidelines.',
    keywords: ['factor analysis', 'EFA', 'factor extraction', 'rotation', 'latent variables', 'multivariate analysis', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/confirmatory-factor-analysis': {
    title: 'Confirmatory Factor Analysis (CFA) | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to confirmatory factor analysis and measurement model validation using structural equation modeling.',
    keywords: ['confirmatory factor analysis', 'CFA', 'SEM', 'measurement model', 'model fit', 'reliability', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/survival-analysis': {
    title: 'Survival Analysis | DataStatPro Knowledge Base',
    description: 'Complete guide to time-to-event analysis including Kaplan-Meier estimation, log-rank tests, and Cox regression.',
    keywords: ['survival analysis', 'time-to-event', 'Kaplan-Meier', 'Cox regression', 'hazard ratio', 'censoring', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/reliability-analysis': {
    title: 'Reliability Analysis | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to measurement reliability including internal consistency, test-retest reliability, and inter-rater reliability.',
    keywords: ['reliability analysis', 'internal consistency', 'Cronbach alpha', 'test-retest', 'inter-rater reliability', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/mediation-moderation': {
    title: 'Mediation and Moderation Analysis | DataStatPro Knowledge Base',
    description: 'Complete guide to mediation and moderation analysis for understanding complex variable relationships in behavioral research.',
    keywords: ['mediation analysis', 'moderation analysis', 'conditional process', 'indirect effects', 'bootstrap', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/cluster-analysis': {
    title: 'Cluster Analysis | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to cluster analysis and unsupervised learning methods for pattern recognition and data segmentation.',
    keywords: ['cluster analysis', 'k-means', 'hierarchical clustering', 'unsupervised learning', 'pattern recognition', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/meta-analysis': {
    title: 'Meta-Analysis | DataStatPro Knowledge Base',
    description: 'Complete guide to meta-analysis and systematic review methods including effect size calculation and heterogeneity assessment.',
    keywords: ['meta-analysis', 'systematic review', 'effect size', 'heterogeneity', 'publication bias', 'forest plot', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/variable-tree-analysis': {
    title: 'Variable Tree Analysis | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to hierarchical variable relationship visualization and analysis for exploring complex data structures.',
    keywords: ['variable tree', 'hierarchical analysis', 'data visualization', 'variable relationships', 'tree analysis', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/data-visualization': {
    title: 'Data Visualization Methods | DataStatPro Knowledge Base',
    description: 'Complete guide to data visualization principles, techniques, and best practices for creating effective statistical graphics.',
    keywords: ['data visualization', 'statistical graphics', 'chart types', 'visualization principles', 'best practices', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/correlation-regression': {
    title: 'Correlation and Linear Regression Analysis | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to correlation analysis and linear regression including assumptions, interpretation, and model diagnostics.',
    keywords: ['correlation', 'linear regression', 'Pearson correlation', 'regression analysis', 'model diagnostics', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/confidence-interval-calculator': {
    title: 'Confidence Interval Calculator | DataStatPro Knowledge Base',
    description: 'Comprehensive guide to confidence interval calculations for means, proportions, differences, correlations, regression parameters, and diagnostic test accuracy measures.',
    keywords: ['confidence intervals', 'statistical inference', 'means', 'proportions', 'regression', 'diagnostic tests', 'DataStatPro'],
    type: 'article'
  },
  '/knowledge-base/effect-size-calculator': {
    title: 'Effect Size Calculator | DataStatPro Knowledge Base',
    description: 'Complete guide to effect size calculations including Cohen\'s d, Hedge\'s g, eta squared, Cramer\'s V, and interpretation guidelines for research studies.',
    keywords: ['effect size', 'Cohen\'s d', 'Hedge\'s g', 'eta squared', 'Cramer\'s V', 'statistical significance', 'DataStatPro'],
    type: 'article'
  }
};

const useSocialMeta = (customMeta?: SocialMetaOptions) => {
  const location = useLocation();

  useEffect(() => {
    // Get the current path - use pathname directly for clean URLs
    let currentPath = location.pathname;
    
    // Handle legacy hash-based URLs for backward compatibility
    if (location.hash && legacyHashRouteMapping[location.hash]) {
      currentPath = legacyHashRouteMapping[location.hash];
    }
    
    // Apply path mapping if needed (for route aliases)
    if (pathRouteMapping[currentPath]) {
      currentPath = pathRouteMapping[currentPath];
    }
    
    // Find matching page meta or use default
    const pageMeta = defaultPageMeta[currentPath] || defaultPageMeta['/'];
    
    // Determine base URLs for different purposes
    const getBaseUrls = () => {
      if (typeof window !== 'undefined') {
        const { protocol, hostname, port } = window.location;
        
        // In development, use the current origin
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          const devBase = `${protocol}//${hostname}${port ? `:${port}` : ''}`;
          return {
            canonical: `${devBase}/app`, // For actual navigation
            social: devBase // For social sharing (clean URLs)
          };
        }
        
        // In production
        return {
          canonical: 'https://www.datastatpro.com/app', // For actual navigation
          social: 'https://www.datastatpro.com' // For social sharing (clean URLs)
        };
      }
      
      // Fallback for SSR
      return {
        canonical: 'https://www.datastatpro.com/app',
        social: 'https://www.datastatpro.com'
      };
    };
    
    const { canonical: canonicalBase, social: socialBase } = getBaseUrls();
    
    // Generate URLs for different purposes
    let canonicalUrl, socialUrl;
    
    if (location.pathname && pathRouteMapping[location.pathname]) {
      // For path-based routes, use the mapped clean path
      const cleanPath = pathRouteMapping[location.pathname];
      canonicalUrl = `${canonicalBase}${cleanPath}`;
      socialUrl = `${socialBase}${cleanPath}`;
    } else {
      // For regular routes, use the current path
      canonicalUrl = `${canonicalBase}${currentPath}`;
      socialUrl = `${socialBase}${currentPath}`;
    }
    
    // Use social URL for meta tags (crawlers will see clean URLs)
    const fullUrl = socialUrl;
    
    // Merge with custom meta if provided
    const finalMeta: SocialMetaOptions = {
      siteName: 'DataStatPro',
      type: 'website',
      twitterCard: 'summary_large_image',
      image: 'https://www.datastatpro.com/logo.png',
      url: fullUrl,
      ...pageMeta,
      ...customMeta
    };

    // Update document title
    if (finalMeta.title) {
      document.title = finalMeta.title;
    }

    // Update or create meta tags
    const updateMetaTag = (property: string, content: string, isProperty = true) => {
      const selector = isProperty ? `meta[property="${property}"]` : `meta[name="${property}"]`;
      let metaTag = document.querySelector(selector) as HTMLMetaElement;
      
      if (!metaTag) {
        metaTag = document.createElement('meta');
        if (isProperty) {
          metaTag.setAttribute('property', property);
        } else {
          metaTag.setAttribute('name', property);
        }
        document.head.appendChild(metaTag);
      }
      
      metaTag.setAttribute('content', content);
    };

    // Update basic meta tags
    if (finalMeta.description) {
      updateMetaTag('description', finalMeta.description, false);
    }

    if (finalMeta.keywords) {
      const keywordsString = Array.isArray(finalMeta.keywords) 
        ? finalMeta.keywords.join(', ') 
        : finalMeta.keywords;
      updateMetaTag('keywords', keywordsString, false);
    }

    // Update Open Graph tags
    if (finalMeta.title) {
      updateMetaTag('og:title', finalMeta.title);
    }

    if (finalMeta.description) {
      updateMetaTag('og:description', finalMeta.description);
    }

    if (finalMeta.image) {
      updateMetaTag('og:image', finalMeta.image);
    }

    if (finalMeta.url) {
      updateMetaTag('og:url', finalMeta.url);
    }

    if (finalMeta.type) {
      updateMetaTag('og:type', finalMeta.type);
    }

    if (finalMeta.siteName) {
      updateMetaTag('og:site_name', finalMeta.siteName);
    }

    // Add Facebook-specific Open Graph tags
    updateMetaTag('og:locale', 'en_US');
    updateMetaTag('og:image:width', '1200');
    updateMetaTag('og:image:height', '630');
    updateMetaTag('og:image:type', 'image/png');
    
    // Add LinkedIn-specific tags
    updateMetaTag('og:image:alt', finalMeta.title || 'DataStatPro - Statistical Analysis Software');

    // Update Twitter Card tags
    if (finalMeta.twitterCard) {
      updateMetaTag('twitter:card', finalMeta.twitterCard);
    }

    if (finalMeta.title) {
      updateMetaTag('twitter:title', finalMeta.title);
    }

    if (finalMeta.description) {
      updateMetaTag('twitter:description', finalMeta.description);
    }

    if (finalMeta.image) {
      updateMetaTag('twitter:image', finalMeta.image);
    }

    if (finalMeta.url) {
      updateMetaTag('twitter:url', finalMeta.url);
    }

    // Update canonical URL (use canonical URL for actual navigation)
    let canonicalLink = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', canonicalUrl);

  }, [location.pathname, customMeta]);

  // Return current meta for components that might need it
  const getCurrentMeta = (): SocialMetaOptions => {
    // Get the current path - check hash first, then pathname
    let currentPath = location.pathname;
    
    // If we have a path, use the mapped route for meta data
    if (location.pathname && pathRouteMapping[location.pathname]) {
      currentPath = pathRouteMapping[location.pathname];
    }
    
    const pageMeta = defaultPageMeta[currentPath] || defaultPageMeta['/'];
    
    // Determine base URLs for different purposes
    const getBaseUrls = () => {
      if (typeof window !== 'undefined') {
        const { protocol, hostname, port } = window.location;
        
        // In development, use the current origin
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          const devBase = `${protocol}//${hostname}${port ? `:${port}` : ''}`;
          return {
            canonical: `${devBase}/app`, // For actual navigation
            social: devBase // For social sharing (clean URLs)
          };
        }
        
        // In production
        return {
          canonical: 'https://www.datastatpro.com/app', // For actual navigation
          social: 'https://www.datastatpro.com' // For social sharing (clean URLs)
        };
      }
      
      // Fallback for SSR
      return {
        canonical: 'https://www.datastatpro.com/app',
        social: 'https://www.datastatpro.com'
      };
    };
    
    const { canonical: canonicalBase, social: socialBase } = getBaseUrls();
    
    // Generate URLs for different purposes
    let canonicalUrl, socialUrl;
    
    if (location.pathname && pathRouteMapping[location.pathname]) {
      // For path-based routes, use the mapped clean path
      const cleanPath = pathRouteMapping[location.pathname];
      canonicalUrl = `${canonicalBase}${cleanPath}`;
      socialUrl = `${socialBase}${cleanPath}`;
    } else {
      // For regular routes, use the current path
      canonicalUrl = `${canonicalBase}${currentPath}`;
      socialUrl = `${socialBase}${currentPath}`;
    }
    
    // Use social URL for meta tags (crawlers will see clean URLs)
    const fullUrl = socialUrl;
    
    return {
      siteName: 'DataStatPro',
      type: 'website',
      twitterCard: 'summary_large_image',
      image: 'https://www.datastatpro.com/logo.png',
      url: fullUrl,
      ...pageMeta,
      ...customMeta
    };
  };

  return { getCurrentMeta };
};

export default useSocialMeta;
export type { SocialMetaOptions };