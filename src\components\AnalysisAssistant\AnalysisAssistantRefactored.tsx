import React, { useState, useEffect } from 'react';
import { Box, Paper } from '@mui/material';
import { useNavigate } from 'react-router-dom';

// Context and Services
import { useDataContext } from '../../context/DataContext';
import { analyzeDataset, generateSmartRecommendations } from '../../services/dataAnalysisService';
import { trainingDatabase } from '../../services/trainingDatabase';

// Custom Hooks
import { useAnalysisState, useQueryProcessor, useViewMode } from './hooks';

// Components
import {
  WelcomeView,
  DatasetView,
  QueryView,
  TrainingView,
  HistoryView,
  FeedbackDialog,
  NavigationBar
} from './components';

// Types
import { AnalysisAssistantProps, AnalysisSuggestion } from './utils/types';

/**
 * AnalysisAssistant component
 * A comprehensive assistant for data analysis, providing recommendations,
 * query processing, and training capabilities
 */
const AnalysisAssistant: React.FC<AnalysisAssistantProps> = ({ onClose }) => {
  const navigate = useNavigate();
  const { currentDataset } = useDataContext();
  
  // Custom hooks for state management
  const { viewMode, previousViewMode, changeViewMode, goBack } = useViewMode();
  
  const {
    datasetAnalysis,
    smartRecommendations,
    variableInsights,
    performanceMetrics,
    userFeedback,
    mlPrediction,
    learningHistory,
    setDatasetAnalysis,
    setSmartRecommendations,
    setVariableInsights
  } = useAnalysisState();
  
  const {
    queryHistory,
    queryContext,
    processQuery,
    addToQueryHistory,
    clearQueryHistory,
    deleteQueryHistoryItem
  } = useQueryProcessor();

  // Local state for feedback dialog
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [selectedSuggestionForFeedback, setSelectedSuggestionForFeedback] = useState<AnalysisSuggestion | null>(null);
  const [feedbackRating, setFeedbackRating] = useState(0);
  const [feedbackComment, setFeedbackComment] = useState('');
  
  // State for training mode
  const [isLearning, setIsLearning] = useState(false);

  // Initialize training system and analyze dataset
  useEffect(() => {
    // Initialize training database if needed
    trainingDatabase.initializeIfNeeded();
    
    // Analyze dataset when it changes
    if (currentDataset) {
      const analysis = analyzeDataset(currentDataset);
      setDatasetAnalysis(analysis);
      
      const recommendations = generateSmartRecommendations(analysis);
      setSmartRecommendations(recommendations);
      
      setVariableInsights({
        numeric: analysis.numericVariables.map(v => ({ name: v, type: 'numeric' })),
        categorical: analysis.categoricalVariables.map(v => ({ name: v, type: 'categorical' })),
        date: analysis.dateVariables.map(v => ({ name: v, type: 'date' }))
      });
    }
  }, [currentDataset, setDatasetAnalysis, setSmartRecommendations, setVariableInsights]);

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
    changeViewMode(newValue);
  };

  // Handle query submission
  const handleQuerySubmit = (query: string) => {
    const suggestions = processQuery(query);
    addToQueryHistory(query, suggestions[0]?.category || 'General');
  };

  // Handle feedback dialog
  const handleOpenFeedbackDialog = (suggestion: AnalysisSuggestion) => {
    setSelectedSuggestionForFeedback(suggestion);
    setFeedbackRating(0);
    setFeedbackComment('');
    setShowFeedbackDialog(true);
  };

  const handleCloseFeedbackDialog = () => {
    setShowFeedbackDialog(false);
  };

  const handleSubmitFeedback = () => {
    if (selectedSuggestionForFeedback && feedbackRating > 0) {
      // Add to feedback database
      trainingDatabase.addFeedback({
        suggestionId: selectedSuggestionForFeedback.id,
        rating: feedbackRating,
        comment: feedbackComment,
        timestamp: new Date().toISOString(),
        context: queryContext
      });
      
      // Close dialog
      setShowFeedbackDialog(false);
    }
  };

  // Handle training mode
  const handleStartTraining = () => {
    setIsLearning(true);
    // Simulate training process
    setTimeout(() => {
      setIsLearning(false);
    }, 3000);
  };

  const handleStopTraining = () => {
    setIsLearning(false);
  };

  // Render the appropriate view based on viewMode
  const renderView = () => {
    switch (viewMode) {
      case 'welcome':
        return <WelcomeView onSelectOption={changeViewMode} />;
      
      case 'dataset':
        return (
          <DatasetView
            datasetAnalysis={datasetAnalysis}
            smartRecommendations={smartRecommendations}
            variableInsights={variableInsights}
            onRecommendationSelect={(query) => {
              handleQuerySubmit(query);
              changeViewMode('query');
            }}
          />
        );
      
      case 'query':
        return (
          <QueryView
            onQuerySubmit={handleQuerySubmit}
            queryContext={queryContext}
            suggestions={queryContext.suggestions}
            onFeedbackRequest={handleOpenFeedbackDialog}
          />
        );
      
      case 'history':
        return (
          <HistoryView
            queryHistory={queryHistory}
            onReplayQuery={(query) => {
              handleQuerySubmit(query);
              changeViewMode('query');
            }}
            onDeleteHistoryItem={deleteQueryHistoryItem}
            onClearHistory={clearQueryHistory}
          />
        );
      
      case 'training':
        return (
          <TrainingView
            performanceMetrics={performanceMetrics}
            learningHistory={learningHistory}
            mlPrediction={mlPrediction}
            isLearning={isLearning}
            onStartTraining={handleStartTraining}
            onStopTraining={handleStopTraining}
          />
        );
      
      default:
        return <WelcomeView onSelectOption={changeViewMode} />;
    }
  };

  return (
    <Paper elevation={3} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Navigation Bar */}
      <NavigationBar
        viewMode={viewMode}
        previousViewMode={previousViewMode}
        hasDataset={!!currentDataset}
        onTabChange={handleTabChange}
        onGoBack={goBack}
      />

      {/* Main Content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {renderView()}
      </Box>

      {/* Feedback Dialog */}
      <FeedbackDialog
        open={showFeedbackDialog}
        suggestion={selectedSuggestionForFeedback}
        rating={feedbackRating}
        comment={feedbackComment}
        onClose={handleCloseFeedbackDialog}
        onRatingChange={setFeedbackRating}
        onCommentChange={setFeedbackComment}
        onSubmit={handleSubmitFeedback}
      />
    </Paper>
  );
};

export default AnalysisAssistant;