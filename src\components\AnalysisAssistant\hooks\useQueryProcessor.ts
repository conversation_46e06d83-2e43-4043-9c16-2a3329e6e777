import { useState, useCallback } from 'react';
import { AnalysisScenario, MatchDetails, AnalysisSuggestion, QueryContext } from '../utils/types';
import { analysisScenarios } from '../utils/analysisScenarios';
import { tokenize, removeStopWords, stem } from '../../../utils/nlpUtils';
import { STATISTICAL_SYNONYMS, STATISTICAL_ABBREVIATIONS } from '../../../utils/queryProcessor';

/**
 * Custom hook to process user queries and match them with analysis scenarios
 */
export const useQueryProcessor = () => {
  const [queryHistory, setQueryHistory] = useState<string[]>([]);
  const [currentQuery, setCurrentQuery] = useState<string>('');
  const [queryContext, setQueryContext] = useState<QueryContext>({
    recentQueries: [],
    datasetContext: null,
    userPreferences: null
  });

  /**
   * Process a query to find matching analysis scenarios
   * @param query The user's query text
   * @returns An array of matching scenarios with match details
   */
  const processQuery = useCallback((query: string) => {
    // Update query state and history
    setCurrentQuery(query);
    setQueryHistory(prev => [...prev, query]);
    
    // Update query context
    setQueryContext(prev => ({
      ...prev,
      recentQueries: [...prev.recentQueries, query].slice(-5) // Keep last 5 queries
    }));
    
    // Preprocess the query
    const preprocessedQuery = preprocessQuery(query);
    
    // Find matching scenarios
    const matches = findMatchingScenarios(preprocessedQuery, query);
    
    return matches;
  }, []);

  /**
   * Preprocess a query by expanding synonyms and abbreviations
   * @param query The raw query text
   * @returns The preprocessed query text
   */
  const preprocessQuery = (query: string): string => {
    let processedQuery = query.toLowerCase();
    
    // Expand abbreviations
    Object.entries(STATISTICAL_ABBREVIATIONS).forEach(([abbr, expansion]) => {
      const abbrRegex = new RegExp(`\\b${abbr}\\b`, 'gi');
      processedQuery = processedQuery.replace(abbrRegex, expansion);
    });
    
    // Expand synonyms
    Object.entries(STATISTICAL_SYNONYMS).forEach(([term, synonyms]) => {
      synonyms.forEach(synonym => {
        const synonymRegex = new RegExp(`\\b${synonym}\\b`, 'gi');
        processedQuery = processedQuery.replace(synonymRegex, term);
      });
    });
    
    return processedQuery;
  };

  /**
   * Find analysis scenarios that match the preprocessed query
   * @param preprocessedQuery The preprocessed query text
   * @param originalQuery The original query text
   * @returns An array of matching scenarios with match details
   */
  const findMatchingScenarios = (preprocessedQuery: string, originalQuery: string) => {
    const matches: { scenario: AnalysisScenario; matchDetails: MatchDetails }[] = [];
    
    // Tokenize the query
    const queryTokens = tokenize(preprocessedQuery);
    const filteredTokens = removeStopWords(queryTokens);
    const stemmedTokens = filteredTokens.map(token => stem(token));
    
    // Check each scenario for matches
    analysisScenarios.forEach(scenario => {
      let keywordMatches = 0;
      let patternMatches = 0;
      const matchedKeywords: string[] = [];
      
      // Check for keyword matches
      scenario.keywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase();
        if (preprocessedQuery.includes(keywordLower)) {
          keywordMatches++;
          matchedKeywords.push(keyword);
        }
      });
      
      // Check for pattern matches
      if (scenario.patterns) {
        scenario.patterns.forEach(pattern => {
          if (pattern.test(preprocessedQuery) || pattern.test(originalQuery)) {
            patternMatches++;
          }
        });
      }
      
      // Calculate match score
      const keywordScore = keywordMatches / scenario.keywords.length;
      const patternScore = scenario.patterns ? patternMatches / scenario.patterns.length : 0;
      const totalScore = keywordScore * 0.7 + patternScore * 0.3;
      
      // If there's a significant match, add to results
      if (keywordMatches > 0 || patternMatches > 0) {
        matches.push({
          scenario,
          matchDetails: {
            score: totalScore,
            matchedKeywords,
            patternMatches,
            confidence: calculateConfidence(totalScore, keywordMatches, patternMatches)
          }
        });
      }
    });
    
    // Sort matches by score
    return matches.sort((a, b) => b.matchDetails.score - a.matchDetails.score);
  };

  /**
   * Calculate confidence level based on match scores
   * @param totalScore The total match score
   * @param keywordMatches Number of keyword matches
   * @param patternMatches Number of pattern matches
   * @returns A confidence value between 0 and 1
   */
  const calculateConfidence = (totalScore: number, keywordMatches: number, patternMatches: number): number => {
    // Base confidence on total score
    let confidence = totalScore;
    
    // Boost confidence if both keywords and patterns match
    if (keywordMatches > 0 && patternMatches > 0) {
      confidence += 0.2;
    }
    
    // Boost confidence if multiple keywords match
    if (keywordMatches > 2) {
      confidence += 0.1;
    }
    
    // Cap confidence at 1.0
    return Math.min(confidence, 1.0);
  };

  /**
   * Generate analysis suggestions based on matching scenarios
   * @param matches The matching scenarios with match details
   * @returns An array of analysis suggestions
   */
  const generateSuggestions = useCallback((matches: { scenario: AnalysisScenario; matchDetails: MatchDetails }[]): AnalysisSuggestion[] => {
    return matches.map((match, index) => ({
      id: `suggestion-${Date.now()}-${index}`,
      title: match.scenario.name,
      description: match.scenario.contextualAdvice || 'No additional advice available.',
      confidence: match.matchDetails.confidence,
      matchedKeywords: match.matchDetails.matchedKeywords,
      suggestedAnalyses: match.scenario.suggestions || [],
      timestamp: new Date().toISOString()
    }));
  }, []);

  return {
    queryHistory,
    currentQuery,
    queryContext,
    processQuery,
    generateSuggestions,
    setCurrentQuery,
    setQueryContext
  };
};