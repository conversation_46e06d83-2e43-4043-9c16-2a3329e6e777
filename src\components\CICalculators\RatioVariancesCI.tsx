import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  CompareArrows as RatioIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  Calculate as CalculateIcon,
  TrendingUp as TrendIcon,
} from '@mui/icons-material';
import 'katex/dist/katex.min.css';
import katex from 'katex';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ratio-variances-ci-tabpanel-${index}`}
      aria-labelledby={`ratio-variances-ci-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface RatioVariancesCIResult {
  variance1: number;
  variance2: number;
  n1: number;
  n2: number;
  confidenceLevel: number;
  df1: number;
  df2: number;
  fRatio: number;
  fCriticalLower: number;
  fCriticalUpper: number;
  lowerBound: number;
  upperBound: number;
  interpretation: string;
  formula: string;
}

const RatioVariancesCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [variance1, setVariance1] = useState<string>('');
  const [variance2, setVariance2] = useState<string>('');
  const [n1, setN1] = useState<string>('');
  const [n2, setN2] = useState<string>('');
  const [confidenceLevel, setConfidenceLevel] = useState<number>(95);
  const [result, setResult] = useState<RatioVariancesCIResult | null>(null);
  const [error, setError] = useState<string>('');

  const renderFormula = (formula: string): string => {
    try {
      return katex.renderToString(formula, {
        displayMode: true,
        throwOnError: false,
        strict: false
      });
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // F-distribution critical values (approximation for common cases)
  const getFCritical = (df1: number, df2: number, alpha: number): { lower: number; upper: number } => {
    // This is a simplified approximation. In practice, you'd use a more comprehensive table or function
    const criticalValues: { [key: string]: { [key: string]: { lower: number; upper: number } } } = {
      '90': {
        '5,5': { lower: 0.198, upper: 5.050 },
        '5,10': { lower: 0.214, upper: 3.326 },
        '5,20': { lower: 0.225, upper: 2.711 },
        '5,30': { lower: 0.229, upper: 2.534 },
        '10,10': { lower: 0.248, upper: 2.978 },
        '10,20': { lower: 0.264, upper: 2.348 },
        '10,30': { lower: 0.270, upper: 2.165 },
        '20,20': { lower: 0.294, upper: 2.124 },
        '20,30': { lower: 0.305, upper: 1.932 },
        '30,30': { lower: 0.315, upper: 1.841 }
      },
      '95': {
        '5,5': { lower: 0.158, upper: 6.256 },
        '5,10': { lower: 0.169, upper: 4.236 },
        '5,20': { lower: 0.177, upper: 3.289 },
        '5,30': { lower: 0.181, upper: 3.070 },
        '10,10': { lower: 0.195, upper: 3.717 },
        '10,20': { lower: 0.208, upper: 2.774 },
        '10,30': { lower: 0.214, upper: 2.538 },
        '20,20': { lower: 0.231, upper: 2.464 },
        '20,30': { lower: 0.240, upper: 2.204 },
        '30,30': { lower: 0.249, upper: 2.086 }
      },
      '99': {
        '5,5': { lower: 0.100, upper: 10.050 },
        '5,10': { lower: 0.107, upper: 6.619 },
        '5,20': { lower: 0.113, upper: 4.938 },
        '5,30': { lower: 0.116, upper: 4.496 },
        '10,10': { lower: 0.123, upper: 5.814 },
        '10,20': { lower: 0.132, upper: 4.103 },
        '10,30': { lower: 0.137, upper: 3.646 },
        '20,20': { lower: 0.148, upper: 3.493 },
        '20,30': { lower: 0.155, upper: 3.030 },
        '30,30': { lower: 0.161, upper: 2.786 }
      }
    };
    
    const levelStr = confidenceLevel.toString();
    if (!criticalValues[levelStr]) {
      // Default to 95% if level not found
      return criticalValues['95']['10,10'];
    }
    
    // Find closest df combination
    const availableDfs = ['5,5', '5,10', '5,20', '5,30', '10,10', '10,20', '10,30', '20,20', '20,30', '30,30'];
    let closestKey = '10,10';
    let minDistance = Infinity;
    
    for (const key of availableDfs) {
      const [keyDf1, keyDf2] = key.split(',').map(Number);
      const distance = Math.abs(df1 - keyDf1) + Math.abs(df2 - keyDf2);
      if (distance < minDistance) {
        minDistance = distance;
        closestKey = key;
      }
    }
    
    return criticalValues[levelStr][closestKey] || criticalValues[levelStr]['10,10'];
  };

  const calculateRatioVariancesCI = useCallback(() => {
    setError('');
    
    const variance1Value = parseFloat(variance1);
    const variance2Value = parseFloat(variance2);
    const n1Value = parseInt(n1);
    const n2Value = parseInt(n2);
    
    // Input validation
    if (isNaN(variance1Value) || isNaN(variance2Value) || isNaN(n1Value) || isNaN(n2Value)) {
      setError('Please enter valid numeric values for all fields.');
      return;
    }
    
    if (variance1Value <= 0 || variance2Value <= 0) {
      setError('Both variances must be positive.');
      return;
    }
    
    if (n1Value < 2 || n2Value < 2) {
      setError('Both sample sizes must be at least 2.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }
    
    const df1 = n1Value - 1;
    const df2 = n2Value - 1;
    const alpha = (100 - confidenceLevel) / 100;
    
    // Calculate F-ratio
    const fRatio = variance1Value / variance2Value;
    
    // Get F critical values
    const fCriticalValues = getFCritical(df1, df2, alpha);
    const fCriticalLower = fCriticalValues.lower;
    const fCriticalUpper = fCriticalValues.upper;
    
    // Calculate confidence interval for the ratio of variances
    const lowerBound = fRatio * fCriticalLower;
    const upperBound = fRatio * fCriticalUpper;
    
    // Interpretation
    let interpretation = '';
    if (lowerBound > 1) {
      interpretation = `We are ${confidenceLevel}% confident that the variance of population 1 is ${lowerBound.toFixed(2)} to ${upperBound.toFixed(2)} times larger than the variance of population 2.`;
    } else if (upperBound < 1) {
      interpretation = `We are ${confidenceLevel}% confident that the variance of population 1 is ${lowerBound.toFixed(2)} to ${upperBound.toFixed(2)} times the variance of population 2 (smaller than population 2).`;
    } else {
      interpretation = `We are ${confidenceLevel}% confident that the ratio of variances is between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}. The interval includes 1, suggesting the variances may not be significantly different.`;
    }
    
    // Formula for ratio of variances confidence interval
    const formula = '\\frac{s_1^2}{s_2^2} \\cdot F_{\\alpha/2,df_2,df_1} \\leq \\frac{\\sigma_1^2}{\\sigma_2^2} \\leq \\frac{s_1^2}{s_2^2} \\cdot F_{1-\\alpha/2,df_2,df_1}';

    const calculationResult: RatioVariancesCIResult = {
      variance1: variance1Value,
      variance2: variance2Value,
      n1: n1Value,
      n2: n2Value,
      confidenceLevel,
      df1,
      df2,
      fRatio,
      fCriticalLower,
      fCriticalUpper,
      lowerBound,
      upperBound,
      interpretation,
      formula
    };
    
    setResult(calculationResult);
    setActiveTab(1); // Switch to Results tab
  }, [variance1, variance2, n1, n2, confidenceLevel]);
  
  const clearAll = () => {
    setVariance1('');
    setVariance2('');
    setN1('');
    setN2('');
    setConfidenceLevel(95);
    setResult(null);
    setError('');
    setActiveTab(0);
  };
  
  const copyToClipboard = () => {
    if (!result) return;
    
    const text = `Ratio of Variances Confidence Interval Results:\n` +
      `Sample Variance 1: ${result.variance1}\n` +
      `Sample Variance 2: ${result.variance2}\n` +
      `Sample Size 1 (n₁): ${result.n1}\n` +
      `Sample Size 2 (n₂): ${result.n2}\n` +
      `Confidence Level: ${result.confidenceLevel}%\n` +
      `F-ratio (s₁²/s₂²): ${result.fRatio.toFixed(2)}\n` +
      `Degrees of Freedom: ${result.df1}, ${result.df2}\n` +
      `${result.confidenceLevel}% CI for Ratio: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n` +
      `F Critical Lower: ${result.fCriticalLower.toFixed(2)}\n` +
      `F Critical Upper: ${result.fCriticalUpper.toFixed(2)}\n` +
      `\nInterpretation: ${result.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <RatioIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" component="h1" fontWeight="bold">
          Ratio of Variances CI
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<TrendIcon />} />
          <Tab label="Guide" icon={<InfoIcon />} />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Variance 1 (s₁²)"
                      value={variance1}
                      onChange={(e) => setVariance1(e.target.value)}
                      type="number"
                      inputProps={{ step: 0.001, min: 0 }}
                      helperText="Variance of the first sample"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Variance 2 (s₂²)"
                      value={variance2}
                      onChange={(e) => setVariance2(e.target.value)}
                      type="number"
                      inputProps={{ step: 0.001, min: 0 }}
                      helperText="Variance of the second sample"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Size 1 (n₁)"
                      value={n1}
                      onChange={(e) => setN1(e.target.value)}
                      type="number"
                      inputProps={{ step: 1, min: 2 }}
                      helperText="Number of observations in sample 1"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Size 2 (n₂)"
                      value={n2}
                      onChange={(e) => setN2(e.target.value)}
                      type="number"
                      inputProps={{ step: 1, min: 2 }}
                      helperText="Number of observations in sample 2"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={confidenceLevel}
                        onChange={(e) => setConfidenceLevel(e.target.value as number)}
                        label="Confidence Level"
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculateRatioVariancesCI}
                    disabled={!variance1 || !variance2 || !n1 || !n2}
                    startIcon={<CalculateIcon />}
                  >
                    Calculate Confidence Interval
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  About This Calculator
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This calculator computes confidence intervals for the ratio of two population variances 
                  using the F-distribution. It's commonly used to test whether two populations have 
                  equal variances.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result ? (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Confidence Interval Results
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                        <Typography variant="subtitle2" color="primary">
                          {result.confidenceLevel}% CI for Ratio (σ₁²/σ₂²)
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                        <Typography variant="subtitle2" color="secondary">
                          Sample F-ratio (s₁²/s₂²)
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          {result.fRatio.toFixed(2)}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Sample Statistics
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Variance 1</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.variance1}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Variance 2</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.variance2}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Size 1</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.n1}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Size 2</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.n2}</Typography>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    F-Distribution Details
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">df₁</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.df1}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">df₂</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.df2}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">F Critical Lower</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.fCriticalLower.toFixed(2)}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">F Critical Upper</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.fCriticalUpper.toFixed(2)}</Typography>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Formula
                  </Typography>
                  <Box sx={{ 
                    p: 2, 
                    bgcolor: 'grey.50', 
                    borderRadius: 1, 
                    border: '1px solid', 
                    borderColor: 'grey.200',
                    mb: 3
                  }}>
                    <div dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }} />
                  </Box>
                  
                  <Typography variant="h6" gutterBottom>
                    Interpretation
                  </Typography>
                  <Typography variant="body1">
                    {result.interpretation}
                  </Typography>
                  
                  <Box sx={{ mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={copyToClipboard}
                      startIcon={<CopyIcon />}
                    >
                      Copy Results
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Key Statistics
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Chip label="F" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="F-ratio" 
                        secondary={result.fRatio.toFixed(2)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="df" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Degrees of Freedom" 
                        secondary={`${result.df1}, ${result.df2}`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="CI" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Confidence Level" 
                        secondary={`${result.confidenceLevel}%`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="n" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Sizes" 
                        secondary={`${result.n1}, ${result.n2}`}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        ) : (
          <Alert severity="info">
            No results to display. Please go to the Calculator tab and perform a calculation.
          </Alert>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h5" gutterBottom>
                  Ratio of Variances Confidence Intervals Guide
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  When to Use
                </Typography>
                <Typography variant="body1" paragraph>
                  Use ratio of variances confidence intervals when you want to compare the variability 
                  between two populations. This is particularly useful for:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="Testing equality of variances (homoscedasticity)" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Quality control comparisons between processes" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Comparing measurement precision between instruments" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Validating assumptions for other statistical tests" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  How It Works
                </Typography>
                <Typography variant="body1" paragraph>
                  The confidence interval for the ratio of variances is calculated using the F-distribution. 
                  The formula is:
                </Typography>
                <Typography variant="body1" paragraph sx={{ fontFamily: 'monospace', bgcolor: 'grey.100', p: 1 }}>
                  [(s₁²/s₂²) × F(α/2, df₁, df₂), (s₁²/s₂²) × F(1-α/2, df₁, df₂)]
                </Typography>
                <Typography variant="body1" paragraph>
                  Where s₁² and s₂² are the sample variances, and F represents the F-distribution 
                  critical values with degrees of freedom df₁ = n₁-1 and df₂ = n₂-1.
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  How to Interpret
                </Typography>
                <Typography variant="body1" paragraph>
                  The confidence interval provides a range of plausible values for the true ratio 
                  of population variances (σ₁²/σ₂²). Key interpretations:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="If the interval includes 1: The variances may be equal" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="If the interval is entirely above 1: Variance 1 is larger" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="If the interval is entirely below 1: Variance 2 is larger" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Assumptions
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="Both populations follow normal distributions" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Observations are independent within and between samples" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Samples are randomly selected" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Important Notes
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="The F-test is sensitive to departures from normality" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Consider using robust alternatives for non-normal data" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="The order of samples affects the interpretation (s₁²/s₂² vs s₂²/s₁²)" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Larger sample sizes provide more reliable results" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Reference
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Ratio Interpretation:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="Ratio = 1: Equal variances" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Ratio > 1: Variance 1 larger" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Ratio < 1: Variance 2 larger" />
                  </ListItem>
                </List>
                
                <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                  <strong>Sample Size Guidelines:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="n ≥ 30: Good approximation" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="n < 30: Check normality" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Balanced samples preferred" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default RatioVariancesCI;