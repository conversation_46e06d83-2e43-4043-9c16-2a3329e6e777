import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  <PERSON>lapse,
  Divider,
  Chip,
  Tooltip,
  alpha,
  styled,
  useTheme
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FileCopy as FileCopyIcon,
  SaveAlt as SaveAltIcon,
  InfoOutlined as InfoOutlinedIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  HelpOutline as HelpOutlineIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useTableTheme } from '../../hooks';

// Interfaces for the types of results
export interface TableData {
  columns: string[];
  rows: (string | number)[][];
}

export interface StatisticalTestResult {
  name: string;
  value: number | string;
  pValue?: number;
  significant?: boolean;
  significance?: 'high' | 'medium' | 'low' | 'none';
  confidenceInterval?: [number, number];
  description?: string;
}

export interface AnalysisResultCardProps {
  title: string;
  timestamp?: Date;
  description?: string;
  significance?: number;
  pValue?: number;
  statisticalTests?: StatisticalTestResult[];
  tableData?: TableData;
  interpretations?: string[];
  assumptions?: { name: string; status: 'passed' | 'warning' | 'failed'; message?: string }[];
  chart?: React.ReactNode;
  expandable?: boolean;
  initialExpanded?: boolean;
  variant?: 'default' | 'outlined' | 'filled';
  significanceLevel?: number;
  significanceThresholds?: number[];
  footnotes?: string[];
  citation?: string;
  homogeneityTestsTable?: TableData; // New prop for Levene's test table
  onSave?: () => void;
  onCopy?: () => void;
}

const ExpandButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'expanded',
})<{ expanded?: boolean }>(({ expanded }) => ({
  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
  transition: 'transform 0.3s',
}));

const AnalysisResultCard: React.FC<AnalysisResultCardProps> = ({
  title,
  timestamp = new Date(),
  description,
  significance,
  pValue,
  statisticalTests = [],
  tableData,
  interpretations = [],
  assumptions = [],
  chart,
  expandable = true,
  initialExpanded = true,
  variant = 'default',
  significanceLevel = 0.05,
  significanceThresholds = [0.001, 0.01, 0.05],
  footnotes = [],
  citation,
  homogeneityTestsTable, // Destructure new prop
  onSave,
  onCopy,
}) => {
  const [expanded, setExpanded] = useState(initialExpanded);
  const [copiedMessage, setCopiedMessage] = useState<string | null>(null);
  const theme = useTheme();
  const { tableContainer } = useTableTheme();

  const handleExpandClick = () => {
    if (expandable) {
      setExpanded(!expanded);
    }
  };

  const handleCopy = () => {
    if (onCopy) {
      onCopy();
    } else {
      // Default copy functionality
      let textToCopy = `${title}\n`;
      if (description) textToCopy += `${description}\n\n`;
      
      if (statisticalTests.length > 0) {
        textToCopy += 'Statistical Tests:\n';
        statisticalTests.forEach(test => {
          textToCopy += `${test.name}: ${test.value}${test.pValue ? `, p = ${test.pValue}` : ''}\n`;
        });
        textToCopy += '\n';
      }
      
      if (interpretations.length > 0) {
        textToCopy += 'Interpretations:\n';
        interpretations.forEach(interpretation => {
          textToCopy += `- ${interpretation}\n`;
        });
      }
      
      navigator.clipboard.writeText(textToCopy)
        .then(() => {
          setCopiedMessage('Results copied to clipboard');
          setTimeout(() => setCopiedMessage(null), 2000);
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
        });
    }
  };

  const getSignificanceLabel = (p: number) => {
    if (p <= significanceThresholds[0]) return { label: 'Highly Significant', color: 'success' };
    if (p <= significanceThresholds[1]) return { label: 'Very Significant', color: 'success' };
    if (p <= significanceThresholds[2]) return { label: 'Significant', color: 'primary' };
    return { label: 'Not Significant', color: 'error' };
  };

  const getSignificanceStars = (p: number) => {
    if (p <= significanceThresholds[0]) return '***';
    if (p <= significanceThresholds[1]) return '**';
    if (p <= significanceThresholds[2]) return '*';
    return '';
  };

  const formatValue = (value: number | string, isPValue: boolean = false) => {
    if (typeof value === 'string') return value;
    if (isPValue) {
      return value < 0.001 ? '< 0.001' : value.toFixed(4);
    }
    return value.toFixed(2);
  };

  const assumptionStatus = assumptions.length > 0 
    ? assumptions.every(a => a.status === 'passed') 
      ? 'passed'
      : assumptions.some(a => a.status === 'failed')
        ? 'failed'
        : 'warning'
    : null;
  
  // Determine background color based on variant
  let bgColor;
  let borderStyle;
  
  switch (variant) {
    case 'outlined':
      bgColor = theme.palette.background.paper;
      borderStyle = `1px solid ${theme.palette.divider}`;
      break;
    case 'filled':
      bgColor = alpha(theme.palette.primary.main, 0.05);
      borderStyle = 'none';
      break;
    default:
      bgColor = theme.palette.background.paper;
      borderStyle = 'none';
  }

  return (
    <Paper 
      elevation={variant === 'default' ? 2 : 0}
      sx={{ 
        mb: 3, 
        overflow: 'hidden',
        backgroundColor: bgColor,
        border: borderStyle,
        borderRadius: 2,
        ...(variant === 'outlined' && {
          borderLeft: `4px solid ${theme.palette.primary.main}`,
        })
      }}
    >
      {/* Header */}
      <Box 
        sx={{ 
          p: 2,
          display: 'flex', 
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: variant === 'filled' ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
          borderBottom: '1px solid',
          borderColor: theme.palette.divider,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h6" component="h3" fontWeight="medium">
            {title}
          </Typography>
          {pValue !== undefined && (
            <Tooltip title={`p-value: ${pValue}`}>
              <Chip
                label={getSignificanceLabel(pValue).label}
                color={getSignificanceLabel(pValue).color as any}
                size="small"
                sx={{ ml: 2, height: 24 }}
              />
            </Tooltip>
          )}
          
          {assumptionStatus && (
            <Tooltip 
              title={
                <Box>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Assumption Check:
                  </Typography>
                  {assumptions.map((assumption, i) => (
                    <Box key={i} sx={{ mb: 0.5 }}>
                      <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                        {assumption.status === 'passed' && 
                          <CheckIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />}
                        {assumption.status === 'warning' && 
                          <WarningIcon color="warning" fontSize="small" sx={{ mr: 0.5 }} />}
                        {assumption.status === 'failed' && 
                          <CloseIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />}
                        {assumption.name}
                      </Typography>
                      {assumption.message && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 4 }}>
                          {assumption.message}
                        </Typography>
                      )}
                    </Box>
                  ))}
                </Box>
              }
              arrow
            >
              <Chip
                icon={
                  assumptionStatus === 'passed' ? <CheckIcon /> :
                  assumptionStatus === 'failed' ? <CloseIcon /> :
                  <WarningIcon />
                }
                label="Assumptions"
                color={
                  assumptionStatus === 'passed' ? 'success' :
                  assumptionStatus === 'failed' ? 'error' :
                  'warning'
                }
                size="small"
                sx={{ ml: 1, height: 24 }}
              />
            </Tooltip>
          )}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {timestamp && (
            <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
              {timestamp.toLocaleString()}
            </Typography>
          )}
          
          {onCopy && (
            <Tooltip title={copiedMessage || "Copy results"}>
              <IconButton size="small" onClick={handleCopy} sx={{ mr: 0.5 }}>
                <FileCopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          
          {onSave && (
            <Tooltip title="Save results">
              <IconButton size="small" onClick={onSave} sx={{ mr: 0.5 }}>
                <SaveAltIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          
          {expandable && (
            <ExpandButton
              expanded={expanded}
              onClick={handleExpandClick}
              aria-expanded={expanded}
              aria-label="show more"
              size="small"
            >
              <ExpandMoreIcon />
            </ExpandButton>
          )}
        </Box>
      </Box>
      
      {/* Description (always visible) */}
      {description && (
        <Box sx={{ px: 2, pt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {description}
          </Typography>
        </Box>
      )}
      
      {/* Collapsible content */}
      <Collapse in={expanded || !expandable} timeout="auto" unmountOnExit>
        <Box sx={{ p: 2 }}>
          {/* Chart */}
          {chart && (
            <Box 
              sx={{ 
                mb: 3, 
                mx: 'auto', 
                maxWidth: '100%', 
                height: 'auto',
                display: 'flex',
                justifyContent: 'center'
              }}
            >
              {chart}
            </Box>
          )}
          
          {/* Statistical Tests */}
          {statisticalTests.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom fontWeight="medium">
                Statistical Results
              </Typography>
              <Box 
                sx={{ 
                  display: 'grid', 
                  gridTemplateColumns: { 
                    xs: '1fr', 
                    sm: 'repeat(2, 1fr)', 
                    md: 'repeat(3, 1fr)' 
                  },
                  gap: 2
                }}
              >
                {statisticalTests.map((test, index) => (
                  <Box 
                    key={index}
                    sx={{ 
                      p: 1.5, 
                      backgroundColor: alpha(theme.palette.background.default, 0.5),
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: theme.palette.divider,
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {test.name}
                      </Typography>
                      {test.pValue !== undefined && (
                        <Tooltip title={`p = ${test.pValue}`}>
                          <Typography 
                            variant="caption" 
                            sx={{ 
                              color: test.pValue <= significanceLevel ? 'success.main' : 'error.main',
                              fontWeight: 'bold'
                            }}
                          >
                            {getSignificanceStars(test.pValue)}
                          </Typography>
                        </Tooltip>
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'baseline', mt: 0.5 }}>
                      <Typography variant="h6" fontWeight="medium">
                        {formatValue(test.value, false)}
                      </Typography>
                      {test.confidenceInterval && (
                        <Typography variant="caption" sx={{ ml: 1 }}>
                          95% CI [{test.confidenceInterval[0].toFixed(2)}, {test.confidenceInterval[1].toFixed(2)}]
                        </Typography>
                      )}
                    </Box>
                    {test.description && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                        {test.description}
                      </Typography>
                    )}
                  </Box>
                ))}
              </Box>
            </Box>
          )}
          
          {/* Table Data */}
          {tableData && (
            <Box sx={{ mb: 3, overflowX: 'auto' }}>
              <Typography variant="subtitle2" gutterBottom fontWeight="medium">
                Results Table
              </Typography>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr>
                    {tableData.columns.map((column, index) => (
                      <th
                        key={index}
                        style={{
                          padding: '8px 16px',
                          textAlign: 'left',
                          backgroundColor: alpha(theme.palette.primary.main, 0.05),
                          border: `1px solid ${theme.palette.divider}`,
                        }}
                      >
                        {column}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tableData.rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <td
                          key={cellIndex}
                          style={{
                            padding: '8px 16px',
                            border: `1px solid ${theme.palette.divider}`,
                            backgroundColor: rowIndex % 2 === 0 ? alpha(theme.palette.background.default, 0.5) : 'transparent',
                          }}
                        >
                          {typeof cell === 'number' ? 
                            (tableData.columns[cellIndex].toLowerCase().includes('p') ? 
                              formatValue(cell, true) : formatValue(cell, false)) : 
                            cell
                          }
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>
          )}
          
          {/* Homogeneity of Variances Table */}
          {homogeneityTestsTable && (
            <Box sx={{ mb: 3, overflowX: 'auto' }}>
              <Typography variant="subtitle2" gutterBottom fontWeight="medium">
                Homogeneity of Variances Tests
              </Typography>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr>
                    {homogeneityTestsTable.columns.map((column, index) => (
                      <th
                        key={index}
                        style={{
                          padding: '8px 16px',
                          textAlign: 'left',
                          backgroundColor: alpha(theme.palette.primary.main, 0.05),
                          border: `1px solid ${theme.palette.divider}`,
                        }}
                      >
                        {column}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {homogeneityTestsTable.rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <td
                          key={cellIndex}
                          style={{
                            padding: '8px 16px',
                            border: `1px solid ${theme.palette.divider}`,
                            backgroundColor: rowIndex % 2 === 0 ? alpha(theme.palette.background.default, 0.5) : 'transparent',
                          }}
                        >
                          {/* Display numbers with appropriate precision, others as is */}
                          {typeof cell === 'number' ? 
                            (homogeneityTestsTable.columns[cellIndex].toLowerCase().includes('p') ? 
                              formatValue(cell, true) : formatValue(cell, false)) : 
                            cell
                          }
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>
          )}

          {/* Interpretations */}
          {interpretations.length > 0 && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoOutlinedIcon color="primary" fontSize="small" sx={{ mr: 1 }} />
                Interpretation
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: theme.palette.mode === 'dark'
                    ? alpha(theme.palette.primary.main, 0.08)
                    : alpha(theme.palette.primary.main, 0.03),
                  borderLeft: `4px solid ${theme.palette.primary.main}`,
                  color: theme.palette.text.primary
                }}
              >
                {interpretations.map((interpretation, index) => (
                  <Typography
                    key={index}
                    variant="body2"
                    paragraph={index < interpretations.length - 1}
                    sx={{
                      mb: index < interpretations.length - 1 ? 1.5 : 0,
                      color: theme.palette.text.primary
                    }}
                  >
                    {interpretation}
                  </Typography>
                ))}
              </Paper>
            </Box>
          )}
          
          {/* Footnotes */}
          {footnotes.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="caption" color="text.secondary">
                <Box component="span" sx={{ display: 'block', fontWeight: 'medium', mb: 0.5 }}>
                  Notes:
                </Box>
                {footnotes.map((note, index) => (
                  <Box component="span" key={index} sx={{ display: 'block', ml: 1, mb: 0.5 }}>
                    {index + 1}. {note}
                  </Box>
                ))}
              </Typography>
            </Box>
          )}
          
          {/* Citation */}
          {citation && (
            <Box sx={{ mt: 1.5 }}>
              <Typography variant="caption" color="text.secondary">
                <Box component="span" sx={{ fontWeight: 'medium' }}>
                  Citation:
                </Box>{' '}
                {citation}
              </Typography>
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default AnalysisResultCard;
