import React, { useEffect, useState } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet-async';
import {
  Box,
  Typography,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  useTheme,
  useMediaQuery,
  alpha,
  Chip,
  Stack,
  Avatar,
  Paper,
  Link,
  Fade,
  Slide,
  Grow,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Divider,
  AppBar,
  Toolbar
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  BarChart as BarChartIcon,
  Functions as FunctionsIcon,
  ArrowForward as ArrowForwardIcon,
  Storage as StorageIcon,
  CleaningServices as CleaningServicesIcon,
  PieChart as PieChartIcon,
  Devices as DevicesIcon,
  DesktopMac,
  Laptop,
  Public as PublicIcon,
  Timeline as TimelineIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  GridOn as GridOnIcon,
  TabletMac,
  PhoneIphone,
  Menu as MenuIcon,
  Close as CloseIcon,
  Login as LoginIcon,
  AccountCircle as AccountCircleIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Email, Facebook, Twitter, LinkedIn } from '@mui/icons-material';

// Import for react-slick slider (testimonials)
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

function stringToColor(name: string) {
  let hash = 0, i;
  for (i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  let color = '#';
  for (i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 0xff;
    color += ('00' + value.toString(16)).substr(-2);
  }
  return color;
}

function stringAvatar(name: string) {
  return {
    sx: {
      bgcolor: stringToColor(name),
    },
    children: name.split(' ').map((n) => n[0]).join(''),
  };
}

const sliderSettings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 2,
  slidesToScroll: 1,
  adaptiveHeight: true,
  arrows: true,
  responsive: [
    { breakpoint: 960, settings: { slidesToShow: 1 } }
  ]
};

const testimonials = [
   {
    quote: "The AI Analysis Assistant is a great feature for beginners. It helps understand the results easily.",
    name: "Muhammad Qaiser Shahbaz",
    title: "Professor at King Abdulaziz University, Saudi Arabia"
  },
  {
    quote: "DataStatPro has revolutionized my research workflow. It's intuitive, powerful, and the visualizations are stunning!",
    name: "Asif Hanif",
    title: "Professor at Sakarya University, Türkiye"
    
  },
  {
    quote: "As a student, DataStatPro's free access for education is a game-changer. It's easy to learn and has all the features I need.",
    name: "Abdullah Alzaidi",
    title: "Student at University of Qatar, Qatar"
  },
  {
    quote: "The cross-platform compatibility is fantastic. I can seamlessly switch between my desktop and tablet.",
    name: "Yasir Hassan",
    title: "Data Analyst at UoL, Pakistan"
  },
    {
    quote: "DataStatPro has revolutionized my research workflow. It's intuitive, powerful, and the visualizations are stunning!",
    name: "Rehan Ahmad Khan",
    title: "Professor at University of the Punjab, Pakistan"
  },
  {
    quote: "As a student, DataStatPro's free access for education is a game-changer. It's easy to learn and has all the features I need.",
    name: "Uzair Nadeem",
    title: "Student at PISJ"
  },
  {
    quote: "The cross-platform compatibility is fantastic. I can seamlessly switch between my desktop and tablet.",
    name: "Jasleen Kaur",
    title: "Data Analyst"
  },
  {
    quote: "The AI Analysis Assistant is a great feature for beginners. It helps understand the results easily.",
    name: "Muhammad Qaiser Shahbaz",
    title: "Professor at King Abdulaziz University, Saudi Arabia"
  },
  {
    quote: "With its advanced import/export features, DataStatPro made handling my large survey dataset both simple and secure.",
    name: "Dr. Anjali Mehra",
    title: "Associate Professor at Delhi University"
  },
  {
    quote: "I love the visualizations—presentation-ready graphs are just a click away. My publications have never looked better.",
    name: "Elena Petrova",
    title: "Biostatistician, Moscow State University"
  },
  {
    quote: "Switching devices is seamless. Whether I start on my laptop or mobile, my data and analyses follow me everywhere.",
    name: "Liam O'Connor",
    title: "Epidemiology Graduate Student, University College Dublin"
  },
  {
    quote: "The Risk Calculator and EpiCalc tools are essential for our lab planning. Highly recommended for medical researchers.",
    name: "Dr. Sarah Ibrahim",
    title: "Research Coordinator, Cairo Medical Institute"
  }
  ];

const features = [
  {
    icon: <DevicesIcon sx={{ fontSize: 48, color: '#4A90E2' }} />,
    title: 'Cross-Platform',
    description: 'Works seamlessly across all devices and operating systems. Access your analysis anywhere, anytime.',
    image: 'cross-platform.png'
  },
  {
    icon: <StorageIcon sx={{ fontSize: 48, color: '#7B68EE' }} />,
    title: 'Data Management',
    description: 'Powerful data import, export, and management tools. Handle large datasets with ease.',
    image: 'data-management.png'
  },
  {
    icon: <CleaningServicesIcon sx={{ fontSize: 48, color: '#50C878' }} />,
    title: 'Advanced Analysis',
    description: 'Advanced statistical analysis tools for complex data sets. Get insights quickly.',
    image: 'advanced-analysis.png'
  },
  {
    icon: <PieChartIcon sx={{ fontSize: 48, color: '#FF6B6B' }} />,
    title: 'Beautiful Charts',
    description: 'Create stunning visualizations with our advanced charting engine. Export in multiple formats.',
    image: 'beautiful-charts.png'
  }
];

const LandingPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();

  // Animation states
  const [heroVisible, setHeroVisible] = useState(false);
  const [featuresVisible, setFeaturesVisible] = useState(false);
  const [ctaVisible, setCtaVisible] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleScroll = () => {
    if (window.pageYOffset > 300) { // Show button after scrolling down 300px
      setShowScrollButton(true);
    } else {
      setShowScrollButton(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth' // Smooth scroll
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLElement;
          if (target.id === 'hero-section') setHeroVisible(true);
          if (target.id === 'features-section') setFeaturesVisible(true);
          if (target.id === 'cta-section') setCtaVisible(true);
        }
      });
    }, observerOptions);

    // Observe sections
    const heroSection = document.getElementById('hero-section');
    const featuresSection = document.getElementById('features-section');
    const ctaSection = document.getElementById('cta-section');
    
    if (heroSection) observer.observe(heroSection);
    if (featuresSection) observer.observe(featuresSection);
    if (ctaSection) observer.observe(ctaSection);

    setTimeout(() => setHeroVisible(true), 300);

    return () => observer.disconnect();
  }, []);

  const navigationLinks = [
    { label: 'Statistical Toolkit', onClick: () => {
      document.getElementById('statistical-toolkit-section')?.scrollIntoView({ behavior: 'smooth' });
      setMobileMenuOpen(false);
    }},
    { label: 'Features', onClick: () => {
      document.getElementById('features-section')?.scrollIntoView({ behavior: 'smooth' });
      setMobileMenuOpen(false);
    }},
    { label: 'Pricing', onClick: () => {
      navigate('/pricing');
      setMobileMenuOpen(false);
    }},
    { label: 'Help', onClick: () => {
      navigate('/app#which-test');
      setMobileMenuOpen(false);
    }}
  ];

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <>
      <Helmet>
        <title>DataStatPro - Professional Statistical Analysis Made Simple</title>
        <meta name="description" content="Transform your data with professional statistical analysis tools. DataStatPro is an alternative to SPSS with beautiful visualizations, cross-platform support, and powerful data management." />
      </Helmet>
      
      <Box sx={{
        minHeight: '100vh',
        bgcolor: theme.palette.background.default,
        overflowX: 'hidden'
      }}>
        {/* IMPROVED HEADER */}
        <AppBar
          position="sticky"
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
          }}
        >
          <Toolbar sx={{ 
            px: { xs: 1, sm: 2, md: 3 },
            py: { xs: 1, sm: 1.5 },
            minHeight: { xs: 64, sm: 70 }
          }}>
            <Container maxWidth="lg" sx={{ px: { xs: 0 } }}>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'space-between',
                width: '100%'
              }}>
                {/* Logo Section */}
                <Box
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: { xs: 1, sm: 2 }, 
                    cursor: 'pointer',
                    flex: '0 0 auto'
                  }}
                  onClick={() => navigate('/app/dashboard')}
                  aria-label="Navigate to DataStatPro App"
                >
                  <Avatar
                    src="/logo.png"
                    alt="DataStatPro"
                    sx={{ 
                      width: { xs: 32, sm: 36, md: 40 }, 
                      height: { xs: 32, sm: 36, md: 40 }
                    }}
                  />
                  <Typography 
                    variant="h6" 
                    component="div" 
                    sx={{ 
                      fontWeight: 'bold',
                      color: 'white',
                      fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
                      display: { xs: isSmallMobile ? 'none' : 'block', sm: 'block' }
                    }}
                  >
                    DataStatPro
                  </Typography>
                </Box>

                {/* Desktop Navigation */}
                {!isMobile && (
                  <Stack direction="row" spacing={3} sx={{ flex: '1 1 auto', justifyContent: 'center' }}>
                    {navigationLinks.map((link, index) => (
                      <Link
                        key={index}
                        component="button"
                        onClick={link.onClick}
                        sx={{
                          color: 'white',
                          textDecoration: 'none',
                          fontWeight: 500,
                          fontSize: '0.95rem',
                          transition: 'opacity 0.2s',
                          '&:hover': {
                            opacity: 0.8,
                            textDecoration: 'underline'
                          }
                        }}
                      >
                        {link.label}
                      </Link>
                    ))}
                  </Stack>
                )}

                {/* Action Buttons */}
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: { xs: 0.5, sm: 1, md: 2 },
                  flex: '0 0 auto'
                }}>
                  {/* Mobile Menu Button */}
                  {isMobile && (
                    <IconButton
                      edge="start"
                      color="inherit"
                      aria-label="menu"
                      onClick={toggleMobileMenu}
                      sx={{ 
                        ml: 1,
                        p: { xs: 0.5, sm: 1 }
                      }}
                    >
                      <MenuIcon />
                    </IconButton>
                  )}

                  {/* Login/Signup Button */}
                  <Button
                    variant={isSmallMobile ? "text" : "outlined"}
                    onClick={() => navigate('/app/auth/login')}
                    startIcon={isSmallMobile ? <LoginIcon /> : undefined}
                    sx={{
                      color: 'white',
                      borderColor: isSmallMobile ? 'transparent' : 'white',
                      fontWeight: 'bold',
                      px: { xs: isSmallMobile ? 1 : 2, sm: 2.5, md: 3 },
                      py: { xs: 0.75, sm: 1 },
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
                      minWidth: isSmallMobile ? 'auto' : { xs: 100, sm: 120 },
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.1),
                        borderColor: isSmallMobile ? 'transparent' : alpha('#ffffff', 0.8),
                      },
                      transition: 'all 0.2s ease-in-out'
                    }}
                  >
                    {isSmallMobile ? '' : 'Login / Sign Up'}
                  </Button>

                  {/* Open App Button */}
                  <Button
                    variant="contained"
                    onClick={() => navigate('/app/dashboard')}
                    endIcon={!isSmallMobile && <ArrowForwardIcon />}
                    sx={{
                      bgcolor: 'white',
                      color: '#667eea',
                      fontWeight: 'bold',
                      px: { xs: 2, sm: 2.5, md: 3 },
                      py: { xs: 0.75, sm: 1 },
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: { xs: '0.85rem', sm: '0.9rem', md: '1rem' },
                      minWidth: { xs: 80, sm: 100 },
                      whiteSpace: 'nowrap',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.9),
                        transform: 'translateY(-1px)',
                        boxShadow: '0 6px 20px rgba(0,0,0,0.15)'
                      },
                      transition: 'all 0.2s ease-in-out'
                    }}
                  >
                    {isSmallMobile ? 'Open' : 'Open App'}
                  </Button>
                </Box>
              </Box>
            </Container>
          </Toolbar>
        </AppBar>

        {/* Mobile Menu Drawer */}
        <Drawer
          anchor="right"
          open={mobileMenuOpen}
          onClose={toggleMobileMenu}
          sx={{
            '& .MuiDrawer-paper': {
              width: { xs: 280, sm: 320 },
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            }
          }}
        >
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* Drawer Header */}
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              p: 2,
              borderBottom: '1px solid rgba(255,255,255,0.1)'
            }}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                Menu
              </Typography>
              <IconButton onClick={toggleMobileMenu} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </Box>

            {/* Navigation Links */}
            <List sx={{ flex: 1, py: 2 }}>
              {navigationLinks.map((link, index) => (
                <ListItem key={index} disablePadding>
                  <ListItemButton 
                    onClick={link.onClick}
                    sx={{
                      py: 2,
                      px: 3,
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.1)'
                      }
                    }}
                  >
                    <ListItemText 
                      primary={link.label} 
                      primaryTypographyProps={{
                        sx: { 
                          color: 'white',
                          fontSize: '1.1rem',
                          fontWeight: 500
                        }
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>

            <Divider sx={{ borderColor: 'rgba(255,255,255,0.2)' }} />

            {/* Action Buttons in Drawer */}
            <Box sx={{ p: 3 }}>
              <Stack spacing={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => {
                    navigate('/app/auth/login');
                    setMobileMenuOpen(false);
                  }}
                  startIcon={<AccountCircleIcon />}
                  sx={{
                    color: 'white',
                    borderColor: 'white',
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    '&:hover': {
                      bgcolor: 'rgba(255,255,255,0.1)',
                      borderColor: 'white'
                    }
                  }}
                >
                  Login / Sign Up
                </Button>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={() => {
                    navigate('/app');
                    setMobileMenuOpen(false);
                  }}
                  endIcon={<ArrowForwardIcon />}
                  sx={{
                    bgcolor: 'white',
                    color: '#667eea',
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    '&:hover': {
                      bgcolor: alpha('#ffffff', 0.9)
                    }
                  }}
                >
                  Open App
                </Button>
              </Stack>
            </Box>
          </Box>
        </Drawer>

        {/* HERO SECTION */}
        <Box
          id="hero-section"
          sx={{
            background: theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
              : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            py: { xs: 8, md: 12 },
            position: 'relative',
            minHeight: '80vh',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          {/* BG Decoration */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: theme.palette.mode === 'dark'
                ? 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")'
                : 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              opacity: 0.5
            }}
          />
          <Container maxWidth="lg">
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <Fade in={heroVisible} timeout={800}>
                  <Stack spacing={4}>
                    <Slide direction="up" in={heroVisible} timeout={1000}>
                      <Chip
                        label="🚀 Professional Statistical Analysis"
                        sx={{
                          bgcolor: alpha('#667eea', theme.palette.mode === 'dark' ? 0.2 : 0.1),
                          color: theme.palette.mode === 'dark' ? '#8fa4f3' : '#667eea',
                          fontWeight: 'bold',
                          alignSelf: 'flex-start',
                          px: 2,
                          py: 1
                        }}
                      />
                    </Slide>
                    <Slide direction="up" in={heroVisible} timeout={1200}>
                      <Typography
                        variant={isMobile ? 'h3' : 'h2'}
                        component="h1"
                        sx={{
                          fontWeight: 800,
                          color: theme.palette.text.primary,
                          lineHeight: 1.2,
                          fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' }
                        }}
                      >
                        DataStatPro
                        <Box component="span" sx={{
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          display: 'block',
                          fontSize: '0.6em'
                        }}>
                          Statistical Analysis Simplified
                        </Box>
                      </Typography>
                    </Slide>
                    <Slide direction="up" in={heroVisible} timeout={1400}>
                      <Typography
                        variant="h5"
                        sx={{
                          maxWidth: 500,
                          lineHeight: 1.6,
                          fontSize: { xs: '1.1rem', md: '1.3rem' },
                          color: theme.palette.mode === 'dark'
                            ? theme.palette.text.primary
                            : theme.palette.text.secondary
                        }}
                      >
                        An affordable alternative to expensive statistical softwares — absolutely free for teaching and learning purposes. Gain insights quickly with built-in interpretations, beautiful visualizations, and seamless cross-platform support.
                      </Typography>
                    </Slide>
                    <Slide direction="up" in={heroVisible} timeout={1600}>
                      <Stack 
                        direction={isMobile ? 'column' : 'row'} 
                        spacing={2}
                        sx={{ width: '100%' }}
                      >
                        <Button
                          variant="contained"
                          size="large"
                          onClick={() => navigate('/app/dashboard')}
                          endIcon={<ArrowForwardIcon />}
                          fullWidth={isMobile}
                          sx={{
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            py: { xs: 1.5, sm: 2 },
                            px: { xs: 3, sm: 4 },
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            fontWeight: 'bold',
                            borderRadius: 2,
                            textTransform: 'none',
                            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)'
                            },
                            transition: 'all 0.3s ease-in-out'
                          }}
                        >
                          Start Analyzing Now
                        </Button>
                        <Button
                          variant="outlined"
                          size="large"
                          onClick={() => navigate('/app#assistant')}
                          fullWidth={isMobile}
                          sx={{
                            py: { xs: 1.5, sm: 2 },
                            px: { xs: 3, sm: 4 },
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            fontWeight: 'bold',
                            borderRadius: 2,
                            textTransform: 'none',
                            borderColor: '#667eea',
                            color: '#667eea',
                            '&:hover': {
                              bgcolor: alpha('#667eea', 0.05),
                              borderColor: '#667eea',
                              transform: 'translateY(-1px)'
                            },
                            transition: 'all 0.2s ease-in-out'
                          }}
                        >
                          AI-Powered Analysis Assistant
                        </Button>
                      </Stack>
                    </Slide>
                  </Stack>
                </Fade>
              </Grid>
              <Grid item xs={12} md={6}>
                <Grow in={heroVisible} timeout={1800}>
                  <Box
                    sx={{
                      position: 'relative',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        width: { xs: '100%', md: '500px' },
                        maxWidth: '500px',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: -30,
                          left: -30,
                          right: -30,
                          bottom: -30,
                          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                          borderRadius: 4,
                          zIndex: -1,
                          filter: 'blur(20px)'
                        }
                      }}
                    >
                      <Paper
                        elevation={20}
                        sx={{
                          p: 1,
                          borderRadius: 3,
                          bgcolor: '#1a1a1a',
                          position: 'relative',
                          overflow: 'hidden',
                          boxShadow: '0 25px 50px rgba(0,0,0,0.25)'
                        }}
                      >
                        <Box
                          sx={{
                            bgcolor: '#f8f9fa',
                            borderRadius: 2,
                            p: 2,
                            minHeight: { xs: 200, md: 300 },
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            color: 'white',
                            position: 'relative'
                          }}
                        >
                          <Box sx={{ textAlign: 'center', width: '100%' }}>
                            <Typography variant="h3" component="p" sx={{ mb: 2, fontWeight: 'bold', color: 'white', lineHeight: 1.3 }}>
                              DataStatPro
                              <br />
                              <Typography variant="h4" component="span" sx={{ fontWeight: 'normal', opacity: 0.9 }}>
                                Statistical Analysis on the Go
                              </Typography>
                            </Typography>
                            <Grid container spacing={1} sx={{ mb: 2 }}>
                              {[
                                { icon: <DesktopMac />, label: 'Desktop' },
                                { icon: <Laptop />, label: 'Laptop' },
                                { icon: <TabletMac />, label: 'Tablet' },
                                { icon: <PhoneIphone />, label: 'Mobile' },
                              ].map((item, index) => (
                                <Grid item xs={6} key={index}>
                                  <Paper
                                    sx={{
                                      p: 1,
                                      bgcolor: theme.palette.background.paper, // Use theme-aware background
                                      borderRadius: 1,
                                      transition: 'all 0.2s ease-in-out',
                                      '&:hover': {
                                        bgcolor: theme.palette.action.hover, // Use theme-aware hover background
                                        transform: 'translateY(-2px)',
                                      },
                                      textAlign: 'center',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: 1,
                                    }}
                                  >
                                    {React.cloneElement(item.icon, { sx: { color: theme.palette.text.primary } })} {/* Set icon color */}
                                    <Typography variant="caption" color="text.primary"> {/* Use primary text color */}
                                      {item.label}
                                    </Typography>
                                  </Paper>
                                </Grid>
                              ))}
                            </Grid>
                            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                              <BarChartIcon sx={{ fontSize: 20 }} />
                              <PieChartIcon sx={{ fontSize: 20 }} />
                              <FunctionsIcon sx={{ fontSize: 20 }} />
                            </Box>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            height: 20,
                            bgcolor: '#1a1a1a',
                            borderRadius: '0 0 8px 8px',
                            position: 'relative',
                            '&::after': {
                              content: '""',
                              position: 'absolute',
                              bottom: -10,
                              left: '50%',
                              transform: 'translateX(-50%)',
                              width: 60,
                              height: 4,
                              bgcolor: '#333',
                              borderRadius: 2
                            }
                          }}
                        />
                      </Paper>
                    </Box>
                  </Box>
                </Grow>
              </Grid>
            </Grid>
          </Container>
        </Box>

        {/* FEATURES SECTION */}
        <Box id="features-section" sx={{ py: { xs: 8, md: 12 }, bgcolor: 'background.default' }}>
          <Container maxWidth="lg">
            <Fade in={featuresVisible} timeout={800}>
              <Box textAlign="center" mb={8}>
                <Typography 
                  variant="h3" 
                  component="h2" 
                  gutterBottom 
                  sx={{
                    fontWeight: 800,
                    color: 'text.primary',
                    fontSize: { xs: '2.5rem', md: '3rem' }
                  }}
                >
                  Streamlined Workflow, Publication-Ready Results
                </Typography>
                <Typography 
                  variant="h6" 
                  color="text.secondary" 
                  sx={{
                    maxWidth: 700,
                    mx: 'auto',
                    lineHeight: 1.6
                  }}
                >
                  From data import to publication-ready visualizations, 
                  DataStatPro provides everything you need for professional statistical analysis.
                </Typography>
              </Box>
            </Fade>
            <Grid container spacing={6}>
              {features.map((feature, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Slide 
                    direction={index % 2 === 0 ? 'right' : 'left'} 
                    in={featuresVisible} 
                    timeout={1000 + index * 200}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 3,
                        border: 'none',
                        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                        }
                      }}
                    >
                      <CardContent sx={{ p: 4 }}>
                        <Grid container spacing={3} alignItems="center">
                          <Grid item xs={12} sm={6}>
                            <Stack spacing={2}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                {feature.icon}
                                <Typography
                                  variant="h5"
                                  fontWeight="bold"
                                  color="text.primary"
                                >
                                  {feature.title}
                                </Typography>
                              </Box>
                              <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{ lineHeight: 1.6 }}
                              >
                                {feature.description}
                              </Typography>
                              <Button
                                variant="text"
                                size="small"
                                onClick={() => navigate('/app/dashboard')}
                                endIcon={<ArrowForwardIcon />}
                                sx={{
                                  alignSelf: 'flex-start',
                                  color: '#667eea',
                                  fontWeight: 'bold',
                                  textTransform: 'none',
                                  '&:hover': {
                                    bgcolor: alpha('#667eea', 0.05),
                                    textDecoration: 'underline',
                                  },
                                }}
                              >
                                Learn More
                              </Button>
                            </Stack>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Paper
                              sx={{
                                p: 2,
                                borderRadius: 2,
                                bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : '#f8f9fa',
                                minHeight: 120,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                border: `2px dashed ${theme.palette.mode === 'dark' ? 'grey.600' : '#e2e8f0'}`,
                                overflow: 'hidden',
                              }}
                            >
                              <img
                                src={feature.image}
                                alt={`${feature.title} feature illustration`}
                                style={{
                                  width: '100%',
                                  height: 'auto',
                                  objectFit: 'cover',
                                  borderRadius: '8px'
                                }}
                              />
                            </Paper>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Slide>
                </Grid>
              ))}
            </Grid>
          </Container>
        </Box>

        {/* STATISTICAL FEATURES SHOWCASE SECTION */}
        <Box id="statistical-toolkit-section" sx={{ py: { xs: 8, md: 12 }, bgcolor: '#f8fafc' }}>
          <Container maxWidth="lg">
            <Box textAlign="center" mb={8}>
              <Typography
                variant="h3" 
                component="h2" 
                gutterBottom 
                sx={{ 
                  fontWeight: 800,
                  color: '#2D3748',
                  fontSize: { xs: '2.5rem', md: '3rem' }
                }}
              >
                Comprehensive Statistical Toolkit
              </Typography>
              <Typography 
                variant="h6" 
                color="text.secondary" 
                sx={{
                  maxWidth: 700,
                  mx: 'auto',
                  lineHeight: 1.6
                }}
              >
                Everything you need for comprehensive data analysis in one place
              </Typography>
            </Box>
            <Grid container spacing={4}>
              {/* Inferential Statistics */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#667eea' }}>
                        <FunctionsIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Inferential Statistics
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Comprehensive suite of statistical tests
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['t-Tests', 'ANOVA', 'Non-parametric Tests', 'Repeated Measures','Assuptions Checks'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#667eea' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#667eea',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#667eea', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Data Visualization */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#FF6B6B' }}>
                        <BarChartIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Data Visualization
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Beautiful, interactive charts and plots
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['Bar & Pie Charts', 'Histograms', 'Box Plots', 'Scatter Plots', 'Rain Cloud Plots'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#FF6B6B' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#FF6B6B',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#FF6B6B', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Correlation Analysis */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#7B68EE' }}>
                        <AnalyticsIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Correlation Analysis
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Explore relationships between variables
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['Correlation Matrix', 'Linear Regression', 'Logistic Regression', 'Pearson', 'Spearman'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#7B68EE' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#7B68EE',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#7B68EE', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Specialized Calculators */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#50C878' }}>
                        <SpeedIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Sample Size / Power
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Tools for research planning and analysis
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['One Group Scenarios', 'Two Group Scenarios', 'Paired Sample Scenarios', 'More than two groups'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#50C878' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#50C878',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#50C878', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Epidemiological Studies */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#4A90E2' }}>
                        <PublicIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Epidemiological Studies
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Analyze health-related states or events
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['Cross Sectional', 'Case Control', 'Cohort', 'Matched Case Control'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#4A90E2' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#4A90E2',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#4A90E2', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Survival Analysis */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#FF9800' }}> {/* Using a different color */}
                        <TimelineIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Survival Analysis
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Analyze time-to-event data
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['KM Survival Estimates', 'Kaplan Survival Curves', 'Log-Rank Test', 'Cox-Regression'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#FF9800' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#FF9800',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#FF9800', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Reliability Analysis */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#9C27B0' }}> {/* Using a different color */}
                        <CheckCircleOutlineIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Reliability Analysis
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Assess the consistency of measurements
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['Cronbach\'s Alpha', 'Cohen\'s Kappa/ Fleiss Kappa', 'Kendall\'s Tau/ Kendall\'s W', 'Intraclass Correlation Coefficient'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#9C27B0' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#9C27B0',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#9C27B0', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              {/* Pivot Analysis */}
              <Grid item xs={12} md={6} lg={3}>
                <Card sx={{
                  height: '100%',
                  borderRadius: 3,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                  }
                }}>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{ mb: 2, color: '#00BCD4' }}> {/* Using a different color */}
                        <GridOnIcon sx={{ fontSize: 48 }} />
                      </Box>
                      <Typography variant="h5" component="h3" fontWeight="bold" gutterBottom>
                        Pivot Analysis
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Summarize and analyze data with pivot tables
                      </Typography>
                      <Box sx={{ mt: 'auto' }}>
                        <Stack spacing={1}>
                          {['Pivot Tables', 'Pivot Charts', 'Google Sheet Integration', 'Hierarchical Grouping'].map((item, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ArrowForwardIcon sx={{ fontSize: 16, color: '#00BCD4' }} />
                              <Typography variant="body2">{item}</Typography>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => navigate('/app/dashboard')}
                        endIcon={<ArrowForwardIcon />}
                        sx={{
                          alignSelf: 'flex-start',
                          color: '#00BCD4',
                          fontWeight: 'bold',
                          textTransform: 'none',
                          mt: 2,
                          '&:hover': {
                            bgcolor: alpha('#00BCD4', 0.05),
                            textDecoration: 'underline',
                          },
                        }}
                      >
                        Learn More
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
            <Box sx={{ textAlign: 'center', mt: 6 }}>
              <Button
                variant="outlined"
                size="large"
                onClick={() => navigate('/app/dashboard')}
                endIcon={<ArrowForwardIcon />}
                sx={{
                  borderColor: '#667eea',
                  color: '#667eea',
                  py: 1.5,
                  px: 4,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  borderRadius: 2,
                  textTransform: 'none',
                  '&:hover': {
                    bgcolor: alpha('#667eea', 0.05),
                    borderColor: '#667eea',
                    transform: 'translateY(-1px)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                Explore All Features
              </Button>
            </Box>
          </Container>
        </Box>

        {/* TESTIMONIALS */}
        <Box sx={{ py: { xs: 8, md: 12 }, bgcolor: '#e0e0e0' }}>
          <Container maxWidth="lg">
            <Box textAlign="center" mb={8}>
              <Typography
                variant="h3"
                component="h2"
                gutterBottom
                sx={{
                  fontWeight: 800,
                  color: '#2D3748',
                  fontSize: { xs: '2.5rem', md: '3rem' }
                }}
              >
                Trusted by Researchers and Professionals
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  maxWidth: 700,
                  mx: 'auto',
                  lineHeight: 1.6
                }}
              >
                Hear from our satisfied users
              </Typography>
            </Box>
            <Box>
              <Slider {...sliderSettings}>
                {testimonials.map(({ quote, name, title }, idx) => (
                  <Box sx={{ px: 2 }} key={idx}>
                  <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                    <CardContent sx={{ p: 4 }}>
                      <Typography variant="body1" color="text.secondary" sx={{ fontStyle: 'italic', mb: 2 }}>
                        "{quote}"
                      </Typography>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar {...stringAvatar(name)} />
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {title}
                          </Typography>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Box>
              ))}
            </Slider>
          </Box>
          </Container>
        </Box>

        {/* PRICING PREVIEW SECTION */}
        <Box sx={{ py: { xs: 8, md: 12 }, bgcolor: 'background.default' }}>
          <Container maxWidth="lg">
            <Box textAlign="center" mb={6}>
              <Typography
                variant="h3"
                component="h2"
                fontWeight="bold"
                sx={{
                  mb: 2,
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Choose Your Plan
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{ mb: 4, maxWidth: '600px', mx: 'auto' }}
              >
                Start free and upgrade as your needs grow
              </Typography>
            </Box>

            <Grid container spacing={4} justifyContent="center">
              {/* Guest Access */}
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 3,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(76, 175, 80, 0.15)',
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        bgcolor: alpha('#4caf50', 0.1),
                        color: '#4caf50',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      <PersonIcon sx={{ fontSize: 24 }} />
                    </Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Guest Access
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="#4caf50" sx={{ mb: 1 }}>
                      Free
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Perfect for exploring and learning
                    </Typography>
                    <Stack spacing={1} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#4caf50' }} />
                        <Typography variant="body2">Full app exploration</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#4caf50' }} />
                        <Typography variant="body2">Sample datasets</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CancelIcon sx={{ fontSize: 16, color: 'text.disabled' }} />
                        <Typography variant="body2" color="text.disabled">Personal data import</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => navigate('/app/dashboard')}
                      sx={{
                        borderColor: '#4caf50',
                        color: '#4caf50',
                        '&:hover': {
                          bgcolor: alpha('#4caf50', 0.1),
                        }
                      }}
                    >
                      Start Exploring
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              {/* Standard Account */}
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 3,
                    border: '2px solid #2196f3',
                    position: 'relative',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(33, 150, 243, 0.15)',
                    }
                  }}
                >
                  <Chip
                    label="Most Popular"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      bgcolor: '#2196f3',
                      color: 'white',
                      fontWeight: 'bold',
                      fontSize: '0.75rem'
                    }}
                  />
                  <CardContent sx={{ flexGrow: 1, p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        bgcolor: alpha('#2196f3', 0.1),
                        color: '#2196f3',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      <BusinessIcon sx={{ fontSize: 24 }} />
                    </Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Standard Account
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="#2196f3" sx={{ mb: 1 }}>
                      Free
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ mb: 3, display: 'block' }}>
                      Currently free during development
                    </Typography>
                    <Stack spacing={1} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#2196f3' }} />
                        <Typography variant="body2">All Guest features</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#2196f3' }} />
                        <Typography variant="body2">Personal data import</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#2196f3' }} />
                        <Typography variant="body2">Local data storage</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={() => navigate('/app#/auth/login')}
                      sx={{
                        bgcolor: '#2196f3',
                        '&:hover': {
                          bgcolor: alpha('#2196f3', 0.8),
                        }
                      }}
                    >
                      Create Account
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              {/* Pro Account */}
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 3,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(255, 152, 0, 0.15)',
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        bgcolor: alpha('#ff9800', 0.1),
                        color: '#ff9800',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      <StarIcon sx={{ fontSize: 24 }} />
                    </Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Pro Account
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="#ff9800" sx={{ mb: 1 }}>
                      $10
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ mb: 3, display: 'block' }}>
                      per month
                    </Typography>
                    <Stack spacing={1} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#ff9800' }} />
                        <Typography variant="body2">All Standard features</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#ff9800' }} />
                        <Typography variant="body2">Pro analysis features</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#ff9800' }} />
                        <Typography variant="body2">Cloud storage</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => window.open('mailto:<EMAIL>?subject=Early Access Request - DataStatPro Pro Account&body=Hi DataStatPro Team,%0D%0A%0D%0AI would like to request early access to Pro account features during the development phase.%0D%0A%0D%0AMy use case:%0D%0A[Please describe how you plan to use DataStatPro]%0D%0A%0D%0ASpecific features I\'m interested in:%0D%0A- Advanced statistical methods%0D%0A- Cloud data storage%0D%0A- Multi-device synchronization%0D%0A- [Add other features you\'re interested in]%0D%0A%0D%0AThank you for considering my request.%0D%0A%0D%0ABest regards')}
                      sx={{
                        borderColor: '#ff9800',
                        color: '#ff9800',
                        '&:hover': {
                          bgcolor: alpha('#ff9800', 0.1),
                        }
                      }}
                    >
                      Request Early Access
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              {/* Educational Account */}
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: 3,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 12px 40px rgba(156, 39, 176, 0.15)',
                    }
                  }}
                >
                  <Chip
                    label="Advanced Analysis Free"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      bgcolor: '#9c27b0',
                      color: 'white',
                      fontWeight: 'bold',
                      fontSize: '0.75rem'
                    }}
                  />
                  <CardContent sx={{ flexGrow: 1, p: 3, textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        bgcolor: alpha('#9c27b0', 0.1),
                        color: '#9c27b0',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2
                      }}
                    >
                      <SchoolIcon sx={{ fontSize: 24 }} />
                    </Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Educational Account
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="#9c27b0" sx={{ mb: 1 }}>
                      Free
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ mb: 3, display: 'block' }}>
                      for .edu emails
                    </Typography>
                    <Stack spacing={1} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#9c27b0' }} />
                        <Typography variant="body2">All Standard features</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#9c27b0' }} />
                        <Typography variant="body2">Advanced Analysis FREE</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#9c27b0' }} />
                        <Typography variant="body2">Advanced statistical tests</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckIcon sx={{ fontSize: 16, color: '#9c27b0' }} />
                        <Typography variant="body2">Interactive visualizations</Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      onClick={() => navigate('/app#/auth/login')}
                      sx={{
                        borderColor: '#9c27b0',
                        color: '#9c27b0',
                        '&:hover': {
                          bgcolor: alpha('#9c27b0', 0.1),
                        }
                      }}
                    >
                      Create Educational Account
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            </Grid>

            {/* View Full Pricing Link */}
            <Box textAlign="center" sx={{ mt: 6 }}>
              <Button
                variant="text"
                size="large"
                onClick={() => navigate('/pricing')}
                endIcon={<ArrowForwardIcon />}
                sx={{
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  color: '#667eea',
                  '&:hover': {
                    bgcolor: alpha('#667eea', 0.1),
                  }
                }}
              >
                View Detailed Pricing & Features
              </Button>
            </Box>
          </Container>
        </Box>

        {/* CTA SECTION */}
        <Box
          id="cta-section"
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            py: { xs: 8, md: 12 },
            position: 'relative',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
              opacity: 0.3
            }}
          />
          <Container maxWidth="md" sx={{ textAlign: 'center', position: 'relative' }}>
            <Fade in={ctaVisible} timeout={800}>
              <Stack spacing={4} sx={{ textAlign: 'center' }}>
                <Typography
                  variant="h3"
                  component="h2"
                  fontWeight="bold"
                  sx={{ fontSize: { xs: '2.5rem', md: '3rem' } }}
                >
                  Ready to Transform Your Data?
                </Typography>
                <Typography 
                  variant="h6"
                  sx={{
                    opacity: 0.9,
                    mx: 'auto',
                    lineHeight: 1.6
                  }}
                >
                  Join thousands of researchers, students, and professionals who trust DataStatPro 
                  for their statistical analysis needs. Start your journey today.
                </Typography>
                <Box>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/app/dashboard')}
                    endIcon={<ArrowForwardIcon />}
                    sx={{
                      bgcolor: 'white',
                      color: '#667eea',
                      py: 2,
                      px: 6,
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      borderRadius: 2,
                      textTransform: 'none',
                      boxShadow: '0 8px 25px rgba(0,0,0,0.2)',
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.95),
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 35px rgba(0,0,0,0.3)'
                      },
                      transition: 'all 0.3s ease-in-out'
                    }}
                  >
                    Start Your Free Analysis
                  </Button>
                </Box>
              </Stack>
            </Fade>
          </Container>
        </Box>

        {/* Scroll to Top Button */}
        {showScrollButton && (
          <Button
            variant="contained"
            onClick={scrollToTop}
            sx={{
              position: 'fixed',
              bottom: 20,
              right: 20,
              bgcolor: '#667eea',
              color: 'white',
              borderRadius: '50%',
              width: 56,
              height: 56,
              minWidth: 0,
              boxShadow: '0 4px 10px rgba(0,0,0,0.2)',
              '&:hover': {
                bgcolor: '#764ba2',
                boxShadow: '0 8px 15px rgba(0,0,0,0.3)',
              },
              zIndex: 1000,
            }}
            aria-label="scroll to top"
          >
            ↑
          </Button>
        )}

        {/* FOOTER */}
        <Box
          component="footer"
          sx={{
            py: 6,
            px: 3,
            bgcolor: '#2D3748',
            color: 'white'
          }}
        >
          <Container maxWidth="lg">
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Avatar src="/logo.png" alt="DataStatPro" sx={{ width: 40, height: 40 }} />
                  <Typography variant="h6" fontWeight="bold">
                    DataStatPro
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ opacity: 0.8, lineHeight: 1.6 }}>
                  Making statistical analysis accessible to everyone. 
                  Professional tools, beautiful visualizations, completely free for educational purposes.
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Stack spacing={2} alignItems={isMobile ? 'flex-start' : 'flex-end'}>
                  <Stack direction="row" spacing={3} flexWrap="wrap">
                    {navigationLinks.map((link, index) => (
                      <Link
                        key={index}
                        component="button"
                        onClick={link.onClick}
                        sx={{
                          color: 'white',
                          textDecoration: 'none',
                          opacity: 0.8,
                          '&:hover': {
                            opacity: 1,
                            textDecoration: 'underline'
                          }
                        }}
                      >
                        {link.label}
                      </Link>
                    ))}
                    <Link
                      href="/privacy"
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        color: 'white',
                        textDecoration: 'none',
                        opacity: 0.8,
                        '&:hover': {
                          opacity: 1,
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Privacy Policy
                    </Link>
                    <Link
                      href="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        color: 'white',
                        textDecoration: 'none',
                        opacity: 0.8,
                        '&:hover': {
                          opacity: 1,
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Terms of Service
                    </Link>
                  </Stack>
                  <Stack direction="row" spacing={2} sx={{ color: 'white', opacity: 0.8 }}>
                    <Link href="mailto:<EMAIL>" color="inherit" sx={{ '&:hover': { opacity: 1 } }}>
                      <Email />
                    </Link>
                    <Link href="https://www.facebook.com/nadeem.shafique.pk" target="_blank" rel="noopener noreferrer" color="inherit" sx={{ '&:hover': { opacity: 1 } }}>
                      <Facebook />
                    </Link>
                    <Link href="https://twitter.com/nadeemshafique" target="_blank" rel="noopener noreferrer" color="inherit" sx={{ '&:hover': { opacity: 1 } }}>
                      <Twitter />
                    </Link>
                    <Link href="https://www.linkedin.com/company/yourcompany" target="_blank" rel="noopener noreferrer" color="inherit" sx={{ '&:hover': { opacity: 1 } }}>
                      <LinkedIn />
                    </Link>
                  </Stack>
                  <Typography variant="body2" sx={{ opacity: 0.6 }}>
                    © 2024 DataStatPro. All rights reserved.
                  </Typography>
                </Stack>
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>
    </>
  );
};

export default LandingPage;
