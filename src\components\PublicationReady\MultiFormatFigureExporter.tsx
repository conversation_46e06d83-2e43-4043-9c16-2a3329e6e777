import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  Paper,
  useTheme,
  alpha,
  FormControlLabel,
  Switch,
  Slider,
  TextField,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
} from '@mui/material';
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  Image as ImageIcon,
  Palette as PaletteIcon,
  Settings as SettingsIcon,
  Preview as PreviewIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface ExportSettings {
  format: 'png' | 'jpg' | 'pdf' | 'svg' | 'tiff' | 'eps';
  dpi: number;
  width: number;
  height: number;
  colorMode: 'color' | 'grayscale' | 'blackwhite';
  quality: number; // For JPG
  transparent: boolean; // For PNG
  compression: 'none' | 'lzw' | 'zip'; // For TIFF
}

interface FigureFile {
  id: string;
  name: string;
  file: File;
  preview: string;
  originalSize: { width: number; height: number };
  uploadedAt: Date;
}

const MultiFormatFigureExporter: React.FC = () => {
  const theme = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [figures, setFigures] = useState<FigureFile[]>([]);
  const [selectedFigure, setSelectedFigure] = useState<FigureFile | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [processedPreview, setProcessedPreview] = useState<string | null>(null);
  
  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    format: 'png',
    dpi: 300,
    width: 1200,
    height: 800,
    colorMode: 'color',
    quality: 95,
    transparent: false,
    compression: 'none',
  });

  const formatOptions = [
    { value: 'png', label: 'PNG', description: 'Best for web, supports transparency' },
    { value: 'jpg', label: 'JPEG', description: 'Smaller file size, good for photos' },
    { value: 'pdf', label: 'PDF', description: 'Vector format, scalable' },
    { value: 'svg', label: 'SVG', description: 'Vector format, web-friendly' },
    { value: 'tiff', label: 'TIFF', description: 'High quality, publication standard' },
    { value: 'eps', label: 'EPS', description: 'Vector format for print' },
  ];

  const dpiPresets = [
    { value: 72, label: '72 DPI (Web)' },
    { value: 150, label: '150 DPI (Draft)' },
    { value: 300, label: '300 DPI (Print)' },
    { value: 600, label: '600 DPI (High Quality)' },
    { value: 1200, label: '1200 DPI (Professional)' },
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = new Image();
          img.onload = () => {
            const figureFile: FigureFile = {
              id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
              name: file.name,
              file,
              preview: e.target?.result as string,
              originalSize: { width: img.width, height: img.height },
              uploadedAt: new Date(),
            };
            setFigures(prev => [...prev, figureFile]);
          };
          img.src = e.target?.result as string;
        };
        reader.readAsDataURL(file);
      }
    });
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const processImage = useCallback(async (figure: FigureFile, settings: ExportSettings): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      const img = new Image();
      img.onload = () => {
        // Set canvas size based on settings
        canvas.width = settings.width;
        canvas.height = settings.height;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw image
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        // Apply color mode transformations
        if (settings.colorMode !== 'color') {
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;
          
          for (let i = 0; i < data.length; i += 4) {
            if (settings.colorMode === 'grayscale') {
              // Convert to grayscale using luminance formula
              const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
              data[i] = gray;     // Red
              data[i + 1] = gray; // Green
              data[i + 2] = gray; // Blue
            } else if (settings.colorMode === 'blackwhite') {
              // Convert to black and white using threshold
              const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
              const bw = gray > 128 ? 255 : 0;
              data[i] = bw;     // Red
              data[i + 1] = bw; // Green
              data[i + 2] = bw; // Blue
            }
          }
          
          ctx.putImageData(imageData, 0, 0);
        }
        
        // Convert to data URL
        let dataUrl: string;
        if (settings.format === 'jpg') {
          dataUrl = canvas.toDataURL('image/jpeg', settings.quality / 100);
        } else {
          dataUrl = canvas.toDataURL('image/png');
        }
        
        resolve(dataUrl);
      };
      
      img.src = figure.preview;
    });
  }, []);

  const generatePreview = async () => {
    if (!selectedFigure) return;
    
    setIsProcessing(true);
    try {
      const preview = await processImage(selectedFigure, exportSettings);
      setProcessedPreview(preview);
      setPreviewDialogOpen(true);
    } catch (error) {
      console.error('Preview generation failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const exportFigure = async (figure: FigureFile, settings: ExportSettings) => {
    setIsProcessing(true);
    try {
      const processedDataUrl = await processImage(figure, settings);
      
      // Create download link
      const link = document.createElement('a');
      link.download = `${figure.name.split('.')[0]}_${settings.format}_${settings.dpi}dpi_${settings.colorMode}.${settings.format}`;
      link.href = processedDataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const exportAllFigures = async () => {
    setIsProcessing(true);
    try {
      for (const figure of figures) {
        await exportFigure(figure, exportSettings);
        // Small delay between exports
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const deleteFigure = (id: string) => {
    setFigures(prev => prev.filter(f => f.id !== id));
    if (selectedFigure?.id === id) {
      setSelectedFigure(null);
    }
  };

  const getFileSizeEstimate = (settings: ExportSettings): string => {
    const pixels = settings.width * settings.height;
    let sizeKB: number;
    
    switch (settings.format) {
      case 'png':
        sizeKB = pixels * (settings.colorMode === 'color' ? 4 : 1) / 1024;
        break;
      case 'jpg':
        sizeKB = pixels * 0.1 * (settings.quality / 100);
        break;
      case 'tiff':
        sizeKB = pixels * (settings.colorMode === 'color' ? 4 : 1) / 1024;
        break;
      default:
        sizeKB = pixels * 0.5 / 1024;
    }
    
    if (sizeKB > 1024) {
      return `~${(sizeKB / 1024).toFixed(1)} MB`;
    }
    return `~${sizeKB.toFixed(0)} KB`;
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
            Multi-Format Figure Exporter
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Convert figures to publication-ready formats with color/grayscale options and custom DPI settings
          </Typography>
        </Box>

        {/* Upload Section */}
        <Card elevation={2} sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Upload Figures
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />
              <Button
                variant="contained"
                startIcon={<UploadIcon />}
                onClick={() => fileInputRef.current?.click()}
              >
                Upload Images
              </Button>
              <Typography variant="body2" color="text.secondary">
                Supported formats: PNG, JPEG, GIF, BMP, WebP
              </Typography>
            </Box>
            
            {figures.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Chip label={`${figures.length} figures uploaded`} color="primary" variant="outlined" />
              </Box>
            )}
          </CardContent>
        </Card>

        <Grid container spacing={3}>
          {/* Export Settings */}
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SettingsIcon color="primary" />
                  Export Settings
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Output Format</InputLabel>
                      <Select
                        value={exportSettings.format}
                        label="Output Format"
                        onChange={(e) => setExportSettings(prev => ({ ...prev, format: e.target.value as ExportSettings['format'] }))}
                      >
                        {formatOptions.map(option => (
                          <MenuItem key={option.value} value={option.value}>
                            <Box>
                              <Typography variant="body2">{option.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>DPI</InputLabel>
                      <Select
                        value={exportSettings.dpi}
                        label="DPI"
                        onChange={(e) => setExportSettings(prev => ({ ...prev, dpi: e.target.value as number }))}
                      >
                        {dpiPresets.map(preset => (
                          <MenuItem key={preset.value} value={preset.value}>
                            {preset.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Color Mode</InputLabel>
                      <Select
                        value={exportSettings.colorMode}
                        label="Color Mode"
                        onChange={(e) => setExportSettings(prev => ({ ...prev, colorMode: e.target.value as ExportSettings['colorMode'] }))}
                      >
                        <MenuItem value="color">Full Color</MenuItem>
                        <MenuItem value="grayscale">Grayscale</MenuItem>
                        <MenuItem value="blackwhite">Black & White</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Width (px)"
                      type="number"
                      value={exportSettings.width}
                      onChange={(e) => setExportSettings(prev => ({ ...prev, width: parseInt(e.target.value) || 1200 }))}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Height (px)"
                      type="number"
                      value={exportSettings.height}
                      onChange={(e) => setExportSettings(prev => ({ ...prev, height: parseInt(e.target.value) || 800 }))}
                    />
                  </Grid>
                  
                  {/* Format-specific options */}
                  {exportSettings.format === 'jpg' && (
                    <Grid item xs={12}>
                      <Typography gutterBottom>Quality: {exportSettings.quality}%</Typography>
                      <Slider
                        value={exportSettings.quality}
                        onChange={(_, value) => setExportSettings(prev => ({ ...prev, quality: value as number }))}
                        min={10}
                        max={100}
                        step={5}
                        marks
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                  )}
                  
                  {exportSettings.format === 'png' && (
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={exportSettings.transparent}
                            onChange={(e) => setExportSettings(prev => ({ ...prev, transparent: e.target.checked }))}
                          />
                        }
                        label="Transparent Background"
                      />
                    </Grid>
                  )}
                  
                  {exportSettings.format === 'tiff' && (
                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <InputLabel>Compression</InputLabel>
                        <Select
                          value={exportSettings.compression}
                          label="Compression"
                          onChange={(e) => setExportSettings(prev => ({ ...prev, compression: e.target.value as ExportSettings['compression'] }))}
                        >
                          <MenuItem value="none">None</MenuItem>
                          <MenuItem value="lzw">LZW</MenuItem>
                          <MenuItem value="zip">ZIP</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  )}
                </Grid>
                
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Estimated file size: {getFileSizeEstimate(exportSettings)}
                  </Typography>
                  <Chip 
                    icon={<InfoIcon />}
                    label={`${exportSettings.width}×${exportSettings.height} @ ${exportSettings.dpi} DPI`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Figure List and Actions */}
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Uploaded Figures
                </Typography>
                
                {figures.length === 0 ? (
                  <Alert severity="info">
                    No figures uploaded yet. Upload images to get started.
                  </Alert>
                ) : (
                  <>
                    <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {figures.map((figure) => (
                        <ListItem
                          key={figure.id}
                          selected={selectedFigure?.id === figure.id}
                          onClick={() => setSelectedFigure(figure)}
                          sx={{ cursor: 'pointer', borderRadius: 1, mb: 1 }}
                        >
                          <Box sx={{ mr: 2, width: 60, height: 60, overflow: 'hidden', borderRadius: 1 }}>
                            <img
                              src={figure.preview}
                              alt={figure.name}
                              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                            />
                          </Box>
                          <ListItemText
                            primary={figure.name}
                            secondary={
                              <Box>
                                <Typography variant="caption" display="block">
                                  {figure.originalSize.width}×{figure.originalSize.height}px
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {figure.uploadedAt.toLocaleDateString()}
                                </Typography>
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteFigure(figure.id);
                              }}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                      <Button
                        variant="outlined"
                        startIcon={<PreviewIcon />}
                        onClick={generatePreview}
                        disabled={!selectedFigure || isProcessing}
                      >
                        Preview
                      </Button>
                      
                      <Button
                        variant="contained"
                        startIcon={isProcessing ? <CircularProgress size={20} /> : <DownloadIcon />}
                        onClick={() => selectedFigure && exportFigure(selectedFigure, exportSettings)}
                        disabled={!selectedFigure || isProcessing}
                      >
                        Export Selected
                      </Button>
                      
                      <Button
                        variant="contained"
                        color="secondary"
                        startIcon={isProcessing ? <CircularProgress size={20} /> : <DownloadIcon />}
                        onClick={exportAllFigures}
                        disabled={figures.length === 0 || isProcessing}
                      >
                        Export All
                      </Button>
                    </Box>
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Color Mode Examples */}
        <Card elevation={2} sx={{ mt: 4 }}>
          <CardContent>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PaletteIcon color="primary" />
                  Color Mode Examples
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="subtitle2" gutterBottom>Full Color</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Original colors preserved. Best for presentations and digital viewing.
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="subtitle2" gutterBottom>Grayscale</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Converted to shades of gray. Suitable for black & white printing.
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="subtitle2" gutterBottom>Black & White</Typography>
                      <Typography variant="body2" color="text.secondary">
                        High contrast black and white. Best for line art and diagrams.
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </CardContent>
        </Card>

        {/* Hidden canvas for image processing */}
        <canvas ref={canvasRef} style={{ display: 'none' }} />

        {/* Preview Dialog */}
        <Dialog open={previewDialogOpen} onClose={() => setPreviewDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Export Preview</DialogTitle>
          <DialogContent>
            {processedPreview && (
              <Box sx={{ textAlign: 'center' }}>
                <img
                  src={processedPreview}
                  alt="Preview"
                  style={{ maxWidth: '100%', maxHeight: '400px', objectFit: 'contain' }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  {exportSettings.width}×{exportSettings.height}px • {exportSettings.dpi} DPI • {exportSettings.colorMode}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPreviewDialogOpen(false)}>Close</Button>
            <Button
              variant="contained"
              onClick={() => {
                if (selectedFigure) {
                  exportFigure(selectedFigure, exportSettings);
                }
                setPreviewDialogOpen(false);
              }}
            >
              Export This Version
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </PublicationReadyGate>
  );
};

export default MultiFormatFigureExporter;