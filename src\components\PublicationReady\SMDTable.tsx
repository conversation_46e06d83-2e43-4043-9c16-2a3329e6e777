import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Snackbar,
} from '@mui/material';

import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType, Column, Dataset } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateMean,
  calculateStandardDeviation,
} from '../../utils/stats/descriptive';

// Effect size calculation functions
const calculateCohenSD = (mean1: number, sd1: number, n1: number, mean2: number, sd2: number, n2: number): number => {
    // Pooled standard deviation
    const pooledSD = Math.sqrt(((n1 - 1) * Math.pow(sd1, 2) + (n2 - 1) * Math.pow(sd2, 2)) / (n1 + n2 - 2));
    // <PERSON>'s d
    return (mean1 - mean2) / pooledSD;
};

const calculateHedgesG = (mean1: number, sd1: number, n1: number, mean2: number, sd2: number, n2: number): number => {
    const cohenSD = calculateCohenSD(mean1, sd1, n1, mean2, sd2, n2);
    const N = n1 + n2;
    const correctionFactor = 1 - (3 / (4 * N - 9));
    return cohenSD * correctionFactor;
};

const calculateEtaSquared = (groupMeans: number[], groupSDs: number[], groupNs: number[], totalMean: number): number => {
    let ssBetween = 0;
    let ssTotal = 0;
    
    const totalN = groupNs.reduce((sum, n) => sum + n, 0);
    
    groupMeans.forEach((mean, i) => {
        ssBetween += groupNs[i] * Math.pow(mean - totalMean, 2);
        ssTotal += groupNs[i] * (Math.pow(groupSDs[i], 2) + Math.pow(mean - totalMean, 2));
    });
    
    return ssBetween / ssTotal;
};

const calculateCramersV = (chiSquare: number, n: number, rows: number, cols: number): number => {
    const minDim = Math.min(rows - 1, cols - 1);
    return Math.sqrt(chiSquare / (n * minDim));
};

const calculateCohenSDConfidenceInterval = (cohenSD: number, n1: number, n2: number, confidenceLevel: number = 0.95): { lower: number; upper: number } => {
    // This is a simplified calculation and might need a more robust implementation
    // based on non-central t-distribution for more accuracy, especially for small sample sizes.
    // For a basic approximation:
    const pooledN = n1 + n2;
    const variance = (pooledN / (n1 * n2)) + (Math.pow(cohenSD, 2) / (2 * (pooledN - 2)));
    const standardError = Math.sqrt(variance);

    // Using Z-score for 95% CI for simplicity (approximation)
    const z = 1.96; // For 95% CI

    const lower = cohenSD - z * standardError;
    const upper = cohenSD + z * standardError;

    return { lower, upper };
};

interface SMDResult {
  variableName: string;
  smd?: number;
  ciLower?: number;
  ciUpper?: number;
  remarks?: string; // Added for interpretation
  // Potentially add p-value if applicable and easily calculable from t-test
}

// Function to calculate confidence interval for Eta squared
const calculateEtaSquaredConfidenceInterval = (etaSquared: number, totalN: number, dfBetween: number): { lower: number; upper: number } => {
    // Using Fisher's z-transformation for eta squared CI
    // This is an approximation - more sophisticated methods exist
    const variance = (1 - etaSquared) * (1 - etaSquared) / (totalN - dfBetween - 1);
    const standardError = Math.sqrt(variance);
    const z = 1.96; // For 95% CI
    
    const lower = Math.max(0, etaSquared - z * standardError);
    const upper = Math.min(1, etaSquared + z * standardError);
    
    return { lower, upper };
};

// Function to generate APA-style narrative summary
const generateAPASummary = (results: SMDResult[], effectSizeType: string, groupingVariable: string): string => {
    if (!results || results.length === 0) return '';
    
    const validResults = results.filter(r => r.smd !== undefined && !isNaN(r.smd));
    if (validResults.length === 0) return 'No valid effect sizes could be calculated for the selected variables.';
    
    const effectSizeLabel = {
        'cohens-d': "Cohen's d",
        'hedges-g': "Hedge's g", 
        'eta-squared': "eta squared (η²)",
        'cramers-v': "Cramer's V"
    }[effectSizeType] || effectSizeType;
    
    const largeEffects = validResults.filter(r => r.remarks === 'Large');
    const mediumEffects = validResults.filter(r => r.remarks === 'Medium');
    const smallEffects = validResults.filter(r => r.remarks === 'Small');
    const verySmallEffects = validResults.filter(r => r.remarks === 'Very Small');
    
    let summary = `Effect size analyses using ${effectSizeLabel} were conducted to examine differences across ${groupingVariable} groups. `;
    
    if (largeEffects.length > 0) {
        const variables = largeEffects.map(r => r.variableName).join(', ');
        const values = largeEffects.map(r => r.smd?.toFixed(2)).join(', ');
        summary += `Large effect sizes were observed for ${variables} (${effectSizeLabel} = ${values}). `;
    }
    
    if (mediumEffects.length > 0) {
        const variables = mediumEffects.map(r => r.variableName).join(', ');
        const values = mediumEffects.map(r => r.smd?.toFixed(2)).join(', ');
        summary += `Medium effect sizes were found for ${variables} (${effectSizeLabel} = ${values}). `;
    }
    
    if (smallEffects.length > 0) {
        const variables = smallEffects.map(r => r.variableName).join(', ');
        const values = smallEffects.map(r => r.smd?.toFixed(2)).join(', ');
        summary += `Small effect sizes were detected for ${variables} (${effectSizeLabel} = ${values}). `;
    }
    
    if (verySmallEffects.length > 0) {
        const variables = verySmallEffects.map(r => r.variableName).join(', ');
        const values = verySmallEffects.map(r => r.smd?.toFixed(2)).join(', ');
        summary += `Very small effect sizes were observed for ${variables} (${effectSizeLabel} = ${values}). `;
    }
    
    // Add confidence interval information
    const withCI = validResults.filter(r => r.ciLower !== undefined && !isNaN(r.ciLower) && r.ciUpper !== undefined && !isNaN(r.ciUpper));
    if (withCI.length > 0) {
        summary += 'All effect sizes are reported with 95% confidence intervals. ';
    }
    
    return summary.trim();
};

// Function to interpret effect sizes
const interpretEffectSize = (value: number | undefined, type: string): string => {
    if (value === undefined || isNaN(value)) {
        return 'N/A';
    }
    
    switch (type) {
        case 'cohens-d':
        case 'hedges-g':
            const absValue = Math.abs(value);
            if (absValue >= 0.8) return 'Large';
            if (absValue >= 0.5) return 'Medium';
            if (absValue >= 0.2) return 'Small';
            return 'Very Small';
        case 'eta-squared':
            if (value >= 0.14) return 'Large';
            if (value >= 0.06) return 'Medium';
            if (value >= 0.01) return 'Small';
            return 'Very Small';
        case 'cramers-v':
            if (value >= 0.5) return 'Large';
            if (value >= 0.3) return 'Medium';
            if (value >= 0.1) return 'Small';
            return 'Very Small';
        default:
            return 'N/A';
    }
};

const SMDTable: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset, grouping variable, and continuous variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [groupingVariableId, setGroupingVariableId] = useState<string | ''>('');
  const [continuousVariableIds, setContinuousVariableIds] = useState<string[]>([]);
  const [effectSizeType, setEffectSizeType] = useState<string>('cohens-d');

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [smdResults, setSmdResults] = useState<SMDResult[] | null>(null);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available columns from the selected dataset
  const availableColumns = selectedDataset?.columns || [];

  // Filter binary categorical columns for grouping variable
  const binaryCategoricalColumns = availableColumns.filter(col =>
      col.type === DataType.CATEGORICAL &&
      selectedDataset?.data.map(row => String(row[col.name])).filter((value, index, self) => self.indexOf(value) === index).length === 2
  );

  // Filter categorical columns for Cramer's V (can have more than 2 categories)
  const categoricalColumns = availableColumns.filter(col =>
      col.type === DataType.CATEGORICAL
  );

  // Filter continuous columns for analysis
  const continuousColumns = availableColumns.filter(col => col.type === DataType.NUMERIC);

  // Determine appropriate columns based on effect size type
  const availableAnalysisColumns = effectSizeType === 'cramers-v' ? categoricalColumns : continuousColumns;

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setGroupingVariableId(''); // Clear grouping variable
    setContinuousVariableIds([]); // Clear continuous variables
    setSmdResults(null); // Clear results
    setError(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle grouping variable selection change
  const handleGroupingVariableChange = (event: SelectChangeEvent<string>) => {
    const newGroupingVariableId = event.target.value;
    setGroupingVariableId(newGroupingVariableId);
    setContinuousVariableIds([]); // Clear continuous variables when grouping variable changes
    setSmdResults(null); // Clear results
    setError(null);
  };

  // Handle continuous variable selection change
  const handleContinuousVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setContinuousVariableIds(typeof value === 'string' ? value.split(',') : value);
    setSmdResults(null); // Clear results when selection changes
    setError(null);
  };

  // Handle effect size type selection change
  const handleEffectSizeTypeChange = (event: SelectChangeEvent<string>) => {
    setEffectSizeType(event.target.value);
    setSmdResults(null); // Clear results when selection changes
    setError(null);
  };

  // Function to run SMD analysis
  const runSMDAnalysis = () => {
    if (!selectedDataset || !groupingVariableId || (effectSizeType !== 'cramers-v' && continuousVariableIds.length === 0) || (effectSizeType === 'cramers-v' && continuousVariableIds.length === 0)) {
      setError('Please select a dataset, a grouping variable, and appropriate variables to analyze.');
      setSmdResults(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: SMDResult[] = [];

    const groupingColumn = availableColumns.find(col => col.id === groupingVariableId);
    
    if (effectSizeType === 'cramers-v') {
      // Handle Cramer's V for categorical variables
      const catCols = availableColumns.filter(col => continuousVariableIds.includes(col.id));
      
      catCols.forEach(column => {
        try {
          // Calculate chi-square and Cramer's V
          const groupingValues = selectedDataset.data.map(row => String(row[groupingColumn.name]));
          const variableValues = selectedDataset.data.map(row => String(row[column.name]));
          
          // Create contingency table
          const groupingCategories = [...new Set(groupingValues)].sort();
          const variableCategories = [...new Set(variableValues)].sort();
          
          if (groupingCategories.length < 2 || variableCategories.length < 2) {
            results.push({
              variableName: column.name,
              smd: NaN,
              ciLower: NaN,
              ciUpper: NaN,
              remarks: 'Categorical variables need at least 2 categories',
              effectSizeType: 'Cramer\'s V'
            });
            return;
          }
          
          // Build contingency table
          const contingencyTable: number[][] = Array(groupingCategories.length).fill(null).map(() => Array(variableCategories.length).fill(0));
          
          for (let i = 0; i < selectedDataset.data.length; i++) {
            const groupIdx = groupingCategories.indexOf(groupingValues[i]);
            const varIdx = variableCategories.indexOf(variableValues[i]);
            if (groupIdx >= 0 && varIdx >= 0) {
              contingencyTable[groupIdx][varIdx]++;
            }
          }
          
          // Calculate chi-square
          const total = selectedDataset.data.length;
          let chiSquare = 0;
          
          for (let i = 0; i < groupingCategories.length; i++) {
            const rowSum = contingencyTable[i].reduce((sum, val) => sum + val, 0);
            for (let j = 0; j < variableCategories.length; j++) {
              const colSum = contingencyTable.map(row => row[j]).reduce((sum, val) => sum + val, 0);
              const expected = (rowSum * colSum) / total;
              const observed = contingencyTable[i][j];
              if (expected > 0) {
                chiSquare += Math.pow(observed - expected, 2) / expected;
              }
            }
          }
          
          // Calculate Cramer's V
          const minDim = Math.min(groupingCategories.length, variableCategories.length) - 1;
          const cramersV = Math.sqrt(chiSquare / (total * minDim));
          
          // Approximate CI
          const se = Math.sqrt((cramersV * cramersV * (1 - cramersV * cramersV)) / (total - 3));
          const z = 1.96;
          const ciLower = Math.max(0, cramersV - z * se);
          const ciUpper = Math.min(1, cramersV + z * se);
          
          results.push({
            variableName: column.name,
            smd: cramersV,
            ciLower: ciLower,
            ciUpper: ciUpper,
            remarks: interpretEffectSize(cramersV, 'cramers-v'),
            effectSizeType: 'Cramer\'s V'
          });
        } catch (e) {
          results.push({
            variableName: column.name,
            smd: NaN,
            ciLower: NaN,
            ciUpper: NaN,
            remarks: 'Error calculating Cramer\'s V',
            effectSizeType: 'Cramer\'s V'
          });
        }
      });
    } else {
      // Handle Cohen's d and Hedges' g for continuous variables
      const continuousCols = availableColumns.filter(col => continuousVariableIds.includes(col.id));
      
      const groupingCategories = Array.from(new Set(selectedDataset.data.map(row => String(row[groupingColumn.name]))));
      
      if (groupingCategories.length === 2) {
        // Binary grouping - use Cohen's d or Hedges' g
        const group1Category = groupingCategories[0];
        const group2Category = groupingCategories[1];

        continuousCols.forEach(column => {
            const group1Data = selectedDataset.data
                .filter(row => String(row[groupingColumn.name]) === group1Category)
                .map(row => row[column.name])
                .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

            const group2Data = selectedDataset.data
                .filter(row => String(row[groupingColumn.name]) === group2Category)
                .map(row => row[column.name])
                .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

            if (group1Data.length > 1 && group2Data.length > 1) {
                try {
                    const mean1 = calculateMean(group1Data);
                    const sd1 = calculateStandardDeviation(group1Data);
                    const n1 = group1Data.length;

                    const mean2 = calculateMean(group2Data);
                    const sd2 = calculateStandardDeviation(group2Data);
                    const n2 = group2Data.length;

                    let effectSize = 0;
                    if (effectSizeType === 'cohens-d') {
                        effectSize = calculateCohenSD(mean1, sd1, n1, mean2, sd2, n2);
                    } else if (effectSizeType === 'hedges-g') {
                        effectSize = calculateHedgesG(mean1, sd1, n1, mean2, sd2, n2);
                    }

                    const ci = calculateCohenSDConfidenceInterval(effectSize, n1, n2);

                    results.push({
                        variableName: column.name,
                        smd: effectSize,
                        ciLower: ci.lower,
                        ciUpper: ci.upper,
                        remarks: interpretEffectSize(effectSize, effectSizeType),
                        effectSizeType: effectSizeType === 'cohens-d' ? "Cohen's d" : "Hedge's g"
                    });
                } catch (e) {
                    results.push({
                        variableName: column.name,
                        smd: NaN,
                        ciLower: NaN,
                        ciUpper: NaN,
                        remarks: 'Error calculating effect size',
                        effectSizeType: effectSizeType === 'cohens-d' ? "Cohen's d" : "Hedge's g"
                    });
                }
            } else {
                results.push({
                    variableName: column.name,
                    smd: NaN,
                    ciLower: NaN,
                    ciUpper: NaN,
                    remarks: 'Insufficient data',
                    effectSizeType: effectSizeType === 'cohens-d' ? "Cohen's d" : "Hedge's g"
                });
            }
        });
      } else {
        // Multi-group - use Eta squared (simplified)
        continuousCols.forEach(column => {
          try {
            const groups: { [key: string]: number[] } = {};
            
            selectedDataset.data.forEach(row => {
              const groupKey = String(row[groupingColumn.name]);
              const value = row[column.name];
              if (typeof value === 'number' && !isNaN(value)) {
                if (!groups[groupKey]) groups[groupKey] = [];
                groups[groupKey].push(value);
              }
            });

            const groupMeans = Object.values(groups).map(data => calculateMean(data));
            const groupSDs = Object.values(groups).map(data => calculateStandardDeviation(data));
            const groupNs = Object.values(groups).map(data => data.length);
            
            const allData = Object.values(groups).flat();
            const totalMean = calculateMean(allData);
            const totalN = allData.length;
            const dfBetween = Object.keys(groups).length - 1;
            
            const etaSquared = calculateEtaSquared(groupMeans, groupSDs, groupNs, totalMean);
            const ci = calculateEtaSquaredConfidenceInterval(etaSquared, totalN, dfBetween);
            
            results.push({
              variableName: column.name,
              smd: etaSquared,
              ciLower: ci.lower,
              ciUpper: ci.upper,
              remarks: interpretEffectSize(etaSquared, 'eta-squared'),
              effectSizeType: 'Eta squared'
            });
          } catch (e) {
            results.push({
              variableName: column.name,
              smd: NaN,
              ciLower: NaN,
              ciUpper: NaN,
              remarks: 'Error calculating Eta squared',
              effectSizeType: 'Eta squared'
            });
          }
        });
      }
    }

    setSmdResults(results);
    setLoading(false);
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Publication Ready: Effect Size Analysis
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
            This table calculates effect sizes for both continuous and categorical variables, including Cohen's d, Hedge's g, Eta squared, and Cramer's V.
        </Alert>

        <Grid container spacing={2}>
           {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Effect Size Type Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="effect-size-type-select-label">Effect Size Type</InputLabel>
              <Select
                labelId="effect-size-type-select-label"
                id="effect-size-type-select"
                value={effectSizeType}
                onChange={handleEffectSizeTypeChange}
                label="Effect Size Type"
              >
                <MenuItem value="cohens-d">Cohen's d (2 groups, continuous)</MenuItem>
                <MenuItem value="hedges-g">Hedge's g (2 groups, continuous, bias-corrected)</MenuItem>
                <MenuItem value="eta-squared">Eta squared (2+ groups, continuous)</MenuItem>
                <MenuItem value="cramers-v">Cramer's V (categorical association)</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Grouping Variable Selection */}
           <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="grouping-variable-select-label">Grouping Variable</InputLabel>
              <Select
                labelId="grouping-variable-select-label"
                id="grouping-variable-select"
                value={groupingVariableId}
                onChange={handleGroupingVariableChange}
                label="Grouping Variable"
                disabled={!selectedDataset}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {availableColumns.filter(col => col.type === DataType.CATEGORICAL).map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Variables Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variables-select-label">
                {effectSizeType === 'cramers-v' ? 'Categorical Variables' : 'Continuous Variables'}
              </InputLabel>
              <Select
                labelId="variables-select-label"
                id="variables-select"
                multiple
                value={continuousVariableIds}
                onChange={handleContinuousVariableChange}
                label={effectSizeType === 'cramers-v' ? 'Categorical Variables' : 'Continuous Variables'}
                disabled={availableAnalysisColumns.length === 0 || !groupingVariableId}
                renderValue={(selected) => selected.map(id => availableColumns.find(col => col.id === id)?.name || '').join(', ')}
              >
                {availableAnalysisColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No {effectSizeType === 'cramers-v' ? 'categorical' : 'continuous'} variables available
                  </MenuItem>
                ) : (
                  availableAnalysisColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runSMDAnalysis}
            disabled={loading || !selectedDataset || !groupingVariableId || continuousVariableIds.length === 0}
          >
            Generate Effect Size Analysis
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {smdResults && !loading && selectedDataset && groupingVariableId && continuousVariableIds.length > 0 && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Effect Size Analysis Results
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Effect Size</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">95% CI</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Interpretation</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {smdResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ fontWeight: 'bold' }}>{result.variableName}</TableCell>
                      <TableCell align="center">{result.smd !== undefined && !isNaN(result.smd) ? result.smd.toFixed(2) : 'N/A'}</TableCell>
                      <TableCell align="center">
                          {result.ciLower !== undefined && !isNaN(result.ciLower) && result.ciUpper !== undefined && !isNaN(result.ciUpper) ?
                           `[${result.ciLower.toFixed(2)}, ${result.ciUpper.toFixed(2)}]` : 'N/A'}
                      </TableCell>
                      <TableCell align="center">{result.remarks || 'N/A'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {/* APA-Style Narrative Summary */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                APA-Style Summary
              </Typography>
              <Paper variant="outlined" sx={{ p: 2, backgroundColor: '#f8f9fa' }}>
                <Typography variant="body1" sx={{ lineHeight: 1.6, fontFamily: 'serif' }}>
                  {generateAPASummary(
                    smdResults, 
                    effectSizeType, 
                    availableColumns.find(col => col.id === groupingVariableId)?.name || 'grouping variable'
                  )}
                </Typography>
              </Paper>
            </Box>

          </Paper>

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `Effect Size Analysis - ${selectedDataset.name}`,
                type: 'other' as const,
                component: 'SMDTable',
                data: {
                  dataset: selectedDataset.name,
                  effectSizeType: effectSizeType,
                  groupingVariable: availableColumns.find(col => col.id === groupingVariableId)?.name || 'Unknown',
                  variables: continuousVariableIds.map((id: string) =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: smdResults,
                  apaSummary: generateAPASummary(
                    smdResults, 
                    effectSizeType, 
                    availableColumns.find(col => col.id === groupingVariableId)?.name || 'grouping variable'
                  ),
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default SMDTable;

