-- Fix email type mismatch in subscription override functions
-- The auth.users.email column is character varying(255) but functions expect TEXT
-- Date: 2025-07-25

-- Drop existing functions first to avoid return type conflicts
DROP FUNCTION IF EXISTS public.get_user_active_override(UUID);
DROP FUNCTION IF EXISTS public.get_all_active_overrides(INTEGER, INTEGER);
DROP FUNCTION IF EXISTS public.get_override_audit_trail(UUID, INTEGER, INTEGER);

-- Recreate get_user_active_override function with proper type casting
CREATE OR REPLACE FUNCTION public.get_user_active_override(target_user_id UUID)
RETURNS TABLE (
  id UUID,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  admin_email TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Users can check their own override, admins can check any user's override
  IF target_user_id != auth.uid() AND NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Can only view your own override status.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    COALESCE(au.email::TEXT, 'Unknown Admin') as admin_email,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  LEFT JOIN auth.users au ON so.admin_id = au.id
  WHERE so.user_id = target_user_id 
    AND so.is_active = true
    AND so.end_date > now()
  ORDER BY so.created_at DESC
  LIMIT 1;
END;
$$;

-- Recreate get_all_active_overrides function with proper type casting
CREATE OR REPLACE FUNCTION public.get_all_active_overrides(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    COALESCE(u.email::TEXT, 'Unknown User') as user_email,
    COALESCE(p.full_name, 'No Name') as user_full_name,
    COALESCE(au.email::TEXT, 'Unknown Admin') as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  LEFT JOIN auth.users u ON so.user_id = u.id
  LEFT JOIN auth.users au ON so.admin_id = au.id
  LEFT JOIN public.profiles p ON so.user_id = p.id
  WHERE so.is_active = true AND so.end_date > now()
  ORDER BY so.end_date ASC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Recreate get_override_audit_trail function with proper type casting
CREATE OR REPLACE FUNCTION public.get_override_audit_trail(
  target_user_id UUID DEFAULT NULL,
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    COALESCE(u.email::TEXT, 'Unknown User') as user_email,
    COALESCE(p.full_name, 'No Name') as user_full_name,
    COALESCE(au.email::TEXT, 'Unknown Admin') as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    so.is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  LEFT JOIN auth.users u ON so.user_id = u.id
  LEFT JOIN auth.users au ON so.admin_id = au.id
  LEFT JOIN public.profiles p ON so.user_id = p.id
  WHERE (target_user_id IS NULL OR so.user_id = target_user_id)
  ORDER BY so.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_active_override(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) TO authenticated;

-- Add comments
COMMENT ON FUNCTION public.get_user_active_override(UUID) IS 'Gets the active override for a user with fixed email type casting and null handling. Users can check their own, admins can check any.';
COMMENT ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) IS 'Gets all active overrides with pagination, fixed email type casting, and null handling. Admin only.';
COMMENT ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) IS 'Gets the complete audit trail of overrides with fixed email type casting and null handling. Admin only.';