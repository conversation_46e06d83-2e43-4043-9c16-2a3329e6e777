import jStat from 'jstat';
import { SimpleLinearRegression } from 'ml-regression-simple-linear';
import { math } from './math-setup'; // Import math from the setup file
import { calculateMean } from './descriptive';

// Multiple linear regression
export const multipleLinearRegression = (
  x: number[] | number[][],
  y: number[]
): {
  coefficients: number[];
  intercept: number;
  rSquared: number;
  pValue: number;
  n: number;
  stdErrors: number[];
  interceptStdError: number;
  pValues: number[];
  interceptPValue: number;
  residuals: number[];
  predicted: number[];
  equation: string;
  standardErrorOfRegression: number; // Added for SER/RMSE
  xNames?: string[];
} => {
  // Convert x to 2D array if it's 1D
  const xMatrix = Array.isArray(x[0]) 
    ? x as number[][] 
    : (x as number[]).map(val => [val]);
  
  if (xMatrix.length !== y.length) {
    throw new Error('Number of observations must be the same for X and y');
  }
  
  if (xMatrix.length < 3) {
    throw new Error('Sample size must be at least 3');
  }
  
  const n = xMatrix.length; // Number of observations
  const p = xMatrix[0].length; // Number of predictors
  
  // For simple linear regression (one predictor), use the ml-regression-simple-linear library
  if (p === 1) {
    // Extract the single predictor column
    const xValues = xMatrix.map(row => row[0]);
    
    // Create and train the regression model
    const regression = new SimpleLinearRegression(xValues, y);
    
    // Get model parameters
    const intercept = regression.intercept;
    const slope = regression.slope;
    const coefficients = [slope];
    
    // Calculate predicted values
    const predicted = regression.predict(xValues);
    
    // Calculate residuals
    const residuals = y.map((yi, i) => yi - predicted[i]);
    
    // Calculate SSE (sum of squared errors)
    const sse = residuals.reduce((sum, res) => sum + res * res, 0);
    
    // Calculate SST (total sum of squares)
    const yMean = calculateMean(y);
    const sst = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    
    // Calculate R-squared with validation
    if (sst === 0) {
      throw new Error('Total sum of squares is zero - dependent variable has no variation');
    }
    const rSquared = 1 - (sse / sst);
    
    // Validate R-squared
    if (!isFinite(rSquared) || rSquared < 0) {
      throw new Error('Invalid R-squared value calculated');
    }
    
    // Calculate standard error of the regression
    if (n <= 2) {
      throw new Error('Insufficient degrees of freedom for simple linear regression');
    }
    const sigma = Math.sqrt(sse / (n - 2));
    
    // Validate sigma
    if (!isFinite(sigma) || sigma <= 0) {
      throw new Error('Invalid standard error of regression calculated');
    }
    
    // Calculate standard errors of coefficients
    // For simple linear regression, we can calculate these directly
    const sumX = xValues.reduce((sum, val) => sum + val, 0);
    const sumXSquared = xValues.reduce((sum, val) => sum + val * val, 0);
    const sxx = sumXSquared - (sumX * sumX) / n;
    
    // Check for zero variance in X
    if (sxx <= 0) {
      throw new Error('Independent variable has zero or negative variance');
    }
    
    const interceptStdError = sigma * Math.sqrt(sumXSquared / (n * sxx));
    const slopeStdError = sigma / Math.sqrt(sxx);
    const stdErrors = [slopeStdError];
    
    // Validate standard errors
    if (!isFinite(interceptStdError) || interceptStdError <= 0) {
      throw new Error('Invalid intercept standard error calculated');
    }
    if (!isFinite(slopeStdError) || slopeStdError <= 0) {
      throw new Error('Invalid slope standard error calculated');
    }
    
    // Calculate t-statistics and p-values
    const tStat = slope / slopeStdError;
    const interceptTStat = intercept / interceptStdError;
    
    // Validate t-statistics
    if (!isFinite(tStat)) {
      throw new Error('Invalid t-statistic calculated for slope');
    }
    if (!isFinite(interceptTStat)) {
      throw new Error('Invalid t-statistic calculated for intercept');
    }
    
    const pValues = [2 * (1 - jStat.studentt.cdf(Math.abs(tStat), n - 2))];
    const interceptPValue = 2 * (1 - jStat.studentt.cdf(Math.abs(interceptTStat), n - 2));
    
    // Calculate F-statistic and p-value for overall model
    // For simple linear regression, F-statistic is the square of the t-statistic
    const F = tStat * tStat;
    
    // Validate F-statistic
    if (!isFinite(F) || F < 0) {
      throw new Error('Invalid F-statistic calculated');
    }
    
    const pValue = 1 - jStat.centralF.cdf(F, 1, n - 2);
    
    // Create equation string
    const equation = `y = ${intercept.toFixed(4)}${slope >= 0 ? ' + ' : ' - '}${Math.abs(slope).toFixed(4)}x`;
    
    return {
      coefficients,
      intercept,
      rSquared,
      pValue,
      n,
      stdErrors,
      interceptStdError,
      pValues,
      interceptPValue,
      residuals,
      predicted,
      equation,
      standardErrorOfRegression: sigma // Added SER
    };
  }
  
  // For multiple linear regression, use the existing implementation
  // Add column of 1s for intercept
  const X = xMatrix.map(row => [1, ...row]);
  
  // Calculate X'X (transpose of X multiplied by X)
  const XtX = Array(p + 1).fill(0).map(() => Array(p + 1).fill(0));
  for (let i = 0; i < p + 1; i++) {
    for (let j = 0; j < p + 1; j++) {
      for (let k = 0; k < n; k++) {
        XtX[i][j] += X[k][i] * X[k][j];
      }
    }
  }
  
  // Calculate X'y
  const Xty = Array(p + 1).fill(0);
  for (let i = 0; i < p + 1; i++) {
    for (let k = 0; k < n; k++) {
      Xty[i] += X[k][i] * y[k];
    }
  }
  
  // Solve for beta: (X'X)^-1 * X'y
  // Using math.js for matrix inversion with error handling
  let XtXInv;
  try {
    // Check for matrix singularity by computing determinant
    const det = math.det(XtX);
    if (Math.abs(det) < 1e-12) {
      throw new Error('Design matrix is singular or near-singular. This may be due to multicollinearity or insufficient data variation.');
    }
    
    XtXInv = math.inv(XtX);
    
    // Validate the inverse matrix
    if (!Array.isArray(XtXInv) || XtXInv.some(row => row.some(val => !isFinite(val)))) {
      throw new Error('Matrix inversion failed - invalid result');
    }
  } catch (error) {
    throw new Error(`Matrix inversion failed: ${error.message}. This may indicate multicollinearity or numerical instability.`);
  }
  
  const beta = Array(p + 1).fill(0);
  for (let i = 0; i < p + 1; i++) {
    for (let j = 0; j < p + 1; j++) {
      beta[i] += XtXInv[i][j] * Xty[j];
    }
  }
  
  // Validate beta coefficients
  if (beta.some(val => !isFinite(val))) {
    throw new Error('Regression coefficients are invalid (NaN or infinite). This may indicate numerical instability or perfect multicollinearity.');
  }
  
  const intercept = beta[0];
  const coefficients = beta.slice(1);
  
  // Calculate predicted values
  const predicted = Array(n).fill(0);
  for (let i = 0; i < n; i++) {
    predicted[i] = intercept;
    for (let j = 0; j < p; j++) {
      predicted[i] += coefficients[j] * xMatrix[i][j];
    }
  }
  
  // Calculate residuals
  const residuals = y.map((yi, i) => yi - predicted[i]);
  
  // Calculate SSE (sum of squared errors)
  const sse = residuals.reduce((sum, res) => sum + res * res, 0);
  
  // Calculate SST (total sum of squares)
  const yMean = calculateMean(y);
  const sst = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
  
  // Calculate R-squared with validation
  if (sst === 0) {
    throw new Error('Total sum of squares is zero - dependent variable has no variation');
  }
  const rSquared = 1 - (sse / sst);
  
  // Validate R-squared
  if (!isFinite(rSquared) || rSquared < 0) {
    throw new Error('Invalid R-squared value calculated');
  }
  
  // Calculate standard error of the regression
  if (n - p - 1 <= 0) {
    throw new Error('Insufficient degrees of freedom for regression');
  }
  const sigma = Math.sqrt(sse / (n - p - 1));
  
  // Validate sigma
  if (!isFinite(sigma) || sigma <= 0) {
    throw new Error('Invalid standard error of regression calculated');
  }
  
  // Calculate standard errors of coefficients
  const stdErrors = Array(p).fill(0);
  for (let j = 0; j < p; j++) {
    const variance = XtXInv[j + 1][j + 1];
    if (variance < 0) {
      throw new Error(`Negative variance for coefficient ${j + 1}`);
    }
    stdErrors[j] = sigma * Math.sqrt(variance);
  }
  
  const interceptVariance = XtXInv[0][0];
  if (interceptVariance < 0) {
    throw new Error('Negative variance for intercept');
  }
  const interceptStdError = sigma * Math.sqrt(interceptVariance);
  
  // Validate standard errors
  if (stdErrors.some(val => !isFinite(val) || val <= 0)) {
    throw new Error('Invalid standard errors calculated');
  }
  if (!isFinite(interceptStdError) || interceptStdError <= 0) {
    throw new Error('Invalid intercept standard error calculated');
  }
  
  // Calculate t-statistics and p-values
  const tStats = coefficients.map((coef, j) => coef / stdErrors[j]);
  const interceptTStat = intercept / interceptStdError;
  
  // Validate t-statistics
  if (tStats.some(val => !isFinite(val))) {
    throw new Error('Invalid t-statistics calculated');
  }
  if (!isFinite(interceptTStat)) {
    throw new Error('Invalid intercept t-statistic calculated');
  }
  
  const pValues = tStats.map(t => 2 * (1 - jStat.studentt.cdf(Math.abs(t), n - p - 1)));
  const interceptPValue = 2 * (1 - jStat.studentt.cdf(Math.abs(interceptTStat), n - p - 1));
  
  // Calculate F-statistic and p-value for overall model
  if (rSquared >= 1) {
    throw new Error('Perfect fit detected - F-statistic cannot be calculated');
  }
  const F = (rSquared / p) / ((1 - rSquared) / (n - p - 1));
  
  // Validate F-statistic
  if (!isFinite(F) || F < 0) {
    throw new Error('Invalid F-statistic calculated');
  }
  
  const pValue = 1 - jStat.centralF.cdf(F, p, n - p - 1);
  
  // Create equation string
  let equation = `y = ${intercept.toFixed(4)}`;
  for (let j = 0; j < p; j++) {
    const sign = coefficients[j] >= 0 ? ' + ' : ' - ';
    equation += `${sign}${Math.abs(coefficients[j]).toFixed(4)}x${j + 1}`;
  }
  
  return {
    coefficients,
    intercept,
    rSquared,
    pValue,
    n,
    stdErrors,
    interceptStdError,
    pValues,
    interceptPValue,
    residuals,
    predicted,
    equation,
    standardErrorOfRegression: sigma // Added SER
  };
};

// Simple linear regression (wrapper for backward compatibility)
export const simpleLinearRegression = (
  x: number[],
  y: number[]
) => {
  return multipleLinearRegression(x, y);
};

// Multiple logistic regression
export const multipleLogisticRegression = (
  x: number[][] | number[],
  y: number[],
  maxIterations = 10000, // Default from original, though IRLS uses 100
  learningRate = 0.05 // Default from original, not directly used by IRLS
): {
  coefficients: number[];
  intercept: number;
  pValues: number[];
  interceptPValue: number;
  stdErrors: number[];
  interceptStdError: number;
  logLikelihood: number;
  aic: number;
  pseudoRSquared: number;
  deviance: number;
  predictions: number[];
  confusionMatrix: number[][];
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  auc: number;
  n: number;
  covMatrix: number[][];
} => {
  if (!y.every((val): val is 0 | 1 => val === 0 || val === 1)) {
    throw new Error('Dependent variable must contain only 0s and 1s for binary logistic regression');
  }
  const yBinary = y as (0 | 1)[];
  const xMatrixInput: number[][] = Array.isArray(x[0]) ? x as number[][] : (x as number[]).map(val => [val]);
  const n = yBinary.length;
  const p = xMatrixInput[0].length;

  if (n !== xMatrixInput.length) {
    throw new Error('X and yBinary must have the same number of observations');
  }
  if (n < p + 1) {
    throw new Error(`Sample size (${n}) must be greater than the number of predictors + intercept (${p + 1})`);
  }
  if (n < 10) {
    console.warn('Sample size is small (< 10) for logistic regression, results may be unstable.');
  }

  const X_arr = xMatrixInput.map(row => [1, ...row]);
  const X = math.matrix(X_arr);
  const Y = math.matrix(yBinary);

  const maxIter = 100; 
  const tolerance = 1e-8; 
  const minStepSize = 1e-10;

  const y_transformed = yBinary.map(y_i => Math.log((y_i + 0.5) / (1.5 - y_i)));
  const initialWeights = yBinary.map(() => 0.25); 
  const W_init = math.diag(initialWeights);
  const Xt_init = math.transpose(X);
  const XtWX_init = math.multiply(math.multiply(Xt_init, W_init), X);
  const XtWy_init = math.multiply(math.multiply(Xt_init, W_init), math.matrix(y_transformed));
  
  const XtWX_init_reg = math.add(XtWX_init, math.multiply(math.eye(p + 1), 1e-4)) as math.Matrix;
  let beta = math.multiply(math.inv(XtWX_init_reg), XtWy_init) as math.Matrix;

  let iter = 0;
  let converged = false;
  let prevLogLikelihood = -Infinity;
  let logLikelihood = -Infinity;
  let noImprovement = 0; 

  while (iter < maxIter && !converged) {
    const eta = math.multiply(X, beta); 
    
    const pi = math.map(eta, (val: number) => {
      const prob = 1 / (1 + Math.exp(-val));
      return Math.max(1e-10, Math.min(1 - 1e-10, prob)); 
    });

    const weightsDiagonal = math.map(pi, (p_i: number) => {
      const weight = p_i * (1 - p_i);
      return Math.max(minStepSize, Math.min(weight, 0.25)); 
    });
    const W = math.diag(weightsDiagonal);

    const pi_vector = pi.valueOf() as number[]; 
    const weights_vector = weightsDiagonal.valueOf() as number[]; 
    const y_vector = Y.valueOf() as number[]; 
    
    const z_vector = (eta.valueOf() as number[]).map((eta_i, i) => {
      const weight_i = weights_vector[i];
      if (weight_i < 1e-10) { 
        return eta_i; 
      }
      return eta_i + (y_vector[i] - pi_vector[i]) / weight_i;
    });
    const z = math.matrix(z_vector);

    const Xt = math.transpose(X);
    const XtW = math.multiply(Xt, W);
    const XtWX = math.multiply(XtW, X);
    const XtWz = math.multiply(XtW, z);

    let beta_new: math.Matrix;
    try {
      const lambda = 1e-6 * Math.pow(10, -iter / maxIter); 
      const XtWX_reg = math.add(XtWX, math.multiply(math.eye(p + 1), lambda)) as math.Matrix;
      const XtWX_inv = math.inv(XtWX_reg);
      beta_new = math.multiply(XtWX_inv, XtWz) as math.Matrix;
    } catch (e) {
      console.error("Matrix inversion failed during IRLS iteration:", e);
      console.warn("Stopping IRLS due to numerical instability. Results may be inaccurate.");
      beta_new = beta; 
      iter = maxIter; 
    }

    let currentLogLikelihood = y_vector.reduce((sum, y_i, i) => {
        const p_i = pi_vector[i];
        const logP = Math.log(Math.max(p_i, 1e-10));
        const log1minusP = Math.log(Math.max(1 - p_i, 1e-10));
        return sum + (y_i * logP + (1 - y_i) * log1minusP);
    }, 0);

    let step = 1.0;
    let beta_update = beta_new;
    while (currentLogLikelihood < prevLogLikelihood && step > minStepSize) {
        step *= 0.5;
        const diff_beta_step = math.subtract(beta_new, beta) as math.Matrix;
        const scaled_diff_beta_step = math.multiply(diff_beta_step, step) as math.Matrix;
        beta_update = math.add(beta, scaled_diff_beta_step) as math.Matrix;
        
        const eta_update = math.multiply(X, beta_update);
        const pi_update = math.map(eta_update, (val: number) => {
            const prob = 1 / (1 + Math.exp(-val));
            return Math.max(1e-10, Math.min(1 - 1e-10, prob));
        });
        const pi_update_vector = pi_update.valueOf() as number[];
        
        currentLogLikelihood = y_vector.reduce((sum, y_i, i) => {
            const p_i = pi_update_vector[i];
            const logP = Math.log(Math.max(p_i, 1e-10));
            const log1minusP = Math.log(Math.max(1 - p_i, 1e-10));
            return sum + (y_i * logP + (1 - y_i) * log1minusP);
        }, 0);
    }

    const delta_beta = math.subtract(beta_update, beta);
    const relativeChange = math.norm(delta_beta) / (math.norm(beta) + tolerance);
    const likelihoodChange = Math.abs((currentLogLikelihood - prevLogLikelihood) / (prevLogLikelihood + tolerance));
    
    if (relativeChange < tolerance && likelihoodChange < tolerance) {
        noImprovement++;
        if (noImprovement >= 3) { 
            converged = true;
        }
    } else {
        noImprovement = 0;
    }

    beta = beta_update;
    prevLogLikelihood = logLikelihood; // This was prevLogLikelihood = logLikelihood;
    logLikelihood = currentLogLikelihood; // This was logLikelihood = currentLogLikelihood;

    iter++;
  } 

  if (!converged) {
    console.warn(`IRLS did not converge within ${maxIter} iterations.`);
  }

  const betaArray = (beta.valueOf() as number[][]).flat();
  const intercept = betaArray[0];
  const coefficients = betaArray.slice(1);

  const finalEta = math.multiply(X, beta);
  const finalPi = math.map(finalEta, (val: number) => 1 / (1 + Math.exp(-val)));
  const predictions = (finalPi.valueOf() as number[]).map(p_i => Math.max(0, Math.min(1, p_i)));

  let finalCovMatrix: number[][] = [];
  try {
      const finalPiVector = finalPi.valueOf() as number[];
      const finalWeightsDiagonal = finalPiVector.map(p_i => Math.max(1e-10, p_i * (1 - p_i)));
      const finalW = math.diag(finalWeightsDiagonal);
      const finalXtW = math.multiply(math.transpose(X), finalW);
      const finalXtWX = math.multiply(finalXtW, X);
      const finalXtWX_reg = math.add(finalXtWX, math.multiply(math.eye(p + 1), 1e-6)) as math.Matrix;
      finalCovMatrix = math.inv(finalXtWX_reg).valueOf() as number[][];
  } catch (e) {
      console.error("Failed to calculate final covariance matrix:", e);
      finalCovMatrix = Array(p + 1).fill(0).map(() => Array(p + 1).fill(NaN));
  }

  const stdErrorsAll = finalCovMatrix.map((row, i) => Math.sqrt(Math.max(1e-10, row[i]))); 
  const interceptStdError = stdErrorsAll[0] || NaN;
  const stdErrors = stdErrorsAll.slice(1);

  const zScores = betaArray.map((b, i) => stdErrorsAll[i] > 0 ? b / stdErrorsAll[i] : NaN);
  const pValuesAll = zScores.map(z => isNaN(z) ? NaN : 2 * (1 - jStat.normal.cdf(Math.abs(z), 0, 1)));
  const interceptPValue = pValuesAll[0] || NaN;
  const pValues = pValuesAll.slice(1);

  const predictedClasses = predictions.map(p_i => p_i >= 0.5 ? 1 : 0);
  let TP = 0, TN = 0, FP = 0, FN = 0;
  const y_vector = Y.valueOf() as number[];
  for (let i = 0; i < n; i++) {
    if (predictedClasses[i] === 1 && y_vector[i] === 1) TP++;
    else if (predictedClasses[i] === 0 && y_vector[i] === 0) TN++;
    else if (predictedClasses[i] === 1 && y_vector[i] === 0) FP++;
    else if (predictedClasses[i] === 0 && y_vector[i] === 1) FN++;
  }
  const confusionMatrix = [[TN, FP], [FN, TP]];
  const accuracy = n > 0 ? (TP + TN) / n : 0;
  const precision = TP + FP === 0 ? 0 : TP / (TP + FP);
  const recall = TP + FN === 0 ? 0 : TP / (TP + FN);
  const f1Score = precision + recall === 0 ? 0 : 2 * (precision * recall) / (precision + recall);

  const finalLogLikelihood = y_vector.reduce((sum, y_i, i) => {
      const p_i = predictions[i];
      const logP = Math.log(Math.max(p_i, 1e-10));
      const log1minusP = Math.log(Math.max(1 - p_i, 1e-10));
      return sum + (y_i * logP + (1 - y_i) * log1minusP);
  }, 0);
  const deviance = -2 * finalLogLikelihood;
  const aic = 2 * (p + 1) - 2 * finalLogLikelihood; 

  let nullLogLikelihood = 0;
  try {
      const ySum = y_vector.reduce<number>((a, b) => a + b, 0);
      const yMean = Math.max(Math.min(ySum / n, 1 - 1e-10), 1e-10);
      const logYMean = Math.log(yMean);
      const log1minusYMean = Math.log(1 - yMean);
      if (!isNaN(logYMean) && isFinite(logYMean) && !isNaN(log1minusYMean) && isFinite(log1minusYMean)) {
          nullLogLikelihood = y_vector.reduce((sum, y_i) => sum + (y_i * logYMean + (1 - y_i) * log1minusYMean), 0);
      } else {
          nullLogLikelihood = -n * Math.log(2);
      }
  } catch (e) {
      console.error('Error calculating null log-likelihood:', e);
      nullLogLikelihood = -n * Math.log(2);
  }
  const pseudoRSquared = (nullLogLikelihood !== 0 && isFinite(finalLogLikelihood) && isFinite(nullLogLikelihood))
      ? Math.max(0, Math.min(1, 1 - (finalLogLikelihood / nullLogLikelihood)))
      : 0;

  let auc = 0.5;
  try {
      const positives = y_vector.filter(y_val => y_val === 1).length;
      const negatives = y_vector.filter(y_val => y_val === 0).length;
      if (positives > 0 && negatives > 0) {
          const sortedPairs = predictions.map((prob, i) => ({ prob, label: y_vector[i] }))
              .sort((a, b) => b.prob - a.prob);
          let truePositives = 0;
          let falsePositives = 0;
          let prevProb = -1;
          let rocPoints = [{ x: 0, y: 0 }];
          for (const pair of sortedPairs) {
              if (isNaN(pair.prob) || !isFinite(pair.prob)) continue;
              if (pair.prob !== prevProb) {
                  rocPoints.push({ x: falsePositives / negatives, y: truePositives / positives });
                  prevProb = pair.prob;
              }
              if (pair.label === 1) truePositives++; else falsePositives++;
          }
          const lastPoint = { x: falsePositives / negatives, y: truePositives / positives };
          const lastAddedPoint = rocPoints[rocPoints.length - 1];
          if (lastAddedPoint.x !== lastPoint.x || lastAddedPoint.y !== lastPoint.y) {
              rocPoints.push(lastPoint);
          }
          if (rocPoints.length >= 2) {
              auc = 0;
              for (let i = 1; i < rocPoints.length; i++) {
                  const width = Math.max(0, rocPoints[i].x - rocPoints[i - 1].x);
                  const height = (rocPoints[i].y + rocPoints[i - 1].y) / 2;
                  auc += width * height;
              }
              if (isNaN(auc) || !isFinite(auc) || auc < 0 || auc > 1) auc = 0.5;
          }
      }
  } catch (e) {
      console.error('Error calculating AUC:', e);
      auc = 0.5;
  }

  const finalStdErrors = stdErrors.map(se => (isFinite(se) && !isNaN(se) ? se : NaN));
  const finalPValues = pValues.map(p_val => (isFinite(p_val) && !isNaN(p_val) ? p_val : NaN));
  const finalInterceptStdError = (isFinite(interceptStdError) && !isNaN(interceptStdError) ? interceptStdError : NaN);
  const finalInterceptPValue = (isFinite(interceptPValue) && !isNaN(interceptPValue) ? interceptPValue : NaN);

  return {
    coefficients,
    intercept,
    pValues: finalPValues,
    interceptPValue: finalInterceptPValue,
    stdErrors: finalStdErrors,
    interceptStdError: finalInterceptStdError,
    logLikelihood: finalLogLikelihood,
    aic,
    pseudoRSquared,
    deviance,
    predictions,
    confusionMatrix,
    accuracy,
    precision,
    recall,
    f1Score,
    auc,
    n,
    covMatrix: finalCovMatrix
  };
};

// Simple logistic regression (wrapper for backward compatibility)
export const logisticRegression = (
  x: number[],
  y: number[], 
  maxIterations = 10000,
  learningRate = 0.05
) => {
  if (!y.every(val => val === 0 || val === 1)) {
    throw new Error('Dependent variable must contain only 0s and 1s for binary logistic regression');
  }
  const xMatrix = x.map(val => [val]);
  return multipleLogisticRegression(xMatrix, y as (0 | 1)[], maxIterations, learningRate);
};
