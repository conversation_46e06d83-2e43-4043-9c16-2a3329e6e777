# Analysis Pipeline System Design

## Overview

This document outlines the design for a programming-like interface that allows users to create, save, and re-execute analysis pipelines for publication-ready statistical outputs. The system will record all analysis steps and enable one-click re-execution when data changes.

## Problem Statement

Currently, users must manually repeat each analysis step (Table1, Table1b, Table2, SMD calculations, etc.) when their dataset changes. This is time-consuming and error-prone. We need a system that:

1. Records all analysis steps and parameters
2. Allows easy re-execution with updated data
3. Maintains reproducibility and version control
4. Provides a programming-like interface for complex workflows

## System Architecture

### Core Components

#### 1. Pipeline Definition System
```typescript
interface AnalysisStep {
  id: string;
  type: 'table1' | 'table1b' | 'table2' | 'table3' | 'smd' | 'effect_size' | 'regression' | 'custom';
  name: string;
  component: string;
  parameters: Record<string, any>;
  dependencies: string[]; // IDs of steps this depends on
  datasetId: string;
  variableSelections: {
    primary?: string[];
    grouping?: string;
    covariates?: string[];
  };
  filters?: DataFilter[];
  timestamp: number;
  status: 'pending' | 'running' | 'completed' | 'error';
  results?: any;
  error?: string;
}

interface AnalysisPipeline {
  id: string;
  name: string;
  description: string;
  steps: AnalysisStep[];
  datasetId: string;
  createdAt: number;
  lastModified: number;
  lastExecuted?: number;
  version: number;
  tags: string[];
  isTemplate: boolean;
}
```

#### 2. Pipeline Execution Engine
```typescript
class PipelineExecutor {
  async executePipeline(pipeline: AnalysisPipeline, options?: ExecutionOptions): Promise<PipelineResult>
  async executeStep(step: AnalysisStep, context: ExecutionContext): Promise<StepResult>
  async validatePipeline(pipeline: AnalysisPipeline): Promise<ValidationResult>
  pauseExecution(): void
  resumeExecution(): void
  cancelExecution(): void
}

interface ExecutionOptions {
  skipCompleted?: boolean;
  parallelExecution?: boolean;
  stopOnError?: boolean;
  datasetOverride?: string;
}
```

#### 3. Visual Pipeline Builder
```typescript
interface PipelineBuilderProps {
  pipeline?: AnalysisPipeline;
  onSave: (pipeline: AnalysisPipeline) => void;
  onExecute: (pipeline: AnalysisPipeline) => void;
  availableSteps: AnalysisStepTemplate[];
}

interface AnalysisStepTemplate {
  type: string;
  name: string;
  description: string;
  icon: React.ComponentType;
  parameterSchema: JSONSchema;
  requiredInputs: string[];
  outputs: string[];
}
```

### Data Flow Architecture

```
Dataset → Pipeline Definition → Execution Engine → Results → Export
    ↓           ↓                    ↓              ↓        ↓
  Filters → Step Parameters → Step Execution → Validation → Publication
```

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Create Pipeline Context
```typescript
// src/context/PipelineContext.tsx
export interface PipelineContextType {
  pipelines: AnalysisPipeline[];
  currentPipeline: AnalysisPipeline | null;
  executionStatus: ExecutionStatus;
  
  // Pipeline management
  createPipeline: (name: string, description: string) => AnalysisPipeline;
  savePipeline: (pipeline: AnalysisPipeline) => void;
  loadPipeline: (id: string) => void;
  deletePipeline: (id: string) => void;
  duplicatePipeline: (id: string, newName: string) => AnalysisPipeline;
  
  // Step management
  addStep: (step: Omit<AnalysisStep, 'id' | 'timestamp'>) => void;
  updateStep: (stepId: string, updates: Partial<AnalysisStep>) => void;
  removeStep: (stepId: string) => void;
  reorderSteps: (stepIds: string[]) => void;
  
  // Execution
  executePipeline: (pipelineId: string, options?: ExecutionOptions) => Promise<void>;
  executeStep: (stepId: string) => Promise<void>;
  pauseExecution: () => void;
  resumeExecution: () => void;
  cancelExecution: () => void;
}
```

#### 1.2 Create Step Registry
```typescript
// src/utils/pipeline/stepRegistry.ts
export class StepRegistry {
  private static steps: Map<string, AnalysisStepTemplate> = new Map();
  
  static registerStep(template: AnalysisStepTemplate): void
  static getStep(type: string): AnalysisStepTemplate | undefined
  static getAllSteps(): AnalysisStepTemplate[]
  static getStepsByCategory(category: string): AnalysisStepTemplate[]
}

// Register existing analysis components
StepRegistry.registerStep({
  type: 'table1',
  name: 'Table 1 - Descriptive Statistics',
  description: 'Generate descriptive statistics table',
  icon: TableIcon,
  parameterSchema: {
    type: 'object',
    properties: {
      variables: { type: 'array', items: { type: 'string' } },
      groupBy: { type: 'string' },
      includeOverall: { type: 'boolean', default: true }
    }
  },
  requiredInputs: ['dataset'],
  outputs: ['table1_results']
});
```

### Phase 2: Visual Pipeline Builder (Week 3-4)

#### 2.1 Pipeline Canvas Component
```typescript
// src/components/Pipeline/PipelineCanvas.tsx
export const PipelineCanvas: React.FC<PipelineCanvasProps> = ({
  pipeline,
  onStepAdd,
  onStepUpdate,
  onStepRemove,
  onStepConnect
}) => {
  // Drag-and-drop interface for building pipelines
  // Visual representation of step dependencies
  // Real-time validation feedback
};
```

#### 2.2 Step Configuration Panel
```typescript
// src/components/Pipeline/StepConfigPanel.tsx
export const StepConfigPanel: React.FC<StepConfigPanelProps> = ({
  step,
  availableDatasets,
  availableVariables,
  onParameterChange
}) => {
  // Dynamic form generation based on step schema
  // Variable selection with data type validation
  // Parameter validation and preview
};
```

### Phase 3: Execution Engine (Week 5-6)

#### 3.1 Pipeline Executor
```typescript
// src/utils/pipeline/PipelineExecutor.ts
export class PipelineExecutor {
  private eventEmitter = new EventEmitter();
  
  async executePipeline(pipeline: AnalysisPipeline): Promise<PipelineResult> {
    // Validate pipeline structure
    const validation = await this.validatePipeline(pipeline);
    if (!validation.isValid) {
      throw new Error(`Pipeline validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Execute steps in dependency order
    const executionOrder = this.calculateExecutionOrder(pipeline.steps);
    const results: Record<string, any> = {};
    
    for (const stepId of executionOrder) {
      const step = pipeline.steps.find(s => s.id === stepId);
      if (!step) continue;
      
      this.eventEmitter.emit('stepStarted', step);
      
      try {
        const result = await this.executeStep(step, { results, pipeline });
        results[stepId] = result;
        this.eventEmitter.emit('stepCompleted', step, result);
      } catch (error) {
        this.eventEmitter.emit('stepError', step, error);
        throw error;
      }
    }
    
    return { results, executionTime: Date.now() };
  }
  
  private async executeStep(step: AnalysisStep, context: ExecutionContext): Promise<any> {
    const stepTemplate = StepRegistry.getStep(step.type);
    if (!stepTemplate) {
      throw new Error(`Unknown step type: ${step.type}`);
    }
    
    // Get the appropriate executor for this step type
    const executor = this.getStepExecutor(step.type);
    return await executor.execute(step, context);
  }
}
```

#### 3.2 Step Executors
```typescript
// src/utils/pipeline/executors/Table1Executor.ts
export class Table1Executor implements StepExecutor {
  async execute(step: AnalysisStep, context: ExecutionContext): Promise<any> {
    const { datasetId, parameters } = step;
    const dataset = context.getDataset(datasetId);
    
    // Apply filters if specified
    const filteredData = this.applyFilters(dataset, step.filters);
    
    // Execute Table1 analysis with parameters
    const results = await this.runTable1Analysis(filteredData, parameters);
    
    return {
      type: 'table1',
      data: results,
      metadata: {
        datasetSize: filteredData.length,
        variables: parameters.variables,
        timestamp: Date.now()
      }
    };
  }
}
```

### Phase 4: Template System (Week 7)

#### 4.1 Pipeline Templates
```typescript
// src/utils/pipeline/templates/
export const CLINICAL_TRIAL_TEMPLATE: AnalysisPipeline = {
  id: 'clinical-trial-template',
  name: 'Clinical Trial Analysis',
  description: 'Standard analysis pipeline for randomized controlled trials',
  isTemplate: true,
  steps: [
    {
      id: 'baseline-characteristics',
      type: 'table1',
      name: 'Baseline Characteristics',
      parameters: {
        groupBy: 'treatment_group',
        includeOverall: true,
        statisticalTests: true
      }
    },
    {
      id: 'primary-outcome',
      type: 'table2',
      name: 'Primary Outcome Analysis',
      dependencies: ['baseline-characteristics'],
      parameters: {
        outcomeVariable: 'primary_outcome',
        groupBy: 'treatment_group',
        adjustForBaseline: true
      }
    },
    {
      id: 'effect-sizes',
      type: 'effect_size',
      name: 'Effect Size Calculations',
      dependencies: ['primary-outcome'],
      parameters: {
        effectSizeType: 'cohens_d',
        confidenceLevel: 0.95
      }
    }
  ]
};
```

### Phase 5: Advanced Features (Week 8-9)

#### 5.1 Conditional Execution
```typescript
interface ConditionalStep extends AnalysisStep {
  condition?: {
    type: 'data_check' | 'result_check' | 'custom';
    expression: string;
    description: string;
  };
}

// Example: Only run normality test if sample size > 30
{
  condition: {
    type: 'data_check',
    expression: 'dataset.length > 30',
    description: 'Run normality test only for large samples'
  }
}
```

#### 5.2 Parameter Binding
```typescript
interface ParameterBinding {
  sourceStepId: string;
  sourceProperty: string;
  targetParameter: string;
  transform?: (value: any) => any;
}

// Example: Use significant variables from correlation analysis in regression
{
  sourceStepId: 'correlation-analysis',
  sourceProperty: 'significantVariables',
  targetParameter: 'predictors',
  transform: (vars) => vars.filter(v => v.pValue < 0.05)
}
```

#### 5.3 Data Versioning
```typescript
interface DataVersion {
  id: string;
  datasetId: string;
  version: number;
  changes: DataChange[];
  timestamp: number;
  checksum: string;
}

interface DataChange {
  type: 'add_column' | 'remove_column' | 'modify_data' | 'filter_applied';
  description: string;
  affectedColumns: string[];
  metadata: Record<string, any>;
}
```

## User Interface Design

### 1. Pipeline Builder Interface

```
┌─────────────────────────────────────────────────────────────┐
│ Pipeline Builder: Clinical Trial Analysis                   │
├─────────────────┬───────────────────────────────────────────┤
│ Step Library    │ Canvas                                    │
│                 │                                           │
│ □ Table 1       │  ┌─────────┐    ┌─────────┐              │
│ □ Table 1b      │  │ Table 1 │───▶│ Table 2 │              │
│ □ Table 2       │  │ (Step 1)│    │ (Step 2)│              │
│ □ Table 3       │  └─────────┘    └─────────┘              │
│ □ SMD           │       │              │                   │
│ □ Effect Size   │       ▼              ▼                   │
│ □ Regression    │  ┌─────────┐    ┌─────────┐              │
│ □ Custom        │  │ SMD     │    │ Export  │              │
│                 │  │ (Step 3)│    │ (Step 4)│              │
│                 │  └─────────┘    └─────────┘              │
├─────────────────┼───────────────────────────────────────────┤
│ Properties      │ Execution Log                             │
│                 │                                           │
│ Step: Table 1   │ ✓ Step 1: Table 1 completed (2.3s)      │
│ Variables: [▼]  │ ✓ Step 2: Table 2 completed (1.8s)      │
│ Group By: [▼]   │ ⚠ Step 3: SMD warning - small sample    │
│ Tests: ☑        │ ✓ Step 4: Export completed (0.5s)       │
└─────────────────┴───────────────────────────────────────────┘
```

### 2. Execution Dashboard

```
┌─────────────────────────────────────────────────────────────┐
│ Pipeline Execution: Clinical Trial Analysis                 │
├─────────────────────────────────────────────────────────────┤
│ Status: Running (Step 2 of 4) ████████░░ 75%               │
│                                                             │
│ ┌─ Step 1: Baseline Characteristics ──────────────────────┐ │
│ │ Status: ✓ Completed (2.3s)                              │ │
│ │ Results: 150 participants, 12 variables analyzed        │ │
│ │ Output: table1_results.json                             │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─ Step 2: Primary Outcome Analysis ──────────────────────┐ │
│ │ Status: ⚡ Running... (1.2s elapsed)                    │ │
│ │ Progress: Calculating group comparisons                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [Pause] [Cancel] [View Results] [Export Pipeline]          │
└─────────────────────────────────────────────────────────────┘
```

## File Structure

```
src/
├── components/
│   └── Pipeline/
│       ├── PipelineBuilder.tsx
│       ├── PipelineCanvas.tsx
│       ├── StepLibrary.tsx
│       ├── StepConfigPanel.tsx
│       ├── ExecutionDashboard.tsx
│       ├── PipelineTemplates.tsx
│       └── index.ts
├── context/
│   └── PipelineContext.tsx
├── utils/
│   └── pipeline/
│       ├── PipelineExecutor.ts
│       ├── StepRegistry.ts
│       ├── executors/
│       │   ├── Table1Executor.ts
│       │   ├── Table2Executor.ts
│       │   ├── SMDExecutor.ts
│       │   └── index.ts
│       ├── templates/
│       │   ├── ClinicalTrialTemplate.ts
│       │   ├── ObservationalStudyTemplate.ts
│       │   └── index.ts
│       └── validation/
│           ├── PipelineValidator.ts
│           └── StepValidator.ts
├── pages/
│   └── PipelineBuilderPage.tsx
└── types/
    └── pipeline.ts
```

## Integration with Existing Systems

### 1. Results Manager Integration
- Pipeline results automatically saved to Results Manager
- Batch export of all pipeline outputs
- Version tracking of pipeline executions

### 2. Data Context Integration
- Automatic detection of dataset changes
- Smart re-execution suggestions
- Data validation before pipeline execution

### 3. Project Management Integration
- Pipelines saved as part of projects
- Cloud synchronization for Pro users
- Collaborative pipeline development

## Benefits

### For Researchers
1. **Reproducibility**: Exact replication of analysis steps
2. **Efficiency**: One-click re-execution with new data
3. **Standardization**: Consistent analysis workflows
4. **Documentation**: Automatic methods documentation

### For Teams
1. **Collaboration**: Shared analysis pipelines
2. **Quality Control**: Standardized procedures
3. **Training**: Template-based learning
4. **Compliance**: Audit trail of all analyses

### For Publications
1. **Methods Section**: Auto-generated methodology
2. **Reproducible Research**: Complete analysis record
3. **Supplementary Materials**: Exportable pipelines
4. **Peer Review**: Transparent analysis process

## Future Enhancements

### 1. AI-Powered Suggestions
- Automatic pipeline generation based on data characteristics
- Smart parameter recommendations
- Analysis quality checks

### 2. Advanced Scheduling
- Automated re-execution on data updates
- Scheduled pipeline runs
- Email notifications for completion

### 3. Integration with External Tools
- R/Python script execution
- SPSS/SAS pipeline import
- Git version control integration

### 4. Performance Optimization
- Parallel step execution
- Incremental updates
- Result caching

## Implementation Timeline

- **Week 1-2**: Core infrastructure and context setup
- **Week 3-4**: Visual pipeline builder interface
- **Week 5-6**: Execution engine and step executors
- **Week 7**: Template system and predefined workflows
- **Week 8-9**: Advanced features and optimization
- **Week 10**: Testing, documentation, and deployment

This design provides a robust foundation for creating reproducible, efficient analysis workflows while maintaining the flexibility to handle complex research scenarios.