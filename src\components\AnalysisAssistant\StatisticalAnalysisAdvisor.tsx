import React, { useState, useCallback, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Grid,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { AuthContext } from '../../context/AuthContext';
import {
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  CompareArrows as CompareArrowsIcon,
  ScatterPlot as ScatterPlotIcon,
  BarChart as BarChartIcon,
  Timeline as TimelineIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  Lightbulb as LightbulbIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Help as HelpIcon,
  Launch as LaunchIcon,
  Login as LoginIcon,
  Upgrade as UpgradeIcon,
  School as SchoolIcon,
  Star as StarIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';

// Types for the decision tree
interface DecisionNode {
  id: string;
  question: string;
  description?: string;
  options: DecisionOption[];
  category?: 'data' | 'objective' | 'assumptions' | 'result';
}

interface DecisionOption {
  id: string;
  label: string;
  description?: string;
  nextNodeId?: string;
  recommendation?: StatisticalMethod;
}

interface StatisticalMethod {
  id: string;
  name: string;
  category: string;
  description: string;
  assumptions: string[];
  dataRequirements: string[];
  interpretation: string;
  implementation: {
    r?: string;
    python?: string;
    spss?: string;
  };
  examples: string[];
  relatedMethods: string[];
}

// Statistical methods database
const statisticalMethods: Record<string, StatisticalMethod> = {
  descriptive_single: {
    id: 'descriptive_single',
    name: 'Descriptive Statistics (Single Variable)',
    category: 'Descriptive',
    description: 'Summarize the central tendency, variability, and distribution shape of a single numeric variable.',
    assumptions: ['Variable should be numeric', 'No missing data assumptions'],
    dataRequirements: ['At least 10-20 observations recommended', 'Single numeric variable'],
    interpretation: 'Mean shows central tendency, standard deviation shows spread, skewness indicates distribution shape.',
    implementation: {
      r: 'summary(data$variable)\nsd(data$variable)\nskewness(data$variable)',
      python: 'data["variable"].describe()\ndata["variable"].skew()',
      spss: 'DESCRIPTIVES VARIABLES=variable /STATISTICS=MEAN STDDEV SKEWNESS KURTOSIS.'
    },
    examples: ['Average test scores in a class', 'Distribution of patient ages', 'Summary of sales figures'],
    relatedMethods: ['histogram', 'boxplot', 'normality_test']
  },
  frequency_table: {
    id: 'frequency_table',
    name: 'Frequency Table & Proportions',
    category: 'Descriptive',
    description: 'Count and percentage breakdown of categories in a categorical variable.',
    assumptions: ['Variable should be categorical', 'Categories should be mutually exclusive'],
    dataRequirements: ['Categorical variable with defined categories'],
    interpretation: 'Shows how observations are distributed across categories, identifies most/least common categories.',
    implementation: {
      r: 'table(data$variable)\nprop.table(table(data$variable))',
      python: 'data["variable"].value_counts()\ndata["variable"].value_counts(normalize=True)',
      spss: 'FREQUENCIES VARIABLES=variable.'
    },
    examples: ['Gender distribution in survey', 'Product preference categories', 'Education level breakdown'],
    relatedMethods: ['bar_chart', 'pie_chart', 'chi_square']
  },
  t_test: {
    id: 't_test',
    name: 'Independent Samples t-test',
    category: 'Comparison',
    description: 'Compare means between two independent groups when data is normally distributed.',
    assumptions: ['Normal distribution in both groups', 'Equal variances (homoscedasticity)', 'Independent observations'],
    dataRequirements: ['Two groups with numeric outcome', 'At least 15-20 observations per group'],
    interpretation: 'p < 0.05 suggests significant difference between group means. Effect size (Cohen\'s d) indicates practical significance.',
    implementation: {
      r: 't.test(outcome ~ group, data=data, var.equal=TRUE)',
      python: 'from scipy.stats import ttest_ind\nttest_ind(group1, group2)',
      spss: 'T-TEST GROUPS=group(1 2) /VARIABLES=outcome.'
    },
    examples: ['Compare test scores between teaching methods', 'Height differences between genders', 'Sales performance between regions'],
    relatedMethods: ['welch_t_test', 'mann_whitney', 'effect_size']
  },
  mann_whitney: {
    id: 'mann_whitney',
    name: 'Mann-Whitney U Test',
    category: 'Comparison',
    description: 'Non-parametric test to compare distributions between two independent groups.',
    assumptions: ['Independent observations', 'Ordinal or continuous data', 'Similar distribution shapes (for median comparison)'],
    dataRequirements: ['Two groups with ordinal or numeric outcome', 'No minimum sample size requirement'],
    interpretation: 'p < 0.05 suggests significant difference in distributions. Reports median differences and effect size (r).',
    implementation: {
      r: 'wilcox.test(outcome ~ group, data=data)',
      python: 'from scipy.stats import mannwhitneyu\nmannwhitneyu(group1, group2)',
      spss: 'NPAR TESTS /M-W= outcome BY group(1 2).'
    },
    examples: ['Compare satisfaction ratings between groups', 'Income differences with skewed data', 'Likert scale comparisons'],
    relatedMethods: ['t_test', 'kruskal_wallis', 'wilcoxon_signed_rank']
  },
  chi_square: {
    id: 'chi_square',
    name: 'Chi-Square Test of Independence',
    category: 'Association',
    description: 'Test association between two categorical variables or compare proportions across groups.',
    assumptions: ['Categorical variables', 'Independent observations', 'Expected frequencies ≥ 5 in most cells'],
    dataRequirements: ['Two categorical variables', 'Sufficient sample size for expected frequencies'],
    interpretation: 'p < 0.05 suggests significant association. Cramér\'s V indicates effect size.',
    implementation: {
      r: 'chisq.test(table(data$var1, data$var2))',
      python: 'from scipy.stats import chi2_contingency\nchi2_contingency(crosstab)',
      spss: 'CROSSTABS /TABLES=var1 BY var2 /STATISTICS=CHISQ.'
    },
    examples: ['Gender vs. product preference', 'Treatment outcome vs. group', 'Education level vs. voting choice'],
    relatedMethods: ['fisher_exact', 'cramers_v', 'contingency_table']
  },
  correlation: {
    id: 'correlation',
    name: 'Pearson Correlation',
    category: 'Association',
    description: 'Measure linear relationship strength between two numeric variables.',
    assumptions: ['Both variables numeric', 'Linear relationship', 'Normal distribution (for significance testing)'],
    dataRequirements: ['Two numeric variables', 'At least 20-30 paired observations'],
    interpretation: 'r ranges from -1 to +1. |r| > 0.7 = strong, 0.3-0.7 = moderate, < 0.3 = weak relationship.',
    implementation: {
      r: 'cor.test(data$var1, data$var2)',
      python: 'from scipy.stats import pearsonr\npearsonr(var1, var2)',
      spss: 'CORRELATIONS /VARIABLES=var1 var2.'
    },
    examples: ['Height vs. weight relationship', 'Study time vs. test scores', 'Temperature vs. ice cream sales'],
    relatedMethods: ['spearman_correlation', 'regression', 'scatterplot']
  },
  descriptive_multiple: {
    id: 'descriptive_multiple',
    name: 'Comprehensive Descriptive Analysis',
    category: 'Descriptive',
    description: 'Complete descriptive summary for datasets with multiple variables of different types.',
    assumptions: ['Mixed variable types acceptable', 'Consider missing data patterns'],
    dataRequirements: ['Multiple variables (numeric and/or categorical)'],
    interpretation: 'Provides overview of data structure, missing patterns, and variable relationships.',
    implementation: {
      r: 'summary(data)\npsych::describe(data)\nVIM::aggr(data)',
      python: 'data.describe(include="all")\ndata.info()\ndata.isnull().sum()',
      spss: 'DESCRIPTIVES VARIABLES=ALL /STATISTICS=MEAN STDDEV MIN MAX.'
    },
    examples: ['Survey data analysis', 'Patient demographics summary', 'Product feature analysis'],
    relatedMethods: ['correlation_matrix', 'missing_data_analysis', 'data_visualization']
  }
};

// Decision tree data structure
const decisionTree: Record<string, DecisionNode> = {
  start: {
    id: 'start',
    question: 'What is your primary research objective?',
    description: 'Select the main goal of your statistical analysis',
    category: 'objective',
    options: [
      {
        id: 'describe',
        label: 'Describe/Summarize Data',
        description: 'Explore and summarize characteristics of your dataset',
        nextNodeId: 'descriptive_type'
      },
      {
        id: 'compare',
        label: 'Compare Groups',
        description: 'Test differences between two or more groups',
        nextNodeId: 'compare_groups'
      },
      {
        id: 'relationship',
        label: 'Examine Relationships',
        description: 'Analyze associations between variables',
        nextNodeId: 'relationship_type'
      },
      {
        id: 'predict',
        label: 'Predict/Model',
        description: 'Build predictive models or forecast outcomes',
        nextNodeId: 'prediction_type'
      }
    ]
  },
  descriptive_type: {
    id: 'descriptive_type',
    question: 'What type of data are you describing?',
    category: 'data',
    options: [
      {
        id: 'single_numeric',
        label: 'Single Numeric Variable',
        description: 'One continuous or discrete numeric variable',
        recommendation: statisticalMethods.descriptive_single
      },
      {
        id: 'single_categorical',
        label: 'Single Categorical Variable',
        description: 'One categorical or nominal variable',
        recommendation: statisticalMethods.frequency_table
      },
      {
        id: 'multiple_variables',
        label: 'Multiple Variables',
        description: 'Several variables of different types',
        recommendation: statisticalMethods.descriptive_multiple
      }
    ]
  },
  compare_groups: {
    id: 'compare_groups',
    question: 'How many groups are you comparing?',
    category: 'data',
    options: [
      {
        id: 'two_groups',
        label: 'Two Groups',
        description: 'Comparing exactly two groups or conditions',
        nextNodeId: 'two_groups_data'
      },
      {
        id: 'multiple_groups',
        label: 'Three or More Groups',
        description: 'Comparing multiple groups simultaneously',
        nextNodeId: 'multiple_groups_data'
      }
    ]
  },
  two_groups_data: {
    id: 'two_groups_data',
    question: 'What type of outcome variable are you comparing?',
    category: 'data',
    options: [
      {
        id: 'numeric_outcome',
        label: 'Numeric/Continuous',
        description: 'Measurements like height, weight, test scores',
        nextNodeId: 'two_groups_assumptions'
      },
      {
        id: 'categorical_outcome',
        label: 'Categorical/Binary',
        description: 'Categories like success/failure, yes/no',
        recommendation: statisticalMethods.chi_square
      }
    ]
  },
  two_groups_assumptions: {
    id: 'two_groups_assumptions',
    question: 'Are your data normally distributed with equal variances?',
    category: 'assumptions',
    options: [
      {
        id: 'normal_equal',
        label: 'Yes, Normal & Equal Variances',
        description: 'Data follows normal distribution, groups have similar spread',
        recommendation: statisticalMethods.t_test
      },
      {
        id: 'not_normal',
        label: 'No, Non-normal or Unequal Variances',
        description: 'Data is skewed, has outliers, or groups have different spreads',
        recommendation: statisticalMethods.mann_whitney
      }
    ]
  },
  relationship_type: {
    id: 'relationship_type',
    question: 'What types of variables are you examining?',
    category: 'data',
    options: [
      {
        id: 'two_numeric',
        label: 'Two Numeric Variables',
        description: 'Both variables are continuous or numeric',
        recommendation: statisticalMethods.correlation
      },
      {
        id: 'numeric_categorical',
        label: 'Numeric & Categorical',
        description: 'One numeric outcome, one categorical predictor',
        nextNodeId: 'compare_groups'
      },
      {
        id: 'two_categorical',
        label: 'Two Categorical Variables',
        description: 'Both variables are categorical or nominal',
        recommendation: statisticalMethods.chi_square
      }
    ]
  }
};

// Navigation mapping for internal DataStatPro routes
const INTERNAL_ROUTES = {
  descriptive_single: '/app/stats/descriptives',
  frequency_table: '/app/stats/frequencies',
  descriptive_multiple: '/app/stats/descriptives',
  t_test: '/app/inferential-stats/independent-samples-ttest',
  mann_whitney: '/app/inferential-stats/mann-whitney-u-test',
  chi_square: '/app/inferential-stats/chi-square-test',
  correlation: '/app/stats/descriptives', // Correlation is part of descriptive stats
  one_way_anova: '/app/inferential-stats/one-way-anova',
  paired_t_test: '/app/inferential-stats/paired-samples-ttest',
  wilcoxon: '/app/inferential-stats/wilcoxon-signed-rank-test',
  kruskal_wallis: '/app/inferential-stats/kruskal-wallis-test',
  repeated_measures_anova: '/app/inferential-stats/repeated-measures-anova',
  two_way_anova: '/app/inferential-stats/two-way-anova'
};

// Access level requirements for different methods
const ACCESS_REQUIREMENTS = {
  descriptive_single: 'guest', // Available to all users
  frequency_table: 'guest',
  descriptive_multiple: 'guest',
  t_test: 'guest',
  mann_whitney: 'standard', // Requires at least standard account
  chi_square: 'guest',
  correlation: 'guest',
  one_way_anova: 'standard',
  paired_t_test: 'guest',
  wilcoxon: 'standard',
  kruskal_wallis: 'standard',
  repeated_measures_anova: 'pro', // Requires pro account
  two_way_anova: 'pro'
};

interface StatisticalAnalysisAdvisorProps {
  currentDataset?: any;
  datasetAnalysis?: any;
}

const StatisticalAnalysisAdvisor: React.FC<StatisticalAnalysisAdvisorProps> = ({
  currentDataset,
  datasetAnalysis
}) => {
  const { 
    user, 
    isGuest, 
    accountType, 
    effectiveTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    isEducationalUser,
    educationalTier
  } = useContext(AuthContext);
  const navigate = useNavigate();
  const [currentNodeId, setCurrentNodeId] = useState<string>('start');
  const [selectedPath, setSelectedPath] = useState<DecisionOption[]>([]);
  const [recommendation, setRecommendation] = useState<StatisticalMethod | null>(null);
  const [activeStep, setActiveStep] = useState<number>(0);
  const [showOnboarding, setShowOnboarding] = useState<boolean>(false);
  const [showAccessDialog, setShowAccessDialog] = useState<boolean>(false);
  const [restrictedMethod, setRestrictedMethod] = useState<string | null>(null);

  const currentNode = decisionTree[currentNodeId];
  const isAuthenticated = !!user;

  // Helper function to check if user can access a specific method
  const canAccessMethod = useCallback((methodId: string): boolean => {
    const requiredLevel = ACCESS_REQUIREMENTS[methodId as keyof typeof ACCESS_REQUIREMENTS];
    if (!requiredLevel) return true; // If no requirement specified, allow access
    
    if (isGuest) return requiredLevel === 'guest';
    if (!user) return requiredLevel === 'guest';
    
    const tierHierarchy = { guest: 0, standard: 1, edu: 1, pro: 2, edu_pro: 2 };
    const requiredTierLevel = tierHierarchy[requiredLevel as keyof typeof tierHierarchy] || 0;
    const userTierLevel = tierHierarchy[effectiveTier as keyof typeof tierHierarchy] || 0;
    
    return userTierLevel >= requiredTierLevel;
  }, [isGuest, user, effectiveTier]);

  // Helper function to get upgrade message based on user type
  const getUpgradeMessage = useCallback((requiredLevel: string): string => {
    if (isGuest) {
      return 'Sign up for a free account to access this analysis method.';
    }
    if (requiredLevel === 'standard' && effectiveTier === 'guest') {
      return 'Upgrade to Standard account to access this advanced analysis method.';
    }
    if (requiredLevel === 'pro') {
      if (isEducationalUser) {
        return 'Upgrade to Educational Pro to access this premium analysis method.';
      }
      return 'Upgrade to Pro account to access this premium analysis method.';
    }
    return 'Account upgrade required to access this analysis method.';
  }, [isGuest, effectiveTier, isEducationalUser]);

  // Helper function to navigate to internal DataStatPro analysis
  const navigateToAnalysis = useCallback((methodId: string) => {
    const route = INTERNAL_ROUTES[methodId as keyof typeof INTERNAL_ROUTES];
    if (route) {
      window.location.href = `http://localhost:5173${route}`;
    } else {
      // Fallback to dashboard if route not found
      window.location.href = 'http://localhost:5173/app/dashboard';
    }
  }, []);

  const handleOptionSelect = useCallback((option: DecisionOption) => {
    const newPath = [...selectedPath, option];
    setSelectedPath(newPath);
    setActiveStep(newPath.length);

    if (option.recommendation) {
      setRecommendation(option.recommendation);
    } else if (option.nextNodeId) {
      setCurrentNodeId(option.nextNodeId);
    }
  }, [selectedPath]);

  const handleReset = useCallback(() => {
    setCurrentNodeId('start');
    setSelectedPath([]);
    setRecommendation(null);
    setActiveStep(0);
  }, []);

  const handleBack = useCallback(() => {
    if (selectedPath.length > 0) {
      const newPath = selectedPath.slice(0, -1);
      setSelectedPath(newPath);
      setActiveStep(newPath.length);
      setRecommendation(null);
      
      if (newPath.length === 0) {
        setCurrentNodeId('start');
      } else {
        // Find the previous node based on the path
        let nodeId = 'start';
        for (const pathOption of newPath) {
          if (pathOption.nextNodeId) {
            nodeId = pathOption.nextNodeId;
          }
        }
        setCurrentNodeId(nodeId);
      }
    }
  }, [selectedPath]);

  const getContextualRecommendations = () => {
    if (!currentDataset || !datasetAnalysis) return [];
    
    const recommendations = [];
    const { variableAnalysis } = datasetAnalysis;
    
    const numericVars = variableAnalysis.filter((v: any) => v.type === 'numeric').length;
    const categoricalVars = variableAnalysis.filter((v: any) => v.type === 'categorical').length;
    
    if (numericVars >= 2) {
      recommendations.push('Consider correlation analysis between numeric variables');
    }
    
    if (numericVars >= 1 && categoricalVars >= 1) {
      recommendations.push('Compare numeric variables across categorical groups');
    }
    
    if (categoricalVars >= 2) {
      recommendations.push('Examine associations between categorical variables');
    }
    
    return recommendations;
  };

  return (
    <Box>
      {/* Header with Enhanced UI */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PsychologyIcon color="primary" />
            <Typography variant="h6">
              Statistical Analysis Advisor
            </Typography>
            {isEducationalUser && (
              <Chip 
                icon={<SchoolIcon />} 
                label={educationalTier === 'pro' ? 'Educational Pro' : 'Educational'} 
                color="secondary" 
                size="small" 
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="How to use this advisor">
              <IconButton 
                size="small" 
                onClick={() => setShowOnboarding(true)}
                color="primary"
              >
                <HelpIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Get personalized recommendations for statistical methods based on your research questions and data characteristics.
        </Typography>
        
        {/* Quick Start Tips */}
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            💡 Quick Start Tips:
          </Typography>
          <Typography variant="body2">
            • Answer questions about your research objective and data type
            • Get method recommendations with DataStatPro integration
            • Click "Analyze in DataStatPro" to implement suggestions directly
          </Typography>
        </Alert>
      </Box>

      {/* Context Panel */}
      {currentDataset && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            📊 Dataset Context: {currentDataset.name}
          </Typography>
          <Typography variant="body2">
            {datasetAnalysis?.variableAnalysis?.length || 0} variables detected.
            {getContextualRecommendations().length > 0 && (
              <>
                <br />
                💡 Suggestions: {getContextualRecommendations().join(', ')}
              </>
            )}
          </Typography>
        </Alert>
      )}

      {/* Progress Stepper */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Stepper activeStep={activeStep} orientation="horizontal" alternativeLabel>
          <Step>
            <StepLabel>Research Objective</StepLabel>
          </Step>
          <Step>
            <StepLabel>Data Characteristics</StepLabel>
          </Step>
          <Step>
            <StepLabel>Method Selection</StepLabel>
          </Step>
          <Step>
            <StepLabel>Implementation</StepLabel>
          </Step>
        </Stepper>
      </Paper>

      {/* Access Control & User Communication */}
      {isGuest && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <LoginIcon fontSize="small" />
                Guest Access - Limited Features
              </Typography>
              <Typography variant="body2">
                You have access to basic statistical methods. Sign up for free to unlock advanced analyses and save your work.
              </Typography>
            </Box>
            <Button 
              variant="contained" 
              size="small" 
              startIcon={<LoginIcon />}
              onClick={() => navigate('/app#auth/register')}
            >
              Sign Up Free
            </Button>
          </Box>
        </Alert>
      )}
      
      {!isAuthenticated && !isGuest && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <StarIcon fontSize="small" />
                Enhanced Features Available
              </Typography>
              <Typography variant="body2">
                Sign in to save recommendations, track usage, get personalized suggestions, and access advanced statistical methods.
              </Typography>
            </Box>
            <Button 
              variant="outlined" 
              size="small" 
              startIcon={<LoginIcon />}
              onClick={() => navigate('/app#auth/login')}
            >
              Sign In
            </Button>
          </Box>
        </Alert>
      )}
      
      {isAuthenticated && accountType === 'standard' && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckCircleIcon fontSize="small" />
            Standard Account - Full Access to Core Methods
          </Typography>
          <Typography variant="body2">
            You have access to all basic and intermediate statistical methods. Upgrade to Pro for advanced analyses.
          </Typography>
        </Alert>
      )}
      
      {isAuthenticated && (accountType === 'pro' || accountType === 'edu_pro') && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <StarIcon fontSize="small" />
            {accountType === 'edu_pro' ? 'Educational Pro' : 'Pro'} Account - Premium Access
          </Typography>
          <Typography variant="body2">
            You have full access to all statistical methods, including advanced analyses and premium features.
          </Typography>
        </Alert>
      )}
      
      {isAuthenticated && accountType === 'edu' && educationalTier === 'free' && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <SchoolIcon fontSize="small" />
                Educational Account - Advanced Analysis Access
              </Typography>
              <Typography variant="body2">
                You have access to advanced statistical methods. Upgrade to Educational Pro for premium publication-ready tools.
              </Typography>
            </Box>
            <Button 
              variant="contained" 
              size="small" 
              startIcon={<UpgradeIcon />}
              onClick={() => navigate('/app#pricing')}
            >
              Upgrade
            </Button>
          </Box>
        </Alert>
      )}

      {/* Decision Tree Interface */}
      {!recommendation ? (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            {currentNode ? (
              <>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    {currentNode.question}
                  </Typography>
                  {selectedPath.length > 0 && (
                    <Button
                      startIcon={<ArrowBackIcon />}
                      onClick={handleBack}
                      size="small"
                    >
                      Back
                    </Button>
                  )}
                </Box>
                
                {currentNode.description && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {currentNode.description}
                  </Typography>
                )}

                <Grid container spacing={2}>
                  {currentNode.options.map((option) => (
                    <Grid item xs={12} sm={6} md={4} key={option.id}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: 2,
                            borderColor: 'primary.main'
                          }
                        }}
                        onClick={() => handleOptionSelect(option)}
                      >
                        <CardContent>
                          <Typography variant="subtitle1" sx={{ mb: 1 }}>
                            {option.label}
                          </Typography>
                          {option.description && (
                            <Typography variant="body2" color="text.secondary">
                              {option.description}
                            </Typography>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </>
            ) : (
              <Alert severity="error">
                <Typography variant="subtitle2">Decision Tree Error</Typography>
                <Typography variant="body2">
                  Unable to load the current question. Please try refreshing the page or starting over.
                </Typography>
                <Button onClick={handleReset} sx={{ mt: 1 }} variant="outlined" size="small">
                  Start Over
                </Button>
              </Alert>
            )}

            {/* Selected Path Display */}
            {selectedPath.length > 0 && (
              <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>Your Selection Path:</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {selectedPath.map((option, index) => (
                    <Chip
                      key={index}
                      label={option.label}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      ) : (
        /* Method Recommendation Display */
        <Box>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CheckCircleIcon color="success" />
                  <Typography variant="h6">
                    Recommended Method: {recommendation.name}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {/* Access Control & Navigation */}
                  {canAccessMethod(recommendation.id) ? (
                    INTERNAL_ROUTES[recommendation.id] ? (
                      <Button
                        variant="contained"
                        startIcon={<PlayArrowIcon />}
                        onClick={() => navigateToAnalysis(recommendation.id)}
                        size="small"
                      >
                        Run in DataStatPro
                      </Button>
                    ) : (
                      <Tooltip title="This method is available but not yet integrated with DataStatPro's interface">
                        <Button
                          variant="outlined"
                          startIcon={<LaunchIcon />}
                          disabled
                          size="small"
                        >
                          Coming Soon
                        </Button>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip title={getUpgradeMessage(recommendation.id)}>
                      <Button
                        variant="outlined"
                        startIcon={<UpgradeIcon />}
                        onClick={() => {
                          setRestrictedMethod(recommendation.id);
                          setShowAccessDialog(true);
                        }}
                        size="small"
                        color="warning"
                      >
                        {ACCESS_REQUIREMENTS[recommendation.id] === 'pro' ? 'Pro Required' : 'Upgrade'}
                      </Button>
                    </Tooltip>
                  )}
                  <Button
                    variant="outlined"
                    onClick={handleReset}
                    startIcon={<ArrowBackIcon />}
                    size="small"
                  >
                    Start Over
                  </Button>
                </Box>
              </Box>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip 
                  label={recommendation.category} 
                  color="primary" 
                  size="small"
                />
                {!canAccessMethod(recommendation.id) && (
                  <Chip 
                    label={ACCESS_REQUIREMENTS[recommendation.id] === 'pro' ? 'Pro Feature' : 'Premium Feature'}
                    color="warning" 
                    size="small"
                    icon={<UpgradeIcon />}
                  />
                )}
              </Box>
              
              <Typography variant="body1" sx={{ mb: 3 }}>
                {recommendation.description}
              </Typography>

              {/* Quick Access Panel for Internal Methods */}
              {canAccessMethod(recommendation.id) && INTERNAL_ROUTES[recommendation.id] && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LaunchIcon fontSize="small" />
                    Available in DataStatPro
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    This analysis is available directly in DataStatPro with an intuitive interface. No coding required!
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<PlayArrowIcon />}
                    onClick={() => navigateToAnalysis(recommendation.id)}
                    size="small"
                  >
                    Start Analysis Now
                  </Button>
                </Alert>
              )}

              {/* Access Restriction Notice */}
              {!canAccessMethod(recommendation.id) && (
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <UpgradeIcon fontSize="small" />
                    {ACCESS_REQUIREMENTS[recommendation.id] === 'pro' ? 'Pro Feature' : 'Premium Feature'}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {getUpgradeMessage(recommendation.id)}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<UpgradeIcon />}
                    onClick={() => navigate('/app#pricing')}
                    size="small"
                    color="warning"
                  >
                    View Pricing
                  </Button>
                </Alert>
              )}

              {/* Method Details Accordions */}
              <Box sx={{ mb: 3 }}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">📋 Assumptions & Requirements</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>Assumptions:</Typography>
                    <List dense>
                      {recommendation.assumptions.map((assumption, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <InfoIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={assumption} />
                        </ListItem>
                      ))}
                    </List>
                    
                    <Typography variant="subtitle2" sx={{ mb: 1, mt: 2 }}>Data Requirements:</Typography>
                    <List dense>
                      {recommendation.dataRequirements.map((requirement, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <CheckCircleIcon fontSize="small" color="success" />
                          </ListItemIcon>
                          <ListItemText primary={requirement} />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">💡 Interpretation Guide</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2">
                      {recommendation.interpretation}
                    </Typography>
                  </AccordionDetails>
                </Accordion>

                {/* DataStatPro Implementation (Priority) */}
                {canAccessMethod(recommendation.id) && INTERNAL_ROUTES[recommendation.id] && (
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LaunchIcon fontSize="small" />
                        🚀 DataStatPro Implementation (Recommended)
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Alert severity="success" sx={{ mb: 2 }}>
                        <Typography variant="body2" paragraph>
                          <strong>No coding required!</strong> This analysis is available directly in DataStatPro with an intuitive, user-friendly interface.
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                          <Button
                            variant="contained"
                            startIcon={<PlayArrowIcon />}
                            onClick={() => navigateToAnalysis(recommendation.id)}
                          >
                            Start Analysis
                          </Button>
                          <Typography variant="caption" color="text.secondary">
                            Route: {INTERNAL_ROUTES[recommendation.id]}
                          </Typography>
                        </Box>
                      </Alert>
                      <Typography variant="body2" color="text.secondary">
                        DataStatPro provides a guided interface for this analysis, including:
                      </Typography>
                      <List dense sx={{ mt: 1 }}>
                        <ListItem>
                          <ListItemIcon><CheckCircleIcon fontSize="small" color="success" /></ListItemIcon>
                          <ListItemText primary="Interactive data selection and variable assignment" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><CheckCircleIcon fontSize="small" color="success" /></ListItemIcon>
                          <ListItemText primary="Automatic assumption checking and validation" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><CheckCircleIcon fontSize="small" color="success" /></ListItemIcon>
                          <ListItemText primary="Professional results with interpretation guidance" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><CheckCircleIcon fontSize="small" color="success" /></ListItemIcon>
                          <ListItemText primary="Export-ready tables and visualizations" />
                        </ListItem>
                      </List>
                    </AccordionDetails>
                  </Accordion>
                )}

                {/* External Tools Reference */}
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">💻 External Tools Reference</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" paragraph color="text.secondary">
                      For reference, here's how you would implement this analysis in external statistical software:
                    </Typography>
                    {recommendation.implementation.r && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>R:</Typography>
                        <Paper sx={{ p: 2, bgcolor: 'grey.100', fontFamily: 'monospace' }}>
                          <Typography variant="body2" component="pre">
                            {recommendation.implementation.r}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                    
                    {recommendation.implementation.python && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>Python:</Typography>
                        <Paper sx={{ p: 2, bgcolor: 'grey.100', fontFamily: 'monospace' }}>
                          <Typography variant="body2" component="pre">
                            {recommendation.implementation.python}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                    
                    {recommendation.implementation.spss && (
                      <Box>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>SPSS:</Typography>
                        <Paper sx={{ p: 2, bgcolor: 'grey.100', fontFamily: 'monospace' }}>
                          <Typography variant="body2" component="pre">
                            {recommendation.implementation.spss}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                    {canAccessMethod(recommendation.id) && INTERNAL_ROUTES[recommendation.id] && (
                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          💡 <strong>Tip:</strong> While you can use these external tools, we recommend using DataStatPro's built-in interface for a more streamlined experience.
                        </Typography>
                      </Alert>
                    )}
                  </AccordionDetails>
                </Accordion>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">📚 Examples & Related Methods</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>Common Examples:</Typography>
                    <List dense>
                      {recommendation.examples.map((example, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <LightbulbIcon fontSize="small" color="warning" />
                          </ListItemIcon>
                          <ListItemText primary={example} />
                        </ListItem>
                      ))}
                    </List>
                    
                    <Typography variant="subtitle2" sx={{ mb: 1, mt: 2 }}>Related Methods:</Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {recommendation.relatedMethods.map((method, index) => (
                        <Chip
                          key={index}
                          label={method.replace('_', ' ')}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Reset Button */}
      {(selectedPath.length > 0 || recommendation) && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={handleReset}
            startIcon={<ArrowBackIcon />}
          >
            Start New Analysis
          </Button>
        </Box>
      )}

      {/* Access Control Dialog */}
      <Dialog open={showAccessDialog} onClose={() => setShowAccessDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <UpgradeIcon color="warning" />
          Upgrade Required
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            {restrictedMethod && getUpgradeMessage(restrictedMethod)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Upgrade your account to access advanced statistical methods and premium features.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAccessDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={() => {
              navigate('/app#pricing');
              setShowAccessDialog(false);
            }}
            startIcon={<UpgradeIcon />}
          >
            View Pricing
          </Button>
        </DialogActions>
      </Dialog>

      {/* Onboarding Dialog */}
      <Dialog open={showOnboarding} onClose={() => setShowOnboarding(false)} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HelpIcon color="primary" />
          How to Use the Statistical Analysis Advisor
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            The Statistical Analysis Advisor helps you choose the right statistical method for your research question and data.
          </Typography>
          
          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>Step-by-Step Guide:</Typography>
          <List>
            <ListItem>
              <ListItemIcon><Typography variant="h6" color="primary">1</Typography></ListItemIcon>
              <ListItemText 
                primary="Define Your Research Goal" 
                secondary="Start by selecting what you want to do with your data (describe, compare, examine relationships, etc.)" 
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Typography variant="h6" color="primary">2</Typography></ListItemIcon>
              <ListItemText 
                primary="Answer Follow-up Questions" 
                secondary="The advisor will ask specific questions about your data type, sample size, and research design" 
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Typography variant="h6" color="primary">3</Typography></ListItemIcon>
              <ListItemText 
                primary="Get Your Recommendation" 
                secondary="Receive a tailored statistical method recommendation with detailed guidance" 
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Typography variant="h6" color="primary">4</Typography></ListItemIcon>
              <ListItemText 
                primary="Run Analysis in DataStatPro" 
                secondary="For supported methods, click 'Run in DataStatPro' to start your analysis immediately" 
              />
            </ListItem>
          </List>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              💡 <strong>Pro Tip:</strong> Have your research question and data characteristics ready before starting for the most accurate recommendations.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowOnboarding(false)} variant="contained">
            Got It!
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StatisticalAnalysisAdvisor;