-- Fix Admin Connection Issues Migration
-- This migration addresses connection instability and data inconsistencies in admin functions
-- Date: 2025-07-25

-- Drop problematic admin functions to recreate them with proper error handling
DROP FUNCTION IF EXISTS public.get_all_users(INTEGER, INTEGER, TEXT);
DROP FUNCTION IF EXISTS public.get_user_statistics();
DROP FUNCTION IF EXISTS public.get_admin_users();

-- Recreate get_all_users with proper email handling and connection stability
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '30s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with proper email field and optimized search
  RETURN QUERY
  SELECT 
    p.id,
    COALESCE(p.email, p.username, 'No email available')::TEXT as email,
    p.username,
    p.full_name,
    p.institution,
    p.country,
    p.avatar_url,
    p.updated_at,
    COALESCE(p.is_admin, false) as is_admin,
    COALESCE(p.accounttype, 'standard') as accounttype,
    COALESCE(p.updated_at, NOW()) as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at
  FROM public.profiles p
  WHERE (
    search_term IS NULL OR 
    search_term = '' OR
    COALESCE(p.full_name, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.email, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.username, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.institution, '') ILIKE '%' || search_term || '%'
  )
  ORDER BY p.updated_at DESC NULLS LAST
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Recreate get_admin_users with connection stability improvements
CREATE OR REPLACE FUNCTION public.get_admin_users()
RETURNS TABLE (
  id UUID,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '15s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    p.id, 
    p.username, 
    p.full_name, 
    p.institution, 
    p.country, 
    p.avatar_url, 
    p.updated_at, 
    p.is_admin
  FROM public.profiles p
  WHERE COALESCE(p.is_admin, false) = true
  ORDER BY p.full_name NULLS LAST, p.username NULLS LAST;
END;
$$;

-- Recreate get_user_statistics with better performance and error handling
CREATE OR REPLACE FUNCTION public.get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '20s'
AS $$
DECLARE
  result JSON;
  total_users INTEGER;
  admin_users INTEGER;
  pro_users INTEGER;
  edu_users INTEGER;
  standard_users INTEGER;
  guest_users INTEGER;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get counts with single query for better performance
  SELECT 
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE COALESCE(is_admin, false) = true) as admins,
    COUNT(*) FILTER (WHERE accounttype = 'pro') as pro,
    COUNT(*) FILTER (WHERE accounttype IN ('edu', 'edu_pro')) as edu,
    COUNT(*) FILTER (WHERE accounttype = 'standard') as standard,
    COUNT(*) FILTER (WHERE accounttype = 'guest') as guest
  INTO total_users, admin_users, pro_users, edu_users, standard_users, guest_users
  FROM public.profiles;
  
  -- Build result JSON
  SELECT json_build_object(
    'total_users', COALESCE(total_users, 0),
    'admin_users', COALESCE(admin_users, 0),
    'pro_users', COALESCE(pro_users, 0),
    'edu_users', COALESCE(edu_users, 0),
    'standard_users', COALESCE(standard_users, 0),
    'guest_users', COALESCE(guest_users, 0),
    'users_last_7_days', 0,
    'users_last_30_days', 0,
    'active_users_last_7_days', 0,
    'active_users_last_30_days', 0
  ) INTO result;

  RETURN result;
END;
$$;

-- Add missing email column to profiles if it doesn't exist
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS email TEXT;

-- Enable trigram extension if not already enabled (for better search performance)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create optimized indexes for admin queries
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_profiles_search ON public.profiles USING gin(
  (COALESCE(full_name, '') || ' ' || COALESCE(email, '') || ' ' || COALESCE(username, '') || ' ' || COALESCE(institution, '')) gin_trgm_ops
);

-- Update RLS policies to be more efficient
DROP POLICY IF EXISTS "Admins can read all profiles" ON public.profiles;
CREATE POLICY "Admins can read all profiles" ON public.profiles
  FOR SELECT TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_users() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_statistics() TO authenticated;

-- Optimize is_user_admin function for better performance
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
SET statement_timeout = '5s'
AS $$
BEGIN
  -- Use EXISTS for better performance and add timeout
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = user_id AND COALESCE(is_admin, false) = true
  );
END;
$$;

-- Add connection stability settings
ALTER DATABASE postgres SET statement_timeout = '60s';
ALTER DATABASE postgres SET idle_in_transaction_session_timeout = '30s';

-- Add helpful comments
COMMENT ON FUNCTION public.get_all_users(INTEGER, INTEGER, TEXT) IS 'Returns paginated list of all users with search. Fixed email field and connection stability.';
COMMENT ON FUNCTION public.get_admin_users() IS 'Returns all admin users. Optimized for connection stability.';
COMMENT ON FUNCTION public.get_user_statistics() IS 'Returns comprehensive user statistics. Optimized single-query approach.';
COMMENT ON FUNCTION public.is_user_admin(UUID) IS 'Checks admin status with performance optimizations and timeout protection.';
