import React, { useState, useCallback, useMemo } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>,
  <PERSON>ack,
  Di<PERSON>r,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  AutoAwesome,
  ContentCopy,
  Download,
  ExpandMore,
  Description,
  PictureAsPdf
} from '@mui/icons-material';
import { useResults } from '../../context/ResultsContext';
import { ResultItem } from '../../context/ResultsContext';

// Types for summary generation
interface SummaryConfig {
  includeEffectSizes: boolean;
  includeConfidenceIntervals: boolean;
  includePValues: boolean;
  includeModelFit: boolean;
  summaryStyle: 'executive' | 'detailed' | 'apa' | 'custom';
  targetAudience: 'academic' | 'clinical' | 'general';
  maxLength: number;
  customTemplate?: string;
}

interface GeneratedSummary {
  title: string;
  executiveSummary: string;
  keyFindings: string[];
  statisticalSummary: string;
  methodsSummary: string;
  limitations: string[];
  recommendations: string[];
  fullText: string;
  wordCount: number;
  timestamp: Date;
}

interface ResultsSummaryGeneratorProps {
  open: boolean;
  onClose: () => void;
  selectedResults: ResultItem[];
  onSummaryGenerated?: (summary: GeneratedSummary) => void;
}

const DEFAULT_CONFIG: SummaryConfig = {
  includeEffectSizes: true,
  includeConfidenceIntervals: true,
  includePValues: true,
  includeModelFit: true,
  summaryStyle: 'executive',
  targetAudience: 'academic',
  maxLength: 1000,
};

export const ResultsSummaryGenerator: React.FC<ResultsSummaryGeneratorProps> = ({
  open,
  onClose,
  selectedResults,
  onSummaryGenerated
}) => {
  const { results } = useResults();
  const [config] = useState<SummaryConfig>(DEFAULT_CONFIG);
  const [generatedSummary, setGeneratedSummary] = useState<GeneratedSummary | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Filter results to include in summary
  const resultsToSummarize = useMemo(() => {
    if (selectedResults && selectedResults.length > 0) {
      return selectedResults;
    }
    return results.filter(result => result.selected);
  }, [results, selectedResults]);

  // Extract statistical information from results including write-ups and interpretations
  const extractStatisticalInfo = useCallback((resultItems: ResultItem[]) => {
    const stats = {
      effectSizes: [] as Array<{type: string, value: number, ci?: [number, number]}>,
      pValues: [] as Array<{test: string, value: number}>,
      confidenceIntervals: [] as Array<{parameter: string, ci: [number, number]}>,
      modelFit: [] as Array<{model: string, metric: string, value: number}>,
      sampleSizes: [] as number[],
      testTypes: [] as string[],
      writeUps: [] as string[],
      interpretations: [] as string[],
      methodsInfo: [] as Array<{component: string, method: string}>,
      correlationMatrices: [] as Array<any>,
      descriptiveStats: [] as Array<any>
    };

    resultItems.forEach(result => {
      if (result.data) {
        // Extract write-ups and interpretations
        if (result.data.writeUp) {
          stats.writeUps.push(`**${result.title}**: ${result.data.writeUp}`);
        }
        
        if (result.data.interpretation) {
          stats.interpretations.push(`**${result.title}**: ${result.data.interpretation}`);
        }
        
        // Extract APA summary from SMDTable
        if (result.data.apaSummary) {
          stats.interpretations.push(`**${result.title}**: ${result.data.apaSummary}`);
        }
        
        // Extract additional component-specific interpretations
        if (result.component === 'PostHocTests' && result.data.interpretation) {
          // PostHocTests already handled above, but ensure it's captured
          if (!stats.interpretations.some(interp => interp.includes(result.title))) {
            stats.interpretations.push(`**${result.title}**: ${result.data.interpretation}`);
          }
        }
        
        if (result.component === 'RegressionTable' && result.data.interpretation) {
          // RegressionTable already handled above, but ensure it's captured
          if (!stats.interpretations.some(interp => interp.includes(result.title))) {
            stats.interpretations.push(`**${result.title}**: ${result.data.interpretation}`);
          }
        }

        // Extract effect sizes
        if (result.data.effectSize) {
          stats.effectSizes.push({
            type: result.data.effectSizeType || 'Unknown',
            value: result.data.effectSize,
            ci: result.data.effectSizeCI
          });
        }

        // Extract p-values
        if (result.data.pValue !== undefined) {
          stats.pValues.push({
            test: result.title,
            value: result.data.pValue
          });
        }

        // Extract confidence intervals
        if (result.data.confidenceInterval) {
          stats.confidenceIntervals.push({
            parameter: result.data.parameter || 'Parameter',
            ci: result.data.confidenceInterval
          });
        }

        // Extract model fit statistics
        if (result.data.modelFit) {
          Object.entries(result.data.modelFit).forEach(([metric, value]) => {
            if (typeof value === 'number') {
              stats.modelFit.push({
                model: result.title,
                metric,
                value
              });
            }
          });
        }

        // Extract sample sizes
        if (result.data.sampleSize) {
          stats.sampleSizes.push(result.data.sampleSize);
        }

        // Track test types
        if (result.type) {
          stats.testTypes.push(result.type);
        }

        // Extract methods information
        if (result.component) {
          let method = 'Statistical Analysis';
          if (result.component === 'PostHocTests') {
            method = result.data.testMethod || 'Post-hoc Analysis';
          } else if (result.component === 'RegressionTable') {
            method = 'Regression Analysis';
          } else if (result.component === 'SMDTable') {
            method = 'Standardized Mean Difference';
          } else if (result.component === 'Table3Generator') {
            method = 'Correlation Analysis';
          }
          stats.methodsInfo.push({ component: result.component, method });
        }

        // Extract correlation matrices from Table3Generator
        if (result.component === 'Table3Generator' && result.data.correlationMatrix) {
          stats.correlationMatrices.push(result.data.correlationMatrix);
        }

        // Extract descriptive statistics
        if (result.data.descriptiveStats) {
          stats.descriptiveStats.push(result.data.descriptiveStats);
        }
      }
    });

    return stats;
  }, []);

  // Generate executive summary
  const generateExecutiveSummary = useCallback((resultItems: ResultItem[], stats: any) => {
    const totalTests = resultItems.length;
    const significantResults = stats.pValues.filter((p: any) => p.value < 0.05).length;
    const uniqueTestTypes = [...new Set(stats.testTypes)].length;
    const totalSampleSize = stats.sampleSizes.reduce((sum: number, n: number) => sum + n, 0);

    let summary = `This analysis encompasses ${totalTests} statistical test${totalTests > 1 ? 's' : ''}`;
    
    if (uniqueTestTypes > 1) {
      summary += ` across ${uniqueTestTypes} different analytical approaches`;
    }
    
    if (totalSampleSize > 0) {
      summary += `, with a combined sample size of ${totalSampleSize.toLocaleString()} observations`;
    }
    
    summary += `. `;

    if (significantResults > 0) {
      const significanceRate = ((significantResults / totalTests) * 100).toFixed(1);
      summary += `${significantResults} of ${totalTests} tests (${significanceRate}%) yielded statistically significant results (p < 0.05). `;
    } else {
      summary += `No statistically significant results were observed at the conventional α = 0.05 level. `;
    }

    // Add effect size summary if available
    if (stats.effectSizes.length > 0) {
      const avgEffectSize = stats.effectSizes.reduce((sum: number, es: any) => sum + Math.abs(es.value), 0) / stats.effectSizes.length;
      let effectMagnitude = 'small';
      if (avgEffectSize > 0.5) effectMagnitude = 'medium';
      if (avgEffectSize > 0.8) effectMagnitude = 'large';
      
      summary += `The observed effect sizes were generally ${effectMagnitude} in magnitude (mean |effect| = ${avgEffectSize.toFixed(3)}). `;
    }

    return summary;
  }, []);

  // Generate methods summary
  const generateMethodsSummary = useCallback((stats: any) => {
    if (!stats.methodsInfo || stats.methodsInfo.length === 0) return 'No specific methods information available.';
    
    let methodsSummary = 'Statistical Methods: ';
    const methodGroups = stats.methodsInfo.reduce((groups: any, method: any) => {
      if (!groups[method.component]) groups[method.component] = [];
      groups[method.component].push(method);
      return groups;
    }, {});
    
    const methodDescriptions = Object.entries(methodGroups).map(([component, methods]: [string, any]) => {
      const methodNames = methods.map((m: any) => m.method).join(', ');
      return `${component}: ${methodNames}`;
    });
    
    methodsSummary += methodDescriptions.join('; ');
    
    if (stats.correlationMatrices && stats.correlationMatrices.length > 0) {
      methodsSummary += `. Correlation analyses included ${stats.correlationMatrices.length} correlation matrix/matrices`;
    }
    
    if (stats.descriptiveStats && stats.descriptiveStats.length > 0) {
      methodsSummary += `. Descriptive statistics were computed for ${stats.descriptiveStats.length} analysis/analyses`;
    }
    
    return methodsSummary + '.';
  }, []);

  // Generate key findings
  const generateKeyFindings = useCallback((resultItems: ResultItem[], stats: any) => {
    const findings: string[] = [];

    // Add findings from interpretations and writeUps
    if (stats.interpretations && stats.interpretations.length > 0) {
      stats.interpretations.slice(0, 3).forEach((interpretation: string, index: number) => {
        const summary = interpretation.length > 100 ? interpretation.substring(0, 100) + '...' : interpretation;
        findings.push(`Analysis ${index + 1}: ${summary}`);
      });
    }

    // Significant results
    if (stats.pValues && stats.pValues.length > 0) {
      const significantResults = stats.pValues.filter((p: any) => p.value < 0.05);
      significantResults.slice(0, 3).forEach((result: any) => {
        findings.push(`${result.test}: Statistically significant result (p = ${result.value.toFixed(4)})`);
      });
    }

    // Large effect sizes
    if (stats.effectSizes && stats.effectSizes.length > 0) {
      const largeEffects = stats.effectSizes.filter((es: any) => Math.abs(es.value) > 0.8);
      largeEffects.slice(0, 2).forEach((effect: any) => {
        const ciText = effect.ci ? ` (95% CI: [${effect.ci[0].toFixed(3)}, ${effect.ci[1].toFixed(3)}])` : '';
        findings.push(`Large ${effect.type}: ${effect.value.toFixed(3)}${ciText}`);
      });
    }

    // Model fit highlights
    if (stats.modelFit && stats.modelFit.length > 0) {
      const goodFitModels = stats.modelFit.filter((fit: any) => 
        (fit.metric.toLowerCase().includes('r2') && fit.value > 0.7) ||
        (fit.metric.toLowerCase().includes('aic') && fit.value < 100)
      );
      goodFitModels.slice(0, 2).forEach((fit: any) => {
        findings.push(`${fit.model}: Good model fit (${fit.metric} = ${fit.value.toFixed(3)})`);
      });
    }

    // If no specific findings, add general ones
    if (findings.length === 0) {
      findings.push(`Analysis completed for ${resultItems.length} result${resultItems.length > 1 ? 's' : ''}`);
      if (stats.testTypes && stats.testTypes.length > 0) {
        const uniqueTests = [...new Set(stats.testTypes)];
        findings.push(`Statistical methods used: ${uniqueTests.join(', ')}`);
      }
      if (stats.sampleSizes && stats.sampleSizes.length > 0) {
        const totalSample = stats.sampleSizes.reduce((sum: number, size: number) => sum + size, 0);
        findings.push(`Total sample size across analyses: ${totalSample}`);
      }
    }

    return findings.slice(0, 8); // Limit to top 8 findings
  }, []);

  // Generate statistical summary
  const generateStatisticalSummary = useCallback((resultItems: ResultItem[], stats: any) => {
    let summary = '';

    // Test overview
    const testTypeCounts = stats.testTypes.reduce((acc: any, type: string) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    summary += 'Statistical Methods: ';
    summary += Object.entries(testTypeCounts)
      .map(([type, count]) => `${type} (n=${count})`)
      .join(', ');
    summary += '. ';

    // Significance summary
    const significantCount = stats.pValues.filter((p: any) => p.value < 0.05).length;
    const totalTests = stats.pValues.length;
    if (totalTests > 0) {
      summary += `Statistical Significance: ${significantCount}/${totalTests} tests significant at α = 0.05. `;
    }

    // Effect size summary
    if (stats.effectSizes.length > 0) {
      const effectSizeTypes = [...new Set(stats.effectSizes.map((es: any) => es.type))];
      summary += `Effect Sizes: Reported as ${effectSizeTypes.join(', ')}. `;
      
      const meanEffect = stats.effectSizes.reduce((sum: number, es: any) => sum + Math.abs(es.value), 0) / stats.effectSizes.length;
      summary += `Mean absolute effect size: ${meanEffect.toFixed(3)}. `;
    }

    return summary;
  }, []);

  // Main summary generation function
  const generateSummary = useCallback(async () => {
    if (resultsToSummarize.length === 0) {
      return;
    }

    setIsGenerating(true);
    
    try {
      // Extract statistical information
      const stats = extractStatisticalInfo(resultsToSummarize);
      
      // Generate different sections
      const executiveSummary = generateExecutiveSummary(resultsToSummarize, stats);
      const keyFindings = generateKeyFindings(resultsToSummarize, stats);
      const statisticalSummary = generateStatisticalSummary(resultsToSummarize, stats);
      const methodsSummary = generateMethodsSummary(stats);
      
      // Generate limitations
      const limitations = [
        'Results are based on the specific analytical approaches selected',
        'Interpretation should consider the context and assumptions of each statistical test',
        'Multiple comparisons may increase the risk of Type I error'
      ];
      
      // Generate recommendations
      const recommendations = [
        'Consider effect sizes alongside statistical significance',
        'Validate findings with independent datasets when possible',
        'Report confidence intervals for key estimates'
      ];
      
      // Combine into full text including write-ups and interpretations
      const sections = [];
      
      // Add write-ups section if available
      if (stats.writeUps.length > 0) {
        sections.push(`## Detailed Results\n${stats.writeUps.join('\n\n')}`);
      }
      
      // Add interpretations section if available
      if (stats.interpretations.length > 0) {
        sections.push(`## Interpretations\n${stats.interpretations.join('\n\n')}`);
      }
      
      sections.push(
        `## Statistical Summary\n${statisticalSummary}`,
        `## Methods\n${methodsSummary}`,
        `## Limitations\n${limitations.map(l => `• ${l}`).join('\n')}`,
        `## Recommendations\n${recommendations.map(r => `• ${r}`).join('\n')}`
      );
      
      const fullText = sections.join('\n\n');
      
      const summary: GeneratedSummary = {
        title: `Analysis Summary - ${new Date().toLocaleDateString()}`,
        executiveSummary,
        keyFindings,
        statisticalSummary,
        methodsSummary,
        limitations,
        recommendations,
        fullText,
        wordCount: fullText.split(' ').length,
        timestamp: new Date()
      };
      
      setGeneratedSummary(summary);
      onSummaryGenerated?.(summary);
      
    } catch (error) {
      console.error('Error generating summary:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [resultsToSummarize, config, extractStatisticalInfo, generateExecutiveSummary, generateKeyFindings, generateStatisticalSummary, onSummaryGenerated]);

  // Copy summary to clipboard
  const copySummary = useCallback(async () => {
    if (generatedSummary) {
      try {
        await navigator.clipboard.writeText(generatedSummary.fullText);
        // Could add a toast notification here
      } catch (error) {
        console.error('Failed to copy summary:', error);
      }
    }
  }, [generatedSummary]);

  // Export summary as Word document
  const exportAsWord = useCallback(() => {
    if (generatedSummary) {
      try {
        // Create a simple HTML document that can be opened by Word
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>${generatedSummary.title}</title>
            <style>
              body { font-family: 'Times New Roman', serif; margin: 1in; line-height: 1.6; }
              h1, h2 { color: #333; }
              h1 { font-size: 18pt; }
              h2 { font-size: 14pt; margin-top: 20pt; }
              p { margin-bottom: 12pt; }
              .meta { color: #666; font-size: 10pt; }
            </style>
          </head>
          <body>
            <h1>${generatedSummary.title}</h1>
            <p class="meta">Generated on ${generatedSummary.timestamp.toLocaleString()} • ${generatedSummary.wordCount} words</p>
            ${generatedSummary.fullText.replace(/## (.*)/g, '<h2>$1</h2>').replace(/• (.*)/g, '<p>• $1</p>').replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}
          </body>
          </html>
        `;
        
        const blob = new Blob([htmlContent], { type: 'application/msword' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `analysis-summary-${new Date().toISOString().split('T')[0]}.doc`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (error) {
        console.error('Failed to export as Word:', error);
      }
    }
  }, [generatedSummary]);

  // Export summary as PDF
  const exportAsPDF = useCallback(() => {
    if (generatedSummary) {
      try {
        // Create a printable version and trigger print dialog
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <title>${generatedSummary.title}</title>
              <style>
                @media print {
                  body { font-family: 'Times New Roman', serif; margin: 0.5in; line-height: 1.6; }
                  h1, h2 { color: #000; page-break-after: avoid; }
                  h1 { font-size: 18pt; margin-bottom: 12pt; }
                  h2 { font-size: 14pt; margin-top: 20pt; margin-bottom: 8pt; }
                  p { margin-bottom: 8pt; }
                  .meta { color: #666; font-size: 10pt; margin-bottom: 20pt; }
                  .no-print { display: none; }
                }
                body { font-family: 'Times New Roman', serif; margin: 1in; line-height: 1.6; }
                h1, h2 { color: #333; }
                h1 { font-size: 18pt; }
                h2 { font-size: 14pt; margin-top: 20pt; }
                p { margin-bottom: 12pt; }
                .meta { color: #666; font-size: 10pt; }
                button { margin: 10px; padding: 10px 20px; }
              </style>
            </head>
            <body>
              <div class="no-print">
                <button onclick="window.print()">Print to PDF</button>
                <button onclick="window.close()">Close</button>
              </div>
              <h1>${generatedSummary.title}</h1>
              <p class="meta">Generated on ${generatedSummary.timestamp.toLocaleString()} • ${generatedSummary.wordCount} words</p>
              ${generatedSummary.fullText.replace(/## (.*)/g, '<h2>$1</h2>').replace(/• (.*)/g, '<p>• $1</p>').replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}
            </body>
            </html>
          `;
          
          printWindow.document.write(htmlContent);
          printWindow.document.close();
        }
      } catch (error) {
        console.error('Failed to export as PDF:', error);
      }
    }
  }, [generatedSummary]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <AutoAwesome sx={{ mr: 1 }} />
          Results Summary Generator
        </Box>
      </DialogTitle>
      <DialogContent>
        <Card elevation={0}>
      <CardContent>

        {resultsToSummarize.length === 0 && (
          <Alert severity="info" sx={{ mb: 2 }}>
            No results selected for summary generation. Please select results from the Results Manager.
          </Alert>
        )}

        {resultsToSummarize.length > 0 && (
          <Box mb={2}>
            <Typography variant="body2" color="text.secondary">
              {resultsToSummarize.length} result{resultsToSummarize.length > 1 ? 's' : ''} selected for summary
            </Typography>
            <Stack direction="row" spacing={1} mt={1} flexWrap="wrap">
              {resultsToSummarize.slice(0, 5).map(result => (
                <Chip 
                  key={result.id} 
                  label={result.title} 
                  size="small" 
                  variant="outlined"
                />
              ))}
              {resultsToSummarize.length > 5 && (
                <Chip 
                  label={`+${resultsToSummarize.length - 5} more`} 
                  size="small" 
                  variant="outlined"
                />
              )}
            </Stack>
          </Box>
        )}

        <Box display="flex" gap={2} mb={3}>
          <Button
            variant="contained"
            onClick={generateSummary}
            disabled={isGenerating || resultsToSummarize.length === 0}
            startIcon={isGenerating ? <CircularProgress size={20} /> : <AutoAwesome />}
          >
            {isGenerating ? 'Generating...' : 'Generate Summary'}
          </Button>
          
          {generatedSummary && (
            <>
              <Button
                variant="outlined"
                onClick={copySummary}
                startIcon={<ContentCopy />}
              >
                Copy
              </Button>
              <Button
                variant="outlined"
                onClick={exportAsWord}
                startIcon={<Description />}
              >
                Export Word
              </Button>
              <Button
                variant="outlined"
                onClick={exportAsPDF}
                startIcon={<PictureAsPdf />}
              >
                Export PDF
              </Button>
            </>
          )}
        </Box>

        {generatedSummary && (
          <Box>
            <Divider sx={{ mb: 2 }} />
            
            <Typography variant="h6" gutterBottom>
              {generatedSummary.title}
            </Typography>
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Generated on {generatedSummary.timestamp.toLocaleString()} • {generatedSummary.wordCount} words
            </Typography>



            {/* Combined Detailed Results and Interpretations */}
            {resultsToSummarize.some(r => r.data?.writeUp || r.data?.interpretation || r.data?.apaSummary) && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="subtitle1">Detailed Results & Interpretations</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={3}>
                    {resultsToSummarize
                      .filter(r => r.data?.writeUp || r.data?.interpretation || r.data?.apaSummary)
                      .map((result, index) => (
                        <Box key={index}>
                          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {result.title}
                          </Typography>
                          
                          {result.data?.writeUp && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                                Results:
                              </Typography>
                              <Typography variant="body2" sx={{ pl: 1, borderLeft: 2, borderColor: 'grey.300' }}>
                                {result.data.writeUp}
                              </Typography>
                            </Box>
                          )}
                          
                          {result.data?.interpretation && (
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                                Interpretation:
                              </Typography>
                              <Typography variant="body2" sx={{ pl: 1, borderLeft: 2, borderColor: 'grey.300' }}>
                                {result.data.interpretation}
                              </Typography>
                            </Box>
                          )}
                          
                          {result.data?.apaSummary && (
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                                APA Summary:
                              </Typography>
                              <Typography variant="body2" sx={{ pl: 1, borderLeft: 2, borderColor: 'grey.300', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                                {result.data.apaSummary}
                              </Typography>
                            </Box>
                          )}
                          
                          {index < resultsToSummarize.filter(r => r.data?.writeUp || r.data?.interpretation || r.data?.apaSummary).length - 1 && (
                            <Divider sx={{ mt: 2 }} />
                          )}
                        </Box>
                      ))
                    }
                  </Stack>
                </AccordionDetails>
              </Accordion>
            )}

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1">Methods Summary</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body1">
                  {generatedSummary.methodsSummary}
                </Typography>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1">Statistical Summary</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body1">
                  {generatedSummary.statisticalSummary}
                </Typography>
              </AccordionDetails>
            </Accordion>


          </Box>
        )}


      </CardContent>
    </Card>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ResultsSummaryGenerator;