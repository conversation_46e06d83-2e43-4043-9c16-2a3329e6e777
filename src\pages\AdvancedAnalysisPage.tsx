import React from 'react';
import { Box, Container } from '@mui/material';
import AdvancedAnalysisOptions from '../components/AdvancedAnalysisAliases/AdvancedAnalysisOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';

interface AdvancedAnalysisPageProps {
  onNavigate: (path: string) => void;
}

const AdvancedAnalysisPage: React.FC<AdvancedAnalysisPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible={true}
      />
      <Box sx={{ mt: 4, mb: 4 }}>
        <AdvancedAnalysisOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default AdvancedAnalysisPage;
