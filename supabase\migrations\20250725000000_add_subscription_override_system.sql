-- Admin Subscription Override System Migration
-- This migration adds support for administrators to temporarily grant elevated access to users
-- Date: 2025-07-25

-- Create subscription_overrides table
CREATE TABLE IF NOT EXISTS public.subscription_overrides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  admin_id UUID REFERENCES auth.users(id) ON DELETE SET NULL NOT NULL,
  original_tier TEXT NOT NULL CHECK (original_tier IN ('guest', 'standard', 'edu', 'edu_pro', 'pro')),
  override_tier TEXT NOT NULL CHECK (override_tier IN ('standard', 'edu', 'edu_pro', 'pro')),
  start_date TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  reason TEXT,
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  
  -- Constraints
  CONSTRAINT valid_override_period CHECK (end_date > start_date),
  CONSTRAINT valid_tier_upgrade CHECK (
    (original_tier = 'guest' AND override_tier IN ('standard', 'edu', 'edu_pro', 'pro')) OR
    (original_tier = 'standard' AND override_tier IN ('edu', 'edu_pro', 'pro')) OR
    (original_tier = 'edu' AND override_tier IN ('edu_pro', 'pro')) OR
    (original_tier = 'edu_pro' AND override_tier = 'pro')
  )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_user_id ON public.subscription_overrides(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_admin_id ON public.subscription_overrides(admin_id);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_active ON public.subscription_overrides(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_end_date ON public.subscription_overrides(end_date);
CREATE INDEX IF NOT EXISTS idx_subscription_overrides_user_active ON public.subscription_overrides(user_id, is_active) WHERE is_active = true;

-- Enable RLS
ALTER TABLE public.subscription_overrides ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admins can manage all subscription overrides" ON public.subscription_overrides
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()));

CREATE POLICY "Users can view their own active overrides" ON public.subscription_overrides
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id AND is_active = true);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_subscription_overrides_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

CREATE TRIGGER subscription_overrides_updated_at
  BEFORE UPDATE ON public.subscription_overrides
  FOR EACH ROW
  EXECUTE FUNCTION public.update_subscription_overrides_updated_at();

-- Create function to automatically expire overrides
CREATE OR REPLACE FUNCTION public.expire_subscription_overrides()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  expired_count INTEGER;
BEGIN
  UPDATE public.subscription_overrides 
  SET is_active = false, updated_at = now()
  WHERE is_active = true AND end_date <= now();
  
  GET DIAGNOSTICS expired_count = ROW_COUNT;
  
  RETURN expired_count;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.expire_subscription_overrides() TO authenticated;

-- Add helpful comments
COMMENT ON TABLE public.subscription_overrides IS 'Stores temporary subscription tier overrides granted by administrators';
COMMENT ON COLUMN public.subscription_overrides.original_tier IS 'The user''s original subscription tier before override';
COMMENT ON COLUMN public.subscription_overrides.override_tier IS 'The elevated tier granted by admin override';
COMMENT ON COLUMN public.subscription_overrides.is_active IS 'Whether the override is currently active (automatically set to false when expired)';
COMMENT ON FUNCTION public.expire_subscription_overrides() IS 'Automatically expires subscription overrides that have passed their end date';
