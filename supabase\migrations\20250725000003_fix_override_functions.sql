-- Fix Subscription Override Functions Migration
-- This migration fixes the override functions to use profiles table instead of auth.users
-- Date: 2025-07-25

-- Drop existing functions to avoid return type conflicts
DROP FUNCTION IF EXISTS public.get_all_active_overrides(INTEGER, INTEGER);
DROP FUNCTION IF EXISTS public.get_override_audit_trail(UUID, INTEGER, INTEGER);
DROP FUNCTION IF EXISTS public.get_expiring_overrides(INTEGER);

-- Fix get_all_active_overrides function to use profiles table
CREATE OR REPLACE FUNCTION public.get_all_active_overrides(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    up.email as user_email,
    up.full_name as user_full_name,
    ap.email as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE so.is_active = true AND so.end_date > now()
  ORDER BY so.end_date ASC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Fix get_override_audit_trail function to use profiles table
CREATE OR REPLACE FUNCTION public.get_override_audit_trail(
  target_user_id UUID DEFAULT NULL,
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    up.email as user_email,
    up.full_name as user_full_name,
    ap.email as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    so.is_active,
    so.created_at,
    so.updated_at
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE (target_user_id IS NULL OR so.user_id = target_user_id)
  ORDER BY so.created_at DESC
  LIMIT page_size OFFSET page_offset;
END;
$$;

-- Fix get_expiring_overrides function to use profiles table
CREATE OR REPLACE FUNCTION public.get_expiring_overrides(days_ahead INTEGER DEFAULT 7)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  admin_email TEXT,
  original_tier TEXT,
  override_tier TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    up.email as user_email,
    up.full_name as user_full_name,
    ap.email as admin_email,
    so.original_tier,
    so.override_tier,
    so.start_date,
    so.end_date,
    so.reason,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  LEFT JOIN public.profiles up ON so.user_id = up.id
  LEFT JOIN public.profiles ap ON so.admin_id = ap.id
  WHERE so.is_active = true 
    AND so.end_date > now()
    AND so.end_date <= (now() + INTERVAL '1 day' * days_ahead)
  ORDER BY so.end_date ASC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_expiring_overrides(INTEGER) TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.get_all_active_overrides(INTEGER, INTEGER) IS 'Gets all active overrides with pagination. Fixed to use profiles table. Admin only.';
COMMENT ON FUNCTION public.get_override_audit_trail(UUID, INTEGER, INTEGER) IS 'Gets the complete audit trail of overrides. Fixed to use profiles table. Admin only.';
COMMENT ON FUNCTION public.get_expiring_overrides(INTEGER) IS 'Gets overrides that will expire within specified days. Fixed to use profiles table. Admin only.';
