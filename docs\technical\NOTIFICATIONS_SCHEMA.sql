-- Admin-to-User Notifications System Schema
-- This table stores notifications that admins can send to all users

CREATE TABLE public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  priority INTEGER DEFAULT 0, -- Higher numbers = higher priority
  target_audience TEXT DEFAULT 'all' CHECK (target_audience IN ('all', 'pro', 'edu', 'standard', 'guest'))
);

-- Table to track which users have read which notifications
CREATE TABLE public.user_notification_reads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_id UUID REFERENCES public.notifications(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(user_id, notification_id)
);

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notification_reads ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notifications table
-- All authenticated users can read active notifications
CREATE POLICY "Users can read active notifications" ON public.notifications
  FOR SELECT USING (
    is_active = true 
    AND (expires_at IS NULL OR expires_at > now())
  );

-- Only admins can insert/update/delete notifications (you'll need to define admin role)
-- For now, we'll allow any authenticated user to manage notifications for testing
-- In production, you should restrict this to admin users only
CREATE POLICY "Authenticated users can manage notifications" ON public.notifications
  FOR ALL USING (auth.uid() IS NOT NULL);

-- RLS Policies for user_notification_reads table
-- Users can only read their own notification read status
CREATE POLICY "Users can read own notification reads" ON public.user_notification_reads
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own notification reads
CREATE POLICY "Users can insert own notification reads" ON public.user_notification_reads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_notifications_active_expires ON public.notifications(is_active, expires_at);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX idx_user_notification_reads_user_id ON public.user_notification_reads(user_id);
CREATE INDEX idx_user_notification_reads_notification_id ON public.user_notification_reads(notification_id);

-- Sample admin notifications (you can insert these via Supabase dashboard)
INSERT INTO public.notifications (title, message, type, priority) VALUES
  ('Welcome to DataStatPro!', 'Explore our comprehensive statistical analysis tools and start your data journey.', 'info', 1),
  ('New Feature: Advanced ANOVA', 'We''ve added support for repeated measures ANOVA. Check it out in the Inferential Statistics section.', 'success', 2),
  ('Maintenance Notice', 'Scheduled maintenance on Sunday 2AM-4AM UTC. The app will remain accessible.', 'warning', 0);
