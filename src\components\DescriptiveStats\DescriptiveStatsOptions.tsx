import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import {
  Show<PERSON>hart as DescriptivesIcon,
  BarChart as FrequenciesIcon,
  GridOn as CrossTabsIcon,
  TableChart as NormalityIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

interface DescriptiveStatsOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Quantitative' | 'Categorical' | 'Distribution';
  color: string;
}

interface DescriptiveStatsOptionsProps {
  onNavigate: (path: string) => void;
}

export const descriptiveStatsOptions: DescriptiveStatsOption[] = [
  {
    name: 'Descriptive Analysis',
    shortDescription: 'Calculate measures of central tendency, variability, and distribution shape for quantitative data',
    detailedDescription: 'Comprehensive descriptive statistics including mean, median, mode, standard deviation, variance, range, interquartile range, skewness, and kurtosis. Provides complete data summary with confidence intervals and outlier detection for quantitative variables.',
    path: 'stats/descriptives',
    icon: <DescriptivesIcon />,
    category: 'Quantitative',
    color: '#4CAF50', // Green
  },
  {
    name: 'Frequency Tables',
    shortDescription: 'Generate frequency distributions and percentage tables for categorical and discrete data',
    detailedDescription: 'Create comprehensive frequency tables with absolute frequencies, relative frequencies, percentages, valid percentages, and cumulative percentages. Includes missing data handling and graphical representations for categorical and discrete quantitative variables.',
    path: 'stats/frequencies',
    icon: <FrequenciesIcon />,
    category: 'Categorical',
    color: '#2196F3', // Blue
  },
  {
    name: 'Cross Tabulation (Contingency Tables)',
    shortDescription: 'Analyze relationships and associations between two or more categorical variables',
    detailedDescription: 'Create detailed cross-tabulation tables (contingency tables) with observed frequencies, expected frequencies, percentages by row/column/total. Includes chi-square test of independence, Cramér\'s V, phi coefficient, and standardized residuals for association analysis.',
    path: 'stats/crosstabs',
    icon: <CrossTabsIcon />,
    category: 'Categorical',
    color: '#FF9800', // Orange
  },
  {
    name: 'Normality Testing',
    shortDescription: 'Test whether data follows a normal distribution using multiple statistical tests',
    detailedDescription: 'Comprehensive normality assessment using Shapiro-Wilk test, Kolmogorov-Smirnov test, Anderson-Darling test, and Jarque-Bera test. Includes Q-Q plots, P-P plots, histograms with normal overlay, and skewness/kurtosis analysis to evaluate distributional assumptions.',
    path: 'stats/normality',
    icon: <NormalityIcon />,
    category: 'Distribution',
    color: '#9C27B0', // Purple
  },
];

const DescriptiveStatsOptions: React.FC<DescriptiveStatsOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  const categories = ['All', 'Quantitative', 'Categorical', 'Distribution'];

  // Generate structured data for descriptive statistics
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Perform Descriptive Statistical Analysis",
      "description": "Step-by-step guide to conducting descriptive statistical analysis including measures of central tendency, variability, and distribution shape.",
      "totalTime": "PT5M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Dataset with quantitative or categorical variables"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Descriptive Analysis Tool"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Upload or input your data",
          "text": "Import your dataset containing the variables you want to analyze"
        },
        {
          "@type": "HowToStep",
          "name": "Select variables for analysis",
          "text": "Choose which quantitative or categorical variables to include in the descriptive analysis"
        },
        {
          "@type": "HowToStep",
          "name": "Choose analysis options",
          "text": "Select measures of central tendency (mean, median, mode), variability (standard deviation, variance), and distribution shape (skewness, kurtosis)"
        },
        {
          "@type": "HowToStep",
          "name": "Generate results",
          "text": "Review comprehensive descriptive statistics including summary tables and visualizations"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Descriptive Statistics Report",
        "description": "Comprehensive summary of data characteristics including central tendency, variability, and distribution properties"
      }
    };
  };

  const filteredOptions = selectedCategory === 'All'
    ? descriptiveStatsOptions
    : descriptiveStatsOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Quantitative': return <DescriptivesIcon />;
      case 'Categorical': return <FrequenciesIcon />; // Using Frequencies icon for general categorical
      case 'Distribution': return <NormalityIcon />;
      default: return <DescriptivesIcon />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Descriptive Statistics Tools | DataStatPro - Central Tendency & Variability Analysis</title>
        <meta name="description" content="Professional descriptive statistics tools: calculate mean, median, mode, standard deviation, variance, skewness, kurtosis. Generate frequency tables, cross-tabulation, and normality tests for comprehensive data analysis." />
        <meta name="keywords" content="descriptive statistics, central tendency, variability, mean, median, mode, standard deviation, variance, frequency tables, cross tabulation, contingency tables, normality test, skewness, kurtosis, data summary, statistical analysis" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Descriptive statistical analysis and data summarization" />
        <meta name="ai:methods" content="Central Tendency, Variability Measures, Frequency Analysis, Normality Testing" />
        <meta name="ai:data-types" content="Quantitative, Categorical, Ordinal" />
        
        {/* Structured data for descriptive statistics */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for descriptive statistics */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What is the difference between mean, median, and mode?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Mean is the arithmetic average, median is the middle value when data is ordered, and mode is the most frequently occurring value. Mean is sensitive to outliers, while median is more robust. Mode is useful for categorical data."
                }
              },
              {
                "@type": "Question",
                "name": "When should I use standard deviation vs variance?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Standard deviation is in the same units as your data and is easier to interpret. Variance is the square of standard deviation and is used in many statistical calculations. Both measure data spread around the mean."
                }
              },
              {
                "@type": "Question",
                "name": "How do I interpret skewness and kurtosis?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Skewness measures asymmetry: positive skew means a tail extending to the right, negative skew means a tail to the left. Kurtosis measures tail heaviness: high kurtosis indicates heavy tails and potential outliers."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Descriptive Statistics - Data Summary & Analysis Tools
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Comprehensive descriptive statistics tools for data exploration and summarization. 
          Calculate measures of central tendency (mean, median, mode), variability (standard deviation, variance), 
          distribution shape (skewness, kurtosis), and generate frequency tables and cross-tabulations.
        </Typography>
        
        {/* AI-friendly feature overview */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(theme.palette.primary.main, 0.05), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Available Tools:</strong> Descriptive Analysis • Frequency Tables • Cross-Tabulation • Normality Testing • 
            Central Tendency Measures • Variability Analysis • Distribution Assessment • Missing Data Handling
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Access fundamental tools to understand the basic features of your dataset,
          including central tendency, dispersion, frequency distributions, and relationships
          between categorical variables.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography 
                    variant="h6" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Launch ${option.name} analysis tool`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Summarizing numeric data?</strong> Try Descriptive Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Counting categories?</strong> Use Frequency Tables
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing two categories?</strong> Assess with Cross Tabulation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Checking for normal distribution?</strong> Normality Test is the tool for you
            </Typography>
          </Box>
        </Box>
      </Paper>
      </Container>
    </>
  );
};

export default DescriptiveStatsOptions;
