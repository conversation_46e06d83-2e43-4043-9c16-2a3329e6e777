# Project Organization Testing Guide

This guide provides comprehensive testing procedures for the new project organization functionality in DataStatPro.

## Prerequisites

1. **Database Migration**: Ensure the migration `20250627000000_add_user_projects_support.sql` has been applied to your Supabase instance
2. **Storage Bucket**: Verify the `userprojects` storage bucket exists in Supabase
3. **RLS Policies**: Confirm Row Level Security policies are properly configured

## Test Scenarios by User Type

### 1. Guest Access Users

**Expected Behavior**: No project features visible, backward compatibility maintained

**Test Steps**:
1. Open app without logging in
2. Navigate to Results Manager
3. Verify no "Manage Projects" button is visible
4. Verify no project selector is shown
5. Run any analysis and add to Results Manager
6. Verify results appear in flat list view
7. Verify no project organization features are visible

**Expected Results**:
- ✅ Flat results list displayed
- ✅ No project management UI elements
- ✅ Results can be added and exported normally
- ✅ Backward compatibility maintained

### 2. Standard Account Users

**Expected Behavior**: Same as Guest Access - no project features

**Test Steps**:
1. Sign up/login with standard account
2. Navigate to Results Manager
3. Verify no project organization features
4. Test result addition and management
5. Verify flat list view is used

**Expected Results**:
- ✅ Same behavior as Guest Access
- ✅ No access to project features
- ✅ Standard functionality works normally

### 3. Pro/Educational Account Users

**Expected Behavior**: Full project organization features available

#### 3.1 Basic Project Management

**Test Steps**:
1. Login with Pro/Edu account
2. Navigate to Results Manager
3. Verify "Manage Projects" button is visible
4. Click "Manage Projects" to open dialog
5. Verify default project exists
6. Create a new project:
   - Click "New Project" button
   - Enter project name
   - Click "Create"
7. Verify project appears in list
8. Test project deletion (non-default project)

**Expected Results**:
- ✅ Project management UI visible
- ✅ Can create new projects
- ✅ Projects appear in organized list
- ✅ Can delete custom projects
- ✅ Cannot delete default project

#### 3.2 Result Organization

**Test Steps**:
1. Run multiple analyses (Table 1, ANOVA, etc.)
2. When adding results, verify project selection dialog appears
3. Add results to different projects
4. Navigate to Results Manager
5. Verify results are organized by project folders
6. Test expanding/collapsing project folders
7. Test moving results between projects using the menu

**Expected Results**:
- ✅ Project selection dialog works
- ✅ Results organized in expandable folders
- ✅ Can move results between projects
- ✅ Project result counts update correctly

#### 3.3 Cloud Storage Integration

**Test Steps**:
1. Create a project with some results
2. In Project Management dialog, click "Save to Cloud" for the project
3. Verify project is saved to cloud (check Supabase storage)
4. Create another project and save to cloud
5. Try to save a third project (should hit 2-project limit)
6. Delete a local project
7. Load a project from cloud
8. Verify project and results are restored

**Expected Results**:
- ✅ Projects save to cloud successfully
- ✅ 2-project limit enforced
- ✅ Can load projects from cloud
- ✅ Project data integrity maintained

## Technical Validation

### 1. Database Checks

**Supabase Console Checks**:
1. Verify `user_projects` table exists
2. Check RLS policies are active
3. Verify storage bucket `userprojects` exists
4. Test that users can only see their own projects

### 2. Local Storage Checks

**Browser DevTools**:
1. Check `statistica_projects` in localStorage
2. Verify project data structure is correct
3. Confirm backward compatibility with existing `statistica_results`

### 3. Error Handling

**Test Error Scenarios**:
1. Network failures during cloud operations
2. Invalid project names
3. Attempting to delete default project
4. Exceeding project limits
5. Corrupted project data

## Performance Testing

### 1. Large Dataset Handling

**Test Steps**:
1. Create projects with many results (50+ results)
2. Test project folder expansion/collapse performance
3. Verify result filtering works with projects
4. Test export functionality with large projects

### 2. Cloud Storage Performance

**Test Steps**:
1. Save large projects to cloud (approaching 2MB limit)
2. Test load times for cloud project retrieval
3. Verify timeout handling for slow connections

## Integration Testing

### 1. Publication Ready Components

**Test Components**:
- Table 1, Table 1a, Table 1b
- Table 2
- PostHoc Tests
- SMD Table
- Flow Diagram

**Test Steps**:
1. Run analyses in each component
2. Verify "Add to Results Manager" button works
3. Test project selection dialog
4. Verify results appear in correct projects

### 2. Cross-Browser Testing

**Browsers to Test**:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

**Test Focus**:
- Project management dialog functionality
- Local storage persistence
- Cloud storage operations

## Regression Testing

### 1. Existing Functionality

**Verify No Breaking Changes**:
- Results export still works
- Result filtering functions correctly
- Existing results remain accessible
- Non-project features unaffected

### 2. Backward Compatibility

**Test Migration Scenarios**:
1. Users with existing results before project feature
2. Mixed project and non-project results
3. Upgrading from non-Pro to Pro account

## Security Testing

### 1. Access Control

**Test Scenarios**:
1. Non-Pro users cannot access project features
2. Users cannot see other users' projects
3. Cloud storage respects user boundaries
4. RLS policies prevent unauthorized access

### 2. Data Validation

**Test Input Validation**:
1. Project name length limits
2. Special characters in project names
3. SQL injection attempts
4. XSS prevention in project names

## Troubleshooting Common Issues

### Issue: Projects not saving to cloud
**Check**: 
- Supabase connection
- RLS policies
- Storage bucket permissions
- User authentication status

### Issue: Project selector not appearing
**Check**:
- User account type (Pro/Edu required)
- Multiple projects exist
- Component import paths

### Issue: Results not organizing by project
**Check**:
- ResultsContext provider wrapping
- Project ID assignment in addResult
- Local storage data structure

## Success Criteria

The implementation is considered successful when:

- ✅ All user types see appropriate UI based on account level
- ✅ Pro users can create, manage, and organize projects
- ✅ Cloud storage works within defined limits
- ✅ Backward compatibility is maintained
- ✅ No regression in existing functionality
- ✅ Performance remains acceptable with project organization
- ✅ Security controls prevent unauthorized access
- ✅ Error handling provides good user experience
