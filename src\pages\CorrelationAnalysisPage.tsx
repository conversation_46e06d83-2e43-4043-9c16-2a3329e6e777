import React from 'react';
import { Box, Container } from '@mui/material';
import CorrelationAnalysisOptions from '../components/CorrelationAnalysis/CorrelationAnalysisOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';

interface CorrelationAnalysisPageProps {
  onNavigate: (path: string) => void;
}

const CorrelationAnalysisPage: React.FC<CorrelationAnalysisPageProps> = ({ onNavigate }) => {
  // Initialize social meta for correlation analysis page
  useSocialMeta();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <CorrelationAnalysisOptions onNavigate={onNavigate} />
      </Box>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible
      />
    </Container>
  );
};

export default CorrelationAnalysisPage;
