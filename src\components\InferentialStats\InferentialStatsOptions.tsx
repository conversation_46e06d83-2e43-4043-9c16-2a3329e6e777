import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  Science as ScienceIcon,
  BarChart as BarChartIcon,
  ShowChart as ShowChartIcon,
  CompareArrows as CompareArrowsIcon,
  GroupWork as GroupWorkIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  Category as CategoryIcon,
  Functions as FunctionsIcon,
  TableChart as TableChartIcon,
} from '@mui/icons-material';

interface InferentialStatsOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'T-Tests' | 'ANOVA' | 'Non-Parametric' | 'Association';
  color: string;
}

interface InferentialStatsOptionsProps {
  onNavigate: (path: string) => void;
  initialCategory?: string; // Add initialCategory prop
}

export const inferentialStatsOptions: InferentialStatsOption[] = [
  {
    name: 'One-Sample T-Test',
    shortDescription: 'Compare one sample mean to a hypothesized value',
    detailedDescription: 'Perform a one-sample t-test to determine if the mean of a single sample is significantly different from a known or hypothesized population mean.',
    path: 'inferential-stats/one-sample-ttest',
    icon: <ScienceIcon />,
    category: 'T-Tests',
    color: '#2196F3', // Blue
  },
  {
    name: 'Independent Samples T-Test',
    shortDescription: 'Compare means of two independent groups',
    detailedDescription: 'Perform an independent samples t-test to compare the means of two unrelated groups and determine if there is a statistically significant difference between them.',
    path: 'inferential-stats/independent-samples-ttest',
    icon: <CompareArrowsIcon />,
    category: 'T-Tests',
    color: '#4CAF50', // Green
  },
  {
    name: 'Paired Samples T-Test',
    shortDescription: 'Compare means of two related samples',
    detailedDescription: 'Perform a paired samples t-test to compare the means of two related samples or repeated measurements on the same subjects and determine if there is a statistically significant difference.',
    path: 'inferential-stats/paired-samples-ttest',
    icon: <ShowChartIcon />,
    category: 'T-Tests',
    color: '#FF9800', // Orange
  },
  {
    name: 'Mann-Whitney U Test',
    shortDescription: 'Non-parametric alternative to Independent T-Test',
    detailedDescription: 'Use the Mann-Whitney U test (also known as the Wilcoxon rank-sum test) to compare two independent groups when the assumptions for the independent samples t-test are not met.',
    path: 'inferential-stats/mann-whitney-u-test', // Assuming a path for this specific test
    icon: <FunctionsIcon />,
    category: 'Non-Parametric',
    color: '#9C27B0', // Purple
  },
  {
    name: 'Wilcoxon Signed-Rank Test',
    shortDescription: 'Non-parametric alternative to Paired T-Test',
    detailedDescription: 'Use the Wilcoxon signed-rank test to compare two related samples or repeated measurements when the assumptions for the paired samples t-test are not met.',
    path: 'inferential-stats/wilcoxon-signed-rank-test', // Assuming a path for this specific test
    icon: <FunctionsIcon />,
    category: 'Non-Parametric',
    color: '#E91E63', // Pink
  },
  {
    name: 'Kruskal-Wallis Test',
    shortDescription: 'Non-parametric alternative to One-Way ANOVA',
    detailedDescription: 'Use the Kruskal-Wallis test to compare three or more independent groups when the assumptions for one-way ANOVA are not met.',
    path: 'inferential-stats/kruskal-wallis-test', // Assuming a path for this specific test
    icon: <FunctionsIcon />,
    category: 'Non-Parametric',
    color: '#00BCD4', // Cyan
  },
   {
    name: 'Friedman Test',
    shortDescription: 'Non-parametric alternative to Repeated Measures ANOVA',
    detailedDescription: 'Use the Friedman test to compare three or more related groups or repeated measurements when the assumptions for repeated measures ANOVA are not met.',
    path: 'inferential-stats/friedman-test', // Assuming a path for this specific test
    icon: <FunctionsIcon />,
    category: 'Non-Parametric',
    color: '#795548', // Brown
  },
  {
    name: 'Chi-Square Test',
    shortDescription: 'Test association between categorical variables',
    detailedDescription: 'Perform a Chi-Square test of independence to determine if there is a statistically significant association between two categorical variables.',
    path: 'inferential-stats/chi-square-test', // Assuming a path for this specific test
    icon: <TableChartIcon />,
    category: 'Association',
    color: '#607D8B', // Blue Grey
  },
  {
    name: 'One-Way ANOVA',
    shortDescription: 'Compare means of three or more independent groups',
    detailedDescription: 'Perform a one-way Analysis of Variance (ANOVA) to determine if there are any statistically significant differences between the means of three or more independent groups.',
    path: 'inferential-stats/one-way-anova', // Assuming a path for this specific test
    icon: <GroupWorkIcon />,
    category: 'ANOVA',
    color: '#FFC107', // Amber
  },
  {
    name: 'Repeated Measures ANOVA',
    shortDescription: 'Compare means of three or more related samples',
    detailedDescription: 'Perform a repeated measures Analysis of Variance (ANOVA) to determine if there are any statistically significant differences between the means of three or more related samples or repeated measurements.',
    path: 'inferential-stats/repeated-measures-anova', // Assuming a path for this specific test
    icon: <GroupWorkIcon />,
    category: 'ANOVA',
    color: '#FF5722', // Deep Orange
  },
   {
    name: 'Two-Way ANOVA',
    shortDescription: 'Examine effects of two factors on a dependent variable',
    detailedDescription: 'Perform a two-way Analysis of Variance (ANOVA) to examine the main effects of two independent categorical variables (factors) and their interaction effect on a continuous dependent variable.',
    path: 'inferential-stats/two-way-anova', // Assuming a path for this specific test
    icon: <GroupWorkIcon />,
    category: 'ANOVA',
    color: '#3F51B5', // Indigo
  },
];

const InferentialStatsOptions: React.FC<InferentialStatsOptionsProps> = ({ onNavigate, initialCategory }) => {
  const theme = useTheme();

  // Map URL sub-paths to internal category names
  const categoryMap: { [key: string]: string } = {
    't-tests': 'T-Tests',
    'non-parametric-tests': 'Non-Parametric',
    'anova': 'ANOVA',
    // Add other mappings if needed
  };

  // Set initial category based on the prop, defaulting to 'All'
  const initialSelectedCategory = initialCategory && categoryMap[initialCategory] ? categoryMap[initialCategory] : 'All';
  const [selectedCategory, setSelectedCategory] = useState<string>(initialSelectedCategory);

  // Generate structured data for inferential statistics
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Perform Inferential Statistical Tests",
      "description": "Comprehensive guide to conducting inferential statistical analysis including hypothesis testing, t-tests, ANOVA, and non-parametric tests.",
      "totalTime": "PT10M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Dataset with appropriate sample size"
        },
        {
          "@type": "HowToSupply",
          "name": "Research hypothesis to test"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Inferential Statistics Suite"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Check assumptions",
          "text": "Verify normality, independence, and homogeneity of variance assumptions for your chosen test"
        },
        {
          "@type": "HowToStep",
          "name": "Select appropriate test",
          "text": "Choose between parametric (t-tests, ANOVA) or non-parametric tests based on your data characteristics"
        },
        {
          "@type": "HowToStep",
          "name": "Set significance level",
          "text": "Determine your alpha level (typically 0.05) and whether to use one-tailed or two-tailed testing"
        },
        {
          "@type": "HowToStep",
          "name": "Interpret results",
          "text": "Analyze p-values, confidence intervals, and effect sizes to draw statistical conclusions"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Statistical Test Results",
        "description": "Comprehensive statistical analysis including test statistics, p-values, confidence intervals, and effect sizes"
      }
    };
  };

  const categories = ['All', 'T-Tests', 'ANOVA', 'Non-Parametric', 'Association'];

  const filteredOptions = selectedCategory === 'All'
    ? inferentialStatsOptions
    : inferentialStatsOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'T-Tests': return <ScienceIcon />;
      case 'ANOVA': return <GroupWorkIcon />;
      case 'Non-Parametric': return <FunctionsIcon />;
      case 'Association': return <TableChartIcon />;
      default: return <CategoryIcon />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Inferential Statistics Tests | DataStatPro - Hypothesis Testing & Statistical Analysis</title>
        <meta name="description" content="Professional inferential statistics tools: t-tests, ANOVA, chi-square, Mann-Whitney, Wilcoxon, Kruskal-Wallis tests. Comprehensive hypothesis testing with p-values, confidence intervals, and effect sizes." />
        <meta name="keywords" content="inferential statistics, hypothesis testing, t-test, ANOVA, chi-square test, Mann-Whitney test, Wilcoxon test, Kruskal-Wallis test, statistical significance, p-value, confidence intervals, effect size, parametric tests, non-parametric tests, statistical analysis" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Inferential statistical analysis and hypothesis testing" />
        <meta name="ai:methods" content="T-Tests, ANOVA, Chi-Square, Non-parametric Tests, Hypothesis Testing" />
        <meta name="ai:assumptions" content="Normality, Independence, Homogeneity of Variance" />
        
        {/* Structured data for inferential statistics */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for inferential statistics */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "When should I use a t-test vs ANOVA?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Use t-tests when comparing means between two groups. Use ANOVA when comparing means among three or more groups. T-tests include one-sample, independent samples, and paired samples variants."
                }
              },
              {
                "@type": "Question",
                "name": "What is the difference between parametric and non-parametric tests?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Parametric tests (t-tests, ANOVA) assume normal distribution and are more powerful. Non-parametric tests (Mann-Whitney, Wilcoxon, Kruskal-Wallis) don't assume normality and are used when data violates parametric assumptions."
                }
              },
              {
                "@type": "Question",
                "name": "How do I interpret p-values and statistical significance?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "P-values represent the probability of obtaining results as extreme as observed, assuming the null hypothesis is true. P < 0.05 typically indicates statistical significance, meaning you can reject the null hypothesis."
                }
              },
              {
                "@type": "Question",
                "name": "What assumptions must be met for ANOVA?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "ANOVA assumes: (1) normality of residuals, (2) independence of observations, (3) homogeneity of variance (homoscedasticity). Violations may require data transformation or non-parametric alternatives."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section with AI-optimized content */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Inferential Statistics - Hypothesis Testing & Statistical Analysis
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Comprehensive inferential statistics tools for hypothesis testing, comparing groups, and making statistical inferences. 
          Includes t-tests, ANOVA, chi-square tests, and non-parametric alternatives with p-values, confidence intervals, and effect sizes.
        </Typography>
        
        {/* AI-friendly feature overview */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(theme.palette.info.main, 0.05), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Test Categories:</strong> T-Tests (One-sample, Independent, Paired) • ANOVA (One-way, Two-way, Repeated Measures) • 
            Non-parametric Tests (Mann-Whitney, Wilcoxon, Kruskal-Wallis) • Association Tests (Chi-Square, Fisher's Exact)
          </Typography>
        </Box>
        
        <Typography variant="body1" color="text.secondary">
          Access a suite of tools for hypothesis testing and inferring properties of a population from a sample,
          including t-tests, ANOVA, non-parametric tests, and tests of association.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography 
                    variant="h6" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  onClick={() => onNavigate(option.path)}
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Launch ${option.name} statistical test`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing two groups?</strong> Use T-Tests (parametric) or Mann-Whitney/Wilcoxon (non-parametric).
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Comparing three or more groups?</strong> Use ANOVA (parametric) or Kruskal-Wallis/Friedman (non-parametric).
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Examining relationships between categorical variables?</strong> Use the Chi-Square Test.
            </Typography>
             <Typography variant="body2" color="text.secondary">
              • <strong>Analyzing effects of multiple factors?</strong> Consider Two-Way ANOVA.
            </Typography>
          </Box>
        </Box>
      </Paper>
      </Container>
    </>
  );
};

export default InferentialStatsOptions;
