// Core application routes (dashboard, auth, etc.)

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';
// Import AuthView enum - will be defined in Auth component
enum AuthView {
  LOGIN = 'login',
  REGISTER = 'register'
}

// Lazy load components
const DashboardPage = lazy(() => import('../../pages/DashboardPage'));
const HomePage = lazy(() => import('../../pages/HomePage'));
const Auth = lazy(() => import('../../components/Auth/Auth'));
const UserProfile = lazy(() => import('../../components/Auth/UserProfile'));
const Settings = lazy(() => import('../../components/Auth/Settings'));
const UpdatePassword = lazy(() => import('../../components/Auth/UpdatePassword'));
const AnalysisAssistantPage = lazy(() => import('../../pages/AnalysisAssistantPage'));
const WhichTestPage = lazy(() => import('../../pages/WhichTestPage'));
const VisualizationGuidePage = lazy(() => import('../../pages/VisualizationGuidePage'));
const StatisticalMethodsPage = lazy(() => import('../../pages/StatisticalMethodsPage'));
const KnowledgeBasePage = lazy(() => import('../../pages/KnowledgeBasePage'));
const TutorialPage = lazy(() => import('../../pages/TutorialPage'));
const VideoTutorialsPage = lazy(() => import('../../pages/VideoTutorialsPage'));
const AnalysisIndexPage = lazy(() => import('../../pages/AnalysisIndexPage'));
const PricingPage = lazy(() => import('../../pages/PricingPage'));
const NotificationsPage = lazy(() => import('../../pages/NotificationsPage'));
const TipOfTheDayPage = lazy(() => import('../../pages/TipOfTheDayPage'));



export const coreRoutes: EnhancedRouteConfig[] = [
  // Dashboard and Home
  {
    path: 'dashboard',
    component: DashboardPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to dashboard
    metadata: {
      title: 'Dashboard',
      description: 'Main application dashboard',
      category: 'core',
      icon: 'Dashboard',
      order: 0
    }
  },
  {
    path: 'home',
    component: DashboardPage, // Map home to dashboard
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Home',
      description: 'Application home page',
      category: 'core',
      hidden: true
    }
  },

  // Authentication
  {
    path: 'auth',
    component: Auth,
    requiresAuth: false,
    allowGuest: false,
    allowPublic: true,
    props: {
      initialView: AuthView.LOGIN
      // onAuthSuccess will be provided by AppRouter
    },
    metadata: {
      title: 'Authentication',
      description: 'Login and registration',
      category: 'auth',
      hidden: true
    },
    children: [
      {
        path: 'auth/login',
        component: Auth,
        requiresAuth: false,
        allowPublic: true,
        props: {
          initialView: AuthView.LOGIN
        },
        metadata: {
          title: 'Login',
          category: 'auth'
        }
      },
      {
        path: 'auth/register',
        component: Auth,
        requiresAuth: false,
        allowPublic: true,
        props: {
          initialView: AuthView.REGISTER
        },
        metadata: {
          title: 'Register',
          category: 'auth'
        }
      }
    ]
  },
  {
    path: 'reset-password',
    component: UpdatePassword,
    requiresAuth: false,
    allowGuest: false,
    allowPublic: true,
    metadata: {
      title: 'Reset Password',
      description: 'Reset your password',
      category: 'auth',
      hidden: true
    }
  },

  // User Management
  {
    path: 'profile',
    component: UserProfile,
    requiresAuth: true,
    allowGuest: false,
    allowPublic: false,
    metadata: {
      title: 'User Profile',
      description: 'Manage your profile settings',
      category: 'user',
      icon: 'Person'
    }
  },
  {
    path: 'settings',
    component: Settings,
    requiresAuth: true,
    allowGuest: false,
    allowPublic: false,
    metadata: {
      title: 'Settings',
      description: 'Application settings and preferences',
      category: 'user',
      icon: 'Settings'
    }
  },
  {
    path: 'notifications',
    component: NotificationsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Notifications',
      description: 'View and manage all your notifications',
      category: 'core',
      icon: 'Notifications',
      order: 5
    }
  },

  // Help and Learning
  {
    path: 'assistant',
    component: AnalysisAssistantPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Analysis Assistant',
      description: 'Get help choosing the right statistical test',
      category: 'help',
      icon: 'Help',
      order: 10
    }
  },
  // Alias route for backward compatibility
  {
    path: 'analysis-assistant',
    component: AnalysisAssistantPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Analysis Assistant',
      description: 'Get help choosing the right statistical test',
      category: 'help',
      icon: 'Help',
      hidden: true // Hide from navigation menus
    }
  },
  {
    path: 'which-test',
    component: WhichTestPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Which Test?',
      description: 'Find the right statistical test for your data',
      category: 'help',
      icon: 'Quiz'
    }
  },
  {
    path: 'visualization-guide',
    component: VisualizationGuidePage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Visualization Guide',
      description: 'Learn about data visualization best practices',
      category: 'help',
      icon: 'BarChart'
    }
  },
  {
    path: 'statistical-methods',
    component: StatisticalMethodsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Statistical Methods',
      description: 'Reference guide for statistical methods',
      category: 'help',
      icon: 'MenuBook'
    }
  },
  {
    path: 'knowledge-base',
    component: KnowledgeBasePage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Knowledge Base',
      description: 'Comprehensive help and documentation',
      category: 'help',
      icon: 'LibraryBooks'
    },
    children: [
      {
        path: 'knowledge-base/:tutorialId',
        component: TutorialPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true,
        metadata: {
          title: 'Tutorial',
          category: 'help'
        }
      }
    ]
  },
  {
    path: 'video-tutorials',
    component: VideoTutorialsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Video Tutorials',
      description: 'Video guides and tutorials',
      category: 'help',
      icon: 'VideoLibrary'
    }
  },
  {
    path: 'tip-of-the-day',
    component: TipOfTheDayPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Tip of the Day',
      description: 'Daily tips to improve your workflow',
      category: 'help',
      icon: 'TipsAndUpdates'
    }
  },
  {
    path: 'analysis-index',
    component: AnalysisIndexPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to analysis index
    metadata: {
      title: 'Analysis Index',
      description: 'Browse all available analyses',
      category: 'analysis',
      icon: 'List'
    }
  },
  {
    path: 'pricing',
    component: PricingPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Pricing',
      description: 'Choose your DataStatPro plan',
      category: 'core',
      icon: 'AttachMoney',
      order: 10
    }
  },



];
