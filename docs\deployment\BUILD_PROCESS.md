# Build Process Documentation

## Automated .htaccess and Social Meta File Synchronization

This project includes an automated build process that ensures the main `.htaccess` file and `social-meta.php` file are automatically copied to the `deploy_assets` folder during the build process.

### How It Works

1. **Main Files Location:**
   - `.htaccess` - Located in the project root directory
   - `social-meta.php` - Located in the project root directory

2. **Deploy Files Location:**
   - `deploy_assets/.htaccess` - Automatically copied during build
   - `deploy_assets/social-meta.php` - Automatically copied during build

3. **Build Scripts:**
   - `npm run build` - Runs Vite build + automatic file copying
   - `npm run build:check` - Runs TypeScript check + Vite build + automatic file copying
   - `npm run copy-htaccess` - Manual command to copy files without building

### What Gets Copied

The automated process copies:
- **Main .htaccess** → **deploy_assets/.htaccess**
  - Social media crawler detection rules
  - Security headers and CSP configuration
  - SPA routing rules
  - PWA settings

- **social-meta.php** → **deploy_assets/social-meta.php**
  - Dynamic meta tag generation for tutorial pages
  - Open Graph and Twitter Card configurations
  - Structured data markup

### Manual Synchronization

If you need to manually sync the files without running a full build:

```bash
npm run copy-htaccess
```

### Important Notes

1. **Always edit the main files** in the project root, not the deploy_assets versions
2. **The deploy_assets versions are overwritten** during each build
3. **Both files are required** for proper social sharing functionality
4. **The process uses PowerShell commands** compatible with Windows environment

### Social Sharing Features

The synchronized files enable:
- Dynamic meta tags for knowledge base tutorials
- Proper Open Graph previews on Facebook/LinkedIn
- Twitter Card support
- Crawler detection and URL rewriting
- Structured data for search engines

### Troubleshooting

If social sharing previews aren't working:
1. Run `npm run build` to ensure files are synchronized
2. Check that both `.htaccess` and `social-meta.php` exist in `deploy_assets/`
3. Verify crawler detection rules are present in `deploy_assets/.htaccess`
4. Test with social media debugger tools (Facebook Debugger, LinkedIn Post Inspector)