import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Chip,
  Box,
  Button,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  Upgrade as UpgradeIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { OverrideStatusBadge } from '../OverrideStatus';

const AccountStatusWidget: React.FC = () => {
  const { 
    user,
    accountType, 
    subscriptionData, 
    subscriptionStatus, 
    hasActiveSubscription,
    nextPaymentDate,
    canUpgradeAccount 
  } = useAuth();
  const navigate = useNavigate();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getAccountTypeDisplay = () => {
    switch (accountType) {
      case 'pro':
        return { label: 'PRO', color: 'primary' as const, icon: <CheckIcon /> };
      case 'edu':
        return { label: 'EDU', color: 'secondary' as const, icon: <CheckIcon /> };
      case 'standard':
        return { label: 'STANDARD', color: 'default' as const, icon: <InfoIcon /> };
      default:
        return { label: 'GUEST', color: 'default' as const, icon: <InfoIcon /> };
    }
  };

  const getStatusAlert = () => {
    if (!user) return null;

    if (subscriptionStatus === 'past_due') {
      return (
        <Alert severity="warning" sx={{ mt: 1, py: 0 }}>
          <Typography variant="caption">
            Payment overdue - Update billing
          </Typography>
        </Alert>
      );
    }

    if (canUpgradeAccount) {
      return (
        <Alert severity="info" sx={{ mt: 1, py: 0 }}>
          <Typography variant="caption">
            Upgrade for cloud features & advanced analytics
          </Typography>
        </Alert>
      );
    }

    return null;
  };

  const handleUpgradeClick = () => {
    if (canUpgradeAccount) {
      navigate('/app#/profile?tab=billing');
    } else if (hasActiveSubscription) {
      navigate('/app#/profile?tab=billing');
    }
  };

  const handleManageBilling = () => {
    navigate('/app#/profile?tab=billing');
  };

  if (!user) {
    return (
      <Card>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h6" gutterBottom>
            Account Status
          </Typography>
          <Chip 
            label="GUEST" 
            color="default"
            size="small"
            icon={<InfoIcon />}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Sign in to access personal data features
          </Typography>
          <Button
            size="small"
            variant="outlined"
            onClick={() => navigate('/app#/auth/login')}
            sx={{ mt: 1 }}
          >
            Sign In
          </Button>
        </CardContent>
      </Card>
    );
  }

  const accountDisplay = getAccountTypeDisplay();

  return (
    <Card>
      <CardContent sx={{ py: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="h6">
            Account Status
          </Typography>
          {hasActiveSubscription && (
            <Button
              size="small"
              variant="text"
              onClick={handleManageBilling}
              sx={{ minWidth: 'auto', p: 0.5 }}
            >
              Manage
            </Button>
          )}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Chip 
            label={accountDisplay.label} 
            color={accountDisplay.color}
            size="small"
            icon={accountDisplay.icon}
          />
          {subscriptionStatus === 'active' && (
            <Chip 
              label="ACTIVE" 
              color="success" 
              size="small"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
          {subscriptionStatus === 'past_due' && (
            <Chip 
              label="OVERDUE" 
              color="warning" 
              size="small"
              icon={<WarningIcon />}
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
        </Box>

        {/* Override Status */}
        <OverrideStatusBadge variant="card" showDetails={true} />

        {/* Subscription Details */}
        {hasActiveSubscription && nextPaymentDate && (
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
            Next payment: {formatDate(nextPaymentDate)}
          </Typography>
        )}

        {/* Progress indicator for trial or subscription period */}
        {subscriptionData && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Billing period
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={75} // This would be calculated based on current period
              sx={{ height: 4, borderRadius: 2, mt: 0.5 }}
            />
          </Box>
        )}

        {/* Status Alert */}
        {getStatusAlert()}

        {/* Action Button */}
        {canUpgradeAccount && (
          <Button
            size="small"
            variant="contained"
            startIcon={<UpgradeIcon />}
            onClick={handleUpgradeClick}
            sx={{ mt: 1, width: '100%' }}
          >
            Upgrade Account
          </Button>
        )}

        {subscriptionStatus === 'past_due' && (
          <Button
            size="small"
            variant="contained"
            color="warning"
            onClick={handleManageBilling}
            sx={{ mt: 1, width: '100%' }}
          >
            Update Payment
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default AccountStatusWidget;
