import React, { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Divider,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
} from '@mui/material';
import FunctionsIcon from '@mui/icons-material/Functions';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';
import {
  CalculateOutlined as CalculateIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { StatsCard } from '../UI';
import { chiSquareTest as calculateChiSquare } from '../../utils/stats/non-parametric';
import jStat from 'jstat'; // Import jStat for accurate p-value calculation

// Add these imports if they're not already present
import {
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';

// Case-Control Calculator class with statistical methods
class CaseControlCalculatorUtils {
  // Odds Ratio with Woolf's CI
  static oddsRatio(a: number, b: number, c: number, d: number) {
    const or = (a * d) / (b * c);
    const logOR = Math.log(or);
    const se = Math.sqrt(1/a + 1/b + 1/c + 1/d);
    return {
      or: or,
      ci: [Math.exp(logOR - 1.96*se), Math.exp(logOR + 1.96*se)] as [number, number]
    };
  }

  // Directly Adjusted Odds Ratio
  static directlyAdjustedOddsRatio(strata: Array<{a: number, b: number, c: number, d: number}>) {
    let sum_w_ln_OR = 0;
    let sum_w = 0;
    const z_alpha_2 = 1.96; // For 95% CI

    strata.forEach(({a, b, c, d}) => {
      // Calculate OR_i for each stratum
      // Avoid division by zero or log of zero/negative
      if (b * c === 0) {
        // If b or c is zero, OR_i is undefined or infinity.
        // This stratum cannot contribute to the direct adjustment.
        // In practice, such strata are often excluded or handled with continuity correction.
        // For this implementation, we'll skip them as per the formula's implicit assumption.
        return; 
      }
      const OR_i = (a * d) / (b * c);
      
      // Calculate w_i (weight) for each stratum
      // Avoid division by zero
      const denominator_w = (1/a || 0) + (1/b || 0) + (1/c || 0) + (1/d || 0);
      if (denominator_w === 0) {
        return; // Skip if denominator is zero
      }
      const w_i = 1 / denominator_w;

      // Accumulate sums
      sum_w_ln_OR += w_i * Math.log(OR_i);
      sum_w += w_i;
    });

    if (sum_w === 0) {
      return {
        orDirect: NaN,
        ci: [NaN, NaN] as [number, number]
      };
    }

    const orDirect = Math.exp(sum_w_ln_OR / sum_w);

    // Calculate confidence interval
    const se_ln_orDirect = 1 / Math.sqrt(sum_w);
    const lowerCI = Math.exp(Math.log(orDirect) - z_alpha_2 * se_ln_orDirect);
    const upperCI = Math.exp(Math.log(orDirect) + z_alpha_2 * se_ln_orDirect);

    return {
      orDirect: orDirect,
      ci: [lowerCI, upperCI] as [number, number]
    };
  }

  // Mantel-Haenszel Adjusted OR (for stratified data)
  static mantelHaenszelOR(strata: Array<{a: number, b: number, c: number, d: number}>) {
    let numerator = 0;
    let denominator = 0;
    
    strata.forEach(({a, b, c, d}) => {
      const n_i = a + b + c + d;
      if (n_i === 0) return; // Skip strata with zero total
      
      numerator += (a * d) / n_i;
      denominator += (b * c) / n_i;
    });
    
    // Check for division by zero
    if (denominator === 0) {
      return {
        mhOR: 0,
        ci: [0, 0] as [number, number]
      };
    }
    
    const mhOR = numerator / denominator;
    
    return {
      mhOR: mhOR,
      ci: this.calculateMantelHaenszelCI(strata, mhOR)
    };
  }

  // Helper functions for Mantel-Haenszel CI
  static calculateMantelHaenszelCI(strata: Array<{a: number, b: number, c: number, d: number}>, mhOR: number) {
    // Calculate R_i, S_i, P_i, Q_i for each stratum
    let sumR = 0;
    let sumS = 0;
    let sumPR = 0;
    let sumQS = 0;
    let sumPSQR = 0;
    
    strata.forEach(({a, b, c, d}) => {
      const n_i = a + b + c + d;
      
      // Skip strata with zero total
      if (n_i === 0) return;
      
      // Calculate R_i and S_i
      const R_i = (a * d) / n_i;
      const S_i = (b * c) / n_i;
      
      // Calculate P_i and Q_i
      const P_i = (a + d) / n_i;
      const Q_i = (b + c) / n_i;
      
      // Accumulate sums for variance calculation
      sumR += R_i;
      sumS += S_i;
      sumPR += P_i * R_i;
      sumQS += Q_i * S_i;
      sumPSQR += (P_i * S_i) + (Q_i * R_i);
    });
    
    // Check for division by zero
    if (sumR === 0 || sumS === 0) {
      return [0, 0] as [number, number]; // Return default values to avoid NaN
    }
    
    // Calculate variance of ln(OR_MH) according to the formula
    const variance = sumPR / (2 * Math.pow(sumR, 2)) + 
                    sumPSQR / (2 * sumR * sumS) + 
                    sumQS / (2 * Math.pow(sumS, 2));
    
    // Calculate confidence interval
    const z = 1.96; // 95% confidence level
    const lowerCI = Math.exp(Math.log(mhOR) - z * Math.sqrt(variance));
    const upperCI = Math.exp(Math.log(mhOR) + z * Math.sqrt(variance));
    
    return [lowerCI, upperCI] as [number, number];
  }

  // Uncorrected Chi-square Test
  static uncorrectedChiSquareTest(a: number, b: number, c: number, d: number) {
    const contingencyTable = [[a, b], [c, d]];
    return calculateChiSquare(contingencyTable);
  }

  // Chi-square Test (Yates' corrected)
  static chiSquareTestYates(a: number, b: number, c: number, d: number) {
    const n = a + b + c + d;
    const expectedA = (a + b)*(a + c)/n;
    const expectedB = (a + b)*(b + d)/n;
    const expectedC = (c + d)*(a + c)/n;
    const expectedD = (c + d)*(b + d)/n;
    
    const chi2 = (Math.pow(Math.abs(a - expectedA) - 0.5, 2)/expectedA) +
                (Math.pow(Math.abs(b - expectedB) - 0.5, 2)/expectedB) +
                (Math.pow(Math.abs(c - expectedC) - 0.5, 2)/expectedC) +
                (Math.pow(Math.abs(d - expectedD) - 0.5, 2)/expectedD);
    
    // For Yates' corrected chi-square, df is always 1 for a 2x2 table
    // The p-value calculation should ideally use a proper chi-square CDF from a library
    // For now, we'll use the imported chiSquareTest's p-value calculation for consistency
    // with the library's approach, but apply it to the Yates' corrected chi2.
    // For Yates' corrected chi-square, df is always 1 for a 2x2 table
    return { chi2: chi2, p: this.chiSquarePValue(chi2, 1) };
  }

  // Fisher's Exact Test (2x2)
  static fisherExact(a: number, b: number, c: number, d: number) {
    // This is a simplified implementation
    // For a more accurate calculation, you would use a proper statistical library
    const factorial = (n: number): number => {
      if (n === 0 || n === 1) return 1;
      let result = 1;
      for (let i = 2; i <= n; i++) result *= i;
      return result;
    };

    const hypergeometric = (a: number, b: number, c: number, d: number) => {
      return (factorial(a+b) * factorial(c+d) * factorial(a+c) * factorial(b+d)) /
             (factorial(a) * factorial(b) * factorial(c) * factorial(d) * factorial(a+b+c+d));
    };
    
    const original = hypergeometric(a, b, c, d);
    let p = 0;
    
    // This is a simplified approach - a full implementation would be more complex
    // and would handle larger numbers better
    for(let x = 0; x <= Math.min(a + b, a + c); x++) {
      const y = a + b - x;
      const z = a + c - x;
      const w = b + d - y;
      if (y >= 0 && z >= 0 && w >= 0) {
        const current = hypergeometric(x, y, z, w);
        if(current <= original) p += current;
      }
    }
    
    return p;
  }

  static chiSquarePValue(chi2: number, df: number): number | null {
    if (df <= 0) {
      return null; // p-value not meaningful for df <= 0
    }
    // Use jStat for accurate p-value calculation from chi-square distribution
    return 1 - jStat.chisquare.cdf(chi2, df);
  }

  // Mantel-Haenszel Chi-Square Test
  static mantelHaenszelChiSquare(strata: Array<{a: number, b: number, c: number, d: number}>) {
    let sum_ai_minus_Eai = 0;
    let sum_Var_ai = 0;

    strata.forEach(({a, b, c, d}) => {
      const n_i = a + b + c + d;

      if (n_i === 0 || n_i === 1) return; // Skip strata with zero or one total observation, as variance would be undefined

      // Expected Value E(a_i)
      const E_ai = ((a + b) * (a + c)) / n_i;

      // Variance Var(a_i)
      const Var_ai = ((a + b) * (c + d) * (a + c) * (b + d)) / (Math.pow(n_i, 2) * (n_i - 1));

      sum_ai_minus_Eai += (a - E_ai);
      sum_Var_ai += Var_ai;
    });

    if (sum_Var_ai === 0) {
      return { chi2: 0, p: null }; // Avoid division by zero
    }

    const mhChi2 = Math.pow(sum_ai_minus_Eai, 2) / sum_Var_ai;
    
    // Degrees of freedom is 1 for Mantel-Haenszel Chi-Square
    return { chi2: mhChi2, p: this.chiSquarePValue(mhChi2, 1) };
  }
}

interface Stratum {
  id: string;
  a: number;
  b: number;
  c: number;
  d: number;
}

const CaseControlCalculator: React.FC = () => {
  const theme = useTheme();

  // State for 2x2 table values
  const [cellValues, setCellValues] = useState({
    a: 0,
    b: 0,
    c: 0,
    d: 0
  });

  // Function to render mathematical formulas using KaTeX
  const renderFormula = useCallback((formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  }, []);

  // State for stratified analysis
  const [strata, setStrata] = useState<Stratum[]>([]);
  const [currentStratumIndex, setCurrentStratumIndex] = useState<number>(0);
  // Remove this line since we're not using an accordion anymore
  // const [showStratifiedAnalysis, setShowStratifiedAnalysis] = useState(false);

  // State for calculation results
  const [results, setResults] = useState<{
    oddsRatio?: { or: number; ci: [number, number] };
    mantelHaenszel?: { mhOR: number; ci: [number, number] };
    directlyAdjustedOR?: { orDirect: number; ci: [number, number] }; // New state for directly adjusted OR
    chiSquareYates?: { chi2: number; p: number | null }; // Revert to number | null
    chiSquareUncorrected?: { chiSquare: number; df: number; pValue: number; cramersV: number; hasLowExpectedFrequencies?: boolean; lowFrequencyCells?: Array<{row: number, col: number, expected: number}>; };
    fisherExact?: number | null;
    mantelHaenszelChiSquare?: { chi2: number; p: number | null };
  }>({});

  // Handle input changes for main 2x2 table
  const handleInputChange = (cell: 'a' | 'b' | 'c' | 'd', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setCellValues(prev => ({
      ...prev,
      [cell]: isNaN(numValue) ? 0 : numValue
    }));
  };

  // Handle input changes for stratified analysis
  const handleStratumInputChange = (id: string, cell: 'a' | 'b' | 'c' | 'd', value: string) => {
    const numValue = value === '' ? 0 : parseInt(value, 10);
    setStrata(prev => prev.map(stratum => 
      stratum.id === id ? { ...stratum, [cell]: isNaN(numValue) ? 0 : numValue } : stratum
    ));
  };

  // Add a new stratum for stratified analysis
  const addStratum = () => {
    const newStratum: Stratum = {
      id: `stratum_${Date.now()}`,
      a: 0,
      b: 0,
      c: 0,
      d: 0
    };
    setStrata(prev => [...prev, newStratum]);
    setCurrentStratumIndex(strata.length); // Select the newly added stratum
  };

  // Remove a stratum
  const removeStratum = () => {
    if (strata.length <= 1) return; // Don't remove if it's the last stratum
    
    const newStrata = strata.filter((_, index) => index !== currentStratumIndex);
    setStrata(newStrata);
    
    // Adjust current index if needed
    if (currentStratumIndex >= newStrata.length) {
      setCurrentStratumIndex(Math.max(0, newStrata.length - 1));
    }
  };

  // Calculate consolidated table values (base table + all strata)
  const getConsolidatedTable = () => {
    // Start with the base table values
    let consolidated = { ...cellValues };
    
    // Add values from all strata
    strata.forEach(stratum => {
      consolidated.a += stratum.a;
      consolidated.b += stratum.b;
      consolidated.c += stratum.c;
      consolidated.d += stratum.d;
    });
    
    return consolidated;
  };

  // Calculate results
  const calculateResults = () => {
    try {
      // Get consolidated table for crude OR and other measures
      const consolidated = getConsolidatedTable();
      const { a, b, c, d } = consolidated;
      const total = a + b + c + d;

      if (total === 0) return;

      // Calculate crude odds ratio using consolidated table
      const oddsRatioResult = CaseControlCalculatorUtils.oddsRatio(a, b, c, d);
      
      // Calculate chi-square test using consolidated table
      const chiSquareYatesResult = CaseControlCalculatorUtils.chiSquareTestYates(a, b, c, d);
      const chiSquareUncorrectedResult = CaseControlCalculatorUtils.uncorrectedChiSquareTest(a, b, c, d);
      
      // Only calculate Fisher's exact test for small sample sizes using consolidated table
      let fisherExactResult = null;
      if (total < 100) {
        fisherExactResult = CaseControlCalculatorUtils.fisherExact(a, b, c, d);
      }

      // Prepare all tables for Mantel-Haenszel calculation
      let allTables = [cellValues, ...strata];
      
      // Calculate Mantel-Haenszel OR and Directly Adjusted OR if there are strata
      let mantelHaenszelResult = null;
      let mantelHaenszelChiSquareResult = null;
      let directlyAdjustedORResult = null;
      if (strata.length > 0) {
        mantelHaenszelResult = CaseControlCalculatorUtils.mantelHaenszelOR(allTables);
        mantelHaenszelChiSquareResult = CaseControlCalculatorUtils.mantelHaenszelChiSquare(allTables);
        directlyAdjustedORResult = CaseControlCalculatorUtils.directlyAdjustedOddsRatio(allTables);
      }

      setResults({
        oddsRatio: oddsRatioResult,
        chiSquareYates: chiSquareYatesResult,
        chiSquareUncorrected: chiSquareUncorrectedResult,
        fisherExact: fisherExactResult,
        ...(mantelHaenszelResult && { mantelHaenszel: mantelHaenszelResult }),
        ...(mantelHaenszelChiSquareResult && { mantelHaenszelChiSquare: mantelHaenszelChiSquareResult }),
        ...(directlyAdjustedORResult && { directlyAdjustedOR: directlyAdjustedORResult }) // Add directly adjusted OR to results
      });
    } catch (error) {
      console.error('Error calculating results:', error);
    }
  };

  // Reset form
  const resetForm = () => {
    setCellValues({ a: 0, b: 0, c: 0, d: 0 });
    setStrata([]);
    setResults({});
  };

  // Format number with specified decimal places
  const formatNumber = (num: number, decimals: number = 3) => {
    return num.toFixed(decimals);
  };

  // Format confidence interval
  const formatCI = (ci: [number, number], decimals: number = 3) => {
    return `${formatNumber(ci[0], decimals)} - ${formatNumber(ci[1], decimals)}`;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>Case-Control Study Calculator</Typography>
      <Typography variant="body1" paragraph>
        Calculate odds ratios and confidence intervals for case-control studies.
      </Typography>
      
      {/* Formulas Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="formulas-content"
          id="formulas-header"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FunctionsIcon color="primary" />
            <Typography variant="h6">Mathematical Formulas</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            {/* Odds Ratio */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Odds Ratio (OR)
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('OR = \\frac{\\text{Odds in cases}}{\\text{Odds in controls}} = \\frac{\\frac{a}{c}}{\\frac{b}{d}} = \\frac{ad}{bc}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Woolf\'s 95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = OR \\times \\exp(\\pm 1.96 \\times SE_{\\ln(OR)})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(OR)} = \\sqrt{\\frac{1}{a} + \\frac{1}{b} + \\frac{1}{c} + \\frac{1}{d}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: a = exposed cases, b = exposed controls, c = unexposed cases, d = unexposed controls
                </Typography>
              </Paper>
            </Grid>

            {/* Mantel-Haenszel OR */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Mantel-Haenszel Adjusted Odds Ratio
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('OR_{MH} = \\frac{\\sum_{i=1}^{k} \\frac{a_i d_i}{n_i}}{\\sum_{i=1}^{k} \\frac{b_i c_i}{n_i}}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>95% Confidence Interval:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('CI = OR_{MH} \\times \\exp(\\pm 1.96 \\times SE_{\\ln(OR_{MH})})')
                  }} />
                </Box>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('SE_{\\ln(OR_{MH})} = \\sqrt{\\frac{\\sum P_i}{2(\\sum R_i)^2} + \\frac{\\sum Q_i}{2(\\sum R_i)(\\sum S_i)} + \\frac{\\sum T_i}{2(\\sum S_i)^2}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Where: k = number of strata, n_i = total in stratum i, and P_i, Q_i, R_i, S_i, T_i are stratum-specific terms
                </Typography>
              </Paper>
            </Grid>

            {/* Directly Adjusted OR */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Directly Adjusted Odds Ratio
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('OR_{adj} = \\frac{\\sum_{i=1}^{k} w_i \\times OR_i}{\\sum_{i=1}^{k} w_i}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Weight for each stratum:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('w_i = \\frac{1}{SE_{\\ln(OR_i)}^2} = \\frac{1}{\\frac{1}{a_i} + \\frac{1}{b_i} + \\frac{1}{c_i} + \\frac{1}{d_i}}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Direct adjustment uses inverse variance weighting to combine stratum-specific odds ratios
                </Typography>
              </Paper>
            </Grid>

            {/* Chi-Square Tests */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Chi-Square Tests
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Uncorrected Chi-Square:</strong>
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2 = \\frac{n(ad - bc)^2}{(a+b)(c+d)(a+c)(b+d)}')
                  }} />
                </Box>
                <Typography variant="body2" gutterBottom>
                  <strong>Yates\' Corrected Chi-Square:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2_{Yates} = \\frac{n(|ad - bc| - \\frac{n}{2})^2}{(a+b)(c+d)(a+c)(b+d)}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Both tests have 1 degree of freedom. Yates\' correction provides more conservative p-values for small samples.
                </Typography>
              </Paper>
            </Grid>

            {/* Test for Homogeneity */}
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom color="primary">
                  Test for Homogeneity of Odds Ratios
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Breslow-Day Test:</strong>
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <div dangerouslySetInnerHTML={{ 
                    __html: renderFormula('\\chi^2_{BD} = \\sum_{i=1}^{k} \\frac{(a_i - E_i)^2}{V_i}')
                  }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  Tests whether the odds ratios are homogeneous across strata. Degrees of freedom = k-1 (number of strata minus 1).
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
      
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>2×2 Contingency Table</Typography>
        <Typography variant="body2" paragraph>
          Enter the values for your 2×2 table to calculate epidemiological measures.
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <TableContainer component={Paper} variant="outlined">
              <Table aria-label="2x2 contingency table">
                <TableHead>
                  <TableRow>
                    <TableCell></TableCell>
                    <TableCell align="center">Cases</TableCell>
                    <TableCell align="center">Controls</TableCell>
                    <TableCell align="center">Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row">Exposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.a || ''}
                        onChange={(e) => handleInputChange('a', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.b || ''}
                        onChange={(e) => handleInputChange('b', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.b}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Unexposed</TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.c || ''}
                        onChange={(e) => handleInputChange('c', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <TextField
                        type="number"
                        value={cellValues.d || ''}
                        onChange={(e) => handleInputChange('d', e.target.value)}
                        inputProps={{ min: 0 }}
                        variant="outlined"
                        size="small"
                        sx={{ width: '80px' }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.c + cellValues.d}</Typography>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell component="th" scope="row">Total</TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.c}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.b + cellValues.d}</Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{cellValues.a + cellValues.b + cellValues.c + cellValues.d}</Typography>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
          <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<CalculateIcon />}
                onClick={calculateResults}
                disabled={Object.values(cellValues).every(val => val === 0) && strata.length === 0}
              >
                Calculate
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={resetForm}
              >
                Reset
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Enter the values in the 2×2 table and click Calculate to compute epidemiological measures.
            </Typography>
          </Grid>
        </Grid>

        {/* Stratified Analysis Section */}
        <Box sx={{ mb: 3, mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>Stratified Analysis (Controlling for potential confounders)</Typography>
          <Typography variant="body2" paragraph>
            Add strata to calculate the Directly adjusted and Mantel-Haenszel adjusted odds ratios. The crude OR will be calculated by combining all tables.
          </Typography>
          
          {/* Stratum Controls */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={addStratum}
            >
              Add Stratum
            </Button>
            
            {strata.length > 0 && (
              <>
                <FormControl sx={{ minWidth: 120 }}>
                  <Select
                    value={currentStratumIndex.toString()}
                    onChange={(e) => setCurrentStratumIndex(Number(e.target.value))}
                    size="small"
                  >
                    {strata.map((_, index) => (
                      <MenuItem key={index} value={index.toString()}>
                        Stratum {index + 1}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={removeStratum}
                  disabled={strata.length <= 0}
                >
                  Delete Stratum
                </Button>
              </>
            )}
          </Box>
          
          {/* Current Stratum Table */}
          {strata.length > 0 && (
            <Box sx={{ mb: 2, p: 2, border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`, borderRadius: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Stratum {currentStratumIndex + 1}</Typography>
              
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell align="center">Cases</TableCell>
                      <TableCell align="center">Controls</TableCell>
                      <TableCell align="center">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Exposed</TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex]?.a || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex]?.id, 'a', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex]?.b || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex]?.id, 'b', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{(strata[currentStratumIndex]?.a || 0) + (strata[currentStratumIndex]?.b || 0)}</Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Unexposed</TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex]?.c || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex]?.id, 'c', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <TextField
                          type="number"
                          value={strata[currentStratumIndex]?.d || ''}
                          onChange={(e) => handleStratumInputChange(strata[currentStratumIndex]?.id, 'd', e.target.value)}
                          inputProps={{ min: 0 }}
                          variant="outlined"
                          size="small"
                          sx={{ width: '70px' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{(strata[currentStratumIndex]?.c || 0) + (strata[currentStratumIndex]?.d || 0)}</Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell component="th" scope="row">Total</TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{(strata[currentStratumIndex]?.a || 0) + (strata[currentStratumIndex]?.c || 0)}</Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{(strata[currentStratumIndex]?.b || 0) + (strata[currentStratumIndex]?.d || 0)}</Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body2">{(strata[currentStratumIndex]?.a || 0) + (strata[currentStratumIndex]?.b || 0) + (strata[currentStratumIndex]?.c || 0) + (strata[currentStratumIndex]?.d || 0)}</Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </Box>

        {/* Results Section */}
        {Object.keys(results).length > 0 && (
          <Box sx={{ mt: 4 }}>
            <Divider sx={{ mb: 3 }} />
            <Typography variant="h6" gutterBottom>Results</Typography>
            
            <Grid container spacing={3}>
              {/* Crude Odds Ratio */}
              {results.oddsRatio && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Crude Odds Ratio"
                    value={formatNumber(results.oddsRatio.or)}
                    description={`95% CI: ${formatCI(results.oddsRatio.ci)}`}
                    color="primary"
                    variant="outlined"
                    tooltip="Crude odds ratio of exposure in cases vs. controls (combined across all tables)"
                  />
                </Grid>
              )}
              
              {/* Directly Adjusted Odds Ratio */}
              {results.directlyAdjustedOR && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Directly Adjusted OR"
                    value={formatNumber(results.directlyAdjustedOR.orDirect)}
                    description={`95% CI: ${formatCI(results.directlyAdjustedOR.ci)}`}
                    color="primary"
                    variant="outlined"
                    tooltip="Directly adjusted odds ratio for stratified data"
                  />
                </Grid>
              )}

              {/* Mantel-Haenszel Adjusted OR */}
              {results.mantelHaenszel && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Mantel-Haenszel OR"
                    value={formatNumber(results.mantelHaenszel.mhOR)}
                    description={`95% CI: ${formatCI(results.mantelHaenszel.ci)}`}
                    color="secondary"
                    variant="outlined"
                    tooltip="Adjusted odds ratio across strata"
                  />
                </Grid>
              )}
              
              {/* Chi-Square Test (Uncorrected) */}
              {results.chiSquareUncorrected && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Chi-Square Test (Uncorrected)"
                    value={formatNumber(results.chiSquareUncorrected.chiSquare)}
                    description={`p-value: ${formatNumber(results.chiSquareUncorrected.pValue)} (df=${results.chiSquareUncorrected.df})`}
                    color="warning"
                    variant="outlined"
                    tooltip="Uncorrected Chi-square test for consolidated 2×2 table"
                  />
                </Grid>
              )}

              {/* Chi-Square Test (Yates) */}
              {results.chiSquareYates && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Chi-Square Test (Yates)"
                    value={formatNumber(results.chiSquareYates.chi2)}
                    description={results.chiSquareYates.p !== null ? 
                      `p-value: ${formatNumber(results.chiSquareYates.p)}` : 
                      'p-value: Not calculated'}
                    color="warning"
                    variant="outlined"
                    tooltip="Chi-square test with Yates' correction for consolidated 2×2 table"
                  />
                </Grid>
              )}
              
              {/* Mantel-Haenszel Chi-Square Test */}
              {results.mantelHaenszelChiSquare && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Mantel-Haenszel Chi-Square"
                    value={formatNumber(results.mantelHaenszelChiSquare.chi2)}
                    description={results.mantelHaenszelChiSquare.p !== null ? 
                      `p-value: ${formatNumber(results.mantelHaenszelChiSquare.p)}` : 
                      'p-value: Not calculated'}
                    color="info"
                    variant="outlined"
                    tooltip="Mantel-Haenszel Chi-Square test for stratified data (df=1)"
                  />
                </Grid>
              )}

              {/* Fisher's Exact Test */}
              {results.fisherExact !== null && results.fisherExact !== undefined && (
                <Grid item xs={12} sm={6} md={4}>
                  <StatsCard
                    title="Fisher's Exact Test"
                    value={`p = ${formatNumber(results.fisherExact)}`}
                    description={`Significance at α = 0.05: ${results.fisherExact < 0.05 ? 'Yes' : 'No'}`}
                    color="success"
                    variant="outlined"
                    tooltip="Fisher's exact test for small sample sizes (consolidated table)"
                  />
                </Grid>
              )}
            </Grid>
            
            {/* Interpretation */}
            <Paper 
              variant="outlined" 
              sx={{ 
                mt: 3, 
                p: 2, 
                backgroundColor: alpha(theme.palette.info.main, 0.05),
                borderColor: alpha(theme.palette.info.main, 0.2)
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Interpretation Guidelines:
              </Typography>
              <Typography variant="body2">
                • <strong>Odds Ratio = 1:</strong> No association between exposure and disease<br />
                • <strong>Odds Ratio &gt; 1:</strong> Positive association (exposure may increase disease odds)<br />
                • <strong>Odds Ratio &lt; 1:</strong> Negative association (exposure may decrease disease odds)<br />
                • <strong>Statistical significance:</strong> If the 95% confidence interval does not include 1, or p-value &lt; 0.05
              </Typography>
            </Paper>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default CaseControlCalculator;
