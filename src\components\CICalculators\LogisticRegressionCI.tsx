import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Clear as ClearIcon,
  Info as InfoIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface InputData {
  coefficient: string;
  standardError: string;
  sampleSize: string;
  confidenceLevel: string;
  analysisType: 'coefficient' | 'oddsRatio';
}

interface Results {
  coefficient: number;
  standardError: number;
  zStatistic: number;
  zCritical: number;
  marginOfError: number;
  lowerBound: number;
  upperBound: number;
  oddsRatio: number;
  orLowerBound: number;
  orUpperBound: number;
  confidenceLevel: number;
  analysisType: string;
  interpretation: string;
}

const LogisticRegressionCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [inputData, setInputData] = useState<InputData>({
    coefficient: '',
    standardError: '',
    sampleSize: '',
    confidenceLevel: '95',
    analysisType: 'coefficient'
  });
  const [results, setResults] = useState<Results | null>(null);
  const [error, setError] = useState<string>('');

  // Get z-critical value
  const getZCritical = (alpha: number): number => {
    const zTable: { [key: string]: number } = {
      '0.10': 1.645,
      '0.05': 1.960,
      '0.01': 2.576,
      '0.005': 2.807,
      '0.001': 3.291
    };
    
    const alphaStr = alpha.toFixed(3);
    return zTable[alphaStr] || 1.96;
  };

  const calculateLogisticRegressionCI = () => {
    try {
      setError('');
      
      const coefficient = parseFloat(inputData.coefficient);
      const standardError = parseFloat(inputData.standardError);
      const sampleSize = parseInt(inputData.sampleSize);
      const confidenceLevel = parseFloat(inputData.confidenceLevel);
      
      // Validation
      if (isNaN(coefficient) || isNaN(standardError) || isNaN(sampleSize) || isNaN(confidenceLevel)) {
        throw new Error('Please enter valid numeric values for all required fields.');
      }
      
      if (sampleSize <= 0) {
        throw new Error('Sample size must be positive.');
      }
      
      if (standardError <= 0) {
        throw new Error('Standard error must be positive.');
      }
      
      if (confidenceLevel <= 0 || confidenceLevel >= 100) {
        throw new Error('Confidence level must be between 0 and 100.');
      }
      
      // Calculate alpha and z-critical
      const alpha = (100 - confidenceLevel) / 100;
      const zCritical = getZCritical(alpha / 2);
      
      // Calculate margin of error
      const marginOfError = zCritical * standardError;
      
      // Calculate confidence interval for coefficient
      const lowerBound = coefficient - marginOfError;
      const upperBound = coefficient + marginOfError;
      
      // Calculate z-statistic
      const zStatistic = coefficient / standardError;
      
      // Calculate odds ratio and its CI
      const oddsRatio = Math.exp(coefficient);
      const orLowerBound = Math.exp(lowerBound);
      const orUpperBound = Math.exp(upperBound);
      
      // Generate interpretation
      let interpretation = '';
      if (inputData.analysisType === 'coefficient') {
        interpretation = `We are ${confidenceLevel}% confident that the true logistic regression coefficient lies between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}. `;
        if (lowerBound > 0) {
          interpretation += 'Since the entire interval is positive, there is evidence of a positive association (increased odds).';
        } else if (upperBound < 0) {
          interpretation += 'Since the entire interval is negative, there is evidence of a negative association (decreased odds).';
        } else {
          interpretation += 'Since the interval contains zero, there is no significant evidence of an association.';
        }
      } else {
        interpretation = `We are ${confidenceLevel}% confident that the true odds ratio lies between ${orLowerBound.toFixed(2)} and ${orUpperBound.toFixed(2)}. `;
        if (orLowerBound > 1) {
          interpretation += 'Since the entire interval is above 1, there is evidence of increased odds.';
        } else if (orUpperBound < 1) {
          interpretation += 'Since the entire interval is below 1, there is evidence of decreased odds.';
        } else {
          interpretation += 'Since the interval contains 1, there is no significant evidence of an association.';
        }
      }
      
      const calculatedResults: Results = {
        coefficient,
        standardError,
        zStatistic,
        zCritical,
        marginOfError,
        lowerBound,
        upperBound,
        oddsRatio,
        orLowerBound,
        orUpperBound,
        confidenceLevel,
        analysisType: inputData.analysisType,
        interpretation
      };
      
      setResults(calculatedResults);
      setActiveTab(1); // Switch to Results tab
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    }
  };

  const clearAll = () => {
    setInputData({
      coefficient: '',
      standardError: '',
      sampleSize: '',
      confidenceLevel: '95',
      analysisType: 'coefficient'
    });
    setResults(null);
    setError('');
    setActiveTab(0);
  };

  const copyToClipboard = () => {
    if (!results) return;
    
    const text = `Logistic Regression Confidence Interval Results:\n` +
      `Analysis Type: ${results.analysisType === 'coefficient' ? 'Regression Coefficient' : 'Odds Ratio'}\n` +
      `Coefficient: ${results.coefficient}\n` +
      `Standard Error: ${results.standardError}\n` +
      `z-statistic: ${results.zStatistic.toFixed(2)}\n` +
      `z-critical: ${results.zCritical.toFixed(2)}\n` +
      `Margin of Error: ${results.marginOfError.toFixed(2)}\n` +
      `${results.confidenceLevel}% CI for Coefficient: [${results.lowerBound.toFixed(2)}, ${results.upperBound.toFixed(2)}]\n` +
      `Odds Ratio: ${results.oddsRatio.toFixed(2)}\n` +
      `${results.confidenceLevel}% CI for Odds Ratio: [${results.orLowerBound.toFixed(2)}, ${results.orUpperBound.toFixed(2)}]\n` +
      `Interpretation: ${results.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        Logistic Regression Confidence Intervals
      </Typography>
      
      <Card>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!results} />
          <Tab label="Guide" />
        </Tabs>
        
        <TabPanel value={activeTab} index={0}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Analysis Type</InputLabel>
                  <Select
                    value={inputData.analysisType}
                    onChange={(e) => setInputData({ ...inputData, analysisType: e.target.value as 'coefficient' | 'oddsRatio' })}
                    label="Analysis Type"
                  >
                    <MenuItem value="coefficient">Regression Coefficient</MenuItem>
                    <MenuItem value="oddsRatio">Odds Ratio</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Regression Coefficient (β)"
                  type="number"
                  value={inputData.coefficient}
                  onChange={(e) => setInputData({ ...inputData, coefficient: e.target.value })}
                  margin="normal"
                  helperText="The estimated logistic regression coefficient"
                />
                
                <TextField
                  fullWidth
                  label="Standard Error"
                  type="number"
                  value={inputData.standardError}
                  onChange={(e) => setInputData({ ...inputData, standardError: e.target.value })}
                  margin="normal"
                  helperText="Standard error of the coefficient"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Sample Size (n)"
                  type="number"
                  value={inputData.sampleSize}
                  onChange={(e) => setInputData({ ...inputData, sampleSize: e.target.value })}
                  margin="normal"
                  helperText="Total number of observations"
                />
                
                <TextField
                  fullWidth
                  label="Confidence Level (%)"
                  type="number"
                  value={inputData.confidenceLevel}
                  onChange={(e) => setInputData({ ...inputData, confidenceLevel: e.target.value })}
                  margin="normal"
                  helperText="Typically 90, 95, or 99"
                />
              </Grid>
            </Grid>
            
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={calculateLogisticRegressionCI}
                startIcon={<CalculateIcon />}
                size="large"
              >
                Calculate CI
              </Button>
              
              <Button
                variant="outlined"
                onClick={clearAll}
                startIcon={<ClearIcon />}
                size="large"
              >
                Clear All
              </Button>
            </Box>
          </CardContent>
        </TabPanel>
        
        <TabPanel value={activeTab} index={1}>
          {results && (
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2">
                  Confidence Interval Results
                </Typography>
                <Tooltip title="Copy results to clipboard">
                  <IconButton onClick={copyToClipboard}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Input Summary
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemText 
                          primary="Analysis Type" 
                          secondary={results.analysisType === 'coefficient' ? 'Regression Coefficient' : 'Odds Ratio'} 
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Coefficient (β)" secondary={results.coefficient} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Standard Error" secondary={results.standardError} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Confidence Level" secondary={`${results.confidenceLevel}%`} />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Statistical Results
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemText primary="z-statistic" secondary={results.zStatistic.toFixed(2)} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="z-critical" secondary={results.zCritical.toFixed(2)} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Margin of Error" secondary={results.marginOfError.toFixed(2)} />
                      </ListItem>
                      <ListItem>
                        <ListItemText primary="Odds Ratio (e^β)" secondary={results.oddsRatio.toFixed(2)} />
                      </ListItem>
                    </List>
                  </Paper>
                </Grid>
              </Grid>
              
              <Paper sx={{ p: 3, mt: 3, bgcolor: 'primary.50' }}>
                <Typography variant="h6" gutterBottom>
                  {results.confidenceLevel}% Confidence Intervals
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Coefficient (β):
                  </Typography>
                  <Typography variant="h5" component="div" sx={{ textAlign: 'center', mb: 2 }}>
                    [{results.lowerBound.toFixed(2)}, {results.upperBound.toFixed(2)}]
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Odds Ratio (e^β):
                  </Typography>
                  <Typography variant="h5" component="div" sx={{ textAlign: 'center', mb: 2 }}>
                    [{results.orLowerBound.toFixed(2)}, {results.orUpperBound.toFixed(2)}]
                  </Typography>
                </Box>
                
                <Divider sx={{ my: 2 }} />
                <Typography variant="body1">
                  <strong>Interpretation:</strong> {results.interpretation}
                </Typography>
              </Paper>
            </CardContent>
          )}
        </TabPanel>
        
        <TabPanel value={activeTab} index={2}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Logistic Regression Confidence Intervals Guide
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                When to Use
              </Typography>
              <Typography paragraph>
                Use logistic regression confidence intervals when you want to:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Estimate the precision of logistic regression coefficients" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Test hypotheses about odds ratios" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Assess the uncertainty in predicted probabilities" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Compare the strength of associations between predictors" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                How to Calculate
              </Typography>
              <Typography paragraph>
                For logistic regression coefficients: CI = β ± z(α/2) × SE(β)
              </Typography>
              <Typography paragraph>
                For odds ratios: OR_CI = [e^(β - z(α/2) × SE(β)), e^(β + z(α/2) × SE(β))]
              </Typography>
              <Typography paragraph>
                Where:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• β = logistic regression coefficient" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• z(α/2) = critical z-value" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• SE(β) = standard error of coefficient" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• OR = e^β (odds ratio)" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Interpretation
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Coefficient CI" 
                    secondary="If the interval doesn't contain 0, the coefficient is statistically significant" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Odds Ratio CI" 
                    secondary="If the interval doesn't contain 1, the odds ratio is statistically significant" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="OR > 1" 
                    secondary="Increased odds of the outcome" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="OR < 1" 
                    secondary="Decreased odds of the outcome" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="OR = 1" 
                    secondary="No association with the outcome" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Assumptions
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Binary or ordinal outcome variable" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Independence of observations" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Linear relationship between logit and predictors" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• No multicollinearity among predictors" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Large sample size (typically n > 50)" />
                </ListItem>
              </List>
            </Box>
          </CardContent>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default LogisticRegressionCI;