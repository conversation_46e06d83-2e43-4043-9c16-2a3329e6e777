// Simplified advanced analysis routes configuration (only verified components)

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load only verified components
const PivotAnalysisViewer = lazy(() => import('../../components/PivotAnalysis/PivotAnalysisViewer'));
const SampleSizeCalculatorsOptions = lazy(() => import('../../components/SampleSizeCalculators/SampleSizeCalculatorsOptions'));
const OneSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/OneSampleCalculator'));
const TwoSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/TwoSampleCalculator'));
const PairedSampleCalculator = lazy(() => import('../../components/SampleSizeCalculators/PairedSampleCalculator'));
const MoreThanTwoGroupsCalculator = lazy(() => import('../../components/SampleSizeCalculators/MoreThanTwoGroupsCalculator'));
const EpiCalcPage = lazy(() => import('../../pages/EpiCalcPage'));
const EpiCalc = lazy(() => import('../../components/EpiCalc/EpiCalc'));
const CaseControlCalculator = lazy(() => import('../../components/EpiCalc/CaseControlCalculator'));
const CohortCalculator = lazy(() => import('../../components/EpiCalc/CohortCalculator'));
const CrossSectionalCalculator = lazy(() => import('../../components/EpiCalc/CrossSectionalCalculator'));
const MatchedCaseControlCalculator = lazy(() => import('../../components/EpiCalc/MatchedCaseControlCalculator'));
const SampleSizePowerCalculator = lazy(() => import('../../components/EpiCalc/SampleSizePowerCalculator'));
const PublicationReadyPage = lazy(() => import('../../pages/PublicationReadyPage'));
const Table2 = lazy(() => import('../../components/PublicationReady/Table2'));
const PostHocTests = lazy(() => import('../../components/PublicationReady/PostHocTests'));
const ResultsManagerPage = lazy(() => import('../../pages/ResultsManagerPage'));
const EffectSizeCalculator = lazy(() => import('../../components/PublicationReady/EffectSizeCalculator'));
const PowerAnalysisCalculator = lazy(() => import('../../components/PublicationReady/PowerAnalysisCalculator'));
const EnhancedStatisticalMethodsGenerator = lazy(() => import('../../components/PublicationReady/EnhancedStatisticalMethodsGenerator'));
const Table3Generator = lazy(() => import('../../components/PublicationReady/Table3Generator'));
const CICalculatorsPage = lazy(() => import('../../components/CICalculators/CICalculatorsPage'));
const SingleMeanCI = lazy(() => import('../../components/CICalculators/SingleMeanCI'));
const SingleProportionCI = lazy(() => import('../../components/CICalculators/SingleProportionCI'));
const DifferenceMeansCI = lazy(() => import('../../components/CICalculators/DifferenceMeansCI'));
const DifferenceProportionsCI = lazy(() => import('../../components/CICalculators/DifferenceProportionsCI'));
const PairedDifferenceCI = lazy(() => import('../../components/CICalculators/PairedDifferenceCI'));
const CorrelationCI = lazy(() => import('../../components/CICalculators/CorrelationCI'));
const VarianceCI = lazy(() => import('../../components/CICalculators/VarianceCI'));
const RatioVariancesCI = lazy(() => import('../../components/CICalculators/RatioVariancesCI'));
const LinearRegressionCI = lazy(() => import('../../components/CICalculators/LinearRegressionCI'));
const LogisticRegressionCI = lazy(() => import('../../components/CICalculators/LogisticRegressionCI'));
const DiagnosticTestCI = lazy(() => import('../../components/CICalculators/DiagnosticTestCI'));
const SurvivalAnalysisCI = lazy(() => import('../../components/CICalculators/SurvivalAnalysisCI'));

export const advancedRoutesSimple: EnhancedRouteConfig[] = [
  // Pivot Analysis
  {
    path: 'pivot',
    component: PivotAnalysisViewer,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to pivot analysis
    metadata: {
      title: 'Pivot Analysis',
      description: 'Create pivot tables and cross-tabulations',
      category: 'analysis',
      icon: 'TableChart',
      order: 6
    },
    children: [
      {
        path: 'pivot/tables',
        component: PivotAnalysisViewer,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to pivot tables
        props: { initialTab: 'tables' },
        metadata: {
          title: 'Pivot Tables',
          description: 'Create and customize pivot tables',
          category: 'analysis'
        }
      }
    ]
  },

  // Sample Size Calculators
  {
    path: 'samplesize',
    component: SampleSizeCalculatorsOptions,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to sample size calculators
    metadata: {
      title: 'Sample Size Calculators',
      description: 'Calculate required sample sizes for studies',
      category: 'tools',
      icon: 'Calculate',
      order: 7
    },
    children: [
      {
        path: 'samplesize/options',
        component: SampleSizeCalculatorsOptions,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to options
        metadata: {
          title: 'Sample Size Options',
          description: 'Choose sample size calculation method',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/one-sample',
        component: OneSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to one sample calculator
        metadata: {
          title: 'One Sample Calculator',
          description: 'Calculate sample size for one sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/two-sample',
        component: TwoSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to two sample calculator
        metadata: {
          title: 'Two Sample Calculator',
          description: 'Calculate sample size for two sample comparisons',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/paired-sample',
        component: PairedSampleCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to paired sample calculator
        metadata: {
          title: 'Paired Sample Calculator',
          description: 'Calculate sample size for paired sample tests',
          category: 'tools'
        }
      },
      {
        path: 'samplesize/more-than-two-groups',
        component: MoreThanTwoGroupsCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to multiple groups calculator
        metadata: {
          title: 'More Than Two Groups Calculator',
          description: 'Calculate sample size for multiple group comparisons',
          category: 'tools'
        }
      }
    ]
  },

  // Epidemiological Calculators
  {
    path: 'epicalc',
    component: EpiCalcPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to epidemiological calculators
    metadata: {
      title: 'Epidemiological Calculators',
      description: 'Calculate epidemiological measures and statistics',
      category: 'tools',
      icon: 'LocalHospital',
      order: 8
    },
    children: [
      {
        path: 'epicalc/case-control',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to case-control studies
        props: { initialTab: 'case_control' },
        metadata: {
          title: 'Case-Control Studies',
          description: 'Calculate measures for case-control studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/cohort',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cohort studies
        props: { initialTab: 'cohort' },
        metadata: {
          title: 'Cohort Studies',
          description: 'Calculate measures for cohort studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/cross-sectional',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to cross-sectional studies
        props: { initialTab: 'cross_sectional' },
        metadata: {
          title: 'Cross-Sectional Studies',
          description: 'Calculate measures for cross-sectional studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/matched-case-control',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to matched case-control
        props: { initialTab: 'matched_case_control' },
        metadata: {
          title: 'Matched Case-Control',
          description: 'Calculate measures for matched case-control studies',
          category: 'tools'
        }
      },
      {
        path: 'epicalc/sample-size-power',
        component: EpiCalc,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to sample size and power
        props: { initialTab: 'sample_size_power' },
        metadata: {
          title: 'Sample Size & Power',
          description: 'Calculate sample size and power for epidemiological studies',
          category: 'tools'
        }
      }
    ]
  },

  // Confidence Interval Calculators
  {
    path: 'ci-calculators',
    component: CICalculatorsPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to confidence interval calculators
    metadata: {
      title: 'Confidence Interval Calculators',
      description: 'Calculate confidence intervals for various statistical measures',
      category: 'tools',
      icon: 'Calculate',
      order: 9
    },
    children: [
      {
        path: 'single-mean',
        component: SingleMeanCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to single mean CI calculator
        metadata: {
          title: 'Single Mean CI',
          description: 'Calculate confidence intervals for a single population mean',
          category: 'tools'
        }
      },
      {
        path: 'single-proportion',
        component: SingleProportionCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to single proportion CI calculator
        metadata: {
          title: 'Single Proportion CI',
          description: 'Calculate confidence intervals for a single population proportion',
          category: 'tools'
        }
      },
      {
        path: 'difference-means',
        component: DifferenceMeansCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to difference of means CI calculator
        metadata: {
          title: 'Difference of Means CI',
          description: 'Calculate confidence intervals for the difference between two population means',
          category: 'tools'
        }
      },
      {
        path: 'difference-proportions',
        component: DifferenceProportionsCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to difference of proportions CI calculator
        metadata: {
          title: 'Difference of Proportions CI',
          description: 'Calculate confidence intervals for the difference between two population proportions',
          category: 'tools'
        }
      },
      {
        path: 'paired-difference',
        component: PairedDifferenceCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to paired difference CI calculator
        metadata: {
          title: 'Paired Difference CI',
          description: 'Calculate confidence intervals for paired differences (before/after, matched pairs)',
          category: 'tools'
        }
      },
      {
        path: 'correlation',
        component: CorrelationCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to correlation CI calculator
        metadata: {
          title: 'Correlation CI',
          description: 'Calculate confidence intervals for correlation coefficients',
          category: 'tools'
        }
      },
      {
        path: 'variance',
        component: VarianceCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to variance CI calculator
        metadata: {
          title: 'Variance CI',
          description: 'Calculate confidence intervals for population variance and standard deviation',
          category: 'tools'
        }
      },
      {
        path: 'ratio-variances',
        component: RatioVariancesCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to ratio of variances CI calculator
        metadata: {
          title: 'Ratio of Variances CI',
          description: 'Calculate confidence intervals for the ratio of two population variances',
          category: 'tools'
        }
      },
      {
        path: 'linear-regression',
        component: LinearRegressionCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to linear regression CI calculator
        metadata: {
          title: 'Linear Regression CI',
          description: 'Calculate confidence intervals for linear regression coefficients and predictions',
          category: 'tools'
        }
      },
      {
        path: 'logistic-regression',
        component: LogisticRegressionCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to logistic regression CI calculator
        metadata: {
          title: 'Logistic Regression CI',
          description: 'Calculate confidence intervals for logistic regression coefficients and odds ratios',
          category: 'tools'
        }
      },
      {
        path: 'diagnostic-test',
        component: DiagnosticTestCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to diagnostic test CI calculator
        metadata: {
          title: 'Diagnostic Test CI',
          description: 'Calculate confidence intervals for diagnostic test performance measures',
          category: 'tools'
        }
      },
      {
        path: 'survival-analysis',
        component: SurvivalAnalysisCI,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to survival analysis CI calculator
        metadata: {
          title: 'Survival Analysis CI',
          description: 'Calculate confidence intervals for survival probabilities and hazard ratios',
          category: 'tools'
        }
      }
    ]
  },

  // Effect Size Calculator
  {
    path: 'effect-size-calculator',
    component: EffectSizeCalculator,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access to effect size calculator
    metadata: {
      title: 'Effect Size Calculator',
      description: 'Calculate and interpret various effect size measures',
      category: 'tools',
      icon: 'Calculate',
      order: 10
    }
  },

  // Publication Ready Tools
  {
    path: 'publication-ready',
    component: PublicationReadyPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true, // Allow public access for publication tools
    metadata: {
      title: 'Publication Ready',
      description: 'Generate publication-ready tables and results',
      category: 'tools',
      icon: 'Article',
      order: 11
    },
    children: [
      {
        path: 'publication-ready/table2',
        component: Table2,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to table2
        metadata: {
          title: 'Table 2 Generator',
          description: 'Generate publication-ready Table 2',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/table3-generator',
        component: Table3Generator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to table3 generator
        metadata: {
          title: 'Table 3 Generator',
          description: 'Generate publication-ready correlation matrices with APA formatting',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/posthoc-tests',
        component: PostHocTests,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to posthoc tests
        metadata: {
          title: 'Post-hoc Tests',
          description: 'Generate publication-ready post-hoc test results',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/results-manager',
        component: ResultsManagerPage,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to results manager
        metadata: {
          title: 'Results Manager',
          description: 'Manage and export analysis results',
          category: 'tools'
        }
      },

      {
        path: 'publication-ready/power-analysis-calculator',
        component: PowerAnalysisCalculator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to power analysis calculator
        metadata: {
          title: 'Power Analysis Calculator',
          description: 'Statistical power analysis and sample size determination',
          category: 'tools'
        }
      },
      {
        path: 'publication-ready/enhanced-statistical-methods',
        component: EnhancedStatisticalMethodsGenerator,
        requiresAuth: false,
        allowGuest: true,
        allowPublic: true, // Allow public access to enhanced statistical methods generator
        metadata: {
          title: 'Enhanced Methods Generator',
          description: 'Advanced statistical methods section generator with custom templates',
          category: 'tools'
        }
      }
    ]
  }
];
