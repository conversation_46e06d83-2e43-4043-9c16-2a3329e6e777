# Composite Score Feature - Product Requirements Document

## 1. Product Overview

The Composite Score feature enables users to create meaningful aggregate scores from multiple categorical variables by defining custom scoring schemes and applying statistical aggregation methods. This feature addresses the common need in survey analysis and research to combine multiple Likert-scale or categorical responses into a single composite measure, allowing researchers and analysts to derive insights from complex multi-item scales.

## 2. Core Features

### 2.1 User Roles

This feature is available to all authenticated users of DataStatPro without role-based restrictions, as it is a core data transformation functionality.

### 2.2 Feature Module

Our composite score feature consists of the following main interface:

1. **Composite Score Dialog**: Variable selection interface, category mapping configuration, aggregation method selection, and result preview.

### 2.3 Page Details

| Page Name      | Module Name            | Feature description                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| -------------- | ---------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Data Transform | Composite Score Dialog | Select multiple categorical variables from dataset. Display all unique category labels across selected variables. Configure numeric mapping for each category label through dropdown or input fields. Choose aggregation method (sum, average, count, standard deviation, minimum, maximum). Specify new variable name with validation. Preview calculation results before applying. Apply transformation and add new composite variable to dataset. |

## 3. Core Process

**User Flow for Creating Composite Score:**

1. User navigates to Data Management > Data Transform
2. User selects "Composite Score" from transformation type dropdown
3. System opens Composite Score Dialog with step-by-step wizard interface
4. User selects multiple categorical variables for composite calculation
5. System automatically detects and displays all distinct category labels
6. User assigns numeric codes to each category label via input fields
7. User selects aggregation method (sum, average, etc.)
8. User enters name for new composite variable
9. System validates inputs and shows preview of calculation
10. User confirms and applies transformation
11. System creates new variable and updates dataset

```mermaid
graph TD
    A[Data Transform Page] --> B[Select Composite Score]
    B --> C[Composite Score Dialog]
    C --> D[Select Variables]
    D --> E[Configure Category Mapping]
    E --> F[Choose Aggregation Method]
    F --> G[Name New Variable]
    G --> H[Preview Results]
    H --> I[Apply Transformation]
    I --> J[Updated Dataset]
```

## 4. User Interface Design

### 4.1 Design Style

* **Primary Colors**: Material-UI default blue (#1976d2) for primary actions, grey (#757575) for secondary elements

* **Button Style**: Material-UI contained buttons with rounded corners (4px border-radius)

* **Font**: Roboto font family with 14px base size for body text, 16px for headings

* **Layout Style**: Card-based dialog layout with stepper navigation, consistent spacing using 8px grid system

* **Icons**: Material-UI icons for actions (add, delete, preview) and status indicators

### 4.2 Page Design Overview

| Page Name      | Module Name            | UI Elements                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| -------------- | ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Data Transform | Composite Score Dialog | **Layout**: Modal dialog (600px width) with stepper header showing progress. **Variable Selection**: Multi-select dropdown with checkboxes, selected variables displayed as chips. **Category Mapping**: Table layout with category labels in left column, numeric input fields in right column. **Aggregation Selection**: Radio button group with options (Sum, Average, Count, Std Dev, Min, Max). **Variable Naming**: Text input with validation feedback. **Action Buttons**: Cancel (outlined), Preview (contained secondary), Apply (contained primary). **Colors**: Primary blue for actions, light grey background (#f5f5f5), white card backgrounds. **Typography**: 16px bold for section headers, 14px regular for body text. |

### 4.3 Responsiveness

The feature is designed desktop-first with mobile-adaptive layout. On smaller screens (<768px), the dialog adjusts to full-width with stacked form elements and touch-optimized input controls with minimum 44px touch targets.

## 5. Technical Requirements

### 5.1 Input Validation

* At least 2 categorical variables must be selected

* All category labels must have numeric mappings assigned

* New variable name must be unique and follow naming conventions

* Aggregation method must be selected

* Numeric mappings must be valid numbers

### 5.2 Data Processing

* Support for missing/null values in categorical data

* Automatic detection of unique category labels across selected variables

* Real-time preview of composite score calculation

* Efficient processing for large datasets (>10,000 rows)

### 5.3 Error Handling

* Clear error messages for validation failures

* Graceful handling of data type mismatches

* Recovery options for incomplete mappings

* Progress indicators for long-running calculations

## 6. Success Metrics

* Feature adoption rate among active users

* Successful completion rate of composite score creation

* User satisfaction with wizard interface

* Reduction in support requests for composite score calculations

