import React, { createContext, useContext, useState, useEffect } from 'react';
import { Dataset, Column, DataType, VariableRole, DataFilter, FilterCondition } from '../types';
import { generateUUID } from '../utils/uuid';
import { AuthContext }  from './AuthContext';
import { supabase } from '../utils/supabaseClient';
import { saveDataset as saveDatasetToSupabase, loadDataset as loadDatasetFromSupabase, listDatasets as listDatasetsFromSupabase, deleteDataset as deleteDatasetFromSupabase, renameDataset as renameDatasetInSupabase } from '../utils/services/datasetService';
import { filterData } from '../utils/dataUtilities';

interface DataContextType {
  // Datasets
  datasets: Dataset[];
  currentDataset: Dataset | null;
  setCurrentDataset: (dataset: Dataset | null) => void;
  addDataset: (dataset: Dataset) => void;
  updateDataset: (dataset: Dataset) => void;
  renameDataset: (datasetId: string, newName: string) => Promise<{ success: boolean; message: string }>;
  removeDataset: (datasetId: string) => void;

  // Column operations
  addColumn: (datasetId: string, column: Omit<Column, 'id'>) => void;
  updateColumn: (datasetId: string, column: Column) => void;
  removeColumn: (datasetId: string, columnId: string) => void;

  // Data operations
  addRow: (datasetId: string, rowData: Record<string, any>) => void;
  updateRow: (datasetId: string, rowIndex: number, rowData: Record<string, any>) => void;
  removeRow: (datasetId: string, rowIndex: number) => void;

  // Data selection
  selectedRows: number[];
  setSelectedRows: (rowIndices: number[]) => void;
  selectedColumns: string[];
  setSelectedColumns: (columnIds: string[]) => void;

  // Data load event
  dataLoadEventInfo: { datasetName: string, timestamp: number } | null;
  clearDataLoadEvent: () => void;

  // Dataset name conflict
  datasetNameConflictInfo: { newDataset: Dataset, existingDatasetId: string } | null;
  resolveConflictByReplacing: (existingDatasetId: string, newDataset: Dataset) => Promise<'added'>; // Returns status
  resolveConflictByRenaming: (newDataset: Dataset, newName: string) => Promise<'added'>; // Returns status
  cancelConflictResolution: () => void;

  // User dataset operations
  saveCurrentDatasetToAccount: () => Promise<{ success: boolean; message: string }>; // Function for UI to trigger save of current dataset
  deleteDatasetFromAccount: (datasetId: string) => Promise<{ success: boolean; message: string }>;

  // Data filtering
  activeFilters: FilterCondition[];
  setActiveFilters: (filters: FilterCondition[]) => void;
  filterLogic: 'AND' | 'OR';
  setFilterLogic: (logic: 'AND' | 'OR') => void;
  clearFilters: () => void;
  applyFilters: (filters: FilterCondition[], logic: 'AND' | 'OR') => void;
  isFilteredDataset: boolean;
  originalDataset: Dataset | null;
  getFilteredRowCount: () => number;
  getTotalRowCount: () => number;
  getDatasetVariables: (datasetId: string) => Column[];
}

const DataContext = createContext<DataContextType | undefined>(undefined);

// Local storage keys
const STORAGE_KEY = 'datastatpro_datasets';
const LEGACY_STORAGE_KEY = 'statistica_datasets';

// Migration function to handle legacy localStorage keys
const migrateDatasetStorage = (): Dataset[] => {
  // First check for new key
  const newData = localStorage.getItem(STORAGE_KEY);
  if (newData) {
    try {
      const parsedData = JSON.parse(newData);
      return parsedData.map((dataset: any) => ({
        ...dataset,
        dateCreated: new Date(dataset.dateCreated),
        dateModified: new Date(dataset.dateModified),
        userId: dataset.userId || null
      }));
    } catch (e) {
      console.error('Error parsing new datasets:', e);
    }
  }

  // Check for legacy key and migrate
  const legacyData = localStorage.getItem(LEGACY_STORAGE_KEY);
  if (legacyData) {
    try {
      const parsedData = JSON.parse(legacyData);
      const migratedData = parsedData.map((dataset: any) => ({
        ...dataset,
        dateCreated: new Date(dataset.dateCreated),
        dateModified: new Date(dataset.dateModified),
        userId: dataset.userId || null
      }));

      // Save to new key and remove legacy key
      localStorage.setItem(STORAGE_KEY, JSON.stringify(migratedData));
      localStorage.removeItem(LEGACY_STORAGE_KEY);
      console.log('📦 Migrated datasets from legacy storage key');

      return migratedData;
    } catch (e) {
      console.error('Error parsing legacy datasets:', e);
      // Clean up corrupted legacy data
      localStorage.removeItem(LEGACY_STORAGE_KEY);
    }
  }

  return [];
};

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get user, guest status, and cloud storage permissions from AuthContext if available
  let user: any = null;
  let isGuest: boolean = false;
  let canAccessCloudStorage: boolean = false;
  try {
    // Try to access AuthContext, but don't throw if not available
    const authContextValue = useContext(AuthContext); // Use imported AuthContext
    // Use 'as any' for a more lenient check if the exact type of authContextValue is uncertain here
    user = (authContextValue as any)?.user || null;
    isGuest = (authContextValue as any)?.isGuest || false;
    canAccessCloudStorage = (authContextValue as any)?.canAccessCloudStorage || false;
  } catch (e) {
    // Auth context might not be available (e.g. if DataProvider is used outside AuthProvider)
    // or if there's an issue accessing it.
    console.log('Auth context not available or error accessing it, using local storage only:', e);
  }

  // Load datasets from local storage or initialize empty array
  // Guest users always start with empty datasets for security
  const [datasets, setDatasets] = useState<Dataset[]>(() => {
    // If user is Guest, always start with empty datasets
    if (isGuest) {
      console.log('🔒 Guest user detected - starting with empty datasets for security');
      return [];
    }

    // Use migration function to handle legacy storage keys
    return migrateDatasetStorage();
  });

  const [currentDataset, _setCurrentDatasetInternal] = useState<Dataset | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [dataLoadEventInfo, setDataLoadEventInfo] = useState<{ datasetName: string, timestamp: number } | null>(null);
  const [datasetNameConflictInfo, setDatasetNameConflictInfo] = useState<{ newDataset: Dataset, existingDatasetId: string } | null>(null);

  // Filtering state
  const [activeFilters, setActiveFilters] = useState<FilterCondition[]>([]);
  const [filterLogic, setFilterLogic] = useState<'AND' | 'OR'>('AND');
  const [originalDataset, setOriginalDataset] = useState<Dataset | null>(null);

  // Auto-clear data load event after 3 seconds
  useEffect(() => {
    let timerId: ReturnType<typeof setTimeout> | null = null;
    if (dataLoadEventInfo) {
      timerId = setTimeout(() => {
        clearDataLoadEvent();
      }, 3000); // 3 seconds
    }
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [dataLoadEventInfo]);

  // Load user datasets from database when user changes
  useEffect(() => {
    const loadUserDatasets = async () => {
      if (user?.id) {
        try {
          // Fetch datasets for this user
          const { data, error } = await listDatasetsFromSupabase();

          if (error) {
            console.error('Error loading user datasets:', error);
            return;
          }

          if (data && data.length > 0) {
            // Load each dataset content
            const userDatasets: Dataset[] = [];

            for (const datasetInfo of data) {
              try {
                const { data: datasetContent, error: loadError } = await loadDatasetFromSupabase(datasetInfo.id);

                if (loadError) {
                  // Handle specific "Dataset file path not found" error more gracefully
                  if (loadError.message === 'Dataset file path not found.') {
                    console.warn(`Skipping dataset ${datasetInfo.id} due to missing file path metadata.`);
                  } else {
                    // Log other loading errors as errors
                    console.error(`Error loading dataset ${datasetInfo.id}:`, loadError);
                  }
                  continue;
                }

                if (datasetContent) {
                  userDatasets.push({
                  id: datasetInfo.id,
                  name: datasetInfo.dataset_name,
                  dateCreated: new Date(),
                  dateModified: new Date(),
                  columns: datasetContent.variableInfo,
                  data: datasetContent.data,
                  userId: user.id
                });
                }
              } catch (e) {
                 console.error(`Unexpected error loading dataset ${datasetInfo.id}:`, e);
                 // Continue to the next dataset even if one fails
                 continue;
              }
            }

            // Merge with existing datasets, avoiding duplicates
            setDatasets(prevDatasets => {
              // Create a map of existing datasets by ID for quick lookup
              const existingDatasetsMap = new Map(prevDatasets.map(dataset => [dataset.id, dataset]));

              // Add or update user datasets from database, prioritizing fetched data
              userDatasets.forEach(userDataset => {
                  existingDatasetsMap.set(userDataset.id, userDataset);
              });

              // Convert the map back to an array
              return Array.from(existingDatasetsMap.values());
            });
          }
        } catch (e) {
          console.error('Error processing user datasets:', e);
        }
      }
    };

    loadUserDatasets();
  }, [user]);

  // Clear all datasets when user switches to Guest mode for security
  useEffect(() => {
    if (isGuest) {
      console.log('🔒 User switched to Guest mode - clearing all datasets for security');
      setDatasets([]);
      _setCurrentDatasetInternal(null);
      setSelectedRows([]);
      setSelectedColumns([]);
      setDataLoadEventInfo(null);
      setDatasetNameConflictInfo(null);
    }
  }, [isGuest]);

  // Save datasets whenever they change (but not for Guest users)
  useEffect(() => {
    // Don't save datasets for Guest users for security
    if (isGuest) {
      return;
    }

    // Filter out cloud datasets before saving to local storage
    const localOnlyDatasets = datasets.filter(dataset => !dataset.userId);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(localOnlyDatasets));

    // Supabase saving logic (This part is handled by saveCurrentDatasetToAccount)
    // The explicit save/delete to Supabase is handled by the functions below.

  }, [datasets, user, isGuest]); // Added 'isGuest' to dependency array

  const clearDataLoadEvent = () => {
    setDataLoadEventInfo(null);
  };

  // Returns 'selected' or 'deselected' or 'unchanged'
  const setCurrentDataset = (dataset: Dataset | null): 'selected' | 'deselected' | 'unchanged' => {
    const oldCurrentDatasetId = currentDataset?.id;
    _setCurrentDatasetInternal(dataset);
    if (dataset && dataset.id !== oldCurrentDatasetId) {
      setDataLoadEventInfo({ datasetName: dataset.name, timestamp: Date.now() });
      return 'selected';
    } else if (dataset === null && oldCurrentDatasetId !== null) {
      return 'deselected';
    }
    // If dataset is the same as before, or null was passed when it was already null
    return 'unchanged';
  };

  const cancelConflictResolution = () => {
    setDatasetNameConflictInfo(null);
  };

  // Returns 'added'
  const resolveConflictByReplacing = async (existingDatasetId: string, newDataset: Dataset): Promise<'added'> => {
     // Remove the old dataset first
     setDatasets(prevDatasets => prevDatasets.filter(d => d.id !== existingDatasetId));
     // Use a slight delay to ensure state update before adding the new one
     await new Promise(resolve => setTimeout(resolve, 0));
     // Add the new dataset (using its own ID)
     const status = await _addDatasetInternal(newDataset, true); // true to force adding and setting current
     setDatasetNameConflictInfo(null);
     return status; // Should be 'added'
  };

  // Returns 'added'
  const resolveConflictByRenaming = async (newDataset: Dataset, newName: string): Promise<'added'> => {
    const datasetToAdd = { ...newDataset, name: newName };
    const status = await _addDatasetInternal(datasetToAdd, true);
    setDatasetNameConflictInfo(null);
    return status; // Should be 'added'
  };

  // Internal function to actually add/update datasets array and set current
  // Returns 'added'
  const _addDatasetInternal = async (datasetToAdd: Dataset, makeCurrent: boolean): Promise<'added'> => {
    setDatasets(prevDatasets => [...prevDatasets.filter(d => d.id !== datasetToAdd.id), datasetToAdd]);
    if (makeCurrent) {
      setCurrentDataset(datasetToAdd); // This will trigger dataLoadEventInfo
    }
    return 'added'; // Return status
  };

  // Add a new dataset (public function)
  // Returns 'added', 'selected', or 'conflict'
  // Ensure the function implementation is marked async
  const addDataset = async (newDatasetObj: Dataset): Promise<'added' | 'selected' | 'conflict'> => {
    const existingDataset = datasets.find(d => d.name === newDatasetObj.name);

    if (existingDataset) {
      // Exclude sample datasets from conflict dialog - they are just set as current
      if (existingDataset.name.includes('Sample Dataset') ||
          existingDataset.name.includes('Health Dataset') ||
          existingDataset.name.includes('Credit Risk Dataset')) {
        const selectionStatus = setCurrentDataset(existingDataset); // This will trigger dataLoadEventInfo
        // If it's already current, explicitly trigger event for "re-selection"
        if (currentDataset?.id === existingDataset.id) {
            setDataLoadEventInfo({ datasetName: existingDataset.name, timestamp: Date.now() });
        }
        return 'selected'; // Return status
      }

      // For non-sample datasets, trigger conflict
      setDatasetNameConflictInfo({ newDataset: newDatasetObj, existingDatasetId: existingDataset.id });
      return 'conflict'; // Return status
    }

    // No conflict, add directly
    return await _addDatasetInternal(newDatasetObj, true); // Return status
  };

  // Check dataset size in bytes
  const getDatasetSizeInBytes = (dataset: Dataset): number => {
    const datasetString = JSON.stringify({
      data: dataset.data,
      variableInfo: dataset.columns
    });
    return new TextEncoder().encode(datasetString).length;
  };

  // Save a dataset to user's account (can be current or another dataset)
  const saveDatasetToAccount = async (datasetToSave: Dataset): Promise<{ success: boolean; message: string }> => {
    if (!user?.id) {
      return { success: false, message: 'User not authenticated. Cannot save dataset to account.' };
    }

    // CRITICAL SECURITY CHECK: Verify user has cloud storage permissions
    if (!canAccessCloudStorage) {
      return {
        success: false,
        message: 'Cloud storage access requires a Pro account. Please upgrade to save datasets to cloud storage.'
      };
    }

    // Ensure the dataset to save actually belongs to the user if it has a userId
    // This check is mainly for safety, the RLS policy should prevent saving others' data.
    if (datasetToSave.userId && datasetToSave.userId !== user.id) {
         return { success: false, message: 'Cannot save a dataset that belongs to another user.' };
    }

    try {
      // Check dataset size
      const datasetSizeBytes = getDatasetSizeInBytes(datasetToSave);
      const maxSizeBytes = 2 * 1024 * 1024; // 2MB

      if (datasetSizeBytes > maxSizeBytes) {
        return {
          success: false,
          message: `Dataset size (${(datasetSizeBytes / (1024 * 1024)).toFixed(2)}MB) exceeds the 2MB limit. Please reduce the size of your dataset before saving.`
        };
      }

      // Prepare dataset content in the format expected by datasetService
      const datasetContent = {
        data: datasetToSave.data,
        variableInfo: datasetToSave.columns
      };

      // Save dataset using datasetService
      // If datasetToSave has a userId, it means it's an existing cloud dataset,
      // so saveDatasetToSupabase will update it.
      // If datasetToSave does NOT have a userId, it means it's a local dataset
      // being moved to the cloud for the first time.
      const { data, error } = await saveDatasetToSupabase(datasetToSave.name, datasetContent);

      if (error) {
        console.error('Error saving dataset:', error);
        return {
          success: false,
          message: error.message || 'Failed to save dataset to your account.'
        };
      }

      // If successfully saved to Supabase:
      if (data) {
         // If the dataset *was* local (didn't have a userId), remove it from local state.
         // If the dataset *was* already cloud (had a userId), it's already updated in Supabase,
         // and we don't remove it from local state because it's needed there for display/editing.
         if (!datasetToSave.userId) {
             removeDataset(datasetToSave.id); // Remove from local state only if it was local
             return {
               success: true,
               message: 'Dataset successfully moved to your cloud account.' // Message for moving
             };
         } else {
             // Dataset was already in cloud, just updated
             // No need to remove from local state
             return {
               success: true,
               message: 'Dataset successfully updated in your cloud account.' // Message for updating
             };
         }
      } else {
         // Should not happen if there's no error, but handle defensively
         return {
           success: false,
           message: 'Failed to save dataset to your account (no data returned).'
         };
      }

    } catch (e: any) {
      console.error('Error in saveDatasetToAccount:', e);
      return {
        success: false,
        message: e.message || 'An unexpected error occurred while saving the dataset.'
      };
    }
  };

  // Expose saveCurrentDatasetToAccount for the UI to trigger the initial move
  const saveCurrentDatasetToAccountForUI = async (): Promise<{ success: boolean; message: string }> => {
      if (!currentDataset) {
          return { success: false, message: 'No dataset is currently selected.' };
      }
      return saveDatasetToAccount(currentDataset);
  };

  // Delete a dataset from user's account
  const deleteDatasetFromAccount = async (datasetId: string): Promise<{ success: boolean; message: string }> => {
    if (!user?.id) {
      return { success: false, message: 'You must be logged in to delete datasets from your account.' };
    }

    try {
      const { error } = await deleteDatasetFromSupabase(datasetId);

      if (error) {
        console.error('Error deleting dataset:', error);
        return {
          success: false,
          message: error.message || 'Failed to delete dataset from your account.'
        };
      }

      // Remove the dataset from local state
      removeDataset(datasetId);

      return {
        success: true,
        message: 'Dataset deleted successfully from your account.'
      };
    } catch (e: any) {
      console.error('Error in deleteDatasetFromAccount:', e);
      return {
        success: false,
        message: e.message || 'An unexpected error occurred while deleting the dataset.'
      };
    }
  };

  // Update an existing dataset
  const updateDataset = (updatedDataset: Dataset) => {
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset =>
        dataset.id === updatedDataset.id
          ? { ...updatedDataset, dateModified: new Date() }
          : dataset
      )
    );

    // If the updated dataset is the current one and is a cloud dataset, save to account
    if (currentDataset?.id === updatedDataset.id && updatedDataset.userId && canAccessCloudStorage) {
      // Call save function asynchronously (with security check)
      saveDatasetToAccount(updatedDataset).then(result => {
        if (!result.success) {
          console.error('Failed to auto-save cloud dataset changes:', result.message);
          // Optionally show a notification to the user
        }
      });
    }

    if (currentDataset?.id === updatedDataset.id) {
      // Use the internal setter to avoid triggering load event on simple update
      _setCurrentDatasetInternal({ ...updatedDataset, dateModified: new Date() });
    }
  };

  // Rename a dataset
  const renameDataset = async (datasetId: string, newName: string): Promise<{ success: boolean; message: string }> => {
    if (!newName.trim()) {
      return { success: false, message: 'Dataset name cannot be empty.' };
    }

    const dataset = datasets.find(d => d.id === datasetId);
    if (!dataset) {
      return { success: false, message: 'Dataset not found.' };
    }

    // Check if a dataset with the new name already exists
    const existingDataset = datasets.find(d => d.name.toLowerCase() === newName.trim().toLowerCase() && d.id !== datasetId);
    if (existingDataset) {
      return { success: false, message: 'A dataset with this name already exists.' };
    }

    try {
      // If it's a cloud dataset, update in Supabase first
      if (dataset.userId) {
        const result = await renameDatasetInSupabase(datasetId, newName.trim());

        if (result.error) {
          return { success: false, message: result.error.message || 'Failed to rename dataset in cloud.' };
        }
      }

      // Update the dataset locally
      const updatedDataset = { ...dataset, name: newName.trim(), dateModified: new Date() };

      setDatasets(prevDatasets =>
        prevDatasets.map(d =>
          d.id === datasetId ? updatedDataset : d
        )
      );

      // Update current dataset if it's the one being renamed
      if (currentDataset?.id === datasetId) {
        _setCurrentDatasetInternal(updatedDataset);
      }

      return { success: true, message: `Dataset renamed to "${newName.trim()}" successfully.` };
    } catch (error: any) {
      console.error('Error renaming dataset:', error);
      return { success: false, message: error.message || 'Failed to rename dataset.' };
    }
  };

  // Remove a dataset
  const removeDataset = (datasetId: string) => {
    setDatasets(prevDatasets =>
      prevDatasets.filter(dataset => dataset.id !== datasetId)
    );

    if (currentDataset?.id === datasetId) {
      setCurrentDataset(null);
    }
  };

  // Add a column to a dataset
  const addColumn = (datasetId: string, column: Omit<Column, 'id'>) => {
    const newColumn: Column = {
      ...column,
      id: generateUUID() // Corrected to use imported function name
    };

    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId) {
          // Add column to columns array
          const updatedColumns = [...dataset.columns, newColumn];

          // Add empty values for this column to all rows
          const updatedData = dataset.data.map(row => ({
            ...row,
            [newColumn.name]: null
          }));

          const updatedDataset = {
            ...dataset,
            columns: updatedColumns,
            data: updatedData,
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (addColumn):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev) return null;

        // Add column to columns array
        const updatedColumns = [...prev.columns, newColumn];

        // Add empty values for this column to all rows
        const updatedData = prev.data.map(row => ({
          ...row,
          [newColumn.name]: null
        }));

        const updatedDataset = {
          ...prev,
          columns: updatedColumns,
          data: updatedData,
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (addColumn - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Update a column in a dataset
  const updateColumn = (datasetId: string, updatedColumn: Column) => {
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId) {
          // Find the column to update
          const columnIndex = dataset.columns.findIndex(col => col.id === updatedColumn.id);

          if (columnIndex === -1) return dataset;

          const oldColumn = dataset.columns[columnIndex];

          // Create updated columns array
          const updatedColumns = [...dataset.columns];
          updatedColumns[columnIndex] = updatedColumn;

          // If column name has changed, update data references
          let updatedData = dataset.data;
          if (oldColumn.name !== updatedColumn.name) {
            updatedData = dataset.data.map(row => {
              const newRow = { ...row };
              if (oldColumn.name in newRow) {
                newRow[updatedColumn.name] = newRow[oldColumn.name];
                delete newRow[oldColumn.name];
              }
              return newRow;
            });
          }

          const updatedDataset = {
            ...dataset,
            columns: updatedColumns,
            data: updatedData,
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (updateColumn):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev) return null;

        // Find the column to update
        const columnIndex = prev.columns.findIndex(col => col.id === updatedColumn.id);

        if (columnIndex === -1) return prev;

        const oldColumn = prev.columns[columnIndex];

        // Create updated columns array
        const updatedColumns = [...prev.columns];
        updatedColumns[columnIndex] = updatedColumn;

        // If column name has changed, update data references
        let updatedData = prev.data;
        if (oldColumn.name !== updatedColumn.name) {
          updatedData = prev.data.map(row => {
            const newRow = { ...row };
            if (oldColumn.name in newRow) {
              newRow[updatedColumn.name] = newRow[oldColumn.name];
              delete newRow[oldColumn.name];
            }
            return newRow;
          });
        }

        const updatedDataset = {
          ...prev,
          columns: updatedColumns,
          data: updatedData,
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (updateColumn - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Remove a column from a dataset
  const removeColumn = (datasetId: string, columnId: string) => {
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId) {
          // Find the column to remove
          const columnToRemove = dataset.columns.find(col => col.id === columnId);

          if (!columnToRemove) return dataset;

          // Remove column from columns array
          const updatedColumns = dataset.columns.filter(col => col.id !== columnId);

          // Remove column data from all rows
          const updatedData = dataset.data.map(row => {
            const newRow = { ...row };
            if (columnToRemove.name in newRow) {
              delete newRow[columnToRemove.name];
            }
            return newRow;
          });

          const updatedDataset = {
            ...dataset,
            columns: updatedColumns,
            data: updatedData,
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (removeColumn):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev) return null;

        // Find the column to remove
        const columnToRemove = prev.columns.find(col => col.id === columnId);

        if (!columnToRemove) return prev;

        // Remove column from columns array
        const updatedColumns = prev.columns.filter(col => col.id !== columnId);

        // Remove column data from all rows
        const updatedData = prev.data.map(row => {
          const newRow = { ...row };
          if (columnToRemove.name in newRow) {
            delete newRow[columnToRemove.name];
          }
          return newRow;
        });

        const updatedDataset = {
          ...prev,
          columns: updatedColumns,
          data: updatedData,
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (removeColumn - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Add a row to a dataset
  const addRow = (datasetId: string, rowData: Record<string, any>) => {
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId) {
          // Create a new row with data for all columns
          const newRow: Record<string, any> = {};

          // Initialize with nulls for all columns
          dataset.columns.forEach(column => {
            newRow[column.name] = null;
          });

          // Add provided data
          Object.keys(rowData).forEach(key => {
            if (dataset.columns.some(col => col.name === key)) {
              newRow[key] = rowData[key];
            }
          });

          const updatedDataset = {
            ...dataset,
            data: [...dataset.data, newRow],
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (addRow):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev) return null;

        // Create a new row with data for all columns
        const newRow: Record<string, any> = {};

        // Initialize with nulls for all columns
        prev.columns.forEach(column => {
          newRow[column.name] = null;
        });

        // Add provided data
        Object.keys(rowData).forEach(key => {
          if (prev.columns.some(col => col.name === key)) {
            newRow[key] = rowData[key];
          }
        });

        const updatedDataset = {
          ...prev,
          data: [...prev.data, newRow],
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (addRow - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Update a row in a dataset
  const updateRow = (datasetId: string, rowIndex: number, rowData: Record<string, any>) => {
    console.log('updateRow called:', { datasetId, rowIndex, rowData }); // Log input
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId && rowIndex >= 0 && rowIndex < dataset.data.length) {
          const updatedData = [...dataset.data];

          // Only update fields that exist in the dataset
          const validData: Record<string, any> = {};
          Object.keys(rowData).forEach(key => {
            if (dataset.columns.some(col => col.name === key)) {
              validData[key] = rowData[key];
            }
          });

          updatedData[rowIndex] = {
            ...updatedData[rowIndex],
            ...validData
          };

          console.log('updateRow - updatedData before setDatasets:', updatedData); // Log updated data

          const updatedDataset = {
            ...dataset,
            data: updatedData,
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (updateRow):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev || rowIndex < 0 || rowIndex >= prev.data.length) return prev;

        const updatedData = [...prev.data];

        // Only update fields that exist in the dataset
        const validData: Record<string, any> = {};
        Object.keys(rowData).forEach(key => {
          if (prev.columns.some(col => col.name === key)) {
            validData[key] = rowData[key];
          }
        });

        updatedData[rowIndex] = {
          ...updatedData[rowIndex],
          ...validData
        };

        console.log('updateRow - updatedData before _setCurrentDatasetInternal:', updatedData); // Log updated data

        const updatedDataset = {
          ...prev,
          data: updatedData,
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (updateRow - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Remove a row from a dataset
  const removeRow = (datasetId: string, rowIndex: number) => {
    setDatasets(prevDatasets =>
      prevDatasets.map(dataset => {
        if (dataset.id === datasetId && rowIndex >= 0 && rowIndex < dataset.data.length) {
          const updatedData = [...dataset.data];
          updatedData.splice(rowIndex, 1);

          const updatedDataset = {
            ...dataset,
            data: updatedData,
            dateModified: new Date()
          };

          // If this is a cloud dataset, save to account (with security check)
          if (updatedDataset.userId && canAccessCloudStorage) {
             saveDatasetToAccount(updatedDataset).then(result => {
                if (!result.success) {
                   console.error('Failed to auto-save cloud dataset changes (removeRow):', result.message);
                }
             });
          }

          return updatedDataset;
        }
        return dataset;
      })
    );

    if (currentDataset?.id === datasetId) {
      _setCurrentDatasetInternal(prev => { // Use internal setter
        if (!prev || rowIndex < 0 || rowIndex >= prev.data.length) return prev;

        const updatedData = [...prev.data];
        updatedData.splice(rowIndex, 1);

        const updatedDataset = {
          ...prev,
          data: updatedData,
          dateModified: new Date()
        };

        // If this is a cloud dataset, save to account (with security check)
        if (updatedDataset.userId && canAccessCloudStorage) {
           saveDatasetToAccount(updatedDataset).then(result => {
              if (!result.success) {
                 console.error('Failed to auto-save cloud dataset changes (removeRow - current):', result.message);
              }
           });
        }

        return updatedDataset;
      });
    }
  };

  // Helper to check if current dataset is a filtered dataset
  const isFilteredDataset = currentDataset?.name.includes('(Filtered)') || false;

  // Filtering functions
  const applyFilters = (filters: FilterCondition[], logic: 'AND' | 'OR') => {
    if (!currentDataset) return;

    // Store the original dataset if this is the first time applying filters
    if (!originalDataset && !isFilteredDataset) {
      setOriginalDataset(currentDataset);
    }

    // Use the original dataset as the source for filtering
    const sourceDataset = originalDataset || currentDataset;

    if (filters.length === 0) {
      // No filters - switch back to original dataset
      clearFilters();
      return;
    }

    // Apply filters to create filtered data
    const filteredRows = filterData(sourceDataset.data, filters, logic);

    // Create a new temporary filtered dataset
    const filteredDataset: Dataset = {
      ...sourceDataset,
      id: `${sourceDataset.id}_filtered_${Date.now()}`,
      name: `${sourceDataset.name} (Filtered - ${filteredRows.length} rows)`,
      data: filteredRows,
      dateModified: new Date()
    };

    // Remove any existing filtered datasets from the same source
    const updatedDatasets = datasets.filter(ds =>
      !ds.id.startsWith(`${sourceDataset.id}_filtered_`)
    );

    // Add the new filtered dataset to the datasets array
    const newDatasets = [...updatedDatasets, filteredDataset];
    setDatasets(newDatasets);

    // Update state
    setActiveFilters(filters);
    setFilterLogic(logic);

    // Set the filtered dataset as current
    _setCurrentDatasetInternal(filteredDataset);
  };

  const clearFilters = () => {
    setActiveFilters([]);

    // Switch back to original dataset if we have one
    if (originalDataset) {
      // Remove any filtered datasets from the datasets array
      const updatedDatasets = datasets.filter(ds =>
        !ds.id.startsWith(`${originalDataset.id}_filtered_`)
      );
      setDatasets(updatedDatasets);

      _setCurrentDatasetInternal(originalDataset);
      setOriginalDataset(null);
    }
  };

  const getFilteredRowCount = () => {
    return currentDataset?.data.length || 0;
  };

  const getTotalRowCount = () => {
    return originalDataset?.data.length || currentDataset?.data.length || 0;
  };

  const getDatasetVariables = (datasetId: string): Column[] => {
    const dataset = datasets.find(d => d.id === datasetId);
    return dataset ? dataset.columns : [];
  };

  const contextValue: DataContextType = {
    datasets,
    currentDataset,
    setCurrentDataset,
    addDataset,
    updateDataset,
    renameDataset,
    removeDataset,
    addColumn,
    updateColumn,
    removeColumn,
    addRow,
    updateRow,
    removeRow,
    selectedRows,
    setSelectedRows,
    selectedColumns,
    setSelectedColumns,
    dataLoadEventInfo,
    clearDataLoadEvent,
    datasetNameConflictInfo,
    resolveConflictByReplacing,
    resolveConflictByRenaming,
    cancelConflictResolution,
    saveCurrentDatasetToAccount: saveCurrentDatasetToAccountForUI, // Expose the UI-facing function
    deleteDatasetFromAccount,
    // Filtering
    activeFilters,
    setActiveFilters,
    filterLogic,
    setFilterLogic,
    clearFilters,
    applyFilters,
    isFilteredDataset,
    originalDataset,
    getFilteredRowCount,
    getTotalRowCount,
    getDatasetVariables
  };

  return (
    <DataContext.Provider value={contextValue}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the data context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
