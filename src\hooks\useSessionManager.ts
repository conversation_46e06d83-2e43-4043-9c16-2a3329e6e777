import { useState, useEffect, useCallback } from 'react';
import { sessionManager } from '../utils/supabaseClient';
import { Session } from '@supabase/supabase-js';

export interface SessionStatus {
  isValid: boolean;
  session: Session | null;
  error?: string;
}

export interface HeartbeatStatus {
  isMonitoring: boolean;
  lastHeartbeat: number;
  timeSinceLastHeartbeat: number;
}

export interface SessionManagerHookReturn {
  // Session validation
  sessionStatus: SessionStatus;
  isValidating: boolean;
  validateSession: () => Promise<SessionStatus>;
  
  // Heartbeat monitoring
  heartbeatStatus: HeartbeatStatus;
  startHeartbeat: () => void;
  stopHeartbeat: () => void;
  
  // Session management
  forceRefresh: () => Promise<{ success: boolean; error?: string }>;
  
  // Session health
  isSessionHealthy: boolean;
  sessionAge: number; // Time since last heartbeat in milliseconds
}

/**
 * Custom React hook for session management utilities
 * Provides session validation, heartbeat monitoring, and session health information
 */
export const useSessionManager = (): SessionManagerHookReturn => {
  const [sessionStatus, setSessionStatus] = useState<SessionStatus>({
    isValid: false,
    session: null
  });
  const [isValidating, setIsValidating] = useState(false);
  const [heartbeatStatus, setHeartbeatStatus] = useState<HeartbeatStatus>({
    isMonitoring: false,
    lastHeartbeat: Date.now(),
    timeSinceLastHeartbeat: 0
  });

  // Update heartbeat status periodically
  useEffect(() => {
    const updateHeartbeatStatus = () => {
      const status = sessionManager.getMonitoringStatus();
      setHeartbeatStatus(status);
    };

    // Update immediately
    updateHeartbeatStatus();

    // Update every 5 seconds
    const interval = setInterval(updateHeartbeatStatus, 5000);

    return () => clearInterval(interval);
  }, []);

  // Set up session validation callback
  useEffect(() => {
    const unsubscribe = sessionManager.onSessionValidation((isValid) => {
      setSessionStatus(prev => ({
        ...prev,
        isValid
      }));
    });

    return unsubscribe;
  }, []);

  // Validate session function
  const validateSession = useCallback(async (): Promise<SessionStatus> => {
    setIsValidating(true);
    
    try {
      const result = await sessionManager.validateSession();
      const status: SessionStatus = {
        isValid: result.isValid,
        session: result.session,
        error: result.error
      };
      
      setSessionStatus(status);
      return status;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const status: SessionStatus = {
        isValid: false,
        session: null,
        error: errorMessage
      };
      
      setSessionStatus(status);
      return status;
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Start heartbeat monitoring
  const startHeartbeat = useCallback(() => {
    sessionManager.startHeartbeatMonitoring();
  }, []);

  // Stop heartbeat monitoring
  const stopHeartbeat = useCallback(() => {
    sessionManager.stopHeartbeatMonitoring();
  }, []);

  // Force session refresh
  const forceRefresh = useCallback(async () => {
    return await sessionManager.forceSessionRefresh();
  }, []);

  // Calculate session health
  const isSessionHealthy = sessionStatus.isValid && heartbeatStatus.timeSinceLastHeartbeat < 60000; // Healthy if valid and heartbeat within 1 minute
  const sessionAge = heartbeatStatus.timeSinceLastHeartbeat;

  return {
    sessionStatus,
    isValidating,
    validateSession,
    heartbeatStatus,
    startHeartbeat,
    stopHeartbeat,
    forceRefresh,
    isSessionHealthy,
    sessionAge
  };
};

export default useSessionManager;