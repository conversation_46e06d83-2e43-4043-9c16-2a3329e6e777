import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Chip,
  OutlinedInput,
  Divider,
  FormHelperText,
  Snackbar,
} from '@mui/material';

import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { Column, DataType } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import { multipleLinearRegression, multipleLogisticRegression } from '../../utils/stats/regression';
import { coxRegressionService, CoxRegressionData } from '../../utils/services/coxRegressionService';
import RegressionInterpretation from './RegressionInterpretation';
import { getOrderedCategoriesByColumnId } from '../../utils/dataUtilities';

interface CategoricalBaseCategories {
  [variableName: string]: string;
}

const RegressionTable: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();


  // State for selected dataset
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [regressionType, setRegressionType] = useState('');
  const [dependentVariable, setDependentVariable] = useState('');
  const [independentVariables, setIndependentVariables] = useState<string[]>([]);
  const [baseCategories, setBaseCategories] = useState<CategoricalBaseCategories>({});
  const [logisticPositiveCategory, setLogisticPositiveCategory] = useState<string>('');
  const [timeVariable, setTimeVariable] = useState<string>(''); // For Cox regression
  const [coxEventCategory, setCoxEventCategory] = useState<string>(''); // For Cox regression event category
  const [regressionResults, setRegressionResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setDependentVariable('');
    setIndependentVariables([]);
    setBaseCategories({});
    setLogisticPositiveCategory('');
    setTimeVariable('');
    setCoxEventCategory('');
    setRegressionResults(null);
    setError(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  const handleRegressionTypeChange = (event: SelectChangeEvent<string>) => {
    setRegressionType(event.target.value);
    setDependentVariable('');
    setIndependentVariables([]);
    setBaseCategories({});
    setLogisticPositiveCategory('');
    setTimeVariable('');
    setCoxEventCategory('');
    setRegressionResults(null);
    setError(null);
  };

  const handleDependentVariableChange = (event: SelectChangeEvent<string>) => {
    const newDependentVariable = event.target.value;
    setDependentVariable(newDependentVariable);

    // If logistic regression and categorical variable, set default positive category
    if (regressionType === 'logistic' && selectedDataset) {
      const column = selectedDataset.columns.find(col => col.name === newDependentVariable);
      if (column && column.type === DataType.CATEGORICAL) {
        const categories = getUniqueCategories(newDependentVariable);
        if (categories.length === 2) {
          setLogisticPositiveCategory(categories[1]); // Default to second category
        }
      } else if (column && column.type === DataType.NUMERIC) {
        // For numeric variables, clear the positive category since it's not needed
        setLogisticPositiveCategory('');
      }
    }
    
    // If Cox regression and categorical variable, set default event category
    if (regressionType === 'cox' && selectedDataset) {
      const column = selectedDataset.columns.find(col => col.name === newDependentVariable);
      if (column && column.type === DataType.CATEGORICAL) {
        const categories = getUniqueCategories(newDependentVariable);
        if (categories.length === 2) {
          setCoxEventCategory(categories[1]); // Default to second category
        }
      } else if (column && column.type === DataType.NUMERIC) {
        // For numeric variables, we'll check if it's binary (0/1) during analysis
        setCoxEventCategory('');
      }
    }
  };

  const handleIndependentVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    const newVariables = typeof value === 'string' ? value.split(',') : value;
    setIndependentVariables(newVariables);
    
    // Initialize base categories for newly added categorical variables
    if (selectedDataset) {
      const newBaseCategories = { ...baseCategories };
      newVariables.forEach(varName => {
        const column = selectedDataset.columns.find(col => col.name === varName);
        if (column && column.type === DataType.CATEGORICAL && !newBaseCategories[varName]) {
          const categories = getUniqueCategories(varName);
          if (categories.length > 0) {
            newBaseCategories[varName] = categories[0]; // Set first category as default base
          }
        }
      });
      
      // Remove base categories for deselected variables
      Object.keys(newBaseCategories).forEach(varName => {
        if (!newVariables.includes(varName)) {
          delete newBaseCategories[varName];
        }
      });
      
      setBaseCategories(newBaseCategories);
    }
  };

  const handleBaseCategoryChange = (variableName: string, baseCategory: string) => {
    setBaseCategories(prev => ({
      ...prev,
      [variableName]: baseCategory
    }));
  };

  // Helper to get unique categories for a column name
  const getUniqueCategories = (columnName: string): string[] => {
    if (!selectedDataset) return [];
    const column = selectedDataset.columns.find(col => col.name === columnName);
    if (!column || column.type !== DataType.CATEGORICAL) return [];

    // Use ordered categories instead of alphabetical sorting
    return getOrderedCategoriesByColumnId(column.id, selectedDataset);
  };

  // Helper to check if a numeric column contains only 0s and 1s
  const isBinaryNumeric = (columnName: string): boolean => {
    if (!selectedDataset) return false;
    const column = selectedDataset.columns.find(col => col.name === columnName);
    if (!column || column.type !== DataType.NUMERIC) return false;

    const uniqueValues = new Set<number>();
    selectedDataset.data.forEach(row => {
      const value = row[column.name];
      if (typeof value === 'number') {
        uniqueValues.add(value);
      }
    });

    // Check if values are only 0 and 1
    const valuesArray = Array.from(uniqueValues).sort();
    return valuesArray.length <= 2 && valuesArray.every(v => v === 0 || v === 1);
  };

  // Helper to check if a column is binary (numeric 0/1 or categorical with exactly 2 values)
  const isBinaryColumn = (columnName: string): boolean => {
    if (!selectedDataset) return false;
    const column = selectedDataset.columns.find(col => col.name === columnName);
    if (!column) return false;

    if (column.type === DataType.NUMERIC) {
      return isBinaryNumeric(columnName);
    } else if (column.type === DataType.CATEGORICAL) {
      const categories = getUniqueCategories(columnName);
      return categories.length === 2;
    }
    return false;
  };

  // Helper to get valid dependent variables for logistic regression (binary numeric or categorical)
  const getValidLogisticDependentVariables = (): string[] => {
    if (!selectedDataset) return [];
    return selectedDataset.columns
      .filter(col => isBinaryColumn(col.name))
      .map(col => col.name);
  };

  // Helper to create dummy variables for categorical variables
  const createDummyVariables = (data: any[], column: Column, baseCategory: string) => {
    const categories = getUniqueCategories(column.name);
    const dummyColumns: { [key: string]: number[] } = {};
    
    // Create dummy variables for all categories except the base
    categories.filter(cat => cat !== baseCategory).forEach(category => {
      const dummyName = `${column.name}_${category}`;
      dummyColumns[dummyName] = data.map(row => row[column.name] === category ? 1 : 0);
    });
    
    return dummyColumns;
  };

  const runAnalysis = async () => {
    if (!selectedDataset || independentVariables.length === 0 || !dependentVariable) {
      setError('Please select a dataset, at least one independent variable, and a dependent variable.');
      return;
    }

    setLoading(true);
    setError(null);
    setRegressionResults(null);

    const dependentCol = selectedDataset.columns.find(col => col.name === dependentVariable);
    const independentCols = selectedDataset.columns.filter(col => independentVariables.includes(col.name));

    if (!dependentCol || independentCols.length !== independentVariables.length) {
      setError('Selected variables not found in the dataset.');
      setLoading(false);
      return;
    }

    const data = selectedDataset.data;

    try {
      let results;
      if (regressionType === 'linear') {
        if (dependentCol.type !== DataType.NUMERIC) {
          setError('Linear regression requires a numeric dependent variable.');
          setLoading(false);
          return;
        }

        // Prepare X matrix with dummy variables for categorical predictors
        const xData: number[][] = [];
        const variableNames: string[] = [];
        
        independentCols.forEach(col => {
          if (col.type === DataType.NUMERIC) {
            xData.push(data.map(row => row[col.name] as number));
            variableNames.push(col.name);
          } else if (col.type === DataType.CATEGORICAL) {
            const baseCategory = baseCategories[col.name];
            if (!baseCategory) {
              setError(`No base category selected for ${col.name}`);
              setLoading(false);
              return;
            }
            const dummyVars = createDummyVariables(data, col, baseCategory);
            Object.entries(dummyVars).forEach(([dummyName, dummyData]) => {
              xData.push(dummyData);
              variableNames.push(dummyName);
            });
          }
        });

        const y = data.map(row => row[dependentCol.name] as number);
        const rawResults = multipleLinearRegression(xData[0].map((_, i) => xData.map(row => row[i])), y);

        // Transform results to match component expectations
        const coefficientsObj: Record<string, number> = {};
        const stdErrorsObj: Record<string, number> = {};
        const pValuesObj: Record<string, number> = {};
        const confidenceIntervalsObj: Record<string, [number, number]> = {};

        variableNames.forEach((varName, index) => {
          coefficientsObj[varName] = rawResults.coefficients[index];
          stdErrorsObj[varName] = rawResults.stdErrors[index];
          pValuesObj[varName] = rawResults.pValues[index];
          // Calculate 95% CI for coefficients (linear regression always uses coefficient CIs)
          const coef = rawResults.coefficients[index];
          const se = rawResults.stdErrors[index];
          confidenceIntervalsObj[varName] = [coef - 1.96 * se, coef + 1.96 * se];
        });

        // Calculate beta (standardized coefficients) - this is a simplified calculation
        const beta = [0, ...rawResults.coefficients]; // Including intercept beta as 0

        // Calculate degrees of freedom and F-statistic
        const dfModel = variableNames.length;
        const dfError = rawResults.n - dfModel - 1;
        const fStatistic = (rawResults.rSquared / dfModel) / ((1 - rawResults.rSquared) / dfError);

        results = {
          coefficients: coefficientsObj,
          intercept: rawResults.intercept,
          interceptStdError: rawResults.interceptStdError,
          interceptPValue: rawResults.interceptPValue,
          stdErrors: stdErrorsObj,
          p_values: pValuesObj,
          confidence_intervals: confidenceIntervalsObj,
          rSquared: rawResults.rSquared,
          pValue: rawResults.pValue,
          beta: beta,
          dfModel: dfModel,
          dfError: dfError,
          fStatistic: fStatistic,
          n: rawResults.n,
          baseCategories: baseCategories // Store base categories for reference
        };

      } else if (regressionType === 'logistic') {
        // Check if dependent variable is binary (numeric 0/1 or categorical with 2 values)
        if (!isBinaryColumn(dependentCol.name)) {
          setError('Logistic regression requires a binary dependent variable (numeric 0/1 or categorical with exactly 2 values).');
          setLoading(false);
          return;
        }

        // For categorical variables, require positive category selection
        if (dependentCol.type === DataType.CATEGORICAL && !logisticPositiveCategory) {
          setError('Please select which category to map to 1.');
          setLoading(false);
          return;
        }

        // Prepare X matrix with dummy variables for categorical predictors
        const xData: number[][] = [];
        const variableNames: string[] = [];
        
        independentCols.forEach(col => {
          if (col.type === DataType.NUMERIC) {
            xData.push(data.map(row => row[col.name] as number));
            variableNames.push(col.name);
          } else if (col.type === DataType.CATEGORICAL) {
            const baseCategory = baseCategories[col.name];
            if (!baseCategory) {
              setError(`No base category selected for ${col.name}`);
              setLoading(false);
              return;
            }
            const dummyVars = createDummyVariables(data, col, baseCategory);
            Object.entries(dummyVars).forEach(([dummyName, dummyData]) => {
              xData.push(dummyData);
              variableNames.push(dummyName);
            });
          }
        });

        // Map dependent variable to 0/1
        const y = data.map(row => {
          const value = row[dependentCol.name];
          if (dependentCol.type === DataType.NUMERIC) {
            // For numeric variables, use values directly (should be 0/1)
            return typeof value === 'number' ? value : 0;
          } else {
            // For categorical variables, map based on selected positive category
            return value === logisticPositiveCategory ? 1 : 0;
          }
        });
        const rawResults = multipleLogisticRegression(xData[0].map((_, i) => xData.map(row => row[i])), y);

        // Transform results to match component expectations
        const coefficientsObj: Record<string, number> = {};
        const stdErrorsObj: Record<string, number> = {};
        const pValuesObj: Record<string, number> = {};
        const confidenceIntervalsObj: Record<string, [number, number]> = {};
        const hazardRatiosObj: Record<string, number> = {};

        variableNames.forEach((varName, index) => {
          coefficientsObj[varName] = rawResults.coefficients[index];
          stdErrorsObj[varName] = rawResults.stdErrors[index];
          pValuesObj[varName] = rawResults.pValues[index];
          const coef = rawResults.coefficients[index];
          const se = rawResults.stdErrors[index];

          // For logistic and Cox regression, calculate CI for odds/hazard ratios (exp transformed)
          // For other regression types, calculate CI for coefficients
          if (regressionType === 'logistic' || regressionType === 'cox') {
            // Calculate 95% CI for odds ratios/hazard ratios: [exp(coef - 1.96*se), exp(coef + 1.96*se)]
            const lowerCI = Math.exp(coef - 1.96 * se);
            const upperCI = Math.exp(coef + 1.96 * se);
            confidenceIntervalsObj[varName] = [lowerCI, upperCI];
          } else {
            // Calculate 95% CI for coefficients (default behavior)
            confidenceIntervalsObj[varName] = [coef - 1.96 * se, coef + 1.96 * se];
          }

          // Calculate odds ratio/hazard ratio (exp of coefficient)
          hazardRatiosObj[varName] = Math.exp(coef);
        });

        results = {
          coefficients: coefficientsObj,
          intercept: rawResults.intercept,
          interceptStdError: rawResults.interceptStdError,
          interceptPValue: rawResults.interceptPValue,
          stdErrors: stdErrorsObj,
          p_values: pValuesObj,
          confidence_intervals: confidenceIntervalsObj,
          hazard_ratios: hazardRatiosObj, // Using hazard_ratios for OR
          logLikelihood: rawResults.logLikelihood,
          aic: rawResults.aic,
          pseudoRSquared: rawResults.pseudoRSquared,
          accuracy: rawResults.accuracy,
          precision: rawResults.precision,
          recall: rawResults.recall,
          f1Score: rawResults.f1Score,
          auc: rawResults.auc,
          n: rawResults.n,
          baseCategories: baseCategories, // Store base categories for reference
          positiveCategory: dependentCol.type === DataType.CATEGORICAL ? logisticPositiveCategory : '1', // Store which category was mapped to 1
          dependentVariableType: dependentCol.type // Store the type of dependent variable
        };

      } else if (regressionType === 'cox') {
        if (!timeVariable) {
          setError('Please select a time variable for Cox regression.');
          setLoading(false);
          return;
        }
        
        const timeCol = selectedDataset.columns.find(col => col.name === timeVariable);
        if (!timeCol || timeCol.type !== DataType.NUMERIC) {
          setError('Cox regression requires a numeric time variable.');
          setLoading(false);
          return;
        }
        
        // Handle both categorical and binary numeric event variables
        let eventData: number[];
        if (dependentCol.type === DataType.CATEGORICAL) {
          const dependentCategories = getUniqueCategories(dependentCol.name);
          if (dependentCategories.length !== 2) {
            setError('Cox regression requires a binary categorical event variable.');
            setLoading(false);
            return;
          }
          
          if (!coxEventCategory) {
            setError('Please select which category represents the event.');
            setLoading(false);
            return;
          }
          
          // Map to 0/1 based on selected event category
          eventData = data.map(row => row[dependentCol.name] === coxEventCategory ? 1 : 0);
        } else if (dependentCol.type === DataType.NUMERIC) {
          // Check if it's a binary numeric variable
          if (!isBinaryNumeric(dependentCol.name)) {
            setError('Numeric event variable must contain only 0 and 1 values.');
            setLoading(false);
            return;
          }
          
          // Use numeric values directly (assuming 1 is event)
          eventData = data.map(row => row[dependentCol.name] as number);
        } else {
          setError('Event variable must be binary categorical or numeric (0/1).');
          setLoading(false);
          return;
        }

        // Prepare covariates with dummy variables for categorical predictors
        const covariates: { [key: string]: number[] } = {};
        
        independentCols.forEach(col => {
          if (col.type === DataType.NUMERIC) {
            covariates[col.name] = data.map(row => row[col.name] as number);
          } else if (col.type === DataType.CATEGORICAL) {
            const baseCategory = baseCategories[col.name];
            if (!baseCategory) {
              setError(`No base category selected for ${col.name}`);
              setLoading(false);
              return;
            }
            const dummyVars = createDummyVariables(data, col, baseCategory);
            Object.entries(dummyVars).forEach(([dummyName, dummyData]) => {
              covariates[dummyName] = dummyData;
            });
          }
        });

        const coxData: CoxRegressionData = {
          time: data.map(row => row[timeCol.name] as number),
          event: eventData,
          covariates: covariates
        };

        const rawResults = await coxRegressionService.runCoxRegression(coxData);

        // Transform results to include base categories and event category
        results = {
          ...rawResults,
          baseCategories: baseCategories, // Store base categories for reference
          eventCategory: dependentCol.type === DataType.CATEGORICAL ? coxEventCategory : '1' // Store event category
        };

      } else {
        setError('Invalid regression type selected.');
        setLoading(false);
        return;
      }

      setRegressionResults(results);

    } catch (err) {
      setError(`Error running regression analysis: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const columnNames = selectedDataset ? selectedDataset.columns.map((col: Column) => col.name) : [];
  const numericColumnNames = selectedDataset 
    ? selectedDataset.columns.filter(col => col.type === DataType.NUMERIC).map(col => col.name) 
    : [];

  // Helper function to get regression type display name
  const getRegressionTypeDisplay = () => {
    switch (regressionType) {
      case 'linear': return 'Linear';
      case 'logistic': return 'Logistic';
      case 'cox': return 'Cox';
      default: return '';
    }
  };

  // Helper function to get info message based on regression type
  const getInfoMessage = () => {
    if (!selectedDatasetId) {
      return 'Please select a dataset to begin your regression analysis.';
    }
    switch (regressionType) {
      case 'linear':
        return 'Linear regression analyzes the relationship between variables. Supports both numeric and categorical independent variables.';
      case 'logistic':
        return 'Logistic regression predicts binary outcomes. Requires a binary dependent variable (numeric 0/1 or categorical with exactly 2 values) and supports both numeric and categorical independent variables.';
      case 'cox':
        return 'Cox regression analyzes time-to-event data. Requires a time variable, binary event variable (categorical or 0/1 numeric), and supports both numeric and categorical covariates.';
      default:
        return 'Select a regression type to continue with your analysis.';
    }
  };

  // Safe accessor for beta values
  const getBetaValue = (index: number): string => {
    if (!regressionResults?.beta || !Array.isArray(regressionResults.beta)) {
      return 'N/A';
    }
    if (index < 0 || index >= regressionResults.beta.length) {
      return 'N/A';
    }
    return regressionResults.beta[index].toFixed(2);
  };

  // Helper function to generate interpretation text for Results Manager
  const generateInterpretationText = (regressionType: string, results: any): string => {
    if (!results || !regressionType) {
      return '';
    }

    const formatPValue = (pValue: number | undefined): string => {
      if (pValue === undefined) return 'N/A';
      return pValue < 0.001 ? '< .001' : `= ${pValue.toFixed(4)}`;
    };

    switch (regressionType) {
      case 'linear':
        const { coefficients, intercept, p_values, rSquared, fStatistic, dfModel, dfError, pValue } = results;
        let linearText = `A linear regression was conducted to examine the relationship between the independent variables and the dependent variable. The model explained ${rSquared?.toFixed(2) || 'N/A'}% of the variance in the dependent variable, F(${dfModel || 'N/A'}, ${dfError || 'N/A'}) = ${fStatistic?.toFixed(2) || 'N/A'}, p ${formatPValue(pValue)}. `;

        Object.keys(coefficients || {}).forEach((variableName: string) => {
          const coef = coefficients?.[variableName];
          const pValue = p_values?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';
          const direction = coef > 0 ? 'positively' : 'negatively';
          linearText += `The variable ${variableName} was ${significance} ${direction} associated with the dependent variable (b = ${coef?.toFixed(2) || 'N/A'}, p ${formattedPValue}). `;
        });

        if (intercept !== undefined && p_values?.['(Intercept)'] !== undefined) {
          const interceptPValue = p_values['(Intercept)'];
          const formattedInterceptPValue = formatPValue(interceptPValue);
          const interceptSignificance = interceptPValue < 0.05 ? 'significantly' : 'not significantly';
          linearText += `The intercept was ${interceptSignificance} different from zero (b = ${intercept?.toFixed(2) || 'N/A'}, p ${formattedInterceptPValue}).`;
        }
        return linearText;

      case 'logistic':
        const { coefficients: logisticCoefficients, hazard_ratios, p_values: logisticPValues, pseudoRSquared, logLikelihood, aic, positiveCategory } = results;
        let logisticText = `A logistic regression was conducted to predict the likelihood of the outcome variable (coded as 1 for ${positiveCategory || 'N/A'}). The model fit statistics were as follows: Log-Likelihood = ${logLikelihood?.toFixed(2) || 'N/A'}, AIC = ${aic?.toFixed(2) || 'N/A'}, Pseudo R² = ${pseudoRSquared?.toFixed(2) || 'N/A'}. `;

        Object.keys(logisticCoefficients || {}).forEach(variableName => {
          const or = hazard_ratios?.[variableName];
          const pValue = logisticPValues?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';
          logisticText += `The variable ${variableName} was ${significance} associated with the outcome. The odds ratio for ${variableName} was ${or?.toFixed(2) || 'N/A'} (p ${formattedPValue}), indicating that for a one-unit increase in ${variableName}, the odds of the outcome occurring are multiplied by ${or?.toFixed(2) || 'N/A'}, holding other variables constant. `;
        });
        return logisticText;

      case 'cox':
        const { coefficients: coxCoefficients, hazard_ratios: coxHazardRatios, p_values: coxPValues, concordance, log_likelihood, n_observations, n_events, eventCategory } = results;
        let coxText = `A Cox proportional hazards regression was conducted to examine the time to event (event defined as ${eventCategory || 'N/A'}). The model fit statistics were as follows: Log-Likelihood = ${log_likelihood?.toFixed(2) || 'N/A'}, Concordance Index = ${concordance?.toFixed(2) || 'N/A'}. The analysis included ${n_observations || 'N/A'} observations and ${n_events || 'N/A'} events. `;

        Object.keys(coxCoefficients || {}).forEach(variableName => {
          const hr = coxHazardRatios?.[variableName];
          const pValue = coxPValues?.[variableName];
          const formattedPValue = formatPValue(pValue);
          const significance = pValue !== undefined && pValue < 0.05 ? 'significantly' : 'not significantly';
          coxText += `The variable ${variableName} was ${significance} associated with the hazard of the event. The hazard ratio for ${variableName} was ${hr?.toFixed(2) || 'N/A'} (p ${formattedPValue}), indicating that for a one-unit increase in ${variableName}, the hazard of the event is multiplied by ${hr?.toFixed(2) || 'N/A'}, holding other covariates constant. `;
        });
        return coxText;

      default:
        return '';
    }
  };

  // Get categorical variables from selected independent variables
  const selectedCategoricalVariables = independentVariables.filter(varName => {
    const column = selectedDataset?.columns.find(col => col.name === varName);
    return column?.type === DataType.CATEGORICAL;
  });

  // Check if dependent variable is categorical for logistic regression
  const dependentVariableCategories = regressionType === 'logistic' && dependentVariable && selectedDataset
    ? (() => {
        const column = selectedDataset.columns.find(col => col.name === dependentVariable);
        return column?.type === DataType.CATEGORICAL ? getUniqueCategories(dependentVariable) : [];
      })()
    : [];

  // Check if dependent variable is categorical for Cox regression
  const coxDependentCategories = regressionType === 'cox' && dependentVariable && selectedDataset
    ? (() => {
        const column = selectedDataset.columns.find(col => col.name === dependentVariable);
        if (column?.type === DataType.CATEGORICAL) {
          return getUniqueCategories(dependentVariable);
        }
        return [];
      })()
    : [];

  // Get valid event variables for Cox regression (categorical or binary numeric)
  const validCoxEventVariables = selectedDataset && regressionType === 'cox'
    ? selectedDataset.columns.filter(col => 
        (col.name !== timeVariable) && 
        (col.type === DataType.CATEGORICAL || (col.type === DataType.NUMERIC && isBinaryNumeric(col.name)))
      ).map(col => col.name)
    : [];

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Regression Analysis
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Configure Regression Model
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
          {getInfoMessage()}
        </Alert>

        <Grid container spacing={2}>
          {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Regression Type Selection - Only show if dataset is selected */}
          {selectedDatasetId && (
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="regression-type-label">Regression Type</InputLabel>
                <Select
                  labelId="regression-type-label"
                  id="regression-type"
                  value={regressionType}
                  label="Regression Type"
                  onChange={handleRegressionTypeChange}
                >
                  <MenuItem value="">
                    <em>-- Select Type --</em>
                  </MenuItem>
                  <MenuItem value="linear">Linear Regression</MenuItem>
                  <MenuItem value="logistic">Logistic Regression</MenuItem>
                  <MenuItem value="cox">Cox Regression</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}

          {/* Variable Selection - Only show if regression type is selected */}
          {regressionType && selectedDataset && (
            <>
              {/* Time Variable for Cox Regression */}
              {regressionType === 'cox' && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="time-variable-label">Time Variable</InputLabel>
                    <Select
                      labelId="time-variable-label"
                      id="time-variable"
                      value={timeVariable}
                      label="Time Variable"
                      onChange={(e) => setTimeVariable(e.target.value)}
                    >
                      <MenuItem value="">
                        <em>-- Select Variable --</em>
                      </MenuItem>
                      {numericColumnNames.map((name: string) => (
                        <MenuItem key={name} value={name}>
                          {name}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>Select the time-to-event variable</FormHelperText>
                  </FormControl>
                </Grid>
              )}

              {/* Dependent Variable */}
              <Grid item xs={12} md={regressionType === 'cox' ? 6 : 6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="dependent-variable-label">
                    Dependent Variable {regressionType === 'cox' ? '(Event)' : ''}
                  </InputLabel>
                  <Select
                    labelId="dependent-variable-label"
                    id="dependent-variable"
                    value={dependentVariable}
                    label={`Dependent Variable ${regressionType === 'cox' ? '(Event)' : ''}`}
                    onChange={handleDependentVariableChange}
                  >
                    <MenuItem value="">
                      <em>-- Select Variable --</em>
                    </MenuItem>
                    {regressionType === 'cox'
                      ? validCoxEventVariables.map((name: string) => {
                          const column = selectedDataset.columns.find(col => col.name === name);
                          const displayType = column?.type === DataType.NUMERIC && isBinaryNumeric(name)
                            ? 'NUMERIC (0/1)'
                            : column?.type;
                          return (
                            <MenuItem key={name} value={name}>
                              {name} ({displayType})
                            </MenuItem>
                          );
                        })
                      : regressionType === 'logistic'
                        ? getValidLogisticDependentVariables().map((name: string) => {
                            const column = selectedDataset.columns.find(col => col.name === name);
                            const displayType = column?.type === DataType.NUMERIC && isBinaryNumeric(name)
                              ? 'NUMERIC (0/1)'
                              : column?.type === DataType.CATEGORICAL
                                ? 'CATEGORICAL (Binary)'
                                : column?.type;
                            return (
                              <MenuItem key={name} value={name}>
                                {name} ({displayType})
                              </MenuItem>
                            );
                          })
                        : columnNames
                            .filter(name => regressionType !== 'cox' || name !== timeVariable)
                            .map((name: string) => {
                              const column = selectedDataset.columns.find(col => col.name === name);
                              return (
                                <MenuItem key={name} value={name}>
                                  {name} ({column?.type})
                                </MenuItem>
                              );
                            })
                    }
                  </Select>
                </FormControl>
              </Grid>

              {/* Event Category Selection for Cox Regression */}
              {regressionType === 'cox' && coxDependentCategories.length === 2 && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="cox-event-category-label">
                      Event Category
                    </InputLabel>
                    <Select
                      labelId="cox-event-category-label"
                      id="cox-event-category"
                      value={coxEventCategory}
                      label="Event Category"
                      onChange={(e) => setCoxEventCategory(e.target.value)}
                    >
                      {coxDependentCategories.map(category => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>
                      Select which category represents the event occurrence
                    </FormHelperText>
                  </FormControl>
                </Grid>
              )}

              {/* Positive Category Selection for Logistic Regression */}
              {regressionType === 'logistic' && dependentVariableCategories.length === 2 && (
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel id="positive-category-label">
                      Map to 1 (Positive Outcome)
                    </InputLabel>
                    <Select
                      labelId="positive-category-label"
                      id="positive-category"
                      value={logisticPositiveCategory}
                      label="Map to 1 (Positive Outcome)"
                      onChange={(e) => setLogisticPositiveCategory(e.target.value)}
                    >
                      {dependentVariableCategories.map(category => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>
                      Select which category represents the positive outcome (will be coded as 1)
                    </FormHelperText>
                  </FormControl>
                </Grid>
              )}

              {/* Independent Variables */}
              <Grid item xs={12} md={12}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="independent-variables-label">
                    Independent Variables {regressionType === 'cox' ? '(Covariates)' : ''}
                  </InputLabel>
                  <Select
                    labelId="independent-variables-label"
                    id="independent-variables"
                    multiple
                    value={independentVariables}
                    onChange={handleIndependentVariableChange}
                    input={<OutlinedInput label={`Independent Variables ${regressionType === 'cox' ? '(Covariates)' : ''}`} />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {columnNames
                      .filter(name => name !== dependentVariable && (regressionType !== 'cox' || name !== timeVariable)) // Exclude dependent and time variables
                      .map((name: string) => {
                        const column = selectedDataset.columns.find(col => col.name === name);
                        return (
                          <MenuItem key={name} value={name}>
                            {name} ({column?.type})
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Grid>

              {/* Base Category Selection for Categorical Variables */}
              {selectedCategoricalVariables.length > 0 && (
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2, mt: 1 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Select Base Categories for Categorical Variables
                    </Typography>
                    <Grid container spacing={2}>
                      {selectedCategoricalVariables.map(varName => {
                        const categories = getUniqueCategories(varName);
                        return (
                          <Grid item xs={12} md={6} key={varName}>
                            <FormControl fullWidth size="small">
                              <InputLabel id={`base-${varName}-label`}>
                                Base Category for {varName}
                              </InputLabel>
                              <Select
                                labelId={`base-${varName}-label`}
                                id={`base-${varName}`}
                                value={baseCategories[varName] || ''}
                                label={`Base Category for ${varName}`}
                                onChange={(e) => handleBaseCategoryChange(varName, e.target.value)}
                              >
                                {categories.map(category => (
                                  <MenuItem key={category} value={category}>
                                    {category}
                                  </MenuItem>
                                ))}
                              </Select>
                              <FormHelperText>
                                Reference category for dummy variables
                              </FormHelperText>
                            </FormControl>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Paper>
                </Grid>
              )}
            </>
          )}
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runAnalysis}
            disabled={
              loading || 
              !selectedDatasetId || 
              !regressionType || 
              !dependentVariable || 
              independentVariables.length === 0 ||
              (regressionType === 'cox' && !timeVariable)
            }
          >
            Run Regression Analysis
          </Button>
        </Box>
      </Paper>

      {/* Loading State */}
      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {/* Error State */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Results Section */}
      {regressionResults && !loading && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {getRegressionTypeDisplay()} Regression Results
            </Typography>

            {/* Results Table */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>B</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>SE</TableCell>
                    {regressionType === 'linear' && (
                      <>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>95% CI</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>β</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>t</TableCell>
                      </>
                    )}
                    {(regressionType === 'logistic' || regressionType === 'cox') && (
                      <>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                          {regressionType === 'logistic' ? 'OR' : 'HR'}
                        </TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>95% CI</TableCell>
                      </>
                    )}
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>p</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Intercept Row (not for Cox) */}
                  {regressionType !== 'cox' && regressionResults.intercept !== undefined && (
                    <TableRow>
                      <TableCell>(Intercept)</TableCell>
                      <TableCell align="right">{regressionResults.intercept.toFixed(2)}</TableCell>
                      <TableCell align="right">{regressionResults.interceptStdError?.toFixed(2) || 'N/A'}</TableCell>
                      {regressionType === 'linear' && (
                        <>
                          <TableCell align="right">
                            {regressionResults.interceptStdError
                              ? `[${(regressionResults.intercept - 1.96 * regressionResults.interceptStdError).toFixed(2)}, ${(regressionResults.intercept + 1.96 * regressionResults.interceptStdError).toFixed(2)}]`
                              : 'N/A'
                            }
                          </TableCell>
                          <TableCell align="right">{getBetaValue(0)}</TableCell>
                          <TableCell align="right">
                            {regressionResults.interceptStdError
                              ? (regressionResults.intercept / regressionResults.interceptStdError).toFixed(2)
                              : 'N/A'
                            }
                          </TableCell>
                        </>
                      )}
                      {(regressionType === 'logistic') && (
                        <>
                          <TableCell align="right">—</TableCell>
                          <TableCell align="right">
                            {regressionResults.interceptStdError
                              ? `[${(regressionResults.intercept - 1.96 * regressionResults.interceptStdError).toFixed(2)}, ${(regressionResults.intercept + 1.96 * regressionResults.interceptStdError).toFixed(2)}]`
                              : 'N/A'
                            }
                          </TableCell>
                        </>
                      )}
                      <TableCell align="right">
                        {regressionResults.interceptPValue !== undefined
                          ? (regressionResults.interceptPValue < 0.001 ? '< .001' : regressionResults.interceptPValue.toFixed(4))
                          : 'N/A'
                        }
                      </TableCell>
                    </TableRow>
                  )}

                  {/* Independent Variables Rows */}
                  {regressionResults.coefficients && Object.keys(regressionResults.coefficients).map((variableName, index) => {
                    // Check if this is a dummy variable and format the name accordingly
                    const displayName = variableName.includes('_') 
                      ? variableName.replace('_', ' = ')
                      : variableName;
                    
                    return (
                      <TableRow key={variableName}>
                        <TableCell>{displayName}</TableCell>
                        <TableCell align="right">
                          {regressionResults.coefficients[variableName]?.toFixed(2) || 'N/A'}
                        </TableCell>
                        <TableCell align="right">
                          {regressionResults.std_errors?.[variableName]?.toFixed(2) || regressionResults.stdErrors?.[variableName]?.toFixed(2) || 'N/A'}
                        </TableCell>
                        {regressionType === 'linear' && (
                          <>
                            <TableCell align="right">
                              {regressionResults.confidence_intervals?.[variableName]
                                ? `[${regressionResults.confidence_intervals[variableName][0].toFixed(2)}, ${regressionResults.confidence_intervals[variableName][1].toFixed(2)}]`
                                : 'N/A'
                              }
                            </TableCell>
                            <TableCell align="right">{getBetaValue(index + 1)}</TableCell>
                            <TableCell align="right">
                              {regressionResults.coefficients[variableName] && (regressionResults.std_errors?.[variableName] || regressionResults.stdErrors?.[variableName])
                                ? (regressionResults.coefficients[variableName] / (regressionResults.std_errors?.[variableName] || regressionResults.stdErrors?.[variableName])).toFixed(2)
                                : 'N/A'
                              }
                            </TableCell>
                          </>
                        )}
                        {(regressionType === 'logistic' || regressionType === 'cox') && (
                          <>
                            <TableCell align="right">
                              {regressionResults.hazard_ratios?.[variableName]?.toFixed(2) || 'N/A'}
                            </TableCell>
                            <TableCell align="right">
                              {regressionResults.confidence_intervals?.[variableName]
                                ? `[${regressionResults.confidence_intervals[variableName][0].toFixed(2)}, ${regressionResults.confidence_intervals[variableName][1].toFixed(2)}]`
                                : 'N/A'
                              }
                            </TableCell>
                          </>
                        )}
                        <TableCell align="right">
                          {regressionResults.p_values?.[variableName] !== undefined
                            ? (regressionResults.p_values[variableName] < 0.001 ? '< .001' : regressionResults.p_values[variableName].toFixed(4))
                            : 'N/A'
                          }
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            <Divider sx={{ my: 2 }} />

            {/* Model Fit Statistics */}
            <Box mt={2}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
                Model Fit Statistics
              </Typography>
              
              {/* Linear Regression Statistics */}
              {regressionType === 'linear' && regressionResults.rSquared !== undefined && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  F({regressionResults.dfModel || 'N/A'}, {regressionResults.dfError || 'N/A'}) = {regressionResults.fStatistic?.toFixed(2) || 'N/A'}, 
                  p {regressionResults.pValue !== undefined 
                    ? (regressionResults.pValue < 0.001 ? '< .001' : `= ${regressionResults.pValue.toFixed(4)}`)
                    : '= N/A'
                  }, 
                  R² = {regressionResults.rSquared.toFixed(2)}
                </Typography>
              )}

              {/* Logistic Regression Statistics */}
              {regressionType === 'logistic' && regressionResults.pseudoRSquared !== undefined && (
                <>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Log-Likelihood: {regressionResults.logLikelihood?.toFixed(2) || 'N/A'}, 
                    AIC: {regressionResults.aic?.toFixed(2) || 'N/A'}, 
                    Pseudo R²: {regressionResults.pseudoRSquared.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Accuracy: {regressionResults.accuracy?.toFixed(2) || 'N/A'}, 
                    Precision: {regressionResults.precision?.toFixed(2) || 'N/A'}, 
                    Recall: {regressionResults.recall?.toFixed(2) || 'N/A'}, 
                    F1-Score: {regressionResults.f1Score?.toFixed(2) || 'N/A'}, 
                    AUC: {regressionResults.auc?.toFixed(2) || 'N/A'}
                  </Typography>
                </>
              )}

              {/* Cox Regression Statistics */}
              {regressionType === 'cox' && regressionResults.concordance !== undefined && (
                <>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Log-Likelihood: {regressionResults.log_likelihood?.toFixed(2) || 'N/A'}, 
                    AIC: {regressionResults.aic?.toFixed(2) || 'N/A'}, 
                    Concordance Index: {regressionResults.concordance.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    N Observations: {regressionResults.n_observations || 'N/A'}, 
                    N Events: {regressionResults.n_events || 'N/A'}
                  </Typography>
                </>
              )}

              {/* Display additional information */}
              {(regressionResults.baseCategories && Object.keys(regressionResults.baseCategories).length > 0) || 
               regressionResults.positiveCategory || 
               regressionResults.eventCategory ? (
                <>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="subtitle2" sx={{ mt: 1, fontStyle: 'italic' }}>
                    Model Configuration:
                  </Typography>
                  
                  {/* Positive category for logistic regression */}
                  {regressionResults.positiveCategory && regressionType === 'logistic' && (
                    <Typography variant="body2" sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
                      {regressionResults.dependentVariableType === DataType.CATEGORICAL
                        ? `Positive outcome (${dependentVariable} = 1): ${regressionResults.positiveCategory}`
                        : `Positive outcome: ${dependentVariable} = 1 (numeric)`
                      }
                    </Typography>
                  )}
                  
                  {/* Event category for Cox regression */}
                  {regressionResults.eventCategory && (
                    <Typography variant="body2" sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
                      Event ({dependentVariable} = 1): {regressionResults.eventCategory}
                    </Typography>
                  )}
                  
                  {/* Base categories */}
                  {regressionResults.baseCategories && Object.keys(regressionResults.baseCategories).length > 0 && (
                    <>
                      <Typography variant="body2" sx={{ fontSize: '0.875rem', color: 'text.secondary', mt: 0.5 }}>
                        Reference categories:
                      </Typography>
                      {Object.entries(regressionResults.baseCategories).map(([varName, baseCategory]) => (
                        <Typography key={varName} variant="body2" sx={{ fontSize: '0.875rem', color: 'text.secondary', ml: 2 }}>
                          {varName}: {baseCategory as string}
                        </Typography>
                      ))}
                    </>
                  )}
                </>
              ) : null}
            </Box>
          </Paper>
        </Box>
      )}

      {/* Interpretation Section */}
      {regressionResults && (
        <RegressionInterpretation
          regressionType={regressionType as 'linear' | 'logistic' | 'cox'}
          results={regressionResults}
        />
      )}

      {/* Add to Results Manager Button */}
      {regressionResults && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <AddToResultsButton
            resultData={{
              title: `${regressionType.charAt(0).toUpperCase() + regressionType.slice(1)} Regression Table (${selectedDataset?.name || 'Unknown'})`,
              type: 'regression' as const,
              component: 'RegressionTable',
              data: {
                dataset: selectedDataset?.name || 'Unknown',
                regressionType: regressionType,
                dependentVariable: dependentVariable,
                independentVariables: independentVariables,
                results: regressionResults,
                interpretation: generateInterpretationText(regressionType, regressionResults),
                timestamp: new Date().toISOString(),
                totalSampleSize: selectedDataset?.data.length || 0
              }
            }}
            onSuccess={() => {
              setSnackbarMessage('Results successfully added to Results Manager!');
              setSnackbarOpen(true);
            }}
            onError={(error) => {
              setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
              setSnackbarOpen(true);
            }}
          />
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default RegressionTable;
