import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  Tabs,
  Tab,
  useTheme,
  FormControlLabel,
  Checkbox,
  Chip,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  CompareArrows as DifferenceIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`difference-means-ci-tabpanel-${index}`}
      aria-labelledby={`difference-means-ci-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface DifferenceMeansCIResult {
  method: string;
  lowerBound: number;
  upperBound: number;
  confidenceLevel: number;
  mean1: number;
  mean2: number;
  difference: number;
  standardError: number;
  degreesOfFreedom?: number;
  interpretation: string;
  formula: string;
}

interface CalculationData {
  mean1: number;
  mean2: number;
  sd1: number;
  sd2: number;
  n1: number;
  n2: number;
  confidenceLevel: number;
  assumeEqualVariances: boolean;
}

const DifferenceMeansCI: React.FC = () => {
  const theme = useTheme();
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<CalculationData>({
    mean1: 0,
    mean2: 0,
    sd1: 0,
    sd2: 0,
    n1: 0,
    n2: 0,
    confidenceLevel: 95,
    assumeEqualVariances: false
  });
  const [result, setResult] = useState<DifferenceMeansCIResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const getZScore = (confidenceLevel: number): number => {
    const zScores: { [key: number]: number } = {
      90: 1.645,
      95: 1.96,
      99: 2.576,
      99.9: 3.291
    };
    return zScores[confidenceLevel] || 1.96;
  };

  const getTScore = (df: number, confidenceLevel: number): number => {
    // Simplified t-values for common confidence levels and degrees of freedom
    const tTable: { [key: number]: { [key: number]: number } } = {
      90: { 1: 6.314, 2: 2.920, 5: 2.015, 10: 1.812, 20: 1.725, 30: 1.697, 50: 1.676, 100: 1.660, 1000: 1.645 },
      95: { 1: 12.706, 2: 4.303, 5: 2.571, 10: 2.228, 20: 2.086, 30: 2.042, 50: 2.009, 100: 1.984, 1000: 1.962 },
      99: { 1: 63.657, 2: 9.925, 5: 4.032, 10: 3.169, 20: 2.845, 30: 2.750, 50: 2.678, 100: 2.626, 1000: 2.581 }
    };
    
    const tValues = tTable[confidenceLevel];
    if (!tValues) return getZScore(confidenceLevel);
    
    // Find closest df
    const dfKeys = Object.keys(tValues).map(Number).sort((a, b) => a - b);
    let closestDf = dfKeys[dfKeys.length - 1]; // Default to largest
    
    for (const key of dfKeys) {
      if (df <= key) {
        closestDf = key;
        break;
      }
    }
    
    return tValues[closestDf] || getZScore(confidenceLevel);
  };

  const calculateDifferenceMeansCI = () => {
    const { mean1, mean2, sd1, sd2, n1, n2, confidenceLevel, assumeEqualVariances } = calculationData;
    
    if (n1 <= 0 || n2 <= 0) {
      setError('Sample sizes must be greater than 0.');
      return;
    }
    
    if (sd1 <= 0 || sd2 <= 0) {
      setError('Standard deviations must be greater than 0.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const difference = mean1 - mean2;
      let standardError: number;
      let degreesOfFreedom: number;
      let method: string;
      
      if (assumeEqualVariances) {
        // Pooled variance t-test
        const pooledVariance = ((n1 - 1) * sd1 * sd1 + (n2 - 1) * sd2 * sd2) / (n1 + n2 - 2);
        standardError = Math.sqrt(pooledVariance * (1/n1 + 1/n2));
        degreesOfFreedom = n1 + n2 - 2;
        method = `Pooled variance t-test (df = ${degreesOfFreedom})`;
      } else {
        // Welch's t-test (unequal variances)
        const se1Sq = (sd1 * sd1) / n1;
        const se2Sq = (sd2 * sd2) / n2;
        standardError = Math.sqrt(se1Sq + se2Sq);
        
        // Welch-Satterthwaite equation for degrees of freedom
        const numerator = Math.pow(se1Sq + se2Sq, 2);
        const denominator = (se1Sq * se1Sq) / (n1 - 1) + (se2Sq * se2Sq) / (n2 - 1);
        degreesOfFreedom = Math.floor(numerator / denominator);
        method = `Welch's t-test (df ≈ ${degreesOfFreedom})`;
      }
      
      const criticalValue = getTScore(degreesOfFreedom, confidenceLevel);
      const margin = criticalValue * standardError;
      
      const lowerBound = difference - margin;
      const upperBound = difference + margin;
      
      // Determine the appropriate formula
      let formula: string;
      if (assumeEqualVariances) {
        formula = '(\\bar{x}_1 - \\bar{x}_2) \\pm t_{\\alpha/2,df} \\cdot s_p\\sqrt{\\frac{1}{n_1} + \\frac{1}{n_2}}';
      } else {
        formula = '(\\bar{x}_1 - \\bar{x}_2) \\pm t_{\\alpha/2,df} \\cdot \\sqrt{\\frac{s_1^2}{n_1} + \\frac{s_2^2}{n_2}}';
      }
      
      let interpretation = `We are ${confidenceLevel}% confident that the true difference in population means (μ₁ - μ₂) lies between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
      
      if (lowerBound > 0) {
        interpretation += ' Since the entire interval is positive, we can conclude that μ₁ > μ₂.';
      } else if (upperBound < 0) {
        interpretation += ' Since the entire interval is negative, we can conclude that μ₁ < μ₂.';
      } else {
        interpretation += ' Since the interval contains 0, we cannot conclude there is a significant difference between the means.';
      }
      
      setResult({
        method,
        lowerBound,
        upperBound,
        confidenceLevel,
        mean1,
        mean2,
        difference,
        standardError,
        degreesOfFreedom,
        interpretation,
        formula
      });
      
      // Automatically navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError('An error occurred during calculation. Please check your inputs.');
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setCalculationData({
      mean1: 0,
      mean2: 0,
      sd1: 0,
      sd2: 0,
      n1: 0,
      n2: 0,
      confidenceLevel: 95,
      assumeEqualVariances: false,
      isPaired: false
    });
    setResult(null);
    setError(null);
    setActiveTab(0);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (!result) return '';
    
    return `Difference of Means Confidence Interval Analysis\n\n` +
           `Group 1 Mean: ${result.mean1}\n` +
           `Group 2 Mean: ${result.mean2}\n` +
           `Difference (μ₁ - μ₂): ${result.difference.toFixed(2)}\n` +
           `Confidence Level: ${result.confidenceLevel}%\n` +
           `Method: ${result.method}\n` +
           `Standard Error: ${result.standardError.toFixed(2)}\n` +
           `Confidence Interval: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n\n` +
           `Interpretation: ${result.interpretation}`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <DifferenceIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Difference of Means Confidence Interval
        </Typography>
      </Box>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Calculate confidence intervals for the difference between two population means.
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!result} />
          <Tab label="Guide" />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <InfoIcon sx={{ mr: 1 }} />
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom color="primary">
                      Group 1
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Sample Size 1 (n₁)"
                      value={calculationData.n1}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, n1: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 1, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Sample Mean 1 (x̄₁)"
                      value={calculationData.mean1}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, mean1: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ step: 'any' }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Standard Deviation 1 (s₁)"
                      value={calculationData.sd1}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, sd1: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 0, step: 'any' }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom color="secondary">
                      Group 2
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Sample Size 2 (n₂)"
                      value={calculationData.n2}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, n2: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 1, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Sample Mean 2 (x̄₂)"
                      value={calculationData.mean2}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, mean2: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ step: 'any' }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Standard Deviation 2 (s₂)"
                      value={calculationData.sd2}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, sd2: parseFloat(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 0, step: 'any' }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={calculationData.confidenceLevel}
                        label="Confidence Level"
                        onChange={(e) => setCalculationData(prev => ({ ...prev, confidenceLevel: Number(e.target.value) }))}
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                        <MenuItem value={99.9}>99.9%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={calculationData.assumeEqualVariances}
                          onChange={(e) => setCalculationData(prev => ({ ...prev, assumeEqualVariances: e.target.checked }))}
                        />
                      }
                      label="Assume equal variances (pooled variance)"
                    />
                  </Grid>
                </Grid>
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculateDifferenceMeansCI}
                    startIcon={<CalculateIcon />}
                    disabled={loading}
                    sx={{ flex: 1 }}
                  >
                    {loading ? 'Calculating...' : 'Calculate CI'}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                    startIcon={<ClearIcon />}
                    sx={{ flex: 1 }}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Interval Results
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                    <Typography variant="subtitle2" color="primary">
                      {result.confidenceLevel}% Confidence Interval
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                    <Typography variant="subtitle2" color="secondary">
                      Difference (μ₁ - μ₂)
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.difference.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'info.50' }}>
                    <Typography variant="subtitle2" color="info.main">
                      Standard Error
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.standardError.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="subtitle2" gutterBottom>
                Method: {result.method}
              </Typography>
              
              <Box sx={{ my: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Formula:
                </Typography>
                <Box
                  dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                  sx={{ '& .katex': { fontSize: '1.1em' } }}
                />
              </Box>
              
              <Typography variant="body2" sx={{ mt: 2 }}>
                {result.interpretation}
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<CopyIcon />}
                onClick={() => copyToClipboard(generateReport())}
                sx={{ mt: 2 }}
              >
                Copy Results
              </Button>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Difference of Means Confidence Interval Guide
            </Typography>
            
            <Typography variant="body1" paragraph>
              A confidence interval for the difference of means helps you estimate the true difference 
              between two population means based on sample data.
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Types of Tests:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• <strong>Paired t-test:</strong> Use when the same subjects are measured twice (before/after, matched pairs)</li>
              <li>• <strong>Independent samples:</strong> Use when comparing two separate groups</li>
              <li>• <strong>Pooled variance:</strong> Assumes equal variances between groups</li>
              <li>• <strong>Welch's t-test:</strong> Does not assume equal variances (more robust)</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Interpretation:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• If the interval contains 0: No significant difference between means</li>
              <li>• If the interval is entirely positive: Group 1 mean is significantly larger</li>
              <li>• If the interval is entirely negative: Group 2 mean is significantly larger</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Assumptions:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• Data should be approximately normally distributed</li>
              <li>• Observations should be independent</li>
              <li>• For pooled variance: variances should be approximately equal</li>
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>
    </Box>
  );
};

export default DifferenceMeansCI;