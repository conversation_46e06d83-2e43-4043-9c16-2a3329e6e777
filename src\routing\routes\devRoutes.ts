// Development-only routes (only available in development environment)

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

// Lazy load development components
const DevTrainingPage = lazy(() => import('../../pages/DevTrainingPage'));

const EducationalTierTest = lazy(() => import('../../components/Testing/EducationalTierTest'));
const InteractiveAdvisorDemo = lazy(() => import('../../components/AnalysisAssistant/demo/InteractiveAdvisorDemo'));
const SimpleAdvisorTest = lazy(() => import('../../components/AnalysisAssistant/demo/SimpleAdvisorTest'));

/**
 * Check if we're in development environment
 */
const isDevelopmentEnvironment = (): boolean => {
  return process.env.NODE_ENV === 'development' || 
         (typeof window !== 'undefined' && (
           window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1' ||
           window.location.port === '5173' ||
           window.location.port === '5174' ||
           window.location.port === '3000'
         ));
};

export const devRoutes: EnhancedRouteConfig[] = [
  // Development Training System
  {
    path: 'dev-training',
    component: DevTrainingPage,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Development Training System',
      description: 'Analysis Assistant training data management (Development Only)',
      category: 'development',
      icon: 'Code',
      order: 999,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  },

  // Educational Tier Testing
  {
    path: 'edu-tier-test',
    component: EducationalTierTest,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Educational Tier Testing',
      description: 'Test and verify educational tier implementation (Development Only)',
      category: 'development',
      icon: 'School',
      order: 996,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  },

  // Interactive Statistical Advisor Demo
  {
    path: 'interactive-advisor-demo',
    component: InteractiveAdvisorDemo,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Interactive Statistical Advisor Demo',
      description: 'Test the Interactive Statistical Advisor with different user types and datasets (Development Only)',
      category: 'development',
      icon: 'Psychology',
      order: 995,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  },

  // Simple Advisor Test
  {
    path: 'simple-advisor-test',
    component: SimpleAdvisorTest,
    requiresAuth: false,
    allowGuest: true,
    allowPublic: true,
    metadata: {
      title: 'Simple Advisor Test',
      description: 'Basic test for Interactive Statistical Advisor component (Development Only)',
      category: 'development',
      icon: 'BugReport',
      order: 994,
      hidden: !isDevelopmentEnvironment(), // Hide in production
      developmentOnly: true
    }
  }
];

/**
 * Get development routes (only returns routes if in development environment)
 * Exception: auth-test is always available for debugging authentication issues
 */
export const getDevRoutes = (): EnhancedRouteConfig[] => {
  if (!isDevelopmentEnvironment()) {
    // In production, only return auth-test route for debugging
    return devRoutes.filter(route => route.path === 'auth-test');
  }
  return devRoutes;
};
