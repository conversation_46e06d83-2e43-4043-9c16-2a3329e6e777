import React from 'react';
import { Box, Container } from '@mui/material';
import CICalculatorsOptions from './CICalculatorsOptions';
import SocialShareWidget from '../UI/SocialShareWidget';

interface CICalculatorsPageProps {
  onNavigate: (path: string) => void;
}

const CICalculatorsPage: React.FC<CICalculatorsPageProps> = ({ onNavigate }) => {
  const handleNavigate = (path: string) => {
    onNavigate(path);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <CICalculatorsOptions onNavigate={handleNavigate} />
      </Box>
      
      <Box sx={{ mt: 6, display: 'flex', justifyContent: 'center' }}>
        <SocialShareWidget 
          title="Confidence Interval Calculators - DataStatPro"
          description="Comprehensive collection of confidence interval calculators for statistical inference. Calculate CIs for means, proportions, differences, correlations, and more."
          hashtags={['statistics', 'confidence-intervals', 'research', 'data-analysis']}
        />
      </Box>
    </Container>
  );
};

export default CICalculatorsPage;