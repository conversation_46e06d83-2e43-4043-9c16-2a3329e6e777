import React, { useState, useRef, useEffect } from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Card,
  CardContent,
  CardActions,
  Chip,
  Divider,
  CircularProgress,
  IconButton,
  Tooltip,
  Collapse,
  Rating
} from '@mui/material';
import {
  Send as SendIcon,
  Lightbulb as LightbulbIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { AnalysisSuggestion } from '../utils/types';

interface QueryViewProps {
  processQuery: (query: string) => any;
  generateSuggestions: (matches: any[]) => AnalysisSuggestion[];
  openFeedbackDialog: (suggestion: AnalysisSuggestion) => void;
  queryHistory: string[];
  currentQuery: string;
  setCurrentQuery: (query: string) => void;
}

/**
 * Query view component for the AnalysisAssistant
 * Allows users to ask questions and get analysis suggestions
 */
const QueryView: React.FC<QueryViewProps> = ({
  processQuery,
  generateSuggestions,
  openFeedbackDialog,
  queryHistory,
  currentQuery,
  setCurrentQuery
}) => {
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [suggestions, setSuggestions] = useState<AnalysisSuggestion[]>([]);
  const [expandedSuggestion, setExpandedSuggestion] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Example queries for inspiration
  const exampleQueries = [
    "How do I compare two independent groups?",
    "What's the best way to analyze the relationship between age and blood pressure?",
    "How should I visualize categorical data?",
    "Which test should I use for pre-post measurements?",
    "How do I check if my data is normally distributed?",
    "What analysis is appropriate for survival data?"
  ];

  // Focus the input field when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  /**
   * Handle query submission
   */
  const handleSubmit = async () => {
    if (!currentQuery.trim()) return;
    
    setIsProcessing(true);
    
    try {
      // Process the query and get matching scenarios
      const matches = processQuery(currentQuery);
      
      // Generate suggestions based on matches
      const newSuggestions = generateSuggestions(matches);
      
      // Simulate network delay for demo purposes
      setTimeout(() => {
        setSuggestions(newSuggestions);
        setIsProcessing(false);
      }, 1000);
    } catch (error) {
      console.error('Error processing query:', error);
      setIsProcessing(false);
    }
  };

  /**
   * Handle pressing Enter in the query input
   */
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  };

  /**
   * Use an example query
   */
  const useExampleQuery = (query: string) => {
    setCurrentQuery(query);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  /**
   * Toggle expanded state of a suggestion
   */
  const toggleExpandSuggestion = (suggestionId: string) => {
    setExpandedSuggestion(expandedSuggestion === suggestionId ? null : suggestionId);
  };

  return (
    <Paper elevation={0} sx={{ p: 3, height: '100%' }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Ask a Question
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" paragraph>
          Describe your analysis needs in plain language
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="What would you like to analyze?"
            variant="outlined"
            value={currentQuery}
            onChange={(e) => setCurrentQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            inputRef={inputRef}
            multiline
            rows={2}
            placeholder="e.g., How do I compare two groups? or What's the best visualization for categorical data?"
          />
          <Button
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={isProcessing || !currentQuery.trim()}
            sx={{ minWidth: '120px', height: '56px' }}
            startIcon={isProcessing ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          >
            {isProcessing ? 'Processing...' : 'Ask'}
          </Button>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" color="textSecondary" gutterBottom>
            Try asking about:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {exampleQueries.map((query, index) => (
              <Chip
                key={index}
                label={query}
                variant="outlined"
                onClick={() => useExampleQuery(query)}
                clickable
              />
            ))}
          </Box>
        </Box>
      </Box>

      {queryHistory.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle2" color="textSecondary" gutterBottom>
            Recent Questions:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {queryHistory.slice(-5).reverse().map((query, index) => (
              <Chip
                key={index}
                label={query.length > 40 ? `${query.substring(0, 40)}...` : query}
                variant="outlined"
                size="small"
                onClick={() => useExampleQuery(query)}
                clickable
              />
            ))}
          </Box>
        </Box>
      )}

      {isProcessing && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {!isProcessing && suggestions.length > 0 && (
        <Box>
          <Divider sx={{ my: 3 }} />
          
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <LightbulbIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h5">
              Analysis Suggestions
            </Typography>
          </Box>
          
          <Grid container spacing={3}>
            {suggestions.map((suggestion) => (
              <Grid item xs={12} key={suggestion.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Typography variant="h6" component="h3" gutterBottom>
                        {suggestion.title}
                      </Typography>
                      <Chip
                        label={`${Math.round(suggestion.confidence * 100)}% match`}
                        color={suggestion.confidence > 0.7 ? "success" : "primary"}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" paragraph>
                      {suggestion.description}
                    </Typography>
                    
                    {suggestion.matchedKeywords && suggestion.matchedKeywords.length > 0 && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Matched Keywords:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {suggestion.matchedKeywords.slice(0, 5).map((keyword, idx) => (
                            <Chip
                              key={idx}
                              label={keyword}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                          {suggestion.matchedKeywords.length > 5 && (
                            <Chip
                              label={`+${suggestion.matchedKeywords.length - 5} more`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Box>
                    )}
                    
                    <Collapse in={expandedSuggestion === suggestion.id}>
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Suggested Analyses:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {suggestion.suggestedAnalyses && suggestion.suggestedAnalyses.map((analysis, idx) => (
                            <Chip
                              key={idx}
                              label={analysis}
                              color="primary"
                              size="small"
                            />
                          ))}
                        </Box>
                        
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Was this suggestion helpful?
                          </Typography>
                          <Rating
                            name={`feedback-${suggestion.id}`}
                            max={5}
                            onChange={() => openFeedbackDialog(suggestion)}
                          />
                        </Box>
                      </Box>
                    </Collapse>
                  </CardContent>
                  
                  <CardActions sx={{ justifyContent: 'space-between' }}>
                    <Button
                      size="small"
                      onClick={() => toggleExpandSuggestion(suggestion.id)}
                      endIcon={expandedSuggestion === suggestion.id ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    >
                      {expandedSuggestion === suggestion.id ? 'Show Less' : 'Show More'}
                    </Button>
                    
                    <Box>
                      <Tooltip title="This suggestion is helpful">
                        <IconButton size="small" onClick={() => openFeedbackDialog(suggestion)}>
                          <ThumbUpIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="This suggestion is not helpful">
                        <IconButton size="small" onClick={() => openFeedbackDialog(suggestion)}>
                          <ThumbDownIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Get help with this analysis">
                        <IconButton size="small">
                          <HelpIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {!isProcessing && currentQuery && suggestions.length === 0 && (
        <Box sx={{ textAlign: 'center', my: 4 }}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No matching suggestions found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Try rephrasing your question or using different keywords
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default QueryView;