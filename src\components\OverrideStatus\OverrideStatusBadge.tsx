import React from 'react';
import {
  <PERSON>,
  Chip,
  Tooltip,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Star as StarIcon,
  Schedule as ScheduleIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../utils/supabaseClient';

interface OverrideStatusBadgeProps {
  variant?: 'chip' | 'card' | 'inline';
  showDetails?: boolean;
  size?: 'small' | 'medium' | 'large';
  userId?: string; // Optional userId for admin views
  refreshTrigger?: number; // Trigger to force refresh
}

const OverrideStatusBadge: React.FC<OverrideStatusBadgeProps> = ({
  variant = 'chip',
  showDetails = false,
  size = 'medium',
  userId,
  refreshTrigger = 0
}) => {
  const { subscriptionOverride, hasActiveOverride, effectiveTier, accountType, user } = useAuth();
  const [expanded, setExpanded] = React.useState(false);
  const [userOverride, setUserOverride] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(false);

  // Fetch override data for specific user (admin view)
  React.useEffect(() => {
    if (userId && userId !== user?.id) {
      const fetchUserOverride = async () => {
        setLoading(true);
        try {
          const { data, error } = await supabase
            .rpc('get_user_active_override', { target_user_id: userId });

          if (error) {
            console.error('Error fetching user override:', error);
            setUserOverride(null);
            return;
          }

          setUserOverride(data && data.length > 0 ? data[0] : null);
        } catch (error) {
          console.error('Error fetching user override:', error);
          setUserOverride(null);
        } finally {
          setLoading(false);
        }
      };

      fetchUserOverride();
    } else {
      setUserOverride(null);
    }
  }, [userId, user?.id, refreshTrigger]);

  // Determine which override data to use
  const currentOverride = userId && userId !== user?.id ? userOverride : subscriptionOverride;
  const hasOverride = userId && userId !== user?.id ? !!userOverride : hasActiveOverride;
  const currentEffectiveTier = userId && userId !== user?.id
    ? (userOverride ? userOverride.override_tier : null)
    : effectiveTier;

  if (loading) {
    return (
      <Chip
        label="Loading..."
        size={size}
        variant="outlined"
        color="default"
      />
    );
  }

  if (!hasOverride || !currentOverride) {
    return null;
  }

  const getTierLabel = (tier: string) => {
    switch (tier) {
      case 'pro': return 'Pro';
      case 'edu_pro': return 'Educational Pro';
      case 'edu': return 'Educational';
      case 'standard': return 'Standard';
      case 'guest': return 'Guest';
      default: return tier;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'pro': return 'primary';
      case 'edu_pro': return 'secondary';
      case 'edu': return 'info';
      case 'standard': return 'default';
      default: return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getWarningLevel = (daysRemaining: number) => {
    if (daysRemaining <= 3) return 'error';
    if (daysRemaining <= 7) return 'warning';
    return 'success';
  };

  if (variant === 'chip') {
    const chipContent = (
      <Chip
        icon={<StarIcon />}
        label={`${getTierLabel(currentEffectiveTier)} (Override)`}
        color={getTierColor(currentEffectiveTier) as any}
        size={size}
        variant="filled"
        sx={{
          fontWeight: 'bold',
          '& .MuiChip-icon': {
            color: 'inherit'
          }
        }}
      />
    );

    if (showDetails) {
      return (
        <Tooltip
          title={
            <Box>
              <Typography variant="body2" fontWeight="bold">
                Subscription Override Active
              </Typography>
              <Typography variant="caption" display="block">
                Original: {getTierLabel(currentOverride.original_tier)}
              </Typography>
              <Typography variant="caption" display="block">
                Override: {getTierLabel(currentOverride.override_tier)}
              </Typography>
              <Typography variant="caption" display="block">
                Expires: {formatDate(currentOverride.end_date)}
              </Typography>
              <Typography variant="caption" display="block">
                Days remaining: {currentOverride.days_remaining}
              </Typography>
              {currentOverride.reason && (
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  Reason: {currentOverride.reason}
                </Typography>
              )}
            </Box>
          }
          arrow
        >
          {chipContent}
        </Tooltip>
      );
    }

    return chipContent;
  }

  if (variant === 'inline') {
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Chip
          icon={<StarIcon />}
          label={getTierLabel(currentEffectiveTier)}
          color={getTierColor(currentEffectiveTier) as any}
          size="small"
        />
        <Typography variant="caption" color="text.secondary">
          (Override expires in {currentOverride.days_remaining} days)
        </Typography>
      </Box>
    );
  }

  if (variant === 'card') {
    return (
      <Card 
        sx={{ 
          mb: 2,
          border: `2px solid`,
          borderColor: `${getTierColor(effectiveTier)}.main`,
          bgcolor: `${getTierColor(effectiveTier)}.50`
        }}
      >
        <CardContent sx={{ pb: 2 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <StarIcon color={getTierColor(effectiveTier) as any} />
              <Typography variant="h6" fontWeight="bold">
                Subscription Override Active
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              aria-label="expand details"
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>

          <Box mt={1}>
            <Typography variant="body2" color="text.secondary">
              You have been temporarily upgraded to{' '}
              <strong>{getTierLabel(currentOverride.override_tier)}</strong> access.
            </Typography>

            <Box display="flex" alignItems="center" gap={1} mt={1}>
              <ScheduleIcon
                fontSize="small"
                color={getWarningLevel(currentOverride.days_remaining) as any}
              />
              <Typography
                variant="body2"
                color={`${getWarningLevel(currentOverride.days_remaining)}.main`}
                fontWeight="medium"
              >
                Expires in {currentOverride.days_remaining} days ({formatDate(currentOverride.end_date)})
              </Typography>
            </Box>
          </Box>

          <Collapse in={expanded}>
            <Box mt={2} pt={2} borderTop={1} borderColor="divider">
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Override Details:</strong>
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="caption" color="text.secondary">
                    Original Tier:
                  </Typography>
                  <Chip
                    label={getTierLabel(currentOverride.original_tier)}
                    size="small"
                    variant="outlined"
                  />
                </Box>

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="caption" color="text.secondary">
                    Override Tier:
                  </Typography>
                  <Chip
                    label={getTierLabel(currentOverride.override_tier)}
                    size="small"
                    color={getTierColor(currentOverride.override_tier) as any}
                  />
                </Box>

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="caption" color="text.secondary">
                    Start Date:
                  </Typography>
                  <Typography variant="caption">
                    {formatDate(currentOverride.start_date)}
                  </Typography>
                </Box>

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="caption" color="text.secondary">
                    End Date:
                  </Typography>
                  <Typography variant="caption">
                    {formatDate(currentOverride.end_date)}
                  </Typography>
                </Box>

                {currentOverride.reason && (
                  <Box>
                    <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                      Reason:
                    </Typography>
                    <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                      "{currentOverride.reason}"
                    </Typography>
                  </Box>
                )}

                <Box display="flex" justifyContent="space-between">
                  <Typography variant="caption" color="text.secondary">
                    Granted by:
                  </Typography>
                  <Typography variant="caption">
                    {currentOverride.admin_email}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Collapse>

          {currentOverride.days_remaining <= 7 && (
            <Alert
              severity={getWarningLevel(currentOverride.days_remaining) as any}
              sx={{ mt: 2 }}
              icon={<InfoIcon />}
            >
              <Typography variant="caption">
                {currentOverride.days_remaining <= 3
                  ? 'Your override expires very soon! You will be reverted to your original tier.'
                  : 'Your override expires soon. You will be reverted to your original tier.'}
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  }

  return null;
};

export default OverrideStatusBadge;
