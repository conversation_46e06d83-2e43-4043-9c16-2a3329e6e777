import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Chip,
  LinearProgress,
  Button
} from '@mui/material';
import {
  People as PeopleIcon,
  Storage as StorageIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
  Code as CodeIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { getEnhancedUserStatistics } from '../../utils/adminStats';
import { useNavigate } from 'react-router-dom';

interface SystemStats {
  total_users: number;
  total_profiles: number;
  admin_users: number;
  standard_users: number;
  pro_users: number;
  edu_users: number;
  edu_pro_users: number;
  users_with_datasets: number;
  total_datasets: number;
  users_last_7_days: number;
  users_last_30_days: number;
  active_users_last_7_days: number;
  active_users_last_30_days: number;
}

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    positive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, subtitle, trend }) => {
  const theme = useTheme();
  
  return (
    <Card 
      elevation={0} 
      variant="outlined"
      sx={{ 
        height: '100%',
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4]
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ 
            p: 1.5, 
            borderRadius: 2, 
            backgroundColor: alpha(color, 0.1),
            color: color
          }}>
            {icon}
          </Box>
          {trend && (
            <Chip
              size="small"
              label={trend.label}
              color={trend.positive ? 'success' : 'error'}
              variant="outlined"
            />
          )}
        </Box>
        
        <Typography variant="h4" component="div" fontWeight="bold" color={color} gutterBottom>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>
        
        <Typography variant="h6" color="text.primary" gutterBottom>
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

const AdminOverview: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSystemStats();
  }, []);

  const fetchSystemStats = async (retryCount = 0, forceRefresh = false) => {
    const maxRetries = 3;
    const startTime = Date.now();
    
    try {
      if (retryCount === 0) {
        setLoading(true);
      }
      setError(null);

      // Add connection test before attempting to fetch stats
      if (retryCount === 0) {
        try {
          const { data: connectionTest, error: connectionError } = await supabase.rpc('test_admin_connection');
          if (connectionError || !connectionTest?.success) {
            throw new Error(connectionTest?.error || 'Admin connection test failed');
          }
        } catch (connErr: any) {
          console.warn('Connection test failed, proceeding anyway', connErr);
        }
      }

      // Use enhanced statistics function with fallback calculations
      const data = await getEnhancedUserStatistics();

      // Validate the data before setting it
      if (!data || typeof data.total_users !== 'number') {
        throw new Error('Invalid statistics data received from server');
      }

      setStats(data);
      console.log('✅ System stats loaded successfully');

    } catch (err: any) {
      console.error(`Error fetching system statistics (attempt ${retryCount + 1})`, err);

      // Enhanced retry logic with different strategies
      if (retryCount < maxRetries) {
        const isNetworkError = err.message?.includes('network') ||
                              err.message?.includes('connection') ||
                              err.message?.includes('timeout') ||
                              err.message?.includes('fetch');
        
        const isAuthError = err.message?.includes('Access denied') ||
                           err.message?.includes('Admin privileges');
        
        const isDatabaseError = err.code === '0A000' || // REFRESH MATERIALIZED VIEW error
                               err.code === '42804' || // Type mismatch error
                               err.message?.includes('function');

        if (isNetworkError || isDatabaseError) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff, max 5s
          setTimeout(() => fetchSystemStats(retryCount + 1, forceRefresh), delay);
          return;
        } else if (isAuthError) {
          const authError = 'Access denied. Please ensure you have admin privileges and try logging in again.';
          setError(authError);
          return;
        }
      }

      // Final fallback: show error with helpful message
      let errorMessage = 'Failed to load system statistics.';
      
      if (err.code === '0A000') {
        errorMessage = 'Database function error detected. The migration may need to be applied.';
      } else if (err.code === '42804') {
        errorMessage = 'Database type mismatch error. Please contact support.';
      } else if (err.message?.includes('Access denied')) {
        errorMessage = 'Access denied. Admin privileges required.';
      } else if (err.message?.includes('network') || err.message?.includes('connection')) {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else {
        errorMessage = err.message || errorMessage;
      }
      
      console.error('❌ Failed to load system stats:', err);
      setError(errorMessage);
      
      // Set minimal safe stats to prevent UI crashes
      const safeStats = {
        total_users: 0,
        total_profiles: 0,
        admin_users: 0,
        standard_users: 0,
        pro_users: 0,
        edu_users: 0,
        edu_pro_users: 0,
        users_with_datasets: 0,
        total_datasets: 0,
        users_last_7_days: 0,
        users_last_30_days: 0,
        active_users_last_7_days: 0,
        active_users_last_30_days: 0
      };
      
      setStats(safeStats);
      console.log('📊 Set safe fallback stats to prevent UI crashes');
      
    } finally {
      if (retryCount === 0) {
        setLoading(false);
        console.log(`⏱️ Total fetch time: ${Date.now() - startTime}ms`);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 300 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading system overview...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ width: '100%' }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Error Loading System Statistics
          </Typography>
          <Typography sx={{ mb: 2 }}>
            {error}
          </Typography>
          <Button
            variant="contained"
            size="small"
            onClick={() => fetchSystemStats(0, true)}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        </Alert>
        
        {/* Show partial data if available */}
        {stats && stats.total_users >= 0 && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Showing partial data from fallback calculations. Some features may be limited.
            </Typography>
          </Alert>
        )}
      </Box>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning">
        <Typography>
          No system statistics available.
        </Typography>
      </Alert>
    );
  }

  const userGrowthRate = stats.users_last_30_days > 0 
    ? ((stats.users_last_7_days / stats.users_last_30_days) * 100).toFixed(1)
    : '0';

  const activeUserRate = stats.total_users > 0
    ? ((stats.active_users_last_7_days / stats.total_users) * 100).toFixed(1)
    : '0';

  const datasetUsageRate = stats.total_users > 0
    ? ((stats.users_with_datasets / stats.total_users) * 100).toFixed(1)
    : '0';

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{
        mb: 4,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            System Overview
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Real-time statistics and key metrics for DataStatPro
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => fetchSystemStats()}
          disabled={loading}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {/* Key Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Users"
            value={stats.total_users}
            icon={<PeopleIcon />}
            color={theme.palette.primary.main}
            subtitle="Registered accounts"
            trend={{
              value: stats.users_last_7_days,
              label: `+${stats.users_last_7_days} this week`,
              positive: true
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Active Users"
            value={stats.active_users_last_7_days}
            icon={<TrendingUpIcon />}
            color={theme.palette.success.main}
            subtitle={`${activeUserRate}% of total users`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Admin Users"
            value={stats.admin_users}
            icon={<SecurityIcon />}
            color={theme.palette.error.main}
            subtitle="System administrators"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Datasets"
            value={stats.total_datasets}
            icon={<StorageIcon />}
            color={theme.palette.info.main}
            subtitle={`${stats.users_with_datasets} users with data`}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Pro Users"
            value={stats.pro_users + stats.edu_pro_users}
            icon={<AssessmentIcon />}
            color={theme.palette.warning.main}
            subtitle="Paid subscriptions"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="New Users (30d)"
            value={stats.users_last_30_days}
            icon={<ScheduleIcon />}
            color={theme.palette.secondary.main}
            subtitle={`Growth rate: ${userGrowthRate}%`}
          />
        </Grid>
      </Grid>

      {/* Account Type Breakdown */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Account Type Distribution
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Standard</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.standard_users} ({((stats.standard_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.standard_users / stats.total_users) * 100}
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Pro</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.pro_users} ({((stats.pro_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.pro_users / stats.total_users) * 100}
                  color="warning"
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Educational</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.edu_users} ({((stats.edu_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.edu_users / stats.total_users) * 100}
                  color="info"
                  sx={{ mb: 2, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Educational Pro</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.edu_pro_users} ({((stats.edu_pro_users / stats.total_users) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats.edu_pro_users / stats.total_users) * 100}
                  color="secondary"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card elevation={0} variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                User Engagement
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Data Usage Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {datasetUsageRate}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={parseFloat(datasetUsageRate)}
                  color="success"
                  sx={{ mb: 3, height: 8, borderRadius: 4 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Weekly Active Rate</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {activeUserRate}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={parseFloat(activeUserRate)}
                  color="primary"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Development Tools Section - Only visible in development */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h5" gutterBottom sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            mb: 3
          }}>
            <CodeIcon sx={{ color: theme.palette.secondary.main }} />
            Development Tools
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                elevation={0} 
                variant="outlined" 
                sx={{ 
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  border: `2px solid ${alpha(theme.palette.secondary.main, 0.2)}`,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8],
                    borderColor: theme.palette.secondary.main
                  }
                }}
                onClick={() => navigate('/app/dev-training')}
              >
                <CardContent sx={{ p: 3, textAlign: 'center' }}>
                  <Box sx={{ 
                    p: 2, 
                    borderRadius: 2, 
                    backgroundColor: alpha(theme.palette.secondary.main, 0.1),
                    color: theme.palette.secondary.main,
                    display: 'inline-flex',
                    mb: 2
                  }}>
                    <BuildIcon sx={{ fontSize: '2rem' }} />
                  </Box>
                  
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    AI Training System
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Access the Analysis Assistant training interface for development and testing
                  </Typography>
                  
                  <Chip
                    size="small"
                    label="DEV ONLY"
                    color="secondary"
                    variant="outlined"
                    sx={{ fontWeight: 'bold' }}
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default AdminOverview;
