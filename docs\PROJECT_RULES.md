# DataStatPro Project Rules and Development Standards

## 📋 Overview

This document outlines the comprehensive project rules, coding standards, and development guidelines for DataStatPro. These rules ensure consistency, maintainability, and quality across the entire codebase.

## 🏗️ Project Structure Rules

### Directory Organization

```
DataStatPro/
├── docs/                      # All documentation files
├── src/                       # Source code
│   ├── components/           # React components (organized by feature)
│   ├── context/              # React Context providers
│   ├── hooks/                # Custom React hooks
│   ├── pages/                # Page components
│   ├── routing/              # Route configuration and guards
│   ├── types/                # TypeScript type definitions
│   ├── utils/                # Utility functions and services
│   └── data/                 # Static data files
├── public/                   # Static assets
├── deploy_assets/            # Build output directory
├── scripts/                  # Build and utility scripts
└── supabase/                 # Database migrations and functions
```

### Component Organization Rules

1. **Feature-Based Structure**: Components are organized by feature/domain
2. **Consistent Naming**: Use PascalCase for component files and directories
3. **Index Files**: Each component directory should have an index.ts for clean imports
4. **Co-location**: Keep related files (components, hooks, utils) close together

### File Naming Conventions

- **Components**: `ComponentName.tsx`
- **Hooks**: `useHookName.ts`
- **Utilities**: `utilityName.ts`
- **Types**: `index.ts` (in types directory)
- **Pages**: `PageName.tsx`
- **Tests**: `ComponentName.test.tsx` or `utilityName.test.ts`

## 💻 Coding Standards

### TypeScript Rules

1. **Strict Mode**: Always use TypeScript strict mode
2. **Type Definitions**: Define explicit types for all props, state, and function parameters
3. **Interface vs Type**: Use interfaces for object shapes, types for unions/primitives
4. **Generic Types**: Use meaningful generic type names (not just `T`)
5. **Null Safety**: Handle null/undefined cases explicitly

```typescript
// ✅ Good
interface UserProfile {
  id: string;
  name: string;
  email: string;
  accountType: 'standard' | 'pro' | 'edu';
}

function processUser(user: UserProfile | null): string {
  if (!user) return 'No user provided';
  return `Processing ${user.name}`;
}

// ❌ Bad
function processUser(user: any) {
  return `Processing ${user.name}`;
}
```

### React Component Rules

1. **Functional Components**: Use functional components with hooks
2. **Props Interface**: Define props interface for every component
3. **Default Props**: Use default parameters instead of defaultProps
4. **Component Structure**: Follow consistent component structure

```typescript
// ✅ Component Structure Template
interface ComponentNameProps {
  prop1: string;
  prop2?: number;
  onAction?: () => void;
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2 = 0,
  onAction
}) => {
  // Hooks
  const [state, setState] = useState<StateType>(initialState);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [dependencies]);
  
  // Event handlers
  const handleEvent = useCallback(() => {
    // Handler logic
  }, [dependencies]);
  
  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};
```

### State Management Rules

1. **Context API**: Use React Context for global state
2. **Local State**: Use useState for component-specific state
3. **State Structure**: Keep state flat and normalized
4. **Immutable Updates**: Always create new state objects

### Styling Rules

1. **Material-UI**: Use MUI components and theming system
2. **Theme Consistency**: Use theme variables for colors, spacing, typography
3. **Responsive Design**: Use MUI breakpoints for responsive layouts
4. **Custom Styles**: Use sx prop or styled components for custom styling

```typescript
// ✅ Good - Using theme and sx prop
<Box
  sx={{
    p: 2,
    bgcolor: 'background.paper',
    borderRadius: 1,
    [theme.breakpoints.down('sm')]: {
      p: 1
    }
  }}
>
```

## 🔧 Development Workflow Rules

### Git Workflow

1. **Branch Naming**: Use descriptive branch names
   - `feature/feature-name`
   - `bugfix/bug-description`
   - `hotfix/critical-fix`
   - `docs/documentation-update`

2. **Commit Messages**: Use conventional commit format
   ```
   type(scope): description
   
   feat(auth): add two-factor authentication
   fix(charts): resolve data visualization bug
   docs(api): update API documentation
   ```

3. **Pull Requests**: Include description, testing notes, and screenshots

### Code Quality Rules

1. **ESLint**: All code must pass ESLint checks
2. **TypeScript**: No TypeScript errors allowed
3. **Testing**: Write tests for new features and bug fixes
4. **Code Review**: All changes require code review

### Testing Rules

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test component interactions and context integration
3. **Statistical Tests**: Validate accuracy of statistical calculations
4. **Test Coverage**: Aim for >80% test coverage
5. **Test Structure**: Use describe/it blocks with clear descriptions

```typescript
// ✅ Test Structure Template
describe('ComponentName', () => {
  it('should render correctly with required props', () => {
    // Test implementation
  });
  
  it('should handle user interactions', () => {
    // Test implementation
  });
  
  it('should validate statistical calculations', () => {
    // Test implementation
  });
});
```

## 📊 Data Management Rules

### Data Flow

1. **Single Source of Truth**: Use context providers for shared state
2. **Data Validation**: Validate all user inputs and API responses
3. **Error Handling**: Implement comprehensive error handling
4. **Loading States**: Show loading indicators for async operations

### API Integration

1. **Supabase Client**: Use centralized Supabase client configuration
2. **Error Handling**: Handle network errors gracefully
3. **Authentication**: Implement proper authentication checks
4. **Data Caching**: Cache frequently accessed data

### Statistical Computing

1. **Accuracy**: Validate all statistical calculations
2. **Performance**: Optimize for large datasets
3. **Error Handling**: Handle edge cases (empty data, invalid inputs)
4. **Documentation**: Document statistical methods and assumptions

## 🔒 Security Rules

### Authentication & Authorization

1. **JWT Tokens**: Secure token storage and management
2. **Route Protection**: Implement route guards for protected pages
3. **Role-Based Access**: Implement proper role-based permissions
4. **Session Management**: Handle session expiration gracefully

### Data Security

1. **Input Validation**: Sanitize all user inputs
2. **XSS Protection**: Prevent cross-site scripting attacks
3. **Data Encryption**: Encrypt sensitive data in local storage
4. **Privacy**: Respect user privacy settings

### Environment Security

1. **Environment Variables**: Use environment variables for sensitive configuration
2. **API Keys**: Never commit API keys to version control
3. **HTTPS**: Always use HTTPS in production
4. **Content Security Policy**: Implement proper CSP headers

## 🚀 Performance Rules

### Code Optimization

1. **Bundle Splitting**: Use code splitting for large components
2. **Lazy Loading**: Implement lazy loading for routes and components
3. **Memoization**: Use React.memo, useMemo, useCallback appropriately
4. **Tree Shaking**: Ensure unused code is eliminated

### Asset Optimization

1. **Image Optimization**: Use WebP format and appropriate sizes
2. **Font Loading**: Optimize font loading strategies
3. **Caching**: Implement proper caching strategies
4. **Compression**: Enable gzip/brotli compression

## 📱 PWA Rules

### Service Worker

1. **Caching Strategy**: Implement appropriate caching strategies
2. **Offline Support**: Provide meaningful offline functionality
3. **Update Handling**: Handle service worker updates gracefully
4. **Background Sync**: Implement background synchronization

### Mobile Optimization

1. **Responsive Design**: Ensure all components work on mobile
2. **Touch Interactions**: Optimize for touch interfaces
3. **Performance**: Optimize for mobile performance
4. **Accessibility**: Ensure mobile accessibility

## 📚 Documentation Rules

### Code Documentation

1. **JSDoc Comments**: Document complex functions and components
2. **Type Documentation**: Document complex types and interfaces
3. **API Documentation**: Document all API endpoints and responses
4. **README Files**: Maintain up-to-date README files

### User Documentation

1. **User Guide**: Maintain comprehensive user documentation
2. **API Reference**: Keep API documentation current
3. **Tutorials**: Provide step-by-step tutorials
4. **Changelog**: Document all changes and updates

## 🔄 Build and Deployment Rules

### Build Process

1. **Automated Builds**: Use automated build processes
2. **Environment Configuration**: Separate development and production configs
3. **Asset Copying**: Ensure proper asset copying (htaccess, social-meta.php)
4. **Bundle Analysis**: Regular bundle size analysis

### Deployment

1. **Environment Variables**: Proper environment variable management
2. **Database Migrations**: Handle database schema changes properly
3. **Rollback Strategy**: Implement rollback procedures
4. **Monitoring**: Monitor application performance and errors

## 🧪 Quality Assurance Rules

### Code Review

1. **Review Checklist**: Use standardized review checklist
2. **Security Review**: Review for security vulnerabilities
3. **Performance Review**: Check for performance implications
4. **Documentation Review**: Ensure documentation is updated

### Testing Strategy

1. **Test-Driven Development**: Write tests before implementation when possible
2. **Regression Testing**: Test for regressions with each change
3. **User Acceptance Testing**: Validate features meet user requirements
4. **Performance Testing**: Test application performance regularly

## 🎯 AI Assistant Integration Rules

### Documentation for AI

1. **Comprehensive Context**: Provide complete context in documentation
2. **Code Examples**: Include practical code examples
3. **Architecture Patterns**: Document architectural decisions
4. **Type Definitions**: Maintain complete type definitions

### AI-Friendly Code

1. **Clear Naming**: Use descriptive variable and function names
2. **Consistent Patterns**: Follow consistent coding patterns
3. **Modular Design**: Keep functions and components focused
4. **Documentation**: Include inline documentation for complex logic

## 📋 Checklist for New Features

### Development Checklist

- [ ] Feature branch created with descriptive name
- [ ] TypeScript types defined
- [ ] Component/function implemented
- [ ] Unit tests written
- [ ] Integration tests added
- [ ] Error handling implemented
- [ ] Loading states added
- [ ] Responsive design verified
- [ ] Accessibility tested
- [ ] Performance optimized
- [ ] Documentation updated
- [ ] Code review completed
- [ ] ESLint checks passed
- [ ] TypeScript compilation successful
- [ ] All tests passing

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Build process successful
- [ ] Assets properly copied
- [ ] Performance tested
- [ ] Security review completed
- [ ] User acceptance testing passed
- [ ] Rollback plan prepared
- [ ] Monitoring configured
- [ ] Documentation deployed

## 🔧 Tools and Configuration

### Required Tools

- **Node.js**: >=16.0.0
- **npm**: >=8.0.0
- **TypeScript**: ^5.0.2
- **Vite**: ^5.4.19
- **ESLint**: ^6.8.0

### Development Scripts

```bash
# Development
npm run dev                 # Start development server
npm run build              # Build for production
npm run build:check        # Build with TypeScript check
npm run lint               # Run ESLint
npm run test               # Run tests
npm run preview            # Preview production build
```

### Browser Support

- **Production**: >0.2%, not dead, not op_mini all
- **Development**: Latest Chrome, Firefox, Safari

## 📞 Support and Resources

### Getting Help

1. **Documentation**: Check existing documentation first
2. **Code Examples**: Look for similar implementations
3. **Team Review**: Discuss with team members
4. **Issue Tracking**: Create issues for bugs and feature requests

### Resources

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Material-UI Documentation](https://mui.com/)
- [Vite Documentation](https://vitejs.dev/)
- [Supabase Documentation](https://supabase.com/docs)

---

**Note**: These rules are living guidelines that should be updated as the project evolves. All team members are responsible for maintaining and improving these standards.