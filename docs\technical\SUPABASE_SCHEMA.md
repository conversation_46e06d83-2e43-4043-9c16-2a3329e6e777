# Supabase Database Schema Documentation

## Overview

This document outlines the Supabase database schema used in the DataStatPro application, with a focus on the user authentication and profile management system.

## Database Structure

### Auth Schema

The `auth` schema is managed by Supabase and contains tables related to user authentication:

- `auth.users` - Contains user authentication information (email, password hash, etc.)

### Public Schema

The `public` schema contains application-specific tables:

- `public.profiles` - Stores additional user profile information linked to `auth.users`
- `public.user_datasets` - Stores metadata about user datasets with references to storage objects

## Recent Schema Updates

The database schema has been updated to align with changes in the registration form:

1. **Removed the `gender` field** from the `profiles` table as it's no longer used in the registration form
2. **Ensured the `country` field** is properly configured to store values from the country dropdown

## Profiles Table Structure

The current structure of the `profiles` table is:

```sql
create table public.profiles (
  id uuid not null references auth.users on delete cascade,
  username text,
  full_name text,
  institution text,
  country text,
  avatar_url text,
  updated_at timestamp with time zone,
  primary key (id)
);

alter table public.profiles enable row level security;
```

## User Datasets Table Structure

The structure of the `user_datasets` table is:

```sql
CREATE TABLE public.user_datasets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  dataset_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,

  CONSTRAINT unique_user_dataset_name UNIQUE (user_id, dataset_name)
);

ALTER TABLE public.user_datasets ENABLE ROW LEVEL SECURITY;
```

## Storage Buckets

The application uses Supabase Storage for storing user data:

- `avatars` - Stores user profile avatars
- `userdatasets` - Stores user dataset files in JSON format

## User Registration Flow

1. When a user registers through the application, their authentication information is stored in `auth.users`
2. A database trigger (`on_auth_user_created`) automatically creates a corresponding record in `public.profiles`
3. The trigger extracts metadata provided during registration (full_name, institution, country) and stores it in the profile

## Database Trigger

The application uses a trigger to automatically create profile records:

```sql
CREATE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    username, 
    full_name, 
    institution, 
    country, 
    avatar_url, 
    updated_at
  ) VALUES (
    new.id, 
    new.email, -- Default username to email
    new.raw_user_meta_data->>'full_name', 
    new.raw_user_meta_data->>'institution', 
    new.raw_user_meta_data->>'country',
    null, -- Default avatar_url to null
    now()
  );
  RETURN new;
END;
$$;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

## Registration Form Integration

The registration form in `Register.tsx` collects the following user information:

- Email address
- Password
- Full name
- Institution (optional)
- Country (selected from dropdown)

This information is passed to the Supabase `signUp` function as metadata, which is then extracted by the trigger to populate the `profiles` table.

## Row Level Security (RLS)

The `profiles` table has Row Level Security enabled to protect user data. This ensures that users can only access their own profile information.

The `user_datasets` table also has Row Level Security enabled, with policies that ensure users can only access, modify, and delete their own datasets.

The storage buckets (`avatars` and `userdatasets`) have Row Level Security enabled with policies that ensure users can only upload, access, modify, and delete their own files.

## Migration Files

Migration scripts for database changes are stored in the `supabase/migrations/` directory. Recent migrations include:

- `20240510_update_profiles_table.sql` - Removes the gender field and ensures the country field is properly configured
- `20250518144129_add_avatar_rls.sql` - Adds RLS policies for the avatars bucket
- `20250518144130_fix_avatar_rls.sql` - Fixes RLS policies for the avatars bucket
- `20250518144131_fix_avatar_upload_rls.sql` - Further fixes for avatar upload RLS
- `20250619145730_fix_user_datasets_rls.sql` - Fixes RLS policies for the user_datasets table
- `20250621160000_fix_user_datasets_and_storage_rls.sql` - Comprehensive fix for RLS policies for both user_datasets table and userdatasets storage bucket