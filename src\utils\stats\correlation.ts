import jStat from 'jstat';

// Common correlation result interface
export interface CorrelationResult {
  r: number;
  pValue: number;
  n: number;
  rSquared: number;
}

// Calculate Pearson correlation coefficient
export const calculatePearsonCorrelation = (
  x: number[],
  y: number[]
): CorrelationResult => {
  if (x.length !== y.length) {
    throw new Error('Arrays must have the same length');
  }
  
  if (x.length < 3) {
    throw new Error('Sample size must be at least 3');
  }
  
  const n = x.length;
  const r = jStat.corrcoeff(x, y);
  
  // Calculate t-statistic for correlation
  const t = r * Math.sqrt((n - 2) / (1 - r * r));
  
  // Calculate two-tailed p-value
  const pValue = 2 * (1 - jStat.studentt.cdf(Math.abs(t), n - 2));
  
  // Calculate r-squared (coefficient of determination)
  const rSquared = r * r;
  
  return { r, pValue, n, rSquared };
};

// Calculate Spearman rank correlation coefficient
export const calculateSpearmanCorrelation = (
  x: number[],
  y: number[]
): CorrelationResult => {
  if (x.length !== y.length) {
    throw new Error('Arrays must have the same length');
  }
  
  if (x.length < 3) {
    throw new Error('Sample size must be at least 3');
  }
  
  const n = x.length;
  
  // Create pairs for ranking
  const pairs = x.map((xVal, i) => ({ x: xVal, y: y[i] }));
  
  // Rank x values
  const xSorted = [...pairs].sort((a, b) => a.x - b.x);
  const xRanks: Record<number, number> = {};
  
  let index = 0;
  while (index < n) {
    let j = index;
    while (j < n - 1 && xSorted[j].x === xSorted[j + 1].x) {
      j++;
    }
    const avgRank = (index + j) / 2 + 1;
    for (let k = index; k <= j; k++) {
      xRanks[xSorted[k].x] = avgRank;
    }
    index = j + 1;
  }
  
  // Rank y values
  const ySorted = [...pairs].sort((a, b) => a.y - b.y);
  const yRanks: Record<number, number> = {};
  
  index = 0;
  while (index < n) {
    let j = index;
    while (j < n - 1 && ySorted[j].y === ySorted[j + 1].y) {
      j++;
    }
    const avgRank = (index + j) / 2 + 1;
    for (let k = index; k <= j; k++) {
      yRanks[ySorted[k].y] = avgRank;
    }
    index = j + 1;
  }
  
  // Calculate Spearman's rho using Pearson correlation on ranks
  const xRankValues = pairs.map(p => xRanks[p.x]);
  const yRankValues = pairs.map(p => yRanks[p.y]);
  
  return calculatePearsonCorrelation(xRankValues, yRankValues);
};

// Calculate Kendall's tau correlation coefficient
export const calculateKendallCorrelation = (
  x: number[],
  y: number[]
): CorrelationResult => {
  if (x.length !== y.length) {
    throw new Error('Arrays must have the same length');
  }
  
  if (x.length < 3) {
    throw new Error('Sample size must be at least 3');
  }
  
  const n = x.length;
  let concordant = 0;
  let discordant = 0;
  let tiesX = 0;
  let tiesY = 0;
  let tiesXY = 0;
  
  // Count concordant and discordant pairs
  for (let i = 0; i < n - 1; i++) {
    for (let j = i + 1; j < n; j++) {
      const xDiff = x[i] - x[j];
      const yDiff = y[i] - y[j];
      
      if (xDiff === 0 && yDiff === 0) {
        tiesXY++;
      } else if (xDiff === 0) {
        tiesX++;
      } else if (yDiff === 0) {
        tiesY++;
      } else if ((xDiff > 0 && yDiff > 0) || (xDiff < 0 && yDiff < 0)) {
        concordant++;
      } else {
        discordant++;
      }
    }
  }
  
  // Calculate Kendall's tau-b (accounts for ties)
  const totalPairs = n * (n - 1) / 2;
  const denominator = Math.sqrt((totalPairs - tiesX) * (totalPairs - tiesY));
  const tau = denominator === 0 ? 0 : (concordant - discordant) / denominator;
  
  // Approximate p-value using normal approximation for large samples
  let pValue: number;
  if (n < 10) {
    // For small samples, use a conservative approach
    pValue = 0.05; // Conservative estimate
  } else {
    // Normal approximation for large samples
    const variance = (2 * (2 * n + 5)) / (9 * n * (n - 1));
    const z = tau / Math.sqrt(variance);
    pValue = 2 * (1 - jStat.normal.cdf(Math.abs(z), 0, 1));
  }
  
  return {
    r: tau,
    pValue,
    n,
    rSquared: tau * tau
  };
};

// Calculate correlation matrix for multiple variables
export const calculateCorrelationMatrix = (
  variables: { [key: string]: number[] },
  method: 'pearson' | 'spearman' | 'kendall' = 'pearson'
): {
  matrix: { [key: string]: { [key: string]: CorrelationResult } };
  variables: string[];
} => {
  const variableNames = Object.keys(variables);
  const matrix: { [key: string]: { [key: string]: CorrelationResult } } = {};
  
  // Initialize matrix
  variableNames.forEach(var1 => {
    matrix[var1] = {};
    variableNames.forEach(var2 => {
      if (var1 === var2) {
        matrix[var1][var2] = { r: 1.0, pValue: 0.0, n: variables[var1].length, rSquared: 1.0 };
      } else {
        let correlationResult: CorrelationResult;
        
        try {
          switch (method) {
            case 'spearman':
              correlationResult = calculateSpearmanCorrelation(variables[var1], variables[var2]);
              break;
            case 'kendall':
              correlationResult = calculateKendallCorrelation(variables[var1], variables[var2]);
              break;
            default:
              correlationResult = calculatePearsonCorrelation(variables[var1], variables[var2]);
          }
        } catch (error) {
          correlationResult = { r: 0, pValue: 1, n: 0, rSquared: 0 };
        }
        
        matrix[var1][var2] = correlationResult;
      }
    });
  });
  
  return { matrix, variables: variableNames };
};
