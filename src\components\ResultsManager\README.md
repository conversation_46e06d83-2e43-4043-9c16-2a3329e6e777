# Results Summary Generator

The Results Summary Generator is an advanced feature that automatically compiles key findings from your analysis results and generates comprehensive executive summaries with effect sizes and confidence intervals.

## Features

### Core Functionality
- **Auto-compilation of Key Findings**: Automatically extracts and summarizes statistical results
- **Executive Summary Generation**: Creates professional, publication-ready summaries
- **Effect Size Integration**: Includes <PERSON>'s d, eta-squared, and other effect size measures
- **Confidence Intervals**: Automatically calculates and displays confidence intervals
- **Multiple Export Formats**: Export to Markdown, HTML, Word, PDF, and JSON

### Summary Templates
- **Executive Template**: Concise overview for stakeholders
- **Detailed Template**: Comprehensive analysis for researchers
- **APA Style Template**: Publication-ready format following APA guidelines
- **Custom Templates**: Create your own summary formats

### Export Options
- Markdown (.md)
- HTML (.html)
- Microsoft Word (.docx)
- PDF (.pdf)
- JSON (.json)
- Plain Text (.txt)
- Email sharing
- Copy to clipboard

## Usage

### Basic Usage
1. Select analysis results in the Results Manager
2. Click the "Generate Summary" button
3. Choose your preferred template
4. Configure summary options
5. Generate and export your summary

### Advanced Configuration
- **Include/Exclude Sections**: Choose which sections to include
- **Statistical Detail Level**: Control the depth of statistical reporting
- **Effect Size Preferences**: Select which effect sizes to display
- **Confidence Level**: Set confidence interval levels (90%, 95%, 99%)
- **Formatting Options**: Customize appearance and style

## Components

### ResultsSummaryGenerator
Main component that provides the summary generation interface.

```typescript
<ResultsSummaryGenerator
  open={summaryGeneratorOpen}
  onClose={() => setSummaryGeneratorOpen(false)}
  selectedResults={getSelectedResults()}
/>
```

### SummaryDisplay
Component for displaying generated summaries with export options.

### SummaryTemplateEngine
Engine for processing templates and generating formatted summaries.

### SummaryExporter
Handles various export formats and sharing options.

## File Structure

```
ResultsManager/
├── ResultsSummaryGenerator.tsx    # Main summary generator component
├── SummaryDisplay.tsx             # Summary display component
├── summaryTemplates.ts            # Template definitions and engine
├── summaryExport.ts               # Export functionality
├── index.ts                       # Component exports
└── README.md                      # This documentation
```

## Integration

The Results Summary Generator is fully integrated with the existing Results Manager and leverages:
- Results Context for data access
- Authentication Context for feature access
- Material-UI for consistent styling
- Existing export infrastructure

## Development

### Adding New Templates
1. Define template structure in `summaryTemplates.ts`
2. Add template processing logic
3. Update template selector in UI

### Adding Export Formats
1. Implement export function in `summaryExport.ts`
2. Add format option to UI
3. Update export handler

### Customizing Summary Generation
1. Modify extraction functions in `ResultsSummaryGenerator.tsx`
2. Update statistical processing logic
3. Enhance formatting options

## Best Practices

- Always include effect sizes when available
- Provide confidence intervals for key statistics
- Use appropriate statistical language
- Follow APA guidelines for statistical reporting
- Include sample sizes and degrees of freedom
- Clearly state assumptions and limitations

## Future Enhancements

- AI-powered summary generation
- Interactive summary editing
- Collaborative summary features
- Version control for summaries
- Integration with reference managers
- Custom branding options