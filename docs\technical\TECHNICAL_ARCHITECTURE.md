# DataStatPro Technical Architecture Documentation

## Overview

DataStatPro is a modern React-based statistical analysis web application built with TypeScript, featuring a comprehensive suite of statistical tools, data visualization capabilities, and educational resources. This document provides a detailed technical overview for developers and AI assistants working with the codebase.

## 🏗️ Architecture Overview

### Core Architecture Pattern
- **Frontend**: React 18 with TypeScript
- **State Management**: React Context API with custom hooks
- **Routing**: React Router v6 with centralized route configuration
- **UI Framework**: Material-UI (MUI) v5 with custom theming
- **Build Tool**: Vite for fast development and optimized builds
- **PWA**: Service Worker with Workbox for offline functionality

### Key Architectural Decisions
1. **Context-based State Management**: Four main contexts (Auth, Data, Theme, Results)
2. **Modular Route System**: Centralized route configuration with lazy loading
3. **Component-based Architecture**: Highly modular UI components
4. **Service Layer**: Utility services for complex operations
5. **Type-safe Development**: Comprehensive TypeScript interfaces

## 📁 Project Structure

```
src/
├── components/           # UI Components (organized by feature)
│   ├── Account/         # User account management
│   ├── Admin/           # Administrative interfaces
│   ├── Advanced/        # Advanced statistical analysis
│   ├── Auth/            # Authentication components
│   ├── Correlation/     # Correlation analysis
│   ├── Dashboard/       # Main dashboard
│   ├── DataManagement/ # Data import/export/editing
│   ├── DescriptiveStats/# Descriptive statistics
│   ├── EpiCalc/         # Epidemiological calculator
│   ├── InferentialStats/# Inferential statistics
│   ├── Layout/          # App layout components
│   ├── SampleSize/      # Sample size calculators
│   ├── UI/              # Reusable UI components
│   └── Visualization/   # Data visualization
├── contexts/            # React contexts
│   ├── AuthContext.tsx # Authentication state
│   ├── DataContext.tsx # Dataset management
│   ├── ThemeContext.tsx# Theme management
│   └── ResultsContext.tsx# Analysis results
├── hooks/               # Custom React hooks
├── routes/              # Route definitions by feature
├── types/               # TypeScript type definitions
├── utils/               # Utility functions and services
│   ├── services/        # Business logic services
│   └── stats/           # Statistical computation modules
└── App.tsx              # Main application component
```

## 🔧 Core Systems

### 1. Authentication System (`AuthContext.tsx`)

**Purpose**: Manages user authentication, profiles, and access control

**Key Features**:
- Supabase integration for authentication
- Guest user support
- Subscription management
- Admin role detection
- Google OAuth integration

**Key Methods**:
```typescript
interface AuthContextType {
  session: Session | null
  user: User | null
  profile: UserProfile | null
  signIn: (email: string, password: string) => Promise<AuthResponse>
  signUp: (email: string, password: string, userData?: any) => Promise<AuthResponse>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
  // ... other methods
}
```

### 2. Data Management System (`DataContext.tsx`)

**Purpose**: Manages datasets, columns, rows, and data operations

**Key Features**:
- Multiple dataset support
- Real-time data validation
- Column type detection
- Data transformation pipeline
- Import/export functionality

**Key Data Structures**:
```typescript
interface Dataset {
  id: string
  name: string
  columns: Column[]
  rows: DataRow[]
  metadata?: DatasetMetadata
}

interface Column {
  id: string
  name: string
  type: DataType
  role?: VariableRole
  statistics?: ColumnStatistics
}
```

### 3. Theme System (`ThemeContext.tsx`)

**Purpose**: Manages application theming and dark/light mode

**Features**:
- System preference detection
- Persistent theme storage
- Material-UI theme integration
- Custom color palettes

### 4. Results Management (`ResultsContext.tsx`)

**Purpose**: Manages statistical analysis results and visualizations

**Features**:
- Result caching
- Export functionality
- Result comparison
- Visualization state management

## 🛠️ Key Utilities and Services

### Statistical Computing (`utils/stats/`)

**Modules**:
- `correlation.ts`: Correlation analysis (Pearson, Spearman, Kendall)
- `descriptive.ts`: Descriptive statistics calculations
- `inference.ts`: Inferential statistics (t-tests, ANOVA, etc.)
- `regression.ts`: Linear and logistic regression
- `nonParametric.ts`: Non-parametric tests
- `sampleSize.ts`: Sample size and power calculations

### Data Processing (`utils/`)

**Key Files**:
- `dataAnalysis.ts`: Core data analysis utilities
- `missingData.ts`: Missing data detection and handling
- `typeConversion.ts`: Data type conversion utilities
- `pasteParser.ts`: Clipboard data parsing
- `sampleDataGenerator.ts`: Sample dataset generation

### Services (`utils/services/`)

**Advanced Analysis Services**:
- `factorAnalysisService.ts`: EFA/CFA implementations
- `coxRegressionService.ts`: Survival analysis
- `mediationModerationService.ts`: Path analysis
- `datasetService.ts`: Dataset management operations

## 🎨 UI Component System

### Design System
- **Base Components**: Located in `components/UI/`
- **Feature Components**: Organized by functionality
- **Layout Components**: Header, sidebar, main content areas
- **Form Components**: Standardized form inputs and validation

### Key UI Patterns
1. **Responsive Design**: Mobile-first approach with breakpoints
2. **Accessibility**: ARIA labels, keyboard navigation
3. **Loading States**: Skeleton loaders and progress indicators
4. **Error Handling**: User-friendly error messages
5. **Tooltips**: Contextual help throughout the interface

## 🔄 Data Flow

### Typical Analysis Workflow
1. **Data Import**: User imports data via DataContext
2. **Data Validation**: Automatic type detection and validation
3. **Analysis Selection**: User selects statistical method
4. **Computation**: Statistical calculations performed
5. **Results Display**: Results stored in ResultsContext
6. **Visualization**: Charts generated and displayed
7. **Export**: Results exported in various formats

### State Management Flow
```
User Action → Component → Context → Service → Utility → Result → UI Update
```

## 🚀 Build and Deployment

### Development Setup
```bash
npm install
npm run dev
```

### Build Process
- **Vite Configuration**: Optimized for production builds
- **PWA Generation**: Automatic service worker generation
- **Asset Optimization**: Image compression, code splitting
- **Deploy Assets**: Automatic copying of `.htaccess` and PHP files

### Environment Configuration
- **Development**: Local Supabase instance
- **Production**: Cloud Supabase with CDN
- **Environment Variables**: Managed via `.env` files

## 🧪 Testing Strategy

### Testing Structure
- **Unit Tests**: Individual component and utility testing
- **Integration Tests**: Context and service integration
- **E2E Tests**: Complete user workflow testing
- **Statistical Validation**: Accuracy testing for calculations

## 🔐 Security Considerations

### Authentication Security
- **JWT Tokens**: Secure token management
- **Row Level Security**: Supabase RLS policies
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Sanitized user inputs

### Data Privacy
- **Local Storage**: Sensitive data encrypted
- **Cloud Storage**: Secure Supabase integration
- **Guest Mode**: No data persistence for guests
- **GDPR Compliance**: User data management tools

## 📊 Performance Optimization

### Frontend Optimization
- **Code Splitting**: Route-based lazy loading
- **Memoization**: React.memo and useMemo usage
- **Virtual Scrolling**: Large dataset handling
- **Debounced Inputs**: Reduced API calls

### Statistical Computing
- **Web Workers**: Heavy calculations in background
- **Caching**: Result caching for repeated analyses
- **Streaming**: Large dataset processing
- **Progressive Loading**: Incremental data loading

## 🔧 Development Guidelines

### Code Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Consistent code formatting
- **Prettier**: Automatic code formatting
- **Conventional Commits**: Standardized commit messages

### Component Development
1. **Props Interface**: Always define TypeScript interfaces
2. **Error Boundaries**: Wrap components in error boundaries
3. **Loading States**: Handle loading and error states
4. **Accessibility**: Include ARIA attributes
5. **Documentation**: JSDoc comments for complex components

### Adding New Statistical Methods
1. **Create Utility Function**: Add to appropriate `utils/stats/` module
2. **Define Types**: Add interfaces to `types/index.ts`
3. **Create Component**: Build UI component in relevant feature folder
4. **Add Route**: Register route in `routes/` configuration
5. **Update Navigation**: Add to sidebar or dashboard
6. **Write Tests**: Unit tests for calculations
7. **Documentation**: Update user guides

## 🔮 Future Considerations

### Planned Enhancements
- **Real-time Collaboration**: Multi-user dataset editing
- **Advanced Visualizations**: 3D plots, interactive dashboards
- **Machine Learning**: Integration of ML algorithms
- **API Development**: RESTful API for external integrations
- **Mobile App**: Native mobile application

### Technical Debt
- **Legacy Components**: Gradual migration to modern patterns
- **Performance**: Optimization of large dataset handling
- **Testing Coverage**: Increased test coverage
- **Documentation**: Comprehensive API documentation

This documentation serves as a comprehensive guide for understanding the DataStatPro codebase and should be updated as the application evolves.