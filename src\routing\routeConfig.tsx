// Central route configuration and initialization

import { routeRegistry } from './RouteRegistry';
import { statisticsRoutes } from './routes/statisticsRoutes';
import { dataManagementRoutes } from './routes/dataManagementRoutes';
import { correlationRoutes } from './routes/correlationRoutes';
import { advancedRoutes } from './routes/advancedRoutes';
import { visualizationRoutes } from './routes/visualizationRoutes';
import { coreRoutes } from './routes/coreRoutes';
import { adminRoutes } from './routes/adminRoutes';
import { getDevRoutes } from './routes/devRoutes';

// Re-export routeRegistry for external use
export { routeRegistry };

/**
 * Initialize all routes
 */
export function initializeRoutes() {
  // Clear existing routes
  routeRegistry.clear();
  
  // Register all route modules
  routeRegistry.registerRoutes(coreRoutes);
  routeRegistry.registerRoutes(dataManagementRoutes);
  routeRegistry.registerRoutes(statisticsRoutes);
  routeRegistry.registerRoutes(correlationRoutes);
  routeRegistry.registerRoutes(advancedRoutes);
  routeRegistry.registerRoutes(visualizationRoutes);
  routeRegistry.registerRoutes(adminRoutes);
  
  // Add development routes in development environment
  if (process.env.NODE_ENV === 'development') {
    console.log('Registering development routes for development environment');
    const devRoutes = getDevRoutes();
    routeRegistry.registerRoutes(devRoutes);
  }
  
  console.log(`Total routes registered: ${routeRegistry.getAllRoutes().length}`);
}

// Export the route registry for use in other parts of the application
export { routeRegistry };