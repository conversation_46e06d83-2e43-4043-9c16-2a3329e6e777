import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Slider,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import { supabase } from '../../utils/supabaseClient';

interface User {
  id: string;
  email: string | null;
  username: string | null;
  full_name: string | null;
  accounttype: string | null;
}

interface QuickOverrideDialogProps {
  open: boolean;
  user: User | null;
  onClose: () => void;
  onSuccess: () => void;
}

const QuickOverrideDialog: React.FC<QuickOverrideDialogProps> = ({
  open,
  user,
  onClose,
  onSuccess
}) => {
  const [overrideTier, setOverrideTier] = useState<string>('pro');
  const [duration, setDuration] = useState<number>(1);
  const [reason, setReason] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleClose = () => {
    setOverrideTier('pro');
    setDuration(1);
    setReason('');
    setError(null);
    onClose();
  };

  const handleCreate = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.rpc('create_subscription_override', {
        target_user_id: user.id,
        override_tier_param: overrideTier,
        duration_months: duration,
        reason_param: reason || null
      });

      if (error) throw error;

      onSuccess();
      handleClose();
    } catch (err: any) {
      console.error('Error creating override:', err);
      setError(err.message || 'Failed to create override');
    } finally {
      setLoading(false);
    }
  };

  const getTierLabel = (tier: string) => {
    switch (tier) {
      case 'pro': return 'Pro';
      case 'edu_pro': return 'Educational Pro';
      case 'edu': return 'Educational';
      case 'standard': return 'Standard';
      default: return tier;
    }
  };

  const getAvailableTiers = () => {
    const currentTier = user?.accounttype || 'standard';
    const tiers = ['standard', 'edu', 'edu_pro', 'pro'];
    const currentIndex = tiers.indexOf(currentTier);
    
    // Only allow upgrades
    return tiers.slice(currentIndex + 1);
  };

  const availableTiers = getAvailableTiers();

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Create Override for {user?.full_name || user?.email || 'User'}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Current Tier: <strong>{getTierLabel(user?.accounttype || 'standard')}</strong>
            </Typography>
          </Box>

          {availableTiers.length === 0 ? (
            <Alert severity="info">
              This user is already at the highest tier (Pro) and cannot be upgraded further.
            </Alert>
          ) : (
            <>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Override Tier</InputLabel>
                <Select
                  value={overrideTier}
                  label="Override Tier"
                  onChange={(e) => setOverrideTier(e.target.value)}
                >
                  {availableTiers.map((tier) => (
                    <MenuItem key={tier} value={tier}>
                      {getTierLabel(tier)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box sx={{ mb: 3 }}>
                <Typography gutterBottom>
                  Duration: {duration} month{duration !== 1 ? 's' : ''}
                </Typography>
                <Slider
                  value={duration}
                  onChange={(_, newValue) => setDuration(newValue as number)}
                  min={1}
                  max={12}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>

              <TextField
                fullWidth
                label="Reason (Optional)"
                multiline
                rows={3}
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Enter reason for this override..."
                sx={{ mb: 2 }}
              />
            </>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        {availableTiers.length > 0 && (
          <Button
            onClick={handleCreate}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Creating...' : 'Create Override'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default QuickOverrideDialog;
