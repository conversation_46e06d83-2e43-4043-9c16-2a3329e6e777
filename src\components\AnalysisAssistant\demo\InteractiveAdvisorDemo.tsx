import React, { useState } from 'react';
import {
  <PERSON>,
  Ty<PERSON>graphy,
  Card,
  CardContent,
  Button,
  ToggleButton,
  ToggleButtonGroup,
  Alert,
  Chip,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Star as StarIcon,
  ExploreIcon,
  Dataset as DatasetIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { AuthContext } from '../../../context/AuthContext';
import { ResultsProvider } from '../../../context/ResultsContext';
import InteractiveStatisticalAdvisor from '../InteractiveStatisticalAdvisor';

// Mock datasets for testing
const mockDatasets = {
  survey: {
    name: 'Customer Satisfaction Survey',
    columns: ['age', 'gender', 'satisfaction_score', 'department', 'years_employed'],
    rows: 250,
    analysis: {
      totalRows: 250,
      variableAnalysis: [
        {
          name: 'age',
          type: 'numeric',
          uniqueValues: Array.from({length: 40}, (_, i) => i + 22),
          missingCount: 3,
          distribution: { mean: 38.5, median: 37, std: 11.2 }
        },
        {
          name: 'gender',
          type: 'categorical',
          uniqueValues: ['Male', 'Female', 'Other'],
          missingCount: 0,
          categories: [
            { value: 'Male', count: 120, percentage: 48 },
            { value: 'Female', count: 125, percentage: 50 },
            { value: 'Other', count: 5, percentage: 2 }
          ]
        },
        {
          name: 'satisfaction_score',
          type: 'numeric',
          uniqueValues: Array.from({length: 10}, (_, i) => i + 1),
          missingCount: 5,
          distribution: { mean: 7.2, median: 7, std: 1.8 }
        },
        {
          name: 'department',
          type: 'categorical',
          uniqueValues: ['Sales', 'Marketing', 'IT', 'HR', 'Finance'],
          missingCount: 0,
          categories: [
            { value: 'Sales', count: 60, percentage: 24 },
            { value: 'Marketing', count: 45, percentage: 18 },
            { value: 'IT', count: 55, percentage: 22 },
            { value: 'HR', count: 40, percentage: 16 },
            { value: 'Finance', count: 50, percentage: 20 }
          ]
        },
        {
          name: 'years_employed',
          type: 'numeric',
          uniqueValues: Array.from({length: 25}, (_, i) => i + 1),
          missingCount: 2,
          distribution: { mean: 8.3, median: 7, std: 6.1 }
        }
      ]
    }
  },
  clinical: {
    name: 'Clinical Trial Data',
    columns: ['patient_id', 'treatment_group', 'baseline_score', 'followup_score', 'age', 'gender'],
    rows: 180,
    analysis: {
      totalRows: 180,
      variableAnalysis: [
        {
          name: 'treatment_group',
          type: 'categorical',
          uniqueValues: ['Control', 'Treatment A', 'Treatment B'],
          missingCount: 0,
          categories: [
            { value: 'Control', count: 60, percentage: 33.3 },
            { value: 'Treatment A', count: 60, percentage: 33.3 },
            { value: 'Treatment B', count: 60, percentage: 33.4 }
          ]
        },
        {
          name: 'baseline_score',
          type: 'numeric',
          uniqueValues: Array.from({length: 50}, (_, i) => i + 20),
          missingCount: 0,
          distribution: { mean: 45.2, median: 44, std: 12.8 }
        },
        {
          name: 'followup_score',
          type: 'numeric',
          uniqueValues: Array.from({length: 60}, (_, i) => i + 15),
          missingCount: 8,
          distribution: { mean: 52.1, median: 51, std: 15.3 }
        }
      ]
    }
  }
};

// Mock auth contexts for different user types
const authContexts = {
  guest: {
    user: null,
    isGuest: true,
    accountType: null,
    effectiveTier: 'guest',
    canAccessAdvancedAnalysis: false,
    canAccessPublicationReady: false,
    isEducationalUser: false,
    educationalTier: null
  },
  standard: {
    user: { id: '1', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'standard',
    effectiveTier: 'standard',
    canAccessAdvancedAnalysis: false,
    canAccessPublicationReady: false,
    isEducationalUser: false,
    educationalTier: null
  },
  pro: {
    user: { id: '2', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'pro',
    effectiveTier: 'pro',
    canAccessAdvancedAnalysis: true,
    canAccessPublicationReady: true,
    isEducationalUser: false,
    educationalTier: null
  },
  educational: {
    user: { id: '3', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'edu',
    effectiveTier: 'edu',
    canAccessAdvancedAnalysis: true,
    canAccessPublicationReady: false,
    isEducationalUser: true,
    educationalTier: 'free'
  }
};

const InteractiveAdvisorDemo: React.FC = () => {
  const [selectedUserType, setSelectedUserType] = useState<keyof typeof authContexts>('guest');
  const [selectedDataset, setSelectedDataset] = useState<keyof typeof mockDatasets | null>(null);

  const currentAuthContext = authContexts[selectedUserType];
  const currentDataset = selectedDataset ? mockDatasets[selectedDataset] : null;

  const getUserTypeInfo = (userType: keyof typeof authContexts) => {
    switch (userType) {
      case 'guest':
        return { label: 'Guest User', icon: <ExploreIcon />, color: 'default' as const };
      case 'standard':
        return { label: 'Standard User', icon: <PersonIcon />, color: 'primary' as const };
      case 'pro':
        return { label: 'Pro User', icon: <StarIcon />, color: 'success' as const };
      case 'educational':
        return { label: 'Educational User', icon: <SchoolIcon />, color: 'secondary' as const };
      default:
        return { label: 'Unknown', icon: <PersonIcon />, color: 'default' as const };
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Interactive Statistical Advisor - Demo
      </Typography>

      {/* User Type Selector */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Select User Type:
        </Typography>
        
        <ToggleButtonGroup
          value={selectedUserType}
          exclusive
          onChange={(e, value) => value && setSelectedUserType(value)}
          sx={{ mb: 2 }}
        >
          {Object.entries(authContexts).map(([key, context]) => {
            const info = getUserTypeInfo(key as keyof typeof authContexts);
            return (
              <ToggleButton key={key} value={key}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {info.icon}
                  <Typography variant="caption">
                    {info.label}
                  </Typography>
                </Box>
              </ToggleButton>
            );
          })}
        </ToggleButtonGroup>

        <Alert severity="info">
          <Typography variant="body2">
            <strong>Current User:</strong> {getUserTypeInfo(selectedUserType).label} 
            ({currentAuthContext.effectiveTier?.toUpperCase()} tier)
          </Typography>
        </Alert>
      </Paper>

      {/* Dataset Selector */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Select Dataset (Optional):
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={4}>
            <Button
              variant={selectedDataset === null ? 'contained' : 'outlined'}
              fullWidth
              onClick={() => setSelectedDataset(null)}
              startIcon={<DatasetIcon />}
            >
              No Dataset
            </Button>
          </Grid>
          {Object.entries(mockDatasets).map(([key, dataset]) => (
            <Grid item xs={12} sm={4} key={key}>
              <Button
                variant={selectedDataset === key ? 'contained' : 'outlined'}
                fullWidth
                onClick={() => setSelectedDataset(key as keyof typeof mockDatasets)}
                startIcon={<AnalyticsIcon />}
              >
                {dataset.name}
              </Button>
            </Grid>
          ))}
        </Grid>

        {currentDataset && (
          <Alert severity="success">
            <Typography variant="body2">
              <strong>Loaded:</strong> {currentDataset.name} - 
              {currentDataset.rows} rows, {currentDataset.columns.length} variables
            </Typography>
          </Alert>
        )}
      </Paper>

      <Divider sx={{ my: 3 }} />

      {/* Interactive Statistical Advisor */}
      <AuthContext.Provider value={currentAuthContext as any}>
        <ResultsProvider>
          <InteractiveStatisticalAdvisor
            currentDataset={currentDataset}
            datasetAnalysis={currentDataset?.analysis}
          />
        </ResultsProvider>
      </AuthContext.Provider>
    </Box>
  );
};

export default InteractiveAdvisorDemo;
