import React from 'react';
import { Box } from '@mui/material';
import AnalysisAssistant from '../components/AnalysisAssistant';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';

interface AnalysisAssistantPageProps {
  onNavigate: (path: string) => void;
}

const AnalysisAssistantPage: React.FC<AnalysisAssistantPageProps> = ({ onNavigate }) => {
  // Initialize social meta for assistant page
  useSocialMeta();

  return (
    <Box sx={{ p: 3 }}>
      <AnalysisAssistant onNavigate={onNavigate} />
      <SocialShareWidget 
        variant="floating"
        position="bottom-right"
        platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
        collapsible
      />
    </Box>
  );
};

export default AnalysisAssistantPage;