import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Zoom
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  BarChart as BarChartIcon,
  Security as SecurityIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  CardMembership as SubscriptionIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

// Import admin dashboard components (to be created)
import AdminOverview from '../components/Admin/AdminOverview';
import UserManagement from '../components/Admin/UserManagement';
import SystemStatistics from '../components/Admin/SystemStatistics';
import NotificationManager from '../components/Admin/NotificationManager';
import AdminSettings from '../components/Admin/AdminSettings';
import SubscriptionOverrides from '../components/Admin/SubscriptionOverrides';
import DatasetQuotaManagement from '../components/Admin/DatasetQuotaManagement';
import AdminErrorBoundary from '../components/Admin/AdminErrorBoundary';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
      style={{ height: value === index ? 'auto' : 0, overflow: value === index ? 'visible' : 'hidden' }}
    >
      {value === index && (
        <Box sx={{ width: '100%', height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `admin-tab-${index}`,
    'aria-controls': `admin-tabpanel-${index}`,
  };
}

const AdminDashboardPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { isAdmin, canAccessAdminDashboard, loading, user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [pageLoading, setPageLoading] = useState(true);
  const [isMaximized, setIsMaximized] = useState(false);

  // Check admin access on component mount
  useEffect(() => {
    if (!loading) {
      if (!user) {
        navigate('/auth');
        return;
      }

      if (!canAccessAdminDashboard) {
        navigate('/dashboard');
        return;
      }

      setPageLoading(false);
    }
  }, [loading, user, canAccessAdminDashboard, navigate]);

  // Keyboard shortcut for maximize toggle (F11 or Ctrl/Cmd + Shift + F)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F11' ||
          ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F')) {
        event.preventDefault();
        handleMaximizeToggle();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isMaximized]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleMaximizeToggle = () => {
    setIsMaximized(!isMaximized);
  };

  // Handle body overflow when maximized
  useEffect(() => {
    if (isMaximized) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMaximized]);

  // Show loading while checking authentication
  if (loading || pageLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading Admin Dashboard...
          </Typography>
        </Box>
      </Container>
    );
  }

  // Show access denied if not admin
  if (!isAdmin || !canAccessAdminDashboard) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            You do not have permission to access the admin dashboard. Admin privileges are required.
          </Typography>
        </Alert>
      </Container>
    );
  }

  const tabs = [
    {
      label: 'Overview',
      icon: <DashboardIcon />,
      component: (
        <AdminErrorBoundary>
          <AdminOverview />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'User Management',
      icon: <PeopleIcon />,
      component: (
        <AdminErrorBoundary>
          <UserManagement />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'System Statistics',
      icon: <BarChartIcon />,
      component: (
        <AdminErrorBoundary>
          <SystemStatistics />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Subscription Overrides',
      icon: <SubscriptionIcon />,
      component: (
        <AdminErrorBoundary>
          <SubscriptionOverrides />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Dataset & Storage Quotas',
      icon: <StorageIcon />,
      component: (
        <AdminErrorBoundary>
          <DatasetQuotaManagement />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Notifications',
      icon: <NotificationsIcon />,
      component: (
        <AdminErrorBoundary>
          <NotificationManager />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Admin Settings',
      icon: <SettingsIcon />,
      component: (
        <AdminErrorBoundary>
          <AdminSettings />
        </AdminErrorBoundary>
      )
    },
  ];

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        position: isMaximized ? 'fixed' : 'relative',
        top: isMaximized ? 0 : 'auto',
        left: isMaximized ? 0 : 'auto',
        right: isMaximized ? 0 : 'auto',
        bottom: isMaximized ? 0 : 'auto',
        zIndex: isMaximized ? 1300 : 'auto', // Above sidebar and other content
        transition: 'all 0.3s ease-in-out',
        overflow: isMaximized ? 'auto' : 'visible'
      }}
    >
      <Container
        maxWidth={isMaximized ? false : "xl"}
        sx={{
          py: 3,
          px: { xs: 2, sm: 3 },
          maxWidth: isMaximized ? '100%' : undefined,
          width: isMaximized ? '100%' : undefined,
          transition: 'all 0.3s ease-in-out'
        }}
      >
        {/* Header */}
        <Box sx={{
          mb: isMaximized ? 2 : 3,
          transition: 'margin 0.3s ease-in-out'
        }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{
            fontWeight: 'bold',
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            fontSize: {
              xs: isMaximized ? '1.5rem' : '1.8rem',
              sm: isMaximized ? '2rem' : '2.5rem'
            },
            transition: 'font-size 0.3s ease-in-out'
          }}>
            <SecurityIcon sx={{
              fontSize: {
                xs: isMaximized ? '1.5rem' : '2rem',
                sm: isMaximized ? '2rem' : '2.5rem'
              },
              color: theme.palette.primary.main,
              transition: 'font-size 0.3s ease-in-out'
            }} />
            Admin Dashboard
            {isMaximized && (
              <Box
                sx={{
                  ml: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  fontSize: '0.75rem',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  display: 'inline-flex',
                  alignItems: 'center',
                  fontWeight: 'bold',
                  animation: 'fadeIn 0.3s ease-in-out'
                }}
              >
                MAXIMIZED
              </Box>
            )}
          </Typography>
          {!isMaximized && (
            <Typography variant="h6" color="text.secondary" sx={{
              fontSize: { xs: '1rem', sm: '1.25rem' },
              transition: 'opacity 0.3s ease-in-out'
            }}>
              System administration and management console
            </Typography>
          )}
        </Box>

        {/* Admin Status Alert */}
        {!isMaximized && (
          <Alert severity="info" sx={{
            mb: 3,
            transition: 'opacity 0.3s ease-in-out'
          }}>
            <Typography variant="body2">
              <strong>Admin Access:</strong> You are logged in as an administrator.
              Please use these tools responsibly and follow your organization's policies.
            </Typography>
          </Alert>
        )}

        {/* Main Content */}
        <Paper
          elevation={0}
          variant="outlined"
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            backgroundColor: alpha(theme.palette.background.paper, isMaximized ? 0.95 : 0.8),
            backdropFilter: 'blur(10px)',
            minHeight: isMaximized ? 'calc(100vh - 120px)' : '70vh',
            transition: 'all 0.3s ease-in-out',
            boxShadow: isMaximized ? theme.shadows[8] : theme.shadows[1],
            '@keyframes fadeIn': {
              '0%': {
                opacity: 0,
                transform: 'translateY(-10px)'
              },
              '100%': {
                opacity: 1,
                transform: 'translateY(0)'
              }
            }
          }}
        >
          {/* Tabs Header with Maximize Button */}
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            position: 'relative',
            display: 'flex',
            alignItems: 'center'
          }}>
            <Box sx={{ flexGrow: 1 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                allowScrollButtonsMobile
                sx={{
                  '& .MuiTab-root': {
                    minHeight: { xs: 60, sm: 72 },
                    textTransform: 'none',
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    fontWeight: 500,
                    px: { xs: 1, sm: 2 },
                    '&.Mui-selected': {
                      color: theme.palette.primary.main,
                      fontWeight: 600,
                    }
                  },
                  '& .MuiTabs-scrollButtons': {
                    '&.Mui-disabled': {
                      opacity: 0.3,
                    },
                  },
                }}
              >
                {tabs.map((tab, index) => (
                  <Tab
                    key={index}
                    icon={tab.icon}
                    label={tab.label}
                    iconPosition="start"
                    {...a11yProps(index)}
                    sx={{
                      '& .MuiTab-iconWrapper': {
                        marginRight: 1,
                        marginBottom: 0
                      }
                    }}
                  />
                ))}
              </Tabs>
            </Box>

            {/* Maximize/Minimize Button */}
            <Box sx={{
              position: 'absolute',
              right: { xs: 8, sm: 16 },
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 1
            }}>
              <Tooltip
                title={
                  <Box>
                    <Typography variant="body2">
                      {isMaximized ? "Exit fullscreen view" : "Maximize for better visibility"}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      Shortcut: F11 or Ctrl+Shift+F
                    </Typography>
                  </Box>
                }
                placement="left"
                TransitionComponent={Zoom}
              >
                <IconButton
                  onClick={handleMaximizeToggle}
                  size="medium"
                  sx={{
                    color: theme.palette.text.secondary,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    backdropFilter: 'blur(8px)',
                    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      transform: 'scale(1.05)',
                      boxShadow: theme.shadows[4]
                    },
                    '&:active': {
                      transform: 'scale(0.95)'
                    }
                  }}
                >
                  {isMaximized ? (
                    <FullscreenExitIcon sx={{ fontSize: '1.25rem' }} />
                  ) : (
                    <FullscreenIcon sx={{ fontSize: '1.25rem' }} />
                  )}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Tab Panels */}
          <Box sx={{
            overflow: 'auto',
            maxHeight: isMaximized ? 'calc(100vh - 200px)' : 'calc(100vh - 300px)',
            transition: 'max-height 0.3s ease-in-out'
          }}>
            {tabs.map((tab, index) => (
              <TabPanel key={index} value={activeTab} index={index}>
                <Box sx={{
                  px: { xs: 1, sm: isMaximized ? 3 : 2 },
                  py: 2,
                  transition: 'padding 0.3s ease-in-out'
                }}>
                  {tab.component}
                </Box>
              </TabPanel>
            ))}
          </Box>
        </Paper>

        {/* Footer */}
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
            DataStatPro Admin Dashboard - Use responsibly and in accordance with your organization's policies
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminDashboardPage;
