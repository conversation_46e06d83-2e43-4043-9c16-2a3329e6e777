import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>ton,
  ToggleButton,
  ToggleButtonGroup,
  Alert,
  Chip,
  Grid,
  Paper
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Search as ExploreIcon
} from '@mui/icons-material';
import { AuthContext } from '../../../context/AuthContext';
import { ResultsProvider } from '../../../context/ResultsContext';
import EnhancedStatisticalAnalysisAdvisor from '../EnhancedStatisticalAnalysisAdvisor';

// Mock dataset for testing
const mockDataset = {
  name: 'Test Dataset',
  columns: ['age', 'gender', 'score', 'group'],
  rows: 150
};

const mockDatasetAnalysis = {
  totalRows: 150,
  variableAnalysis: [
    {
      name: 'age',
      type: 'numeric',
      uniqueValues: Array.from({length: 50}, (_, i) => i + 18),
      missingCount: 0,
      distribution: { mean: 35.2, median: 34, std: 12.1 }
    },
    {
      name: 'gender',
      type: 'categorical',
      uniqueValues: ['Male', 'Female'],
      missingCount: 0,
      categories: [
        { value: 'Male', count: 75, percentage: 50 },
        { value: 'Female', count: 75, percentage: 50 }
      ]
    },
    {
      name: 'score',
      type: 'numeric',
      uniqueValues: Array.from({length: 100}, (_, i) => i + 1),
      missingCount: 3,
      distribution: { mean: 78.5, median: 79, std: 15.2 }
    },
    {
      name: 'group',
      type: 'categorical',
      uniqueValues: ['Control', 'Treatment A', 'Treatment B'],
      missingCount: 0,
      categories: [
        { value: 'Control', count: 50, percentage: 33.3 },
        { value: 'Treatment A', count: 50, percentage: 33.3 },
        { value: 'Treatment B', count: 50, percentage: 33.4 }
      ]
    }
  ]
};

// Mock auth contexts for different user types
const authContexts = {
  guest: {
    user: null,
    isGuest: true,
    accountType: null,
    effectiveTier: 'guest',
    canAccessAdvancedAnalysis: false,
    canAccessPublicationReady: false,
    isEducationalUser: false,
    educationalTier: null
  },
  standard: {
    user: { id: '1', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'standard',
    effectiveTier: 'standard',
    canAccessAdvancedAnalysis: false,
    canAccessPublicationReady: false,
    isEducationalUser: false,
    educationalTier: null
  },
  pro: {
    user: { id: '2', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'pro',
    effectiveTier: 'pro',
    canAccessAdvancedAnalysis: true,
    canAccessPublicationReady: true,
    isEducationalUser: false,
    educationalTier: null
  },
  educational: {
    user: { id: '3', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'edu',
    effectiveTier: 'edu',
    canAccessAdvancedAnalysis: true,
    canAccessPublicationReady: false,
    isEducationalUser: true,
    educationalTier: 'free'
  },
  educationalPro: {
    user: { id: '4', email: '<EMAIL>' },
    isGuest: false,
    accountType: 'edu_pro',
    effectiveTier: 'edu_pro',
    canAccessAdvancedAnalysis: true,
    canAccessPublicationReady: true,
    isEducationalUser: true,
    educationalTier: 'pro'
  }
};

const AccessControlDemo: React.FC = () => {
  const [selectedUserType, setSelectedUserType] = useState<keyof typeof authContexts>('guest');

  const currentAuthContext = authContexts[selectedUserType];

  const getUserTypeInfo = (userType: keyof typeof authContexts) => {
    switch (userType) {
      case 'guest':
        return { label: 'Guest User', icon: <ExploreIcon />, color: 'default' as const, description: 'No account, basic methods only' };
      case 'standard':
        return { label: 'Standard User', icon: <PersonIcon />, color: 'primary' as const, description: 'Free account, basic + intermediate methods' };
      case 'pro':
        return { label: 'Pro User', icon: <StarIcon />, color: 'success' as const, description: 'Paid subscription, all methods available' };
      case 'educational':
        return { label: 'Educational User', icon: <SchoolIcon />, color: 'secondary' as const, description: 'Student/faculty, advanced methods included' };
      case 'educationalPro':
        return { label: 'Educational Pro', icon: <SchoolIcon />, color: 'success' as const, description: 'Educational Pro subscription, full access' };
      default:
        return { label: 'Unknown', icon: <PersonIcon />, color: 'default' as const, description: '' };
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Enhanced Statistical Analysis Advisor - Access Control Demo
      </Typography>

      {/* User Type Selector */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Select User Type to Test Access Control:
        </Typography>
        
        <Grid container spacing={2}>
          {Object.entries(authContexts).map(([key, context]) => {
            const info = getUserTypeInfo(key as keyof typeof authContexts);
            return (
              <Grid item xs={12} sm={6} md={4} key={key}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    border: selectedUserType === key ? '2px solid' : '1px solid',
                    borderColor: selectedUserType === key ? 'primary.main' : 'divider'
                  }}
                  onClick={() => setSelectedUserType(key as keyof typeof authContexts)}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      {info.icon}
                      <Typography variant="subtitle1" fontWeight="medium">
                        {info.label}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {info.description}
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Chip 
                        label={`Tier: ${context.effectiveTier?.toUpperCase()}`}
                        size="small"
                        color={info.color}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </Paper>

      {/* Current User Info */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Testing as:</strong> {getUserTypeInfo(selectedUserType).label} 
          {currentAuthContext.user && ` (${currentAuthContext.user.email})`}
          <br />
          <strong>Effective Tier:</strong> {currentAuthContext.effectiveTier?.toUpperCase()}
          <br />
          <strong>Access Levels:</strong> 
          {currentAuthContext.effectiveTier === 'guest' && ' Guest methods only'}
          {currentAuthContext.effectiveTier === 'standard' && ' Guest + Standard methods'}
          {(currentAuthContext.effectiveTier === 'pro' || currentAuthContext.effectiveTier === 'edu_pro') && ' All methods (Guest + Standard + Pro)'}
          {currentAuthContext.effectiveTier === 'edu' && ' Guest + Standard methods (Educational)'}
        </Typography>
      </Alert>

      {/* Enhanced Statistical Analysis Advisor */}
      <AuthContext.Provider value={currentAuthContext as any}>
        <ResultsProvider>
          <EnhancedStatisticalAnalysisAdvisor
            currentDataset={mockDataset}
            datasetAnalysis={mockDatasetAnalysis}
          />
        </ResultsProvider>
      </AuthContext.Provider>
    </Box>
  );
};

export default AccessControlDemo;
