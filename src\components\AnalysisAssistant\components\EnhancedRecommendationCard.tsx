import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Chip,
  Box,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  <PERSON>lapse,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Rating,
  alpha,
  useTheme
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Upgrade as UpgradeIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  Speed as SpeedIcon,
  Star as StarIcon
} from '@mui/icons-material';

interface RecommendationCardProps {
  method: {
    id: string;
    name: string;
    category: string;
    subcategory: string;
    description: string;
    assumptions: string[];
    dataRequirements: string[];
    route: string;
    accessLevel: 'guest' | 'standard' | 'pro' | 'edu';
    icon: React.ReactNode;
    examples: string[];
    contextualRecommendations?: string[];
  };
  hasAccess: boolean;
  isBookmarked: boolean;
  onBookmarkToggle: (methodId: string) => void;
  onRunAnalysis: (method: any) => void;
  onViewDetails: (method: any) => void;
  showContext?: boolean;
  priority?: 'high' | 'medium' | 'low';
  confidence?: number;
  estimatedTime?: string;
  usageStats?: {
    popularity: number;
    successRate: number;
    avgRating: number;
  };
}

const EnhancedRecommendationCard: React.FC<RecommendationCardProps> = ({
  method,
  hasAccess,
  isBookmarked,
  onBookmarkToggle,
  onRunAnalysis,
  onViewDetails,
  showContext = false,
  priority = 'medium',
  confidence,
  estimatedTime,
  usageStats
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'Highly Recommended';
      case 'medium':
        return 'Recommended';
      case 'low':
        return 'Consider';
      default:
        return 'Available';
    }
  };

  return (
    <Card 
      sx={{ 
        mb: 2,
        position: 'relative',
        cursor: 'pointer',
        opacity: hasAccess ? 1 : 0.8,
        border: priority === 'high' ? `2px solid ${alpha(theme.palette.error.main, 0.3)}` : 'none',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-2px)',
          transition: 'all 0.2s ease-in-out'
        }
      }}
      onClick={() => onViewDetails(method)}
    >
      {/* Priority indicator */}
      {priority === 'high' && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            bgcolor: theme.palette.error.main,
            color: 'white',
            px: 1,
            py: 0.5,
            borderBottomLeftRadius: 8,
            fontSize: '0.7rem',
            fontWeight: 'bold'
          }}
        >
          TOP PICK
        </Box>
      )}

      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
            <Avatar sx={{ 
              bgcolor: alpha(theme.palette.primary.main, 0.1), 
              width: 48, 
              height: 48,
              color: theme.palette.primary.main
            }}>
              {method.icon}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                <Typography variant="h6" sx={{ fontSize: '1.1rem' }}>
                  {method.name}
                </Typography>
                {!hasAccess && (
                  <Chip 
                    label="PRO" 
                    size="small" 
                    color="warning" 
                    sx={{ fontSize: '0.7rem' }} 
                  />
                )}
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {method.description}
              </Typography>
              
              {/* Priority and confidence indicators */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Chip 
                  label={getPriorityLabel(priority)}
                  size="small"
                  sx={{ 
                    bgcolor: alpha(getPriorityColor(priority), 0.1),
                    color: getPriorityColor(priority),
                    fontWeight: 'medium'
                  }}
                />
                {confidence && (
                  <Chip 
                    label={`${Math.round(confidence * 100)}% match`}
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                )}
                {estimatedTime && (
                  <Chip 
                    icon={<SpeedIcon />}
                    label={estimatedTime}
                    size="small"
                    variant="outlined"
                  />
                )}
              </Box>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Tooltip title={isBookmarked ? "Remove bookmark" : "Bookmark method"}>
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onBookmarkToggle(method.id);
                }}
              >
                {isBookmarked ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Show/hide details">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  setExpanded(!expanded);
                }}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Context-aware recommendations */}
        {showContext && method.contextualRecommendations && method.contextualRecommendations.length > 0 && (
          <Alert 
            severity="info" 
            sx={{ mb: 2 }}
            icon={<TipsAndUpdatesIcon />}
          >
            <Typography variant="body2">
              {method.contextualRecommendations[0]}
            </Typography>
          </Alert>
        )}

        {/* Usage statistics */}
        {usageStats && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2, p: 1, bgcolor: alpha(theme.palette.info.main, 0.05), borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <StarIcon fontSize="small" color="warning" />
              <Typography variant="caption">
                {usageStats.avgRating.toFixed(1)}
              </Typography>
            </Box>
            <Typography variant="caption" color="text.secondary">
              {usageStats.successRate}% success rate
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {usageStats.popularity}% popularity
            </Typography>
          </Box>
        )}

        {/* Category tags */}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip 
            label={method.category} 
            size="small" 
            variant="outlined" 
            color="primary" 
          />
          <Chip 
            label={method.subcategory} 
            size="small" 
            variant="outlined" 
          />
          <Chip 
            label={`${method.accessLevel.toUpperCase()} Access`} 
            size="small" 
            color={hasAccess ? 'success' : 'warning'}
          />
        </Box>

        {/* Expandable details */}
        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Data Requirements:
            </Typography>
            <List dense>
              {method.dataRequirements.slice(0, 3).map((req, index) => (
                <ListItem key={index} sx={{ py: 0 }}>
                  <ListItemIcon sx={{ minWidth: 24 }}>
                    <CheckCircleIcon fontSize="small" color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={req} 
                    primaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItem>
              ))}
            </List>
            
            <Typography variant="subtitle2" sx={{ mb: 1, mt: 1 }}>
              Key Assumptions:
            </Typography>
            <List dense>
              {method.assumptions.slice(0, 2).map((assumption, index) => (
                <ListItem key={index} sx={{ py: 0 }}>
                  <ListItemIcon sx={{ minWidth: 24 }}>
                    <WarningIcon fontSize="small" color="warning" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={assumption} 
                    primaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        </Collapse>
      </CardContent>

      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
        <Button
          variant="text"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onViewDetails(method);
          }}
        >
          View Details
        </Button>
        
        {hasAccess ? (
          <Button
            variant="contained"
            size="small"
            startIcon={<PlayArrowIcon />}
            onClick={(e) => {
              e.stopPropagation();
              onRunAnalysis(method);
            }}
          >
            Run Analysis
          </Button>
        ) : (
          <Button
            variant="outlined"
            size="small"
            startIcon={<UpgradeIcon />}
            color="warning"
            onClick={(e) => {
              e.stopPropagation();
              // Handle upgrade action
            }}
          >
            Upgrade
          </Button>
        )}
      </CardActions>
    </Card>
  );
};

export default EnhancedRecommendationCard;
