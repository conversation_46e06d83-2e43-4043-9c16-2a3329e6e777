import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  <PERSON>ing,
  TextField,
  Box
} from '@mui/material';
import { AnalysisSuggestion } from '../utils/types';

interface FeedbackDialogProps {
  open: boolean;
  suggestion: AnalysisSuggestion | null;
  rating: number;
  comment: string;
  onClose: () => void;
  onRatingChange: (value: number) => void;
  onCommentChange: (value: string) => void;
  onSubmit: () => void;
}

/**
 * Feedback dialog component for the AnalysisAssistant
 * Allows users to provide feedback on analysis suggestions
 */
const FeedbackDialog: React.FC<FeedbackDialogProps> = ({
  open,
  suggestion,
  rating,
  comment,
  onClose,
  onRatingChange,
  onCommentChange,
  onSubmit
}) => {
  if (!suggestion) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Provide Feedback</DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            How helpful was this suggestion?
          </Typography>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            {suggestion.title}
          </Typography>
          <Rating
            name="feedback-rating"
            value={rating}
            onChange={(_, newValue) => onRatingChange(newValue || 0)}
            size="large"
            sx={{ mt: 1 }}
          />
        </Box>
        
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            Additional Comments (Optional)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            placeholder="What did you like or dislike about this suggestion? How could it be improved?"
            value={comment}
            onChange={(e) => onCommentChange(e.target.value)}
          />
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button 
          onClick={onSubmit} 
          color="primary" 
          variant="contained"
          disabled={rating === 0}
        >
          Submit Feedback
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FeedbackDialog;