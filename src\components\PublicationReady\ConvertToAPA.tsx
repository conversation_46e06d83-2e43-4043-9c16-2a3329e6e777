import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Snackbar,
  IconButton,
  Tooltip,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  FormLabel,
  Divider
} from '@mui/material';
import {
  TableChart as TableChartIcon,
  ContentCopy as ContentCopyIcon,
  Download as DownloadIcon,
  CloudUpload as CloudUploadIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Preview as PreviewIcon,
  Transform as TransformIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  ViewCarousel as ViewCarouselIcon,
  Clear as ClearIcon,
  Psychology as PsychologyIcon,
  School as SchoolIcon,
  DragIndicator as DragIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  defaultDropAnimationSideEffects,
  DropAnimation
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  horizontalListSortingStrategy
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Document, Packer, Paragraph, Table as DocxTable, TableRow as DocxTableRow, TableCell as DocxTableCell, WidthType, AlignmentType, TextRun, HeadingLevel } from 'docx';
import { saveAs } from 'file-saver';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`apa-tabpanel-${index}`}
      aria-labelledby={`apa-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface ParsedTable {
  headers: string[];
  rows: string[][];
  rowHeaders?: string[]; // First column as row headers
  multiRowHeaders?: string[][]; // Multiple columns as row headers
  multiColumnHeaders?: string[][]; // Multiple rows as column headers
  hasRowHeaders: boolean; // Whether table has row headers
  hasColumnHeaders: boolean; // Whether table has column headers
  numHeaderRows: number; // Number of header rows (1 or 2)
  numHeaderCols: number; // Number of header columns (1 or 2)
  tableNumber?: string;
  tableTitle?: string;
  tableNote?: string;
}

interface APAFormattingOptions {
  tableNumber: string;
  tableTitle: string;
  tableNote: string;
  includeStatistics: boolean;
  italicizeStatistics: boolean;
  rightAlignNumbers: boolean;
  includeSignificance: boolean;
  significanceThreshold: number;
  decimalPlaces: number;
  includeConfidenceIntervals: boolean;
  detectRowHeaders: boolean; // Auto-detect row headers
  treatFirstColumnAsRowHeaders: boolean; // Force first column as row headers
  treatFirstRowAsColumnHeaders: boolean; // Force first row as column headers
  numHeaderRows: number; // Number of header rows (1 or 2)
  numHeaderCols: number; // Number of header columns (1 or 2)
}

const ConvertToAPA: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  
  // State for input and options
  const [rawInput, setRawInput] = useState<string>('');
  const [inputFormat, setInputFormat] = useState<'auto' | 'csv' | 'tsv' | 'spaces'>('auto');
  const [parsedTable, setParsedTable] = useState<ParsedTable | null>(null);
  
  // State for APA formatting options
  const [apaOptions, setApaOptions] = useState<APAFormattingOptions>({
    tableNumber: '1',
    tableTitle: 'Results of Statistical Analysis',
    tableNote: 'Note. *p < .05. **p < .01. ***p < .001.',
    includeStatistics: true,
    italicizeStatistics: true,
    rightAlignNumbers: true,
    includeSignificance: true,
    significanceThreshold: 0.05,
    decimalPlaces: 3,
    includeConfidenceIntervals: false,
    detectRowHeaders: true,
    treatFirstColumnAsRowHeaders: false,
    treatFirstRowAsColumnHeaders: true,
    numHeaderRows: 1,
    numHeaderCols: 1
  });
  
  // State for view mode
  const [viewMode, setViewMode] = useState<'tabs' | 'stacked' | 'side-by-side'>('tabs');
  const [activeTab, setActiveTab] = useState<number>(0);
  
  // State for processing
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  
  // State for drag-and-drop functionality
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<any>(null);

  // Set default view mode based on screen size
  useEffect(() => {
    if (isMobile) {
      setViewMode('tabs');
    } else if (isTablet) {
      setViewMode('stacked');
    }
  }, [isMobile, isTablet]);

  // Sample data for demonstration
  const sampleData = `Variable,Mean,SD,t,df,p,95% CI
Condition A,12.45,2.34,3.42,28,.002,"[8.21, 16.69]"
Condition B,8.76,1.98,-2.11,28,.045,"[4.52, 13.00]"
Condition C,15.23,3.12,4.67,28,.001,"[11.99, 18.47]"`;

  // Sample data with both row and column headers
  const sampleDataWithRowHeaders = `,Group A,Group B,Group C,Total
Male,45,38,52,135
Female,42,47,39,128
Total,87,85,91,263`;

  // Sample data with multi-row and multi-column headers
  const sampleDataMultiHeaders = `,Dependent,Gender,,
,Female,Male,Total,p
Total N (%),55 (55.0),45 (45.0),100,
Income,Mean (SD),70162.1 (24403.0),75329.2 (31368.8),72487.3 (27727.8),0.356
Education,College,12 (21.8),9 (20.0),21 (21.0),0.832
,Graduate,16 (29.1),13 (28.9),29 (29.0),
,High School,14 (25.5),9 (20.0),23 (23.0),
,PhD,13 (23.6),14 (31.1),27 (27.0),
Satisfaction,Mean (SD),5.8 (3.2),6.0 (2.5),5.9 (2.9),0.838
Weight,Mean (SD),70.3 (11.0),83.6 (11.7),76.3 (13.1),<0.001
BloodPressure,Mean (SD),115.8 (6.8),117.2 (8.2),116.4 (7.5),0.373
Cholesterol,Mean (SD),158.1 (9.9),160.4 (13.4),159.2 (11.6),0.324`;

  // Handle view mode change
  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: 'tabs' | 'stacked' | 'side-by-side' | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
      setActiveTab(0);
    }
  };

  // Clear all data
  const clearAll = () => {
    setRawInput('');
    setParsedTable(null);
    setError(null);
  };

  // Load sample data
  const loadSampleData = () => {
    setRawInput(sampleData);
    parseTable(sampleData);
  };

  // Load sample data with row headers
  const loadSampleDataWithRowHeaders = () => {
    setRawInput(sampleDataWithRowHeaders);
    parseTable(sampleDataWithRowHeaders);
  };

  // Load sample data with multi-row and multi-column headers
  const loadSampleDataMultiHeaders = () => {
    setRawInput(sampleDataMultiHeaders);
    parseTable(sampleDataMultiHeaders);
  };

  // Remove header column
  const removeHeaderColumn = (columnIndex: number) => {
    if (!parsedTable) return;
    
    const newHeaders = parsedTable.headers.filter((_, index) => index !== columnIndex);
    const newRows = parsedTable.rows.map(row => row.filter((_, index) => index !== columnIndex));
    
    // Update multi-column headers if they exist
    const newMultiColumnHeaders = parsedTable.multiColumnHeaders?.map(headerRow => 
      headerRow.filter((_, index) => index !== columnIndex)
    );
    
    setParsedTable({
      ...parsedTable,
      headers: newHeaders,
      rows: newRows,
      multiColumnHeaders: newMultiColumnHeaders
    });
  };

  // Remove header row (for multi-row headers)
  const removeHeaderRow = (rowIndex: number) => {
    if (!parsedTable || !parsedTable.multiColumnHeaders) return;
    
    const newMultiColumnHeaders = parsedTable.multiColumnHeaders.filter((_, index) => index !== rowIndex);
    
    setParsedTable({
      ...parsedTable,
      multiColumnHeaders: newMultiColumnHeaders.length > 0 ? newMultiColumnHeaders : undefined,
      numHeaderRows: Math.max(1, parsedTable.numHeaderRows - 1)
    });
  };

  // Remove data row
  const removeDataRow = (rowIndex: number) => {
    if (!parsedTable) return;
    
    const newRows = parsedTable.rows.filter((_, index) => index !== rowIndex);
    const newRowHeaders = parsedTable.rowHeaders?.filter((_, index) => index !== rowIndex);
    const newMultiRowHeaders = parsedTable.multiRowHeaders?.filter((_, index) => index !== rowIndex);
    
    setParsedTable({
      ...parsedTable,
      rows: newRows,
      rowHeaders: newRowHeaders,
      multiRowHeaders: newMultiRowHeaders
    });
  };

  // Parse input table data
  const parseTable = (input: string = rawInput) => {
    if (!input.trim()) {
      setError('Please enter some table data to parse.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let delimiter: string | RegExp = ',';
      
      // Auto-detect delimiter
      if (inputFormat === 'auto') {
        const commas = (input.match(/,/g) || []).length;
        const tabs = (input.match(/\t/g) || []).length;
        const spaces = (input.match(/\s+/g) || []).length; // Match one or more spaces
        
        if (tabs > commas && tabs > spaces) {
          delimiter = '\t';
        } else if (spaces > commas && spaces > tabs) {
          delimiter = /\s+/; // Use one or more spaces as delimiter
        } else {
          delimiter = ',';
        }
      } else if (inputFormat === 'tsv') {
        delimiter = '\t';
      } else if (inputFormat === 'spaces') {
        delimiter = /\s+/; // Use one or more spaces as delimiter
      }

      const lines = input.trim().split('\n');
      
      if (lines.length < 2) {
        throw new Error('Table must have at least a header row and one data row.');
      }

      // Helper function to parse a single line, handling quoted fields
      const parseLine = (line: string, currentDelimiter: string | RegExp): string[] => {
        if (typeof currentDelimiter === 'string' && currentDelimiter === ',') {
          // Regex to split CSV by commas that are NOT inside double quotes
          const csvSplitRegex = /,(?=(?:[^"]*"[^"]*")*[^"]*$)/;
          const cells = line.split(csvSplitRegex).map(cell => {
            // Remove leading/trailing whitespace and quotes
            cell = cell.trim();
            if (cell.startsWith('"') && cell.endsWith('"')) {
              cell = cell.substring(1, cell.length - 1).replace(/""/g, '"'); // Unescape double quotes
            }
            return cell;
          });
          // Preserve all cells including empty ones for proper table structure
          // Don't remove trailing empty cells to maintain consistent column structure
          return cells;
        } else {
          const cells = line.split(currentDelimiter).map(cell => {
            cell = cell.trim();
            // Only remove quotes if they wrap the entire cell content
            if ((cell.startsWith('"') && cell.endsWith('"')) || (cell.startsWith("'") && cell.endsWith("'"))) {
              cell = cell.substring(1, cell.length - 1);
            }
            return cell;
          });
          
          // Preserve all cells including empty ones for proper table structure
          // Don't remove trailing empty cells to maintain consistent column structure
          return cells;
        }
      };

      // Parse all lines first
      const allRows: string[][] = [];
      for (let i = 0; i < lines.length; i++) {
        const row = parseLine(lines[i], delimiter);
        if (row.length > 0) {
          allRows.push(row);
        }
      }

      if (allRows.length < 1) {
        throw new Error('No valid data found.');
      }

      // Determine table structure with multi-header support
      let headers: string[] = [];
      let rows: string[][] = [];
      let rowHeaders: string[] = [];
      let multiRowHeaders: string[][] = [];
      let multiColumnHeaders: string[][] = [];
      let hasRowHeaders = false;
      let hasColumnHeaders = false;
      let numHeaderRows = apaOptions.numHeaderRows;
      let numHeaderCols = apaOptions.numHeaderCols;
      let dataStartRow = 0;
      let dataStartCol = 0;

      // Auto-detect or use explicit settings for headers
      if (apaOptions.detectRowHeaders) {
        // Auto-detect number of header rows
        if (allRows.length > 1 && isLikelyColumnHeaderRow(allRows[0])) {
          hasColumnHeaders = true;
          numHeaderRows = 1;
          // Check if second row is also a header
          if (allRows.length > 2 && isLikelyColumnHeaderRow(allRows[1])) {
            numHeaderRows = 2;
          }
        }
        
        // Auto-detect number of header columns
        if (allRows.length > 0) {
          const firstColumnValues = allRows.slice(numHeaderRows).map(row => row[0] || '');
          if (isLikelyRowHeaderColumn(firstColumnValues)) {
            hasRowHeaders = true;
            numHeaderCols = 1;
            // Check if second column is also a header
            if (allRows[0] && allRows[0].length > 1) {
              const secondColumnValues = allRows.slice(numHeaderRows).map(row => row[1] || '');
              if (isLikelyRowHeaderColumn(secondColumnValues)) {
                numHeaderCols = 2;
              }
            }
          }
        }
      } else {
        // Use explicit settings
        hasColumnHeaders = apaOptions.treatFirstRowAsColumnHeaders;
        hasRowHeaders = apaOptions.treatFirstColumnAsRowHeaders;
        if (hasColumnHeaders) numHeaderRows = Math.max(1, numHeaderRows);
        if (hasRowHeaders) numHeaderCols = Math.max(1, numHeaderCols);
      }

      dataStartRow = hasColumnHeaders ? numHeaderRows : 0;
      dataStartCol = hasRowHeaders ? numHeaderCols : 0;

      // Extract multi-column headers
      if (hasColumnHeaders && numHeaderRows > 0) {
        multiColumnHeaders = [];
        for (let i = 0; i < numHeaderRows; i++) {
          if (allRows[i]) {
            // Use the same slicing logic as data rows for consistency
            const headerRow = allRows[i].slice(dataStartCol);
            multiColumnHeaders.push(headerRow);
          }
        }
        // Use the last header row as the main headers
        headers = multiColumnHeaders[multiColumnHeaders.length - 1] || [];
        
        // Ensure headers array has consistent length with data columns
        const maxDataCols = Math.max(...allRows.slice(numHeaderRows).map(row => row.slice(dataStartCol).length));
        while (headers.length < maxDataCols) {
          headers.push(`Column ${headers.length + 1}`);
        }
      } else {
        // Generate default column headers
        const maxCols = Math.max(...allRows.map(row => row.length));
        headers = Array.from({ length: maxCols - dataStartCol }, (_, i) => `Column ${i + 1}`);
      }

      // Extract multi-row headers
      if (hasRowHeaders && numHeaderCols > 0) {
        multiRowHeaders = [];
        for (let i = dataStartRow; i < allRows.length; i++) {
          if (allRows[i]) {
            const headerCols = allRows[i].slice(0, numHeaderCols);
            multiRowHeaders.push(headerCols);
          }
        }
        // Use the last header column as the main row headers
        rowHeaders = multiRowHeaders.map(row => row[row.length - 1] || '');
      }

      // Extract data rows
      for (let i = dataStartRow; i < allRows.length; i++) {
        const row = allRows[i].slice(dataStartCol);
        if (row.length > 0) {
          // Pad row to match header length
          while (row.length < headers.length) {
            row.push('');
          }
          rows.push(row.slice(0, headers.length));
        }
      }

      if (rows.length === 0) {
        throw new Error('No valid data rows found.');
      }

      const parsed: ParsedTable = {
        headers,
        rows,
        rowHeaders: hasRowHeaders ? rowHeaders : undefined,
        multiRowHeaders: hasRowHeaders && numHeaderCols > 1 ? multiRowHeaders : undefined,
        multiColumnHeaders: hasColumnHeaders && numHeaderRows > 1 ? multiColumnHeaders : undefined,
        hasRowHeaders,
        hasColumnHeaders,
        numHeaderRows,
        numHeaderCols,
        tableNumber: apaOptions.tableNumber,
        tableTitle: apaOptions.tableTitle,
        tableNote: apaOptions.tableNote
      };

      setParsedTable(parsed);
      setLoading(false);
      setSnackbarMessage('Table parsed successfully!');
      setSnackbarOpen(true);
      
      // Switch to preview tab after successful parsing
      if (viewMode === 'tabs') {
        setActiveTab(1);
      }
    } catch (err) {
      setError(`Error parsing table: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  };

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    
    // Find the dragged item data
    const [type, rowIndex, colIndex] = (active.id as string).split('-');
    if (parsedTable) {
      if (type === 'row') {
        setDraggedItem({
          type: 'row',
          index: parseInt(rowIndex),
          data: parsedTable.rows[parseInt(rowIndex)]
        });
      } else if (type === 'col') {
        setDraggedItem({
          type: 'column',
          index: parseInt(colIndex),
          data: parsedTable.headers[parseInt(colIndex)]
        });
      }
    }
  }, [parsedTable]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over || !parsedTable) {
      setActiveId(null);
      setDraggedItem(null);
      return;
    }

    const activeId = active.id as string;
    const overId = over.id as string;
    
    if (activeId !== overId) {
      const [activeType, activeRowIndex, activeColIndex] = activeId.split('-');
      const [overType, overRowIndex, overColIndex] = overId.split('-');
      
      if (activeType === overType) {
        const newTable = { ...parsedTable };
        
        if (activeType === 'row') {
          // Reorder rows
          const activeIndex = parseInt(activeRowIndex);
          const overIndex = parseInt(overRowIndex);
          newTable.rows = arrayMove(newTable.rows, activeIndex, overIndex);
          
          // Also reorder row headers if they exist
          if (newTable.rowHeaders) {
            newTable.rowHeaders = arrayMove(newTable.rowHeaders, activeIndex, overIndex);
          }
          if (newTable.multiRowHeaders) {
            newTable.multiRowHeaders = newTable.multiRowHeaders.map(headerCol => 
              arrayMove(headerCol, activeIndex, overIndex)
            );
          }
        } else if (activeType === 'col') {
          // Reorder columns
          const activeIndex = parseInt(activeColIndex);
          const overIndex = parseInt(overColIndex);
          newTable.headers = arrayMove(newTable.headers, activeIndex, overIndex);
          
          // Also reorder data in all rows
          newTable.rows = newTable.rows.map(row => arrayMove(row, activeIndex, overIndex));
          
          // Also reorder multi-column headers if they exist
          if (newTable.multiColumnHeaders) {
            newTable.multiColumnHeaders = newTable.multiColumnHeaders.map(headerRow => 
              arrayMove(headerRow, activeIndex, overIndex)
            );
          }
        }
        
        setParsedTable(newTable);
        setSnackbarMessage('Table reorganized successfully!');
        setSnackbarOpen(true);
      }
    }
    
    setActiveId(null);
    setDraggedItem(null);
  }, [parsedTable]);

  // Check if a value is numeric
  const isNumeric = (value: string): boolean => {
    if (!value || value.trim() === '') return false;
    
    // Check if it's a statistical notation like "mean(sd)" - preserve these as non-numeric
    if (value.match(/^\d+\.?\d*\(\d+\.?\d*\)$/)) {
      return false; // Treat statistical notation as non-numeric to preserve formatting
    }
    
    const cleaned = value.replace(/[,\[\]]/g, ''); // Remove commas and brackets but preserve parentheses for statistical notation
    return !isNaN(parseFloat(cleaned)) && isFinite(parseFloat(cleaned));
  };

  // Detect if a column contains mostly non-numeric values (likely row headers)
  const isLikelyRowHeaderColumn = (columnValues: string[]): boolean => {
    if (columnValues.length === 0) return false;
    const nonNumericCount = columnValues.filter(val => !isNumeric(val) && val.trim() !== '').length;
    return nonNumericCount / columnValues.length > 0.7; // 70% threshold
  };

  // Detect if a row contains mostly non-numeric values (likely column headers)
  const isLikelyColumnHeaderRow = (rowValues: string[]): boolean => {
    if (rowValues.length === 0) return false;
    const nonNumericCount = rowValues.filter(val => !isNumeric(val) && val.trim() !== '').length;
    return nonNumericCount / rowValues.length > 0.5; // 50% threshold
  };

  // Check if a value is a statistical term that should be italicized
  const isStatisticalTerm = (value: string): boolean => {
    const statTerms = ['M', 'SD', 't', 'F', 'r', 'p', 'df', 'CI', 'β', 'χ²', 'd', 'η²'];
    return statTerms.some(term => value.toLowerCase().includes(term.toLowerCase()));
  };

  // Check if a column is likely a p-value column based on header
  const isPValueColumn = (header: string): boolean => {
    const pValueHeaders = ['p', 'p-value', 'p value', 'pvalue', 'sig', 'significance', 'prob', 'probability'];
    const trimmedHeader = header.trim().toLowerCase();
    return pValueHeaders.some(term => trimmedHeader.includes(term.toLowerCase()) || trimmedHeader === term.toLowerCase());
  };

  // Check if a value looks like a p-value
  const isPValueLike = (value: string): boolean => {
    if (!value || typeof value !== 'string') return false;
    
    const trimmed = value.trim();
    if (!trimmed) return false;
    
    // Check for patterns like: 0.001, .001, <0.001, < .001, p=0.001, etc.
    const pValuePatterns = [
      /^<?\s*0?\.\d+$/,  // 0.001, .001, <0.001, < .001
      /^<?\s*[01]\.\d+$/,  // 1.000, 0.500, <1.000
      /^p\s*[=<>]?\s*0?\.\d+$/i,  // p=0.001, p<.001
      /^<?\s*\.\d+$/,  // .001, <.001
      /^ns$/i,  // "ns" for non-significant
      /^n\.?s\.?$/i  // "n.s." for non-significant
    ];
    return pValuePatterns.some(pattern => pattern.test(trimmed));
  };

  // Format p-value according to APA style
  const formatPValue = (value: string): string => {
    const trimmed = value.trim();
    
    // Handle non-significant indicators
    if (/^ns$/i.test(trimmed) || /^n\.?s\.?$/i.test(trimmed)) {
      return 'ns';
    }
    
    // Handle values that start with < or >
    if (trimmed.startsWith('<') || trimmed.startsWith('>')) {
      const operator = trimmed.charAt(0);
      const numPart = trimmed.substring(1).trim();
      const num = parseFloat(numPart);
      if (!isNaN(num)) {
        if (num < 0.001) return '< .001';
        if (num < 1) return `${operator} ${num.toFixed(4).replace(/^0/, '')}`;
        return `${operator} ${num.toFixed(apaOptions.decimalPlaces)}`;
      }
      return trimmed;
    }
    
    // Handle p=value format
    if (trimmed.toLowerCase().startsWith('p')) {
      const numPart = trimmed.replace(/^p\s*[=<>]?\s*/i, '');
      const num = parseFloat(numPart);
      if (!isNaN(num)) {
        if (num < 0.001) return '< .001';
        if (num < 1) return num.toFixed(4).replace(/^0/, '');
        return num.toFixed(apaOptions.decimalPlaces);
      }
      return trimmed;
    }
    
    // Handle plain numeric values
    const num = parseFloat(trimmed);
    if (!isNaN(num)) {
      if (num < 0.001) return '< .001';
      if (num < 1) return num.toFixed(4).replace(/^0/, '');
      return num.toFixed(apaOptions.decimalPlaces);
    }
    
    return trimmed;
  };

  // Format a numeric value according to APA style
  const formatNumericValue = (value: string): string => {
    // Check if it's a CI string (e.g., "[X, Y]")
    if (value.match(/^\[\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*,\s*[-+]?\d*\.?\d+(?:e[-+]?\d+)?\s*\]$/)) {
      // If it's a CI, format its components but keep the brackets
      const parts = value.substring(1, value.length - 1).split(',').map(s => {
        const num = parseFloat(s.trim());
        return isNaN(num) ? s.trim() : num.toFixed(apaOptions.decimalPlaces);
      });
      return `[${parts.join(', ')}]`;
    }

    // Check if it's statistical notation like "mean(sd)" - preserve the format
    if (value.match(/^\d+\.?\d*\(\d+\.?\d*\)$/)) {
      const parts = value.match(/^(\d+\.?\d*)\((\d+\.?\d*)\)$/);
      if (parts) {
        const mean = parseFloat(parts[1]);
        const sd = parseFloat(parts[2]);
        return `${mean.toFixed(apaOptions.decimalPlaces)}(${sd.toFixed(apaOptions.decimalPlaces)})`;
      }
      return value; // Fallback to original if parsing fails
    }

    if (!isNumeric(value)) return value; // Only process as a single number if it truly is one
    
    const cleaned = value.replace(/[,\[\]]/g, ''); // Remove commas and brackets but preserve parentheses
    const num = parseFloat(cleaned);
    
    // Handle p-values specially
    if (value.toLowerCase().includes('p')) {
      if (num < 0.001) return '< .001';
      if (num < 1) return num.toFixed(4).replace(/^0/, '');
      return num.toFixed(apaOptions.decimalPlaces);
    }
    
    return num.toFixed(apaOptions.decimalPlaces);
  };

  // Add significance indicators
  const addSignificanceIndicators = (value: string, pValue?: number): string => {
    if (!apaOptions.includeSignificance || !pValue) return value;
    
    if (pValue < 0.001) return value + '***';
    if (pValue < 0.01) return value + '**';
    if (pValue < apaOptions.significanceThreshold) return value + '*';
    
    return value;
  };

  // Copy to clipboard
  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setSnackbarMessage('Table copied to clipboard!');
      setSnackbarOpen(true);
    } catch (err) {
      setSnackbarMessage('Failed to copy to clipboard');
      setSnackbarOpen(true);
    }
  };

  // Generate APA formatted text
  const generateAPAText = (): string => {
    if (!parsedTable) return '';

    let apaText = `Table ${apaOptions.tableNumber}\n\n`;
    apaText += `${apaOptions.tableTitle}\n\n`;
    
    // Create the table
    const headers = parsedTable.headers;
    const hasRowHeaders = parsedTable.hasRowHeaders;
    const hasColumnHeaders = parsedTable.hasColumnHeaders;
    const numHeaderRows = parsedTable.numHeaderRows;
    const numHeaderCols = parsedTable.numHeaderCols;
    const rowHeaders = parsedTable.rowHeaders || [];
    const multiRowHeaders = parsedTable.multiRowHeaders || [];
    const multiColumnHeaders = parsedTable.multiColumnHeaders || [];
    
    // Calculate column widths including row headers
    const headerColsCount = hasRowHeaders ? numHeaderCols : 0;
    const allColumns = hasRowHeaders ? 
      Array(headerColsCount).fill('').concat(headers) : headers;
    
    const maxWidths = allColumns.map((header, index) => {
      if (index < headerColsCount && hasRowHeaders) {
        // Row header column widths
        const colHeaderWidths = multiRowHeaders.map(row => (row[index] || '').length);
        return Math.max(8, ...colHeaderWidths); // Minimum width of 8
      } else {
        // Data column widths
        const dataIndex = index - headerColsCount;
        const headerWidth = header.length;
        const dataWidths = parsedTable.rows.map(row => (row[dataIndex] || '').length);
        return Math.max(headerWidth, ...dataWidths);
      }
    });

    // Multi-row column headers
    if (hasColumnHeaders && numHeaderRows > 1 && multiColumnHeaders.length > 0) {
      multiColumnHeaders.forEach((headerRow, headerRowIndex) => {
        const fullHeaderRow = hasRowHeaders ? 
          Array(headerColsCount).fill('').concat(headerRow) : headerRow;
        
        apaText += fullHeaderRow.map((header, index) => {
          if (index < headerColsCount && hasRowHeaders) {
            return ''.padEnd(maxWidths[index]); // Empty corner cells
          }
          return header.padEnd(maxWidths[index]);
        }).join('  ') + '\n';
      });
      
      // Separator line after multi-row headers
      apaText += maxWidths.map(width => '-'.repeat(width)).join('  ') + '\n';
    } else if (hasColumnHeaders) {
      // Single row header
      const headerRow = hasRowHeaders ? 
        Array(headerColsCount).fill('').concat(headers) : headers;
      
      apaText += headerRow.map((header, index) => {
        if (index < headerColsCount && hasRowHeaders) {
          return ''.padEnd(maxWidths[index]); // Empty corner cells
        }
        return header.padEnd(maxWidths[index]);
      }).join('  ') + '\n';
      
      // Separator line
      apaText += maxWidths.map(width => '-'.repeat(width)).join('  ') + '\n';
    }

    // Data rows with multi-column row headers
    parsedTable.rows.forEach((row, rowIndex) => {
      let fullRow: string[] = [];
      
      if (hasRowHeaders && numHeaderCols > 1 && multiRowHeaders[rowIndex]) {
        // Multi-column row headers
        fullRow = [...multiRowHeaders[rowIndex], ...row];
      } else if (hasRowHeaders) {
        // Single column row header
        fullRow = [rowHeaders[rowIndex] || '', ...row];
      } else {
        fullRow = row;
      }
      
      const formattedRow = fullRow.map((cell, colIndex) => {
        let formatted = cell;
        
        // Apply formatting based on column type
        if (colIndex < headerColsCount && hasRowHeaders) {
          // Row header - left aligned, no special formatting
          return formatted.padEnd(maxWidths[colIndex]);
        } else {
          // Data cell - apply formatting if applicable
          const columnHeader = headers[colIndex - headerColsCount] || '';
          const isPValueCol = isPValueColumn(columnHeader);
          const isPValueCell = isPValueCol || isPValueLike(cell);
          
          if (isPValueCell) {
            formatted = formatPValue(cell);
            return formatted.padStart(maxWidths[colIndex]);
          } else if (apaOptions.rightAlignNumbers && isNumeric(cell)) {
            formatted = formatNumericValue(cell);
            return formatted.padStart(maxWidths[colIndex]);
          }
          return formatted.padEnd(maxWidths[colIndex]);
        }
      });
      apaText += formattedRow.join('  ') + '\n';
    });

    if (apaOptions.tableNote) {
      apaText += `\n${apaOptions.tableNote}`;
    }

    return apaText;
  };

  // Download as text file
  const downloadAsText = () => {
    const content = generateAPAText();
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `apa_table_${apaOptions.tableNumber}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    setSnackbarMessage('Table downloaded successfully!');
    setSnackbarOpen(true);
  };

  // Download as Word document
  const downloadAsWord = async () => {
    if (!parsedTable) return;

    try {
      // Helper function to check if text should be italicized
      const shouldItalicize = (text: string): boolean => {
        return apaOptions.italicizeStatistics && isStatisticalTerm(text);
      };

      // Helper function to format cell values
      const formatCellValue = (text: string, columnHeader: string = ''): string => {
        const isPValueCol = isPValueColumn(columnHeader);
        const isPValueCell = isPValueCol || isPValueLike(text);
        
        if (isPValueCell) {
          return formatPValue(text);
        } else if (apaOptions.rightAlignNumbers && isNumeric(text)) {
          return formatNumericValue(text);
        }
        return text;
      };

      // Create table rows for Word document
      const createTableRows = (): DocxTableRow[] => {
        const rows: DocxTableRow[] = [];

        // Add header rows
        if (parsedTable.hasColumnHeaders) {
          if (parsedTable.multiColumnHeaders && parsedTable.numHeaderRows > 1) {
            // Multiple header rows
            parsedTable.multiColumnHeaders.forEach((headerRow) => {
              const cells = headerRow.map(header => 
                new DocxTableCell({
                  children: [new Paragraph({
                    children: [new TextRun({
                      text: header,
                      bold: true,
                      font: "Times New Roman",
                      size: 20 // 10pt = 20 half-points
                    })],
                    alignment: AlignmentType.CENTER
                  })],
                  width: { size: 100 / headerRow.length, type: WidthType.PERCENTAGE }
                })
              );
              rows.push(new DocxTableRow({ children: cells }));
            });
          } else {
            // Single header row
            const headerCells = parsedTable.headers.map(header => 
              new DocxTableCell({
                children: [new Paragraph({
                  children: [new TextRun({
                    text: header,
                    bold: true,
                    font: "Times New Roman",
                    size: 20
                  })],
                  alignment: AlignmentType.CENTER
                })],
                width: { size: 100 / parsedTable.headers.length, type: WidthType.PERCENTAGE }
              })
            );
            rows.push(new DocxTableRow({ children: headerCells }));
          }
        }

        // Add data rows
        parsedTable.rows.forEach((row, rowIndex) => {
          let fullRow: string[] = [];
          
          // Handle row headers
          if (parsedTable.hasRowHeaders && parsedTable.numHeaderCols > 1 && parsedTable.multiRowHeaders?.[rowIndex]) {
            fullRow = [...parsedTable.multiRowHeaders[rowIndex], ...row];
          } else if (parsedTable.hasRowHeaders && parsedTable.rowHeaders) {
            fullRow = [parsedTable.rowHeaders[rowIndex] || '', ...row];
          } else {
            fullRow = row;
          }

          const cells = fullRow.map((cell, cellIndex) => {
            const isRowHeader = parsedTable.hasRowHeaders && cellIndex < parsedTable.numHeaderCols;
            const columnHeader = isRowHeader ? '' : parsedTable.headers[cellIndex - (parsedTable.hasRowHeaders ? parsedTable.numHeaderCols : 0)] || '';
            const formattedValue = formatCellValue(cell, columnHeader);
            const isPValueCol = isPValueColumn(columnHeader);
            const isPValueCell = isPValueCol || isPValueLike(cell);
            const isItalic = shouldItalicize(cell) || (apaOptions.italicizeStatistics && isPValueCell);
            const isNumericCell = isNumeric(cell);
            const shouldRightAlign = (isNumericCell || isPValueCell) && apaOptions.rightAlignNumbers;

            return new DocxTableCell({
              children: [new Paragraph({
                children: [new TextRun({
                  text: formattedValue,
                  bold: isRowHeader,
                  italics: isItalic,
                  font: "Times New Roman",
                  size: 20
                })],
                alignment: shouldRightAlign ? AlignmentType.RIGHT : 
                          isRowHeader ? AlignmentType.LEFT : AlignmentType.CENTER
              })],
              width: { size: 100 / fullRow.length, type: WidthType.PERCENTAGE }
            });
          });

          rows.push(new DocxTableRow({ children: cells }));
        });

        return rows;
      };

      // Create the Word document
      const doc = new Document({
        sections: [{
          children: [
            // Table number
            new Paragraph({
              children: [new TextRun({
                text: `Table ${apaOptions.tableNumber}`,
                bold: true,
                font: "Times New Roman",
                size: 20
              })],
              spacing: { after: 120 } // 6pt spacing
            }),
            
            // Table title
            new Paragraph({
              children: [new TextRun({
                text: apaOptions.tableTitle,
                italics: true,
                font: "Times New Roman",
                size: 20
              })],
              spacing: { after: 240 } // 12pt spacing
            }),
            
            // The table
            new DocxTable({
              rows: createTableRows(),
              width: { size: 100, type: WidthType.PERCENTAGE },
              borders: {
                top: { style: "single", size: 1 },
                bottom: { style: "single", size: 1 },
                left: { style: "none" },
                right: { style: "none" },
                insideHorizontal: { style: "single", size: 1 },
                insideVertical: { style: "none" }
              }
            }),
            
            // Table note
            ...(apaOptions.tableNote ? [new Paragraph({
              children: [new TextRun({
                text: apaOptions.tableNote,
                font: "Times New Roman",
                size: 20
              })],
              spacing: { before: 240 } // 12pt spacing
            })] : [])
          ]
        }]
      });

      // Generate and download the document
      const buffer = await Packer.toBuffer(doc);
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      saveAs(blob, `apa_table_${apaOptions.tableNumber}.docx`);
      
      setSnackbarMessage('Word document downloaded successfully!');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error generating Word document:', error);
      setSnackbarMessage('Error generating Word document');
       setSnackbarOpen(true);
     }
   };

  // Render raw input section
  const renderInputSection = () => (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
        Input Table Data
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel>Input Format</InputLabel>
            <Select
              value={inputFormat}
              label="Input Format"
              onChange={(e) => setInputFormat(e.target.value as any)}
            >
              <MenuItem value="auto">Auto-detect</MenuItem>
              <MenuItem value="csv">Comma-separated (CSV)</MenuItem>
              <MenuItem value="tsv">Tab-separated (TSV)</MenuItem>
              <MenuItem value="spaces">Space-separated</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            <Button
              variant="outlined"
              size="small"
              onClick={loadSampleData}
              startIcon={<SchoolIcon />}
              sx={{ textTransform: 'none' }}
            >
              Sample Data
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={loadSampleDataWithRowHeaders}
              startIcon={<TableChartIcon />}
              sx={{ textTransform: 'none' }}
            >
              Row+Col Headers
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={loadSampleDataMultiHeaders}
              startIcon={<PsychologyIcon />}
              sx={{ textTransform: 'none' }}
            >
              Multi Headers
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={clearAll}
              startIcon={<ClearIcon />}
              sx={{ textTransform: 'none' }}
            >
              Clear All
            </Button>

          </Stack>
        </Grid>
      </Grid>

      <TextField
        fullWidth
        multiline
        rows={12}
        placeholder="Paste your table data here...

Example formats:
• CSV: Variable,Mean,SD,p
• TSV: Variable	Mean	SD	p
• Spaces: Variable    Mean    SD    p

With row headers:
• ,Group A,Group B,Group C
• Male,45,38,52
• Female,42,47,39"
        value={rawInput}
        onChange={(e) => setRawInput(e.target.value)}
        variant="outlined"
        sx={{ 
          mb: 2,
          '& .MuiInputBase-root': {
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            minHeight: '300px'
          }
        }}
      />

      <Button
        variant="contained"
        startIcon={<TransformIcon />}
        onClick={() => parseTable()}
        disabled={!rawInput.trim() || loading}
        sx={{ 
          borderRadius: 2,
          textTransform: 'none',
          fontWeight: 600
        }}
      >
        {loading ? 'Parsing...' : 'Parse Table'}
      </Button>
    </Box>
  );

  // Render APA options section
  const renderAPAOptions = () => (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
        APA Formatting Options
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <TextField
              fullWidth
              label="Table Number"
              value={apaOptions.tableNumber}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableNumber: e.target.value }))}
              size="small"
            />
            
            <TextField
              fullWidth
              label="Table Title"
              value={apaOptions.tableTitle}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableTitle: e.target.value }))}
              size="small"
            />
            
            <TextField
              fullWidth
              multiline
              rows={2}
              label="Table Note"
              value={apaOptions.tableNote}
              onChange={(e) => setApaOptions(prev => ({ ...prev, tableNote: e.target.value }))}
              size="small"
              placeholder="Note. *p < .05. **p < .01. ***p < .001."
            />
          </Stack>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Grid container spacing={1}> {/* Use a nested Grid for better control */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.includeStatistics}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, includeStatistics: e.target.checked }))}
                  />
                }
                label="Include Statistical Formatting"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.italicizeStatistics}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, italicizeStatistics: e.target.checked }))}
                  />
                }
                label="Italicize Statistical Terms"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.rightAlignNumbers}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, rightAlignNumbers: e.target.checked }))}
                  />
                }
                label="Right-align Numbers"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.includeSignificance}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, includeSignificance: e.target.checked }))}
                  />
                }
                label="Add Significance Indicators"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                Table Structure Detection
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.detectRowHeaders}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, detectRowHeaders: e.target.checked }))}
                  />
                }
                label="Auto-detect Row/Column Headers"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.treatFirstRowAsColumnHeaders}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, treatFirstRowAsColumnHeaders: e.target.checked }))}
                    disabled={apaOptions.detectRowHeaders}
                  />
                }
                label="First Row as Column Headers"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={apaOptions.treatFirstColumnAsRowHeaders}
                    onChange={(e) => setApaOptions(prev => ({ ...prev, treatFirstColumnAsRowHeaders: e.target.checked }))}
                    disabled={apaOptions.detectRowHeaders}
                  />
                }
                label="First Column as Row Headers"
              />
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Header Rows</InputLabel>
                <Select
                  value={apaOptions.numHeaderRows}
                  label="Header Rows"
                  onChange={(e) => setApaOptions(prev => ({ ...prev, numHeaderRows: Number(e.target.value) }))}
                  disabled={apaOptions.detectRowHeaders}
                >
                  <MenuItem value={1}>1 Row</MenuItem>
                  <MenuItem value={2}>2 Rows</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth size="small">
                <InputLabel>Header Columns</InputLabel>
                <Select
                  value={apaOptions.numHeaderCols}
                  label="Header Columns"
                  onChange={(e) => setApaOptions(prev => ({ ...prev, numHeaderCols: Number(e.target.value) }))}
                  disabled={apaOptions.detectRowHeaders}
                >
                  <MenuItem value={1}>1 Column</MenuItem>
                  <MenuItem value={2}>2 Columns</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                type="number"
                label="Decimal Places"
                value={apaOptions.decimalPlaces}
                onChange={(e) => setApaOptions(prev => ({ ...prev, decimalPlaces: parseInt(e.target.value) || 3 }))}
                size="small"
                inputProps={{ min: 0, max: 6 }}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );

  // Render preview table
  const renderPreviewTable = () => {
    if (!parsedTable) return null;

    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
            APA Formatted Table Preview
          </Typography>
          
          <Stack direction="row" spacing={1}>
            <Button
              variant={isEditMode ? "contained" : "outlined"}
              size="small"
              onClick={() => setIsEditMode(!isEditMode)}
              startIcon={<EditIcon />}
              sx={{ textTransform: 'none' }}
              color={isEditMode ? "primary" : "inherit"}
            >
              {isEditMode ? 'Exit Edit' : 'Edit Mode'}
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<ContentCopyIcon />}
              onClick={() => copyToClipboard(generateAPAText())}
              sx={{ textTransform: 'none' }}
            >
              Copy Table
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<DownloadIcon />}
              onClick={downloadAsText}
              sx={{ textTransform: 'none' }}
            >
              Download
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<DescriptionIcon />}
              onClick={downloadAsWord}
              sx={{ textTransform: 'none' }}
            >
              Word
            </Button>
          </Stack>
        </Box>

        <Paper variant="outlined" sx={{ p: 3, borderRadius: 2 }}>
          {/* Table Number and Title */}
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            Table {apaOptions.tableNumber}
          </Typography>
          <Typography variant="subtitle1" sx={{ fontStyle: 'italic', mb: 3 }}>
            {apaOptions.tableTitle}
          </Typography>

          {/* The actual table */}
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <TableContainer>
              <Table size="small" sx={{
                '& .MuiTableCell-root': {
                  border: 'none',
                  borderBottom: theme.palette.mode === 'dark'
                    ? '1px solid rgba(255, 255, 255, 0.15)'
                    : '1px solid #ddd'
                }
              }}>
              <TableHead>
                {/* Multi-row column headers */}
                {parsedTable.hasColumnHeaders && parsedTable.numHeaderRows > 1 && parsedTable.multiColumnHeaders ? (
                  parsedTable.multiColumnHeaders.map((headerRow, headerRowIndex) => (
                    <TableRow key={`header-${headerRowIndex}`} sx={{
                      borderTop: headerRowIndex === 0 ? (theme.palette.mode === 'dark'
                        ? '2px solid rgba(255, 255, 255, 0.8)'
                        : '2px solid #000') : 'none',
                      borderBottom: headerRowIndex === parsedTable.multiColumnHeaders!.length - 1 ? (theme.palette.mode === 'dark'
                        ? '1px solid rgba(255, 255, 255, 0.8)'
                        : '1px solid #000') : 'none'
                    }}>
                      {/* Empty cells for row headers */}
                      {parsedTable.hasRowHeaders && Array.from({ length: parsedTable.numHeaderCols }).map((_, index) => (
                        <TableCell key={`empty-${index}`} sx={{ fontWeight: 600 }}>
                          {/* Empty corner cells */}
                        </TableCell>
                      ))}
                      {/* Column headers */}
                      <SortableContext items={headerRow.map((_, index) => `header-${index}`)} strategy={horizontalListSortingStrategy}>
                        {headerRow.map((header, index) => (
                          isEditMode ? (
                            <SortableHeaderCell
                              key={`header-${index}`}
                              id={`header-${index}`}
                              header={header}
                              columnIndex={index}
                              sx={{
                                fontWeight: 600,
                                fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal',
                                textAlign: apaOptions.rightAlignNumbers ? 'right' : 'left'
                              }}
                            />
                          ) : (
                            <TableCell 
                              key={index}
                              sx={{ 
                                fontWeight: 600,
                                fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal',
                                textAlign: apaOptions.rightAlignNumbers ? 'right' : 'left'
                              }}
                            >
                              {header}
                            </TableCell>
                          )
                        ))}
                      </SortableContext>
                    </TableRow>
                  ))
                ) : parsedTable.hasColumnHeaders ? (
                  /* Single row column headers */
                  <TableRow sx={{
                    borderTop: theme.palette.mode === 'dark'
                      ? '2px solid rgba(255, 255, 255, 0.8)'
                      : '2px solid #000',
                    borderBottom: theme.palette.mode === 'dark'
                      ? '1px solid rgba(255, 255, 255, 0.8)'
                      : '1px solid #000'
                  }}>
                    {/* Empty cells for row headers */}
                    {parsedTable.hasRowHeaders && Array.from({ length: parsedTable.numHeaderCols }).map((_, index) => (
                      <TableCell key={`empty-${index}`} sx={{ fontWeight: 600 }}>
                        {/* Empty corner cells */}
                      </TableCell>
                    ))}
                    {/* Column headers */}
                    <SortableContext items={parsedTable.headers.map((_, index) => `header-${index}`)} strategy={horizontalListSortingStrategy}>
                      {parsedTable.headers.map((header, index) => (
                        isEditMode ? (
                          <SortableHeaderCell
                            key={`header-${index}`}
                            id={`header-${index}`}
                            header={header}
                            columnIndex={index}
                            sx={{
                              fontWeight: 600,
                              fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal',
                              textAlign: apaOptions.rightAlignNumbers ? 'right' : 'left'
                            }}
                          />
                        ) : (
                          <TableCell 
                            key={index}
                            sx={{ 
                              fontWeight: 600,
                              fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal',
                              textAlign: apaOptions.rightAlignNumbers ? 'right' : 'left'
                            }}
                          >
                            {header}
                          </TableCell>
                        )
                      ))}
                    </SortableContext>
                  </TableRow>
                ) : null}
              </TableHead>
              <TableBody>
                <SortableContext items={parsedTable.rows.map((_, index) => `row-${index}`)} strategy={verticalListSortingStrategy}>
                  {parsedTable.rows.map((row, rowIndex) => (
                    isEditMode ? (
                      <SortableTableRow key={`row-${rowIndex}`} id={`row-${rowIndex}`} rowIndex={rowIndex}>
                        {/* Multi-column row headers */}
                        {parsedTable.hasRowHeaders && parsedTable.numHeaderCols > 1 && parsedTable.multiRowHeaders ? (
                          parsedTable.multiRowHeaders[rowIndex]?.map((header, headerIndex) => (
                            <TableCell 
                              key={`row-header-${headerIndex}`}
                              sx={{ 
                                fontWeight: 600,
                                textAlign: 'left',
                                fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal'
                              }}
                            >
                              {header}
                            </TableCell>
                          ))
                        ) : parsedTable.hasRowHeaders ? (
                          /* Single column row header */
                          <TableCell 
                            sx={{ 
                              fontWeight: 600,
                              textAlign: 'left',
                              fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(parsedTable.rowHeaders?.[rowIndex] || '') ? 'italic' : 'normal'
                            }}
                          >
                            {parsedTable.rowHeaders?.[rowIndex] || ''}
                          </TableCell>
                        ) : null}
                        {/* Data cells */}
                        {row.map((cell, cellIndex) => {
                          // The headers array already excludes row header columns, so cellIndex maps directly
                          const columnHeader = parsedTable.headers[cellIndex] || '';
                          const isPValueCol = isPValueColumn(columnHeader);
                          const isPValueLike_result = isPValueLike(cell);
                          const isPValueCell = isPValueCol || isPValueLike_result;
                          const isNumericCell = isNumeric(cell);
                          
                          let formattedCell = cell;
                          if (isPValueCell) {
                            formattedCell = formatPValue(cell);
                          } else if (apaOptions.rightAlignNumbers && isNumericCell) {
                            formattedCell = formatNumericValue(cell);
                          }
                          
                          return (
                            <TableCell 
                              key={cellIndex}
                              sx={{ 
                                textAlign: (apaOptions.rightAlignNumbers && (isNumericCell || isPValueCell)) ? 'right' : 'left',
                                fontStyle: apaOptions.italicizeStatistics && (isStatisticalTerm(cell) || isPValueCell) ? 'italic' : 'normal'
                              }}
                            >
                              {formattedCell}
                            </TableCell>
                          );
                        })}
                      </SortableTableRow>
                    ) : (
                      <TableRow key={rowIndex}>
                    {/* Multi-column row headers */}
                    {parsedTable.hasRowHeaders && parsedTable.numHeaderCols > 1 && parsedTable.multiRowHeaders ? (
                      parsedTable.multiRowHeaders[rowIndex]?.map((header, headerIndex) => (
                        <TableCell 
                          key={`row-header-${headerIndex}`}
                          sx={{ 
                            fontWeight: 600,
                            textAlign: 'left',
                            fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(header) ? 'italic' : 'normal'
                          }}
                        >
                          {header}
                        </TableCell>
                      ))
                    ) : parsedTable.hasRowHeaders ? (
                      /* Single column row header */
                      <TableCell 
                        sx={{ 
                          fontWeight: 600,
                          textAlign: 'left',
                          fontStyle: apaOptions.italicizeStatistics && isStatisticalTerm(parsedTable.rowHeaders?.[rowIndex] || '') ? 'italic' : 'normal'
                        }}
                      >
                        {parsedTable.rowHeaders?.[rowIndex] || ''}
                      </TableCell>
                    ) : null}
                    {/* Data cells */}
                    {row.map((cell, cellIndex) => {
                      // The headers array already excludes row header columns, so cellIndex maps directly
                      const columnHeader = parsedTable.headers[cellIndex] || '';
                      const isPValueCol = isPValueColumn(columnHeader);
                      const isPValueLike_result = isPValueLike(cell);
                      const isPValueCell = isPValueCol || isPValueLike_result;
                      const isNumericCell = isNumeric(cell);
                      

                       
                       let formattedCell = cell;
                       if (isPValueCell) {
                         formattedCell = formatPValue(cell);
                      } else if (apaOptions.rightAlignNumbers && isNumericCell) {
                        formattedCell = formatNumericValue(cell);
                      }
                      
                      return (
                        <TableCell 
                          key={cellIndex}
                          sx={{ 
                            textAlign: (apaOptions.rightAlignNumbers && (isNumericCell || isPValueCell)) ? 'right' : 'left',
                            fontStyle: apaOptions.italicizeStatistics && (isStatisticalTerm(cell) || isPValueCell) ? 'italic' : 'normal'
                          }}
                        >
                          {formattedCell}
                        </TableCell>
                      );
                    })}
                      </TableRow>
                    )
                  ))}
                </SortableContext>
              </TableBody>
            </Table>
          </TableContainer>
        </DndContext>

          {/* Table Note */}
          {apaOptions.tableNote && (
            <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
              {apaOptions.tableNote}
            </Typography>
          )}
        </Paper>

        {/* Text preview */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
            Text Format Preview
          </Typography>
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 2, 
              backgroundColor: theme.palette.grey[50],
              borderRadius: 2
            }}
          >
            <Typography 
              component="pre" 
              variant="body2" 
              sx={{ 
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                fontSize: '0.875rem'
              }}
            >
              {generateAPAText()}
            </Typography>
          </Paper>
        </Box>
      </Box>
    );
  };

  // Sortable Table Row Component
  const SortableTableRow: React.FC<{
    id: string;
    children: React.ReactNode;
    isDragging?: boolean;
  }> = ({ id, children, isDragging }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
    } = useSortable({ id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging || isSortableDragging ? 0.5 : 1,
    };

    return (
      <TableRow
        ref={setNodeRef}
        style={style}
        {...attributes}
        sx={{
          cursor: isEditMode ? 'grab' : 'default',
          '&:active': {
            cursor: isEditMode ? 'grabbing' : 'default',
          },
        }}
      >
        {isEditMode && (
          <TableCell
            {...listeners}
            sx={{
              width: 40,
              padding: '4px 8px',
              borderRight: `1px solid ${theme.palette.divider}`,
            }}
          >
            <DragIcon fontSize="small" color="action" />
          </TableCell>
        )}
        {children}
      </TableRow>
    );
  };

  // Sortable Table Header Cell Component
  const SortableHeaderCell: React.FC<{
    id: string;
    header: string;
    columnIndex: number;
    sx?: any;
    isDragging?: boolean;
  }> = ({ id, header, columnIndex, sx, isDragging }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
    } = useSortable({ id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging || isSortableDragging ? 0.5 : 1,
    };

    return (
      <TableCell
        ref={setNodeRef}
        style={style}
        {...attributes}
        sx={{
          ...sx,
          cursor: isEditMode ? 'grab' : 'default',
          '&:active': {
            cursor: isEditMode ? 'grabbing' : 'default',
          },
          position: 'relative',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ flex: 1 }}>
            {header}
          </Box>
          {isEditMode && (
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
              <Tooltip title="Delete column">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeHeaderColumn(columnIndex);
                  }}
                  sx={{ mr: 0.5, p: 0.25 }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Box {...listeners} sx={{ display: 'flex', cursor: 'grab' }}>
                <DragIcon fontSize="small" color="action" />
              </Box>
            </Box>
          )}
        </Box>
      </TableCell>
    );
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
          Convert to APA Table Format
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Transform your raw data tables into properly formatted APA-style tables for academic publications
        </Typography>
      </Box>

      {/* Configuration Panel */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardHeader 
          title="Table Converter"
          avatar={<TableChartIcon color="primary" />}
          sx={{ pb: 1 }}
        />
        <CardContent>
          {/* View Mode Selection */}
          {parsedTable && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500 }}>
                Display Mode
              </Typography>
              <ToggleButtonGroup
                value={viewMode}
                exclusive
                onChange={handleViewModeChange}
                size="small"
                sx={{ mb: 2 }}
              >
                <ToggleButton value="tabs" aria-label="tabs view">
                  <ViewCarouselIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Tabs'}
                </ToggleButton>
                <ToggleButton value="stacked" aria-label="stacked view">
                  <ViewListIcon sx={{ mr: 1 }} />
                  {!isMobile && 'Stacked'}
                </ToggleButton>
                {!isMobile && (
                  <ToggleButton value="side-by-side" aria-label="side by side view">
                    <ViewModuleIcon sx={{ mr: 1 }} />
                    Side by Side
                  </ToggleButton>
                )}
              </ToggleButtonGroup>
            </Box>
          )}

          {/* APA Guidelines Information */}
          <Accordion defaultExpanded={false}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                APA Table Guidelines
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" paragraph>
                This tool helps format tables according to APA 7th edition guidelines:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Table number and title:</strong> Above the table, flush left
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Column headers:</strong> Brief, descriptive labels
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Number alignment:</strong> Right-aligned for consistency
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Statistical symbols:</strong> Italicized (M, SD, p, etc.)
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>P-values:</strong> {'Report as p = .xxx or p < .001'}
                </Typography>
                <Typography component="li" variant="body2" color="text.secondary">
                  <strong>Table notes:</strong> Below table for significance levels
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          {!parsedTable ? (
            <Box sx={{ mt: 3 }}>
              {renderInputSection()}
            </Box>
          ) : (
            <Box sx={{ mt: 3 }}>
              {viewMode === 'tabs' ? (
                <Box>
                  <Tabs 
                    value={activeTab} 
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
                  >
                    <Tab 
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SettingsIcon fontSize="small" />
                          {!isMobile && 'Input & Options'}
                        </Box>
                      } 
                    />
                    <Tab 
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <PreviewIcon fontSize="small" />
                          {!isMobile && 'Preview'}
                        </Box>
                      } 
                    />
                  </Tabs>
                  <TabPanel value={activeTab} index={0}>
                    <Stack spacing={3}>
                      {renderInputSection()}
                      <Divider />
                      {renderAPAOptions()}
                    </Stack>
                  </TabPanel>
                  <TabPanel value={activeTab} index={1}>
                    {renderPreviewTable()}
                  </TabPanel>
                </Box>
              ) : viewMode === 'stacked' ? (
                <Stack spacing={4}>
                  {renderInputSection()}
                  <Divider />
                  {renderAPAOptions()}
                  <Divider />
                  {renderPreviewTable()}
                </Stack>
              ) : (
                <Grid container spacing={4}>
                  <Grid item xs={12} lg={6}>
                    <Stack spacing={3}>
                      {renderInputSection()}
                      <Divider />
                      {renderAPAOptions()}
                    </Stack>
                  </Grid>
                  <Grid item xs={12} lg={6}>
                    {renderPreviewTable()}
                  </Grid>
                </Grid>
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card elevation={2}>
          <CardContent>
            <Box display="flex" flexDirection="column" alignItems="center" py={4}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Processing Table...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Parsing and formatting your data
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, borderRadius: 2 }}
          action={
            <Button color="inherit" size="small" onClick={() => setError(null)}>
              Dismiss
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Empty State */}
      {!loading && !parsedTable && !rawInput && (
        <Card elevation={1}>
          <CardContent>
            <Box textAlign="center" py={6}>
              <TableChartIcon sx={{ fontSize: 80, color: theme.palette.grey[400], mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Ready to Convert
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Paste your raw table data above to get started with APA formatting
              </Typography>
              <Button
                variant="outlined"
                onClick={loadSampleData}
                startIcon={<SchoolIcon />}
                sx={{ textTransform: 'none' }}
              >
                Try with Sample Data
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default ConvertToAPA;
