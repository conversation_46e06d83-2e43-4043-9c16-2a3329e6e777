import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Slider,
  TextField,
  Button,
  useTheme,
  alpha,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  ToggleButtonGroup,
  ToggleButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Help as HelpIcon,
  ContentCopy as ContentCopyIcon,
  PictureAsPdf as PictureAsPdfIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';
import { getZScore, calculateOneWayAnovaSampleSize, calculateFactorialAnovaSampleSize, calculateRepeatedMeasuresAnovaSampleSize, calculateOneWayAnovaPower } from '../../utils/stats/sampleSize';

const MoreThanTwoGroupsCalculator: React.FC = () => {
  // Function to render mathematical formulas
  const renderFormula = (formula: string): string => {
    const result = unified()
      .use(remarkParse)
      .use(remarkMath)
      .use(remarkRehype)
      .use(rehypeKatex)
      .use(rehypeStringify)
      .processSync(`$${formula}$`);
    return String(result);
  };
  const theme = useTheme();

  // State for More than 2 Groups calculator type
  const [moreThanTwoGroupsCalculatorType, setMoreThanTwoGroupsCalculatorType] = useState<string>('oneWayAnova');

  // State for calculator parameters (using twoSampleConfidenceLevel and twoSamplePower for consistency as in original file)
  const [twoSampleConfidenceLevel, setTwoSampleConfidenceLevel] = useState<number>(0.95);
  const [twoSamplePower, setTwoSamplePower] = useState<number>(0.80); // 80% power

  // More than 2 Groups parameters
  const [k, setK] = useState<number>(3); // Number of groups for One-way ANOVA
  const [deltaAnova, setDeltaAnova] = useState<number>(5); // Minimum detectable effect size for ANOVA
  const [sigmaAnova, setSigmaAnova] = useState<number>(10); // Common standard deviation for ANOVA
  // const [m, setM] = useState<number>(2); // Number of levels for factor A for Factorial ANOVA - Not used yet
  // const [nLevelsFactorB, setNLevelsFactorB] = useState<number>(2); // Number of levels for factor B for Factorial ANOVA - Not used yet
  // const [rhoFactorial, setRhoFactorial] = useState<number>(0.3); // Expected correlation for Factorial ANOVA - Not used yet
  const [rRepeatedMeasures, setRRepeatedMeasures] = useState<number>(4); // Number of repeated measurements for Repeated Measures ANOVA
  const [rhoRepeated, setRhoRepeated] = useState<number>(0.5); // Correlation between repeated measures for Repeated Measures ANOVA
  const [deltaRepeatedMeasures, setDeltaRepeatedMeasures] = useState<number>(2); // Effect size across time or conditions for Repeated Measures ANOVA
  const [sigmaRepeatedMeasures, setSigmaRepeatedMeasures] = useState<number>(6); // Standard deviation for Repeated Measures ANOVA

  // State for results
  const [moreThanTwoGroupsRequiredSize, setMoreThanTwoGroupsRequiredSize] = useState<number | null>(null);
  const [moreThanTwoGroupsPowerCurveData, setMoreThanTwoGroupsPowerCurveData] = useState<any[]>([]);

  // Calculate more than 2 groups sample size when parameters change
  useEffect(() => {
    calculateMoreThanTwoGroupsSampleSize();
    generateMoreThanTwoGroupsPowerCurveData(); // This might need adjustment for Repeated Measures ANOVA
  }, [twoSampleConfidenceLevel, twoSamplePower, k, deltaAnova, sigmaAnova, rRepeatedMeasures, rhoRepeated, deltaRepeatedMeasures, sigmaRepeatedMeasures, moreThanTwoGroupsCalculatorType]);
  
  // Function to calculate required sample size for more than 2 groups
  const calculateMoreThanTwoGroupsSampleSize = () => {
    let sampleSize: number;
    const alpha = 1 - twoSampleConfidenceLevel;
    const power = twoSamplePower;

    switch (moreThanTwoGroupsCalculatorType) {
      case 'oneWayAnova':
        sampleSize = calculateOneWayAnovaSampleSize(k, deltaAnova, sigmaAnova, alpha, power);
        break;
      // case 'factorialAnova': // Factorial ANOVA not fully implemented yet
      //   sampleSize = calculateFactorialAnovaSampleSize(m, deltaAnova, sigmaAnova, rhoFactorial, alpha, power);
      //   break;
      case 'repeatedMeasuresAnova':
        sampleSize = calculateRepeatedMeasuresAnovaSampleSize(rRepeatedMeasures, deltaRepeatedMeasures, sigmaRepeatedMeasures, rhoRepeated, alpha, power);
        break;
      default:
        sampleSize = 0;
    }
    setMoreThanTwoGroupsRequiredSize(sampleSize);
  };

  // Generate data for more than 2 groups power curve visualization
  const generateMoreThanTwoGroupsPowerCurveData = () => {
    const data = [];
    const minN = 5; 
    const maxN = 150; 
    const stepN = 5; 

    const alpha = 1 - twoSampleConfidenceLevel; 
    const zAlpha = getZScore(1 - alpha / 2); 
    const zBeta = getZScore(twoSamplePower);

    if (moreThanTwoGroupsCalculatorType === 'oneWayAnova') {
        const commonStdDev = sigmaAnova; 
        const numGroups = k; 

        if (commonStdDev === 0 || numGroups < 2) {
            setMoreThanTwoGroupsPowerCurveData([]);
            return;
        }

        for (let n_per_group = minN; n_per_group <= maxN; n_per_group += stepN) {
            if (n_per_group <= 0) continue; 
            let detectableDifference: number;
            if (n_per_group > 0 && commonStdDev > 0 && numGroups > 0) {
                detectableDifference = (zAlpha + zBeta) * commonStdDev * Math.sqrt(numGroups / n_per_group);
            } else {
                detectableDifference = Infinity; 
            }
            data.push({
                sampleSize: n_per_group,
                detectableDifference: detectableDifference, 
            });
        }
    } else if (moreThanTwoGroupsCalculatorType === 'repeatedMeasuresAnova') {
        // Power curve for Repeated Measures ANOVA might be different or not applicable in the same way
        // For now, let's clear it or adapt if a similar curve makes sense
        // Formula: n = ( (Z_α + Z_β)^2 * σ^2 * (1 - ρ) ) / (Δ^2 * r)
        // To find Δ for a given n: Δ^2 = ( (Z_α + Z_β)^2 * σ^2 * (1 - ρ) ) / (n * r)
        // Δ = Math.sqrt( ( (Z_α + Z_β)^2 * σ^2 * (1 - ρ) ) / (n * r) )
        const stdDev = sigmaRepeatedMeasures;
        const correlation = rhoRepeated;
        const numMeasurements = rRepeatedMeasures;

        if (stdDev === 0 || numMeasurements <= 1 || correlation < -1 || correlation > 1) {
            setMoreThanTwoGroupsPowerCurveData([]);
            return;
        }

        for (let n_participants = minN; n_participants <= maxN; n_participants += stepN) {
            if (n_participants <= 0) continue;
            let detectableEffectSize: number;
            if (n_participants > 0 && stdDev > 0 && numMeasurements > 0 && (1-correlation) > 0) {
                detectableEffectSize = Math.sqrt( (Math.pow(zAlpha + zBeta, 2) * Math.pow(stdDev, 2) * (1 - correlation)) / (n_participants * numMeasurements) );
            } else {
                detectableEffectSize = Infinity;
            }
            data.push({
                sampleSize: n_participants, // Here sampleSize refers to total participants
                detectableDifference: detectableEffectSize, // Y-axis value (effect size Δ)
            });
        }
    }
    setMoreThanTwoGroupsPowerCurveData(data);
  };
  
  // Handle more than 2 groups calculator type change
  const handleMoreThanTwoGroupsCalculatorTypeChange = (_event: React.MouseEvent<HTMLElement>, newValue: string) => {
    if (newValue !== null) {
      setMoreThanTwoGroupsCalculatorType(newValue);
      // Set default values based on calculator type
      if (newValue === 'oneWayAnova') {
        setK(3);
        setDeltaAnova(5);
        setSigmaAnova(10);
      } 
      // else if (newValue === 'factorialAnova') { // Factorial ANOVA not fully implemented
      //   setM(2);
      //   setNLevelsFactorB(2);
      //   setDeltaAnova(3); // Re-using deltaAnova for now
      //   setSigmaAnova(8); // Re-using sigmaAnova for now
      //   setRhoFactorial(0.3);
      // }
       else if (newValue === 'repeatedMeasuresAnova') {
        setRRepeatedMeasures(4);
        setDeltaRepeatedMeasures(2);
        setSigmaRepeatedMeasures(6);
        setRhoRepeated(0.5);
      }
    }
  };
  
  // Handle copying more than 2 groups results to clipboard
  const handleCopyMoreThanTwoGroupsResults = () => {
    if (moreThanTwoGroupsRequiredSize) {
      let resultText = '';
      const powerText = `with ${Math.round(twoSamplePower * 100)}% power at a ${Math.round(twoSampleConfidenceLevel * 100)}% confidence level.`;
      
      switch (moreThanTwoGroupsCalculatorType) {
        case 'oneWayAnova':
          resultText = `Required Sample Size: ${moreThanTwoGroupsRequiredSize} per group for a One-way ANOVA with ${k} groups, minimum detectable effect size of ${deltaAnova}, and common standard deviation of ${sigmaAnova}, ${powerText}`;
          break;
        // case 'factorialAnova': // Factorial ANOVA not fully implemented
        //   resultText = `Required Sample Size: ${moreThanTwoGroupsRequiredSize} per cell for a Factorial ANOVA (${m}x${nLevelsFactorB} design) with effect size of ${deltaAnova}, standard deviation of ${sigmaAnova}, and expected correlation of ${rhoFactorial}, ${powerText}`;
        //   break;
        case 'repeatedMeasuresAnova':
          resultText = `Required Sample Size: ${moreThanTwoGroupsRequiredSize} participants for a Repeated Measures ANOVA with ${rRepeatedMeasures} measurements, effect size of ${deltaRepeatedMeasures}, standard deviation of ${sigmaRepeatedMeasures}, and correlation between measures of ${rhoRepeated}, ${powerText}`;
          break;
        default:
          resultText = 'No results to copy.';
      }
      navigator.clipboard.writeText(resultText);
    }
  };

  // Handle exporting results as PDF
  const handleExportPDF = () => {
    // For now, just a placeholder
    console.log('Export PDF functionality would go here');
  };

  // Handle reset for more than 2 groups
  const handleMoreThanTwoGroupsReset = () => {
    setTwoSampleConfidenceLevel(0.95);
    setTwoSamplePower(0.80);
    // Reset One-Way ANOVA params
    setK(3);
    setDeltaAnova(5);
    setSigmaAnova(10);
    // Reset Factorial ANOVA params (if/when implemented)
    // setM(2);
    // setNLevelsFactorB(2);
    // setRhoFactorial(0.3);
    // Reset Repeated Measures ANOVA params
    setRRepeatedMeasures(4);
    setDeltaRepeatedMeasures(2);
    setSigmaRepeatedMeasures(6);
    setRhoRepeated(0.5);
    
    // Recalculate with defaults based on current/default calculator type
    // setTimeout is a small hack to ensure state updates before recalculation if needed
    setTimeout(() => calculateMoreThanTwoGroupsSampleSize(), 0);
  };

  return (
    <Box sx={{ p: 3 }}>
      <ToggleButtonGroup
        value={moreThanTwoGroupsCalculatorType}
        exclusive
        onChange={handleMoreThanTwoGroupsCalculatorTypeChange}
        aria-label="More than 2 Groups Calculator Type"
        fullWidth
        sx={{ mb: 3 }}
      >
        <ToggleButton
          value="oneWayAnova"
          aria-label="One-Way ANOVA"
          sx={{
            '&.Mui-selected': {
              bgcolor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
              '&:hover': {
                bgcolor: theme.palette.primary.dark,
              },
            },
          }}
        >
          One-Way ANOVA
        </ToggleButton>
        <ToggleButton
          value="repeatedMeasuresAnova"
          aria-label="Repeated Measures ANOVA"
          sx={{
            '&.Mui-selected': {
              bgcolor: theme.palette.secondary.main,
              color: theme.palette.secondary.contrastText,
              '&:hover': {
                bgcolor: theme.palette.secondary.dark,
              },
            },
          }}
        >
          Repeated Measures ANOVA
        </ToggleButton>
      </ToggleButtonGroup>

      {/* Formulas Section */}
      <Accordion sx={{ mb: 3 }}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="formulas-content"
          id="formulas-header"
        >
          <Typography variant="h6">📐 Mathematical Formulas</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {moreThanTwoGroupsCalculatorType === 'oneWayAnova' && (
            <Box>
              <Typography variant="h6" gutterBottom color="primary">
                One-Way ANOVA Sample Size
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Sample Size Formula:
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                <div dangerouslySetInnerHTML={{ 
                  __html: renderFormula('n = \\frac{(Z_{\\alpha} + Z_{\\beta})^2 \\sigma^2 k}{\\Delta^2}')
                }} />
              </Box>
              
              <Typography variant="subtitle2" gutterBottom>
                Variable Definitions:
              </Typography>
              <Box sx={{ ml: 2 }}>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('n') }} style={{ display: 'inline-block' }} /> = Sample size per group
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\alpha}') }} style={{ display: 'inline-block' }} /> = Critical value for Type I error (α)
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\beta}') }} style={{ display: 'inline-block' }} /> = Critical value for Type II error (β)
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('\\sigma') }} style={{ display: 'inline-block' }} /> = Common standard deviation
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('k') }} style={{ display: 'inline-block' }} /> = Number of groups
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('\\Delta') }} style={{ display: 'inline-block' }} /> = Minimum detectable difference between means
                </Typography>
              </Box>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Effect Size (Cohen's f):
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                <div dangerouslySetInnerHTML={{ 
                  __html: renderFormula('f = \\frac{\\Delta}{\\sigma}')
                }} />
              </Box>
              
              <Typography variant="subtitle2" gutterBottom>
                Key Assumptions:
              </Typography>
              <Box sx={{ ml: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Independence of observations
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Normal distribution within groups
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Homogeneity of variances (equal σ across groups)
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Random sampling from populations
                </Typography>
              </Box>
            </Box>
          )}
          
          {moreThanTwoGroupsCalculatorType === 'repeatedMeasuresAnova' && (
            <Box>
              <Typography variant="h6" gutterBottom color="secondary">
                Repeated Measures ANOVA Sample Size
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Sample Size Formula:
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                <div dangerouslySetInnerHTML={{ 
                  __html: renderFormula('n = \\frac{(Z_{\\alpha} + Z_{\\beta})^2 \\sigma^2 (1 - \\rho)}{\\Delta^2 \\cdot r}')
                }} />
              </Box>
              
              <Typography variant="subtitle2" gutterBottom>
                Variable Definitions:
              </Typography>
              <Box sx={{ ml: 2 }}>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('n') }} style={{ display: 'inline-block' }} /> = Number of participants
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\alpha}') }} style={{ display: 'inline-block' }} /> = Critical value for Type I error (α)
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{\\beta}') }} style={{ display: 'inline-block' }} /> = Critical value for Type II error (β)
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('\\sigma') }} style={{ display: 'inline-block' }} /> = Standard deviation of measurements
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('\\rho') }} style={{ display: 'inline-block' }} /> = Correlation between repeated measures
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('\\Delta') }} style={{ display: 'inline-block' }} /> = Effect size across time/conditions
                </Typography>
                <Typography variant="body2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <span dangerouslySetInnerHTML={{ __html: renderFormula('r') }} style={{ display: 'inline-block' }} /> = Number of repeated measurements
                </Typography>
              </Box>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Sphericity Assumption:
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, mb: 2 }}>
                <div dangerouslySetInnerHTML={{ 
                  __html: renderFormula('\\text{Var}(X_i - X_j) = \\text{constant for all } i \\neq j')
                }} />
              </Box>
              
              <Typography variant="subtitle2" gutterBottom>
                Key Assumptions:
              </Typography>
              <Box sx={{ ml: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Independence of participants
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Normal distribution of differences
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Sphericity (equal variances of differences)
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Compound symmetry or sphericity correction
                </Typography>
              </Box>
            </Box>
          )}
            
            {/* Common Information */}
            <Grid container spacing={2}>
              <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Common Z-Scores and Guidelines
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Common Significance Levels (α):
                  </Typography>
                  <Box sx={{ pl: 2, mb: 2 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>α = 0.05: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.025} = 1.96') }} style={{ display: 'inline-block' }} /></Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>α = 0.01: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.005} = 2.58') }} style={{ display: 'inline-block' }} /></Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>α = 0.10: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.05} = 1.64') }} style={{ display: 'inline-block' }} /></Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Common Power Levels (1-β):
                  </Typography>
                  <Box sx={{ pl: 2, mb: 2 }}>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>Power = 0.80: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.20} = 0.84') }} style={{ display: 'inline-block' }} /></Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>Power = 0.90: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.10} = 1.28') }} style={{ display: 'inline-block' }} /></Typography>
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>Power = 0.95: <span dangerouslySetInnerHTML={{ __html: renderFormula('Z_{0.05} = 1.64') }} style={{ display: 'inline-block' }} /></Typography>
                  </Box>
                </Grid>
              </Grid>
              
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Effect Size Guidelines (Cohen's f):
              </Typography>
              <Box sx={{ pl: 2, mb: 2 }}>
                <Typography variant="body2">Small effect: f = 0.10</Typography>
                <Typography variant="body2">Medium effect: f = 0.25</Typography>
                <Typography variant="body2">Large effect: f = 0.40</Typography>
              </Box>
              
              <Typography variant="subtitle2" gutterBottom>
                Interpretation Guidelines:
              </Typography>
              <Box sx={{ pl: 2 }}>
                <Typography variant="body2">Larger sample sizes increase power to detect smaller effects</Typography>
                <Typography variant="body2">Higher correlations in repeated measures reduce required sample size</Typography>
                <Typography variant="body2">More groups in one-way ANOVA require larger sample sizes</Typography>
                <Typography variant="body2">Consider practical significance alongside statistical significance</Typography>
              </Box>
              </Grid>
            </Grid>
        </AccordionDetails>
      </Accordion>

      {moreThanTwoGroupsCalculatorType === 'oneWayAnova' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>One-Way ANOVA Parameters</Typography>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Confidence Level</Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Slider
                      value={twoSampleConfidenceLevel * 100}
                      onChange={(_, newValue) => setTwoSampleConfidenceLevel((newValue as number) / 100)}
                      step={1}
                      min={80}
                      max={99}
                      marks={[
                        { value: 80, label: '80%' },
                        { value: 90, label: '90%' },
                        { value: 95, label: '95%' },
                        { value: 99, label: '99%' },
                      ]}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>
                  Statistical Power (1-β)
                  <Tooltip title="The probability of correctly detecting a true difference between groups.">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Slider
                      value={twoSamplePower * 100}
                      onChange={(_, newValue) => setTwoSamplePower((newValue as number) / 100)}
                      step={5}
                      min={70}
                      max={95}
                      marks={[
                        { value: 70, label: '70%' },
                        { value: 80, label: '80%' },
                        { value: 90, label: '90%' },
                        { value: 95, label: '95%' },
                      ]}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Number of Groups (k)</Typography>
                <TextField
                  type="number"
                  value={k}
                  onChange={(e) => setK(Math.max(2, parseInt(e.target.value, 10) || 2))}
                  fullWidth
                  inputProps={{ min: 2 }}
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Smallest Difference Between Means (Δ)</Typography>
                <TextField
                  type="number"
                  value={deltaAnova}
                  onChange={(e) => setDeltaAnova(parseFloat(e.target.value) || 0)}
                  fullWidth
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Common Standard Deviation (σ)</Typography>
                <TextField
                  type="number"
                  value={sigmaAnova}
                  onChange={(e) => setSigmaAnova(parseFloat(e.target.value) || 0)}
                  fullWidth
                />
              </Box>
              {/* Placeholder for Effect Size (f) if you plan to add it */}
              {/* 
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Effect Size (f) - Cohen's f"
                  type="number"
                  // value={...}
                  // onChange={...}
                  fullWidth
                  margin="normal"
                  helperText="Optional: Calculated if Δ and σ are provided"
                />
              </Grid>
              */}
              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Button variant="outlined" onClick={handleMoreThanTwoGroupsReset} startIcon={<RefreshIcon />}>
                  Reset
                </Button>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Results</Typography>
                <Box>
                  <Tooltip title="Copy results">
                    <IconButton onClick={handleCopyMoreThanTwoGroupsResults}>
                      <ContentCopyIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Export as PDF">
                    <IconButton onClick={handleExportPDF}>
                      <PictureAsPdfIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              {moreThanTwoGroupsRequiredSize !== null && (
                <>
                  <Card sx={{ mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" gutterBottom>Required Sample Size</Typography>
                      <Typography variant="h2" color="primary">
                        {moreThanTwoGroupsRequiredSize}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        participants
                      </Typography>
                    </CardContent>
                  </Card>
                  <Box sx={{ height: 300, mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>Smallest Detectable Difference (Δ) by Sample Size (at {Math.round(twoSamplePower * 100)}% Power)</Typography>
                    {moreThanTwoGroupsPowerCurveData.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                          <LineChart 
                            data={moreThanTwoGroupsPowerCurveData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                              dataKey="sampleSize" 
                              label={{ value: 'Sample Size (per group)', position: 'outerBottom', offset: 15, style: { fontSize: '12px' } }}
                              height={70}
                            />
                            <YAxis 
                              dataKey="detectableDifference" 
                              type="number" 
                              domain={['auto', 'auto']} 
                              name="Smallest Detectable Difference (Δ)" 
                              label={{ value: 'Smallest Detectable Difference (Δ)', angle: -90, position: 'outside', offset: -60, style: { fontSize: '12px' } }}
                              tickFormatter={(value) => value === Infinity ? "∞" : value.toFixed(1)}
                              padding={{ top: 30 }}
                              width={100}
                            />
                            <RechartsTooltip 
                              formatter={(value: number, name: string, props: any) => {
                                if (props.dataKey === 'detectableDifference') {
                                  return [value === Infinity ? "∞" : value.toFixed(2), 'Smallest Detectable Difference (Δ)'];
                                }
                                return [value, name];
                              }}
                              labelFormatter={(label) => `Sample Size per Group: ${label}`}
                            />
                            <Line type="monotone" dataKey="detectableDifference" stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
                          </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <Typography sx={{textAlign: 'center', color: theme.palette.text.secondary, mt:2}}>Detectable difference curve data will appear here after calculation.</Typography>
                    )}
                  </Box>
                  {/* Interpretation Placeholder */}
                  <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
                    Interpretation: To detect a minimum difference of {deltaAnova} between {k} group means with a common standard deviation of {sigmaAnova}, using a significance level of {((1 - twoSampleConfidenceLevel) * 100).toFixed(0)}% and achieving {twoSamplePower * 100}% power, you need approximately {moreThanTwoGroupsRequiredSize} participants per group.
                  </Typography>
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* THIS IS THE SECTION TO CORRECT */}
      {moreThanTwoGroupsCalculatorType === 'repeatedMeasuresAnova' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>Repeated Measures ANOVA Parameters</Typography>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Confidence Level</Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Slider
                      value={twoSampleConfidenceLevel * 100}
                      onChange={(_, newValue) => setTwoSampleConfidenceLevel((newValue as number) / 100)}
                      step={1}
                      min={80}
                      max={99}
                      marks={[
                        { value: 80, label: '80%' },
                        { value: 90, label: '90%' },
                        { value: 95, label: '95%' },
                        { value: 99, label: '99%' },
                      ]}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>
                  Statistical Power (1-β)
                  <Tooltip title="The probability of correctly detecting a true effect across repeated measures.">
                    <IconButton size="small" sx={{ ml: 1 }}><HelpIcon fontSize="small" /></IconButton>
                  </Tooltip>
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <Slider
                      value={twoSamplePower * 100}
                      onChange={(_, newValue) => setTwoSamplePower((newValue as number) / 100)}
                      step={5}
                      min={70}
                      max={95}
                      marks={[
                        { value: 70, label: '70%' },
                        { value: 80, label: '80%' },
                        { value: 90, label: '90%' },
                        { value: 95, label: '95%' },
                      ]}
                    />
                  </Grid>
                </Grid>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Number of Repeated Measurements (r)</Typography>
                <TextField
                  type="number"
                  value={rRepeatedMeasures}
                  onChange={(e) => setRRepeatedMeasures(Math.max(2, parseInt(e.target.value, 10) || 2))}
                  fullWidth
                  inputProps={{ min: 2 }}
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Effect Size Across Time/Conditions (Δ)</Typography>
                <TextField
                  type="number"
                  value={deltaRepeatedMeasures}
                  onChange={(e) => setDeltaRepeatedMeasures(parseFloat(e.target.value) || 0)}
                  fullWidth
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Standard Deviation (σ)</Typography>
                <TextField
                  type="number"
                  value={sigmaRepeatedMeasures}
                  onChange={(e) => setSigmaRepeatedMeasures(parseFloat(e.target.value) || 0)}
                  fullWidth
                />
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography gutterBottom>Correlation Between Repeated Measures (ρ)</Typography>
                <Slider
                  value={rhoRepeated}
                  onChange={(_, newValue) => setRhoRepeated(newValue as number)}
                  step={0.01}
                  min={0}
                  max={0.99}
                  marks={[
                    { value: 0, label: '0.0' },
                    { value: 0.25, label: '0.25' },
                    { value: 0.5, label: '0.5' },
                    { value: 0.75, label: '0.75' },
                    { value: 0.99, label: '0.99' },
                  ]}
                  valueLabelDisplay="auto"
                />
                 <TextField // Added TextField for precise input if needed
                  type="number"
                  value={rhoRepeated}
                  onChange={(e) => {
                    const val = parseFloat(e.target.value);
                    if (!isNaN(val) && val >= 0 && val <= 0.99) {
                      setRhoRepeated(val);
                    }
                  }}
                  fullWidth
                  inputProps={{ step: 0.01, min:0, max:0.99 }}
                  sx={{mt:1}}
                />
              </Box>
              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Button variant="outlined" onClick={handleMoreThanTwoGroupsReset} startIcon={<RefreshIcon />}>
                  Reset
                </Button>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Results</Typography>
                <Box>
                  <Tooltip title="Copy results">
                    <IconButton onClick={handleCopyMoreThanTwoGroupsResults}><ContentCopyIcon /></IconButton>
                  </Tooltip>
                  <Tooltip title="Export as PDF">
                    <IconButton onClick={handleExportPDF}><PictureAsPdfIcon /></IconButton>
                  </Tooltip>
                </Box>
              </Box>
              {moreThanTwoGroupsRequiredSize !== null && (
                <>
                  <Card sx={{ mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Typography variant="h6" gutterBottom>Required Sample Size</Typography>
                      <Typography variant="h2" color="primary">
                        {moreThanTwoGroupsRequiredSize}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        participants
                      </Typography>
                    </CardContent>
                  </Card>
                  <Box sx={{ height: 300, mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>Smallest Detectable Effect Size (Δ) by Sample Size (at {Math.round(twoSamplePower * 100)}% Power)</Typography>
                    {moreThanTwoGroupsPowerCurveData.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart 
                          data={moreThanTwoGroupsPowerCurveData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis 
                    dataKey="sampleSize" 
                    label={{ value: 'Number of Participants', position: 'outerBottom', offset: 15, style: { fontSize: '12px' } }}
                    height={70}
                  />
                          <YAxis 
  dataKey="detectableDifference" 
  type="number" 
  domain={['auto', 'auto']} 
  name="Smallest Detectable Effect Size (Δ)" 
  label={{ value: 'Smallest Detectable Effect Size (Δ)', angle: -90, position: 'outside', offset: -60, style: { fontSize: '12px' } }} 
  tickFormatter={(value) => value === Infinity ? "∞" : value.toFixed(2)} 
  padding={{ top: 30 }}
  width={100}
/>
                          <RechartsTooltip 
                            formatter={(value: number, name: string, props: any) => {
                              if (props.dataKey === 'detectableDifference') {
                                return [value === Infinity ? "∞" : value.toFixed(3), 'Smallest Detectable Effect Size (Δ)'];
                              }
                              return [value, name];
                            }}
                            labelFormatter={(label) => `Number of Participants: ${label}`}
                          />
                          <Line type="monotone" dataKey="detectableDifference" stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
                        </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <Typography sx={{textAlign: 'center', color: theme.palette.text.secondary, mt:2}}>Detectable effect size curve data will appear here after calculation.</Typography>
                    )}
                  </Box>
                  <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
                    Interpretation: To detect an effect size of {deltaRepeatedMeasures} across {rRepeatedMeasures} repeated measurements, with a standard deviation of {sigmaRepeatedMeasures} and a correlation of {rhoRepeated} between measures, using a significance level of {((1 - twoSampleConfidenceLevel) * 100).toFixed(0)}% and achieving {twoSamplePower * 100}% power, you need approximately {moreThanTwoGroupsRequiredSize} participants.
                  </Typography>
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default MoreThanTwoGroupsCalculator;
