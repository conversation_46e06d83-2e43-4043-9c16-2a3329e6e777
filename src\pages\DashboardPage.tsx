import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  Card<PERSON>ontent,
  Button,
  styled,
  Paper,
  Chip,
  IconButton,
  Avatar,
  useTheme,
  alpha,
  Link
} from '@mui/material';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';
import {
  Storage as StorageIcon,
  Calculate as CalculateIcon,
  Science as ScienceIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  TableChart as TableChartIcon,
  Timeline as TimelineIcon,
  Equalizer as EqualizerIcon,
  CompareArrows as CompareArrowsIcon,
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  Bookmarks as BookmarksIcon,
  Settings as SettingsIcon,
  HelpOutline as HelpOutlineIcon,
  Lightbulb as LightbulbIcon,
  Assistant as AssistantIcon,
  AutoAwesome as AutoAwesomeIcon,
  AccountCircle as AccountCircleIcon,
  Insights as InsightsIcon,
  QuestionAnswer as QuestionAnswerIcon,
  Visibility as VisibilityIcon,
  MenuBook as MenuBookIcon,
  PlayCircleOutline as PlayCircleOutlineIcon,
  PivotTableChart as PivotTableChartIcon,
  CandlestickChart as CandlestickChartIcon,
  Psychology as PsychologyIcon,
  ArrowForward as ArrowForwardIcon,
  TrendingUp as TrendingUpIcon,
  School as SchoolIcon,
  CloudUpload as CloudUploadIcon,
  CloudDownload as CloudDownloadIcon,
  Edit as EditIcon,
  TableView as TableViewIcon,
  Transform as TransformIcon,
  Dataset as DatasetIcon,
  Email as EmailIcon,
  LibraryBooks as LibraryBooksIcon,
  Notifications as NotificationsIcon,
  BarChart as VisualizationIcon,
  TipsAndUpdates as TipsAndUpdatesIcon
} from '@mui/icons-material';
import VaccinesIcon from '@mui/icons-material/Vaccines';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface DashboardPageProps {
  onNavigate: (path: string) => void;
}

// Hero Section Card
const HeroCard = styled(Paper)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  padding: theme.spacing(4),
  borderRadius: theme.spacing(2),
  marginBottom: theme.spacing(4),
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '-50%',
    right: '-10%',
    width: '300px',
    height: '300px',
    borderRadius: '50%',
    background: alpha(theme.palette.common.white, 0.1),
  }
}));

// Section Header
const SectionHeader = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  marginTop: theme.spacing(4),
}));

// Main Feature Card
const FeatureCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s ease-in-out',
  borderRadius: theme.spacing(2),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `0 12px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
    borderColor: theme.palette.primary.main,
  },
}));

// Quick Action Card - Updated for better appearance
const QuickActionCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  transition: 'all 0.3s ease',
  cursor: 'pointer',
  borderRadius: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  textDecoration: 'none',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    borderColor: theme.palette.primary.main,
    '& .icon-wrapper': {
      transform: 'scale(1.1)',
    }
  },
}));

// Icon Container
const IconContainer = styled(Box)<{ color?: string }>(({ theme, color }) => ({
  width: 56,
  height: 56,
  borderRadius: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: alpha((color as string) || theme.palette.primary.main, 0.1),
  marginBottom: theme.spacing(2),
}));

// Quick Action Icon Wrapper
const QuickActionIconWrapper = styled(Box)<{ color?: string }>(({ theme, color }) => ({
  width: 64,
  height: 64,
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: alpha((color as string) || theme.palette.primary.main, 0.1),
  marginBottom: theme.spacing(1.5),
  transition: 'transform 0.3s ease',
}));

// Sub Item Button
const SubItemButton = styled(Button)(({ theme }) => ({
  justifyContent: 'flex-start',
  paddingLeft: theme.spacing(1),
  paddingRight: theme.spacing(1),
  marginBottom: theme.spacing(0.5),
  textTransform: 'none',
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    color: theme.palette.primary.main,
  },
}));

// User Menu Button
const UserMenuButton = styled(IconButton)(({ theme }) => ({
  color: 'white',
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.white, 0.1),
  },
}));

const DashboardPage: React.FC<DashboardPageProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const { canAccessPublicationReady, canAccessAdvancedAnalysis } = useAuth();
  
  // Initialize social meta for dashboard page
  useSocialMeta();

  // Data Management items
  const dataManagementItems = [
    { title: 'Import Data', icon: <CloudUploadIcon />, path: '/data-management/import', color: theme.palette.info.main },
    { title: 'Export Data', icon: <CloudDownloadIcon />, path: '/data-management/export', color: theme.palette.success.main },
    { title: 'Data Editor', icon: <EditIcon />, path: '/data-management/editor', color: theme.palette.warning.main },
    { title: 'Variable Editor', icon: <TableViewIcon />, path: '/data-management/variables', color: theme.palette.error.main },
    { title: 'Transform Data', icon: <TransformIcon />, path: '/data-management/transform', color: theme.palette.secondary.main },
    { title: 'Sample Datasets', icon: <DatasetIcon />, path: '/data-management/sample', color: theme.palette.primary.main },
  ];

  const statisticalAnalysis = [
    {
      title: 'Descriptive Statistics',
      icon: <EqualizerIcon />,
      color: theme.palette.success.main,
      path: '/stats/descriptives',
      navigationPath: '/stats',
      description: 'Explore and summarize your data',
      subItems: [
        { title: 'Descriptives', path: '/stats/descriptives' },
        { title: 'Frequencies', path: '/stats/frequencies' },
        { title: 'Cross Tabulation', path: '/stats/crosstabs' },
        { title: 'Normality Test', path: '/stats/normality' },
      ],
      requiresAccess: false,
      hasAccess: true,
    },
    {
      title: 'Inferential Statistics',
      icon: <ScienceIcon />,
      color: theme.palette.warning.main,
      path: '/inferential-stats/one-sample-ttest',
      navigationPath: '/inferential-stats',
      description: 'Test hypotheses and make inferences',
      subItems: [
        { title: 'One-Sample t-test', path: '/inferential-stats/one-sample-ttest' },
        { title: 'Independent Samples t-test', path: '/inferential-stats/independent-samples-ttest' },
        { title: 'Paired Samples t-test', path: '/inferential-stats/paired-samples-ttest' },
        { title: 'One-Way ANOVA', path: '/inferential-stats/one-way-anova' },
      ],
      requiresAccess: false,
      hasAccess: true,
    },
    {
      title: 'Correlation Analysis',
      icon: <CompareArrowsIcon />,
      color: theme.palette.error.main,
      path: '/correlation-analysis',
      navigationPath: '/correlation-analysis',
      description: 'Explore relationships between variables',
      subItems: [
        { title: 'Correlation', path: '/correlation-analysis' },
        { title: 'Linear Regression', path: '/correlation-analysis' },
        { title: 'Logistic Regression', path: '/correlation-analysis' },
      ],
      requiresAccess: false,
      hasAccess: true,
    },
    {
      title: 'Advanced Analysis',
      icon: <PsychologyIcon />,
      color: theme.palette.secondary.main,
      path: '/advanced-analysis',
      navigationPath: '/advanced-analysis',
      description: 'Sophisticated statistical methods',
      subItems: [
        { title: 'Survival Analysis', path: '/advanced-analysis/survival' },
        { title: 'Reliability Analysis', path: '/advanced-analysis/reliability' },
        { title: 'Mediation/Moderation', path: '/advanced-analysis/mediation' },
        { title: 'Exploratory Factor Analysis', path: '/advanced-analysis/efa' },
      ],
      requiresAccess: true,
      hasAccess: canAccessAdvancedAnalysis,
    },
  ];

  const visualizationReporting = [
    // Always show Publication Ready (like sidebar) but with access control
    {
      title: 'Publication Ready',
      icon: <MenuBookIcon />,
      color: theme.palette.primary.main,
      path: '/publication-ready/table1',
      navigationPath: '/publication-ready',
      description: 'Craft Publication-Ready Tables/Figures + Expert Write-Ups',
      subItems: [
        { title: 'Table 1', path: '/publication-ready/table1' },
        { title: 'Table 1a', path: '/publication-ready/table1a' },
        { title: 'SMD Table', path: '/publication-ready/smd-table' },
        { title: 'Table 2', path: '/publication-ready/table2' },
        { title: 'Flow Diagram', path: '/publication-ready/flow-diagram' },
        { title: 'Regression Table', path: '/publication-ready/regression-table' },
      ],
      // Add access control properties
      requiresAccess: true,
      hasAccess: canAccessPublicationReady,
    },
    {
      title: 'Data Visualization',
      icon: <ShowChartIcon />,
      color: theme.palette.info.main,
      path: '/charts',
      navigationPath: '/charts',
      description: 'Create compelling visual representations',
      subItems: [
        { title: 'Bar Charts', path: '/charts/bar' },
        { title: 'Pie Charts', path: '/charts/pie' },
        { title: 'Histograms', path: '/charts/histogram' },
        { title: 'Box Plots', path: '/charts/boxplot' },
        { title: 'Scatter Plots', path: '/charts/scatter' },
        { title: 'Sankey Diagrams', path: '/charts/sankey' },
        { title: 'Pivot Charts', path: '/charts' },
      ],
      requiresAccess: false,
      hasAccess: true,
    },
  ];

  const quickActions = [
    { title: 'Dataset Manager', icon: <DatasetIcon />, path: 'data-management/datasets', color: theme.palette.primary.main },
    { title: 'Sample Size Calculators', icon: <CalculateIcon />, path: '/samplesize', color: theme.palette.warning.main },
    { title: 'Epi Calculators', icon: <VaccinesIcon />, path: '/epicalc', color: theme.palette.error.main },
    { title: 'Pivot Analysis', icon: <InsightsIcon />, path: '/pivot', color: theme.palette.success.main },
    { title: 'AI Assistant', icon: <AssistantIcon />, path: '/assistant', color: theme.palette.secondary.main },
    // Always show Publication Tools (like sidebar) but with access control
    {
      title: 'Publication Tools',
      icon: <MenuBookIcon />,
      path: '/publication-ready',
      color: theme.palette.success.main,
      requiresAccess: true,
      hasAccess: canAccessPublicationReady
    }
  ];

  const helpResources = [
    {
      title: 'Knowledgebase',
      icon: <LibraryBooksIcon />,
      path: '/knowledge-base',
      description: 'Comprehensive help and documentation',
      color: theme.palette.primary.main
    },
    {
      title: 'Video Tutorials',
      icon: <PlayCircleOutlineIcon />,
      path: '/video-tutorials',
      description: 'Learn with video guides',
      color: theme.palette.warning.main
    },
    {
      title: 'Notification Center',
      icon: <NotificationsIcon />,
      path: '/notifications',
      description: 'Stay updated with latest news',
      color: theme.palette.secondary.main
    },
    {
      title: 'Guided Workflows',
      icon: <AutoAwesomeIcon />,
      path: '/inference/workflow',
      description: 'Step-by-step analysis guides',
      color: theme.palette.success.main
    },
    {
      title: 'Which Analysis?',
      icon: <HelpOutlineIcon />,
      path: '/assistant',
      description: 'AI Powered, find the right Analysis',
      color: theme.palette.info.main
    },
    {
      title: 'Statistical Methods',
      icon: <SchoolIcon />,
      path: '/statistical-methods',
      description: 'Statistical methods explained',
      color: theme.palette.error.main
    },
    {
      title: 'Visualization Guide',
      icon: <VisualizationIcon />,
      path: '/visualization-guide',
      description: 'Learn to create stunning charts',
      color: theme.palette.primary.dark
    },
    {
      title: 'Tip of the Day',
      icon: <TipsAndUpdatesIcon />,
      path: '/tip-of-the-day',
      description: 'Daily tips to improve your workflow',
      color: theme.palette.warning.dark
    },
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1400, margin: '0 auto' }}>
      {/* Hero Section with User Menu */}
      <HeroCard elevation={0}>
        <Box sx={{ position: 'absolute', top: 24, right: 24, display: 'flex', gap: 1 }}>
          <UserMenuButton onClick={() => onNavigate('/profile')}>
            <AccountCircleIcon />
          </UserMenuButton>
          <UserMenuButton onClick={() => onNavigate('/settings')}>
            <SettingsIcon />
          </UserMenuButton>
        </Box>
        <Grid container alignItems="center" spacing={3}>
          <Grid item xs={12} md={8}>
            <Typography variant="h3" fontWeight="bold" gutterBottom>
              Welcome to DataStatPro
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9, mb: 3 }}>
              Powerful statistical analysis made simple. DataStatPro provides powerful tools for data analysis, visualization, and statistical inference. Use the intuitive interface to explore your data, run statistical tests, and generate publication-quality tables and visualizations.
            </Typography>
                        <Typography variant="h6" sx={{ opacity: 0.9, mb: 3 }}>
              Save time with AI-crafted narratives that transform results into ready-to-publish insights instantly.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button 
                variant="contained" 
                color="secondary"
                size="large"
                startIcon={<TrendingUpIcon />}
                onClick={() => onNavigate('/stats/descriptives')}
                sx={{
                  borderRadius: 3,
                  textTransform: 'none',
                  fontWeight: 'bold'
                }}
              >
                Start Analysis
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{ 
                  borderRadius: 3,
                  textTransform: 'none',
                  color: 'white',
                  borderColor: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: alpha(theme.palette.common.white, 0.1)
                  }
                }}
                onClick={() => onNavigate('data-management/import')}
              >
                Import Data
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <InsightsIcon sx={{ fontSize: 120, opacity: 0.3 }} />
            </Box>
          </Grid>
        </Grid>
      </HeroCard>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }} elevation={0} variant="outlined">
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Quick Actions
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Frequently used tools for quick access
        </Typography>
        <Grid container spacing={3}>
          {quickActions.map((action) => (
            <Grid item xs={6} sm={4} md={2} key={action.title}>
              <QuickActionCard
                onClick={() => {
                  // Only navigate if user has access or no access control required
                  if (!action.requiresAccess || action.hasAccess) {
                    onNavigate(action.path);
                  }
                  // If access is required but user doesn't have it, do nothing
                  // The individual components will show upgrade prompts
                }}
                elevation={0}
                sx={{
                  ...(action.requiresAccess && !action.hasAccess && {
                    opacity: 0.6,
                    cursor: 'not-allowed'
                  })
                }}
              >
                <QuickActionIconWrapper className="icon-wrapper" color={action.color}>
                  {React.cloneElement(action.icon, { sx: { fontSize: 32, color: action.color } })}
                </QuickActionIconWrapper>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: action.requiresAccess && !action.hasAccess ? 0.5 : 0 }}>
                    <Typography variant="subtitle1" fontWeight="medium" align="center">
                      {action.title}
                    </Typography>
                    {action.requiresAccess && !action.hasAccess && (
                      <Box
                        sx={{
                          ml: 0.5,
                          bgcolor: theme.palette.warning.main,
                          color: 'white',
                          fontSize: '0.5rem',
                          px: 0.5,
                          borderRadius: 0.5,
                          display: 'inline-flex',
                          alignItems: 'center',
                          height: 12
                        }}
                      >
                        PRO
                      </Box>
                    )}
                  </Box>
                </Box>
              </QuickActionCard>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Data Management Section - Modified to match Quick Actions */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }} elevation={0} variant="outlined">
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Data Management
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Import, export, and manage your data
        </Typography>
        <Grid container spacing={3}>
          {dataManagementItems.map((item) => (
            <Grid item xs={6} sm={4} md={2} key={item.title}>
              <QuickActionCard 
                onClick={() => onNavigate(item.path)}
                elevation={0}
              >
                <QuickActionIconWrapper className="icon-wrapper" color={item.color}>
                  {React.cloneElement(item.icon, { sx: { fontSize: 32, color: item.color } })}
                </QuickActionIconWrapper>
                <Typography variant="subtitle1" fontWeight="medium" align="center">
                  {item.title}
                </Typography>
              </QuickActionCard>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Statistical Analysis Section */}
      <SectionHeader>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Statistical Analysis
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Comprehensive statistical tools for every research need
        </Typography>
      </SectionHeader>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statisticalAnalysis.map((section) => (
          <Grid item xs={12} sm={6} md={3} key={section.title}>
            <FeatureCard
              elevation={0}
              sx={{
                cursor: (!section.requiresAccess || section.hasAccess) ? 'pointer' : 'default',
                ...(section.requiresAccess && !section.hasAccess && {
                  opacity: 0.7,
                })
              }}
              onClick={() => {
                if (!section.requiresAccess || section.hasAccess) {
                  onNavigate(section.navigationPath);
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <IconContainer color={section.color}>
                  {React.cloneElement(section.icon, { sx: { color: section.color } })}
                </IconContainer>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6" fontWeight="bold">
                    {section.title}
                  </Typography>
                  {section.requiresAccess && !section.hasAccess && (
                    <Box
                      sx={{
                        ml: 1,
                        bgcolor: theme.palette.warning.main,
                        color: 'white',
                        fontSize: '0.65rem',
                        px: 0.7,
                        borderRadius: 1,
                        display: 'inline-flex',
                        alignItems: 'center',
                        height: 16
                      }}
                    >
                      PRO
                    </Box>
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 2, minHeight: 40 }}>
                  {section.description}
                </Typography>
                <Box>
                  {section.subItems.map((subItem) => (
                    <SubItemButton
                      key={subItem.title}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        if (!section.requiresAccess || section.hasAccess) {
                          onNavigate(subItem.path);
                        }
                      }}
                      fullWidth
                      size="small"
                      disabled={section.requiresAccess && !section.hasAccess}
                      sx={{
                        ...(section.requiresAccess && !section.hasAccess && {
                          opacity: 0.6,
                          cursor: 'not-allowed'
                        })
                      }}
                    >
                      {subItem.title}
                    </SubItemButton>
                  ))}
                  {/* Add "more" link */}
                  <Box sx={{ mt: 1, textAlign: 'center' }}>
                    <Link
                      component="button"
                      variant="body2"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        if (!section.requiresAccess || section.hasAccess) {
                          onNavigate(section.navigationPath);
                        }
                      }}
                      sx={{
                        color: section.color,
                        textDecoration: 'none',
                        fontWeight: 'medium',
                        cursor: (!section.requiresAccess || section.hasAccess) ? 'pointer' : 'not-allowed',
                        opacity: (section.requiresAccess && !section.hasAccess) ? 0.6 : 1,
                        '&:hover': {
                          textDecoration: (!section.requiresAccess || section.hasAccess) ? 'underline' : 'none',
                        },
                      }}
                      disabled={section.requiresAccess && !section.hasAccess}
                    >
                      more →
                    </Link>
                  </Box>
                </Box>
              </CardContent>
            </FeatureCard>
          </Grid>
        ))}
      </Grid>

      {/* Visualization & Reporting */}
      <SectionHeader>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Visualization & Reporting
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create stunning visualizations and publication-ready outputs
        </Typography>
      </SectionHeader>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {visualizationReporting.map((section) => (
          <Grid item xs={12} md={6} key={section.title}>
            <FeatureCard
              elevation={0}
              sx={{
                cursor: (!section.requiresAccess || section.hasAccess) ? 'pointer' : 'default',
                ...(section.requiresAccess && !section.hasAccess && {
                  opacity: 0.7,
                })
              }}
              onClick={() => {
                if (!section.requiresAccess || section.hasAccess) {
                  onNavigate(section.navigationPath);
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="flex-start" mb={2}>
                  <IconContainer color={section.color}>
                    {React.cloneElement(section.icon, { sx: { color: section.color } })}
                  </IconContainer>
                  <Box sx={{ ml: 2, flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {section.title}
                      </Typography>
                      {section.requiresAccess && !section.hasAccess && (
                        <Box
                          sx={{
                            ml: 1,
                            bgcolor: theme.palette.warning.main,
                            color: 'white',
                            fontSize: '0.65rem',
                            px: 0.7,
                            borderRadius: 1,
                            display: 'inline-flex',
                            alignItems: 'center',
                            height: 16
                          }}
                        >
                          PRO
                        </Box>
                      )}
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {section.description}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ ml: 9 }}>
                  {section.subItems.map((subItem) => (
                    <SubItemButton
                      key={subItem.title}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        // Only navigate if user has access or no access control required
                        if (!section.requiresAccess || section.hasAccess) {
                          onNavigate(subItem.path);
                        }
                        // If access is required but user doesn't have it, do nothing
                        // The individual components will show upgrade prompts
                      }}
                      fullWidth
                      startIcon={<ArrowForwardIcon sx={{ fontSize: 16 }} />}
                      disabled={section.requiresAccess && !section.hasAccess}
                      sx={{
                        ...(section.requiresAccess && !section.hasAccess && {
                          opacity: 0.6,
                          cursor: 'not-allowed'
                        })
                      }}
                    >
                      {subItem.title}
                    </SubItemButton>
                  ))}
                  {/* Add "more" link */}
                  <Box sx={{ mt: 1, textAlign: 'center' }}>
                    <Link
                      component="button"
                      variant="body2"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click
                        if (!section.requiresAccess || section.hasAccess) {
                          onNavigate(section.navigationPath);
                        }
                      }}
                      sx={{
                        color: section.color,
                        textDecoration: 'none',
                        fontWeight: 'medium',
                        cursor: (!section.requiresAccess || section.hasAccess) ? 'pointer' : 'not-allowed',
                        opacity: (section.requiresAccess && !section.hasAccess) ? 0.6 : 1,
                        '&:hover': {
                          textDecoration: (!section.requiresAccess || section.hasAccess) ? 'underline' : 'none',
                        },
                      }}
                      disabled={section.requiresAccess && !section.hasAccess}
                    >
                      more →
                    </Link>
                  </Box>
                </Box>
              </CardContent>
            </FeatureCard>
          </Grid>
        ))}
      </Grid>

      {/* Help & Resources - Improved Design with additional text */}
      <Paper sx={{ p: 3, borderRadius: 2, backgroundColor: alpha(theme.palette.primary.main, 0.02) }} elevation={0}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Help & Resources
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Get help and learn how to make the most of our platform
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {helpResources.map((resource) => (
            <Grid item xs={12} sm={6} md={3} lg={3} key={resource.title}>
              <Card
                onClick={() => onNavigate(resource.path)}
                sx={{
                  height: '100%',
                  textDecoration: 'none',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
                elevation={0}
                variant="outlined"
              >
                <CardContent sx={{ textAlign: 'center', p: 3 }}>
                  <IconContainer color={resource.color} sx={{ margin: '0 auto', mb: 2 }}>
                    {React.cloneElement(resource.icon, { sx: { fontSize: 28, color: resource.color } })}
                  </IconContainer>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    {resource.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {resource.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        {/* Additional text at the bottom */}
        <Box 
          sx={{ 
            mt: 3, 
            pt: 3, 
            borderTop: `1px solid ${theme.palette.divider}`,
            textAlign: 'center' 
          }}
        >
          <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.8 }}>
            Explore powerful tools for guided analysis and insightful interpretations. For any bugs or feature requests, please email us at{' '}
            <Link 
              href="mailto:<EMAIL>"
              sx={{ 
                color: theme.palette.primary.main,
                textDecoration: 'none',
                fontWeight: 'medium',
                '&:hover': {
                  textDecoration: 'underline'
                }
              }}
            >
              <EMAIL>
            </Link>
            . We're here to help enhance your data analysis journey!
          </Typography>
        </Box>
      </Paper>
      
      {/* Social Share Widget */}
      <SocialShareWidget 
        mode="floating"
        position="bottom-right"
        platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
      />
    </Box>
  );
};

export default DashboardPage;
