<?php
// Social Media Meta Tag Handler for DataStatPro
// This script serves appropriate meta tags to social media crawlers

// Get parameters from URL
$path = isset($_GET['path']) ? $_GET['path'] : '';
$tutorialId = isset($_GET['tutorial']) ? $_GET['tutorial'] : '';

// Define comprehensive page metadata
$pages = [
    // Main application pages
    '' => [
        'name' => 'DataStatPro - Professional Statistical Analysis Platform',
        'description' => 'Professional statistical analysis platform with comprehensive tools for data analysis, visualization, and reporting. Perfect for researchers, analysts, and data scientists.',
        'category' => 'Statistical Software'
    ],
    'dashboard' => [
        'name' => 'Dashboard - DataStatPro',
        'description' => 'Access your statistical analysis dashboard with project management, recent analyses, and quick access to all DataStatPro tools.',
        'category' => 'Dashboard'
    ],
    'data-entry' => [
        'name' => 'Data Entry - DataStatPro',
        'description' => 'Enter and manage your research data with our intuitive data entry interface. Import from Excel, CSV, or enter data manually.',
        'category' => 'Data Management'
    ],
    'descriptive-statistics' => [
        'name' => 'Descriptive Statistics - DataStatPro',
        'description' => 'Calculate comprehensive descriptive statistics including measures of central tendency, variability, and distribution shape.',
        'category' => 'Descriptive Statistics'
    ],
    'inferential-statistics' => [
        'name' => 'Inferential Statistics - DataStatPro',
        'description' => 'Perform hypothesis testing, confidence intervals, and statistical inference with our comprehensive inferential statistics tools.',
        'category' => 'Inferential Statistics'
    ],
    'regression-analysis' => [
        'name' => 'Regression Analysis - DataStatPro',
        'description' => 'Conduct linear, logistic, and advanced regression analyses with detailed output and interpretation guides.',
        'category' => 'Regression Analysis'
    ],
    'advanced-analysis' => [
        'name' => 'Advanced Analysis - DataStatPro',
        'description' => 'Access advanced statistical methods including factor analysis, survival analysis, and multivariate techniques.',
        'category' => 'Advanced Statistics'
    ],
    'data-visualization' => [
        'name' => 'Data Visualization - DataStatPro',
        'description' => 'Create professional charts, graphs, and visualizations to effectively communicate your statistical results.',
        'category' => 'Data Visualization'
    ],
    'knowledge-base' => [
        'name' => 'Knowledge Base - DataStatPro',
        'description' => 'Comprehensive tutorials and guides for statistical analysis, from basic concepts to advanced techniques.',
        'category' => 'Education'
    ],
    'settings' => [
        'name' => 'Settings - DataStatPro',
        'description' => 'Customize your DataStatPro experience with user preferences, data formats, and analysis settings.',
        'category' => 'Settings'
    ]
];

// Define tutorial metadata
$tutorials = [
    'logistic-regression' => [
        'name' => 'Logistic Regression Tutorial',
        'description' => 'A comprehensive guide covering the basics of logistic regression, how to use the component, computational details, formula explanations, and interpretation of results.',
        'category' => 'Regression Analysis'
    ],
    't-tests-and-alternatives' => [
        'name' => 'T-Tests and Alternatives',
        'description' => 'Detailed coverage of one-sample, independent samples, and paired t-tests, plus non-parametric alternatives including Mann-Whitney U, Wilcoxon signed-rank, and Sign tests.',
        'category' => 'Inferential Statistics'
    ],
    'anova-tests-and-alternatives' => [
        'name' => 'ANOVA Tests and Alternatives',
        'description' => 'Comprehensive guide to Analysis of Variance (ANOVA) tests and their non-parametric alternatives for comparing multiple groups.',
        'category' => 'Inferential Statistics'
    ],
    'numerical-descriptives' => [
        'name' => 'Numerical Descriptives',
        'description' => 'Learn how to calculate and interpret descriptive statistics for numerical data including measures of central tendency and variability.',
        'category' => 'Descriptive Statistics'
    ],
    'categorical-descriptives' => [
        'name' => 'Categorical Descriptives',
        'description' => 'Understanding frequency tables, cross-tabulations, and descriptive statistics for categorical data analysis.',
        'category' => 'Descriptive Statistics'
    ],
    'epidemiological-calculators' => [
        'name' => 'Epidemiological Calculators',
        'description' => 'Calculate epidemiological measures including odds ratios, relative risks, and confidence intervals for public health research.',
        'category' => 'Epidemiology'
    ],
    'sample-size-power-analysis' => [
        'name' => 'Sample Size and Power Analysis',
        'description' => 'Determine appropriate sample sizes and conduct power analysis for your research studies using statistical calculations.',
        'category' => 'Study Design'
    ],
    'correlation-linear-regression' => [
        'name' => 'Correlation and Linear Regression',
        'description' => 'Explore relationships between variables using correlation analysis and linear regression modeling techniques.',
        'category' => 'Regression Analysis'
    ],
    'exploratory-factor-analysis' => [
        'name' => 'Exploratory Factor Analysis',
        'description' => 'Discover underlying factor structures in your data using exploratory factor analysis techniques and interpretation.',
        'category' => 'Advanced Analysis'
    ],
    'confirmatory-factor-analysis' => [
        'name' => 'Confirmatory Factor Analysis',
        'description' => 'Test theoretical factor models using confirmatory factor analysis and structural equation modeling approaches.',
        'category' => 'Advanced Analysis'
    ],
    'survival-analysis' => [
        'name' => 'Survival Analysis',
        'description' => 'Analyze time-to-event data using survival analysis methods including Kaplan-Meier curves and Cox regression.',
        'category' => 'Advanced Analysis'
    ],
    'reliability-analysis' => [
        'name' => 'Reliability Analysis',
        'description' => 'Assess the reliability and internal consistency of measurement scales using Cronbach\'s alpha and other methods.',
        'category' => 'Psychometrics'
    ],
    'mediation-moderation' => [
        'name' => 'Mediation and Moderation Analysis',
        'description' => 'Understand and test mediation and moderation effects in your research using advanced statistical techniques.',
        'category' => 'Advanced Analysis'
    ],
    'cluster-analysis' => [
        'name' => 'Cluster Analysis',
        'description' => 'Group similar observations using hierarchical and k-means clustering techniques for data exploration.',
        'category' => 'Advanced Analysis'
    ],
    'meta-analysis' => [
        'name' => 'Meta-Analysis',
        'description' => 'Combine results from multiple studies using meta-analysis techniques for evidence synthesis.',
        'category' => 'Advanced Analysis'
    ],
    'variable-tree-analysis' => [
        'name' => 'Variable Tree Analysis',
        'description' => 'Explore variable relationships and hierarchical structures using tree-based analysis methods.',
        'category' => 'Advanced Analysis'
    ],
    'data-visualization' => [
        'name' => 'Data Visualization',
        'description' => 'Create effective charts and graphs to visualize your data and communicate statistical results clearly.',
        'category' => 'Data Visualization'
    ]
];

// Get the current protocol and host for URLs
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$baseUrl = $protocol . '://' . $host;

// Determine page data based on path or tutorial
$pageData = null;
$pageUrl = $baseUrl;

if (!empty($tutorialId)) {
    // Handle tutorial pages
    $pageData = isset($tutorials[$tutorialId]) ? $tutorials[$tutorialId] : [
        'name' => 'Knowledge Base Tutorial',
        'description' => 'Learn statistical analysis with comprehensive tutorials and guides from DataStatPro.',
        'category' => 'Statistics'
    ];
    $pageUrl .= '/knowledge-base/' . urlencode($tutorialId);
} elseif (!empty($path)) {
    // Handle regular app pages
    $pageData = isset($pages[$path]) ? $pages[$path] : $pages[''];
    $pageUrl .= ($path !== '' ? '/' . $path : '');
} else {
    // Default to home page
    $pageData = $pages[''];
}

// Generate the page title and description
$pageTitle = htmlspecialchars($pageData['name']);
$pageDescription = htmlspecialchars($pageData['description']);
$imageUrl = 'https://www.datastatpro.com/logo.png';
$keywords = htmlspecialchars($pageData['name'] . ', statistics, data analysis, ' . strtolower($pageData['category']) . ', DataStatPro');

// Set content type
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Basic Meta Tags -->
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="<?php echo $keywords; ?>">
    <meta name="author" content="DataStatPro">
    <link rel="canonical" href="<?php echo $pageUrl; ?>">
    
    <!-- Open Graph Tags for Facebook/LinkedIn -->
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="<?php echo $pageDescription; ?>">
    <meta property="og:type" content="article">
    <meta property="og:url" content="<?php echo $pageUrl; ?>">
    <meta property="og:site_name" content="DataStatPro">
    <meta property="og:image" content="<?php echo $imageUrl; ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/png">
    <meta property="og:image:alt" content="<?php echo $pageTitle; ?>">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $pageTitle; ?>">
    <meta name="twitter:description" content="<?php echo $pageDescription; ?>">
    <meta name="twitter:image" content="<?php echo $imageUrl; ?>">
    <meta name="twitter:url" content="<?php echo $pageUrl; ?>">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "<?php echo !empty($tutorialId) ? 'EducationalResource' : 'WebApplication'; ?>",
        "name": "<?php echo addslashes($pageData['name']); ?>",
        "description": "<?php echo addslashes($pageData['description']); ?>",
        <?php if (!empty($tutorialId)): ?>
        "educationalLevel": "Advanced",
        "learningResourceType": "Tutorial",
        <?php endif; ?>
        "about": "<?php echo addslashes($pageData['category']); ?>",
        "provider": {
            "@type": "Organization",
            "name": "DataStatPro",
            "url": "https://datastatpro.com"
        },
        "url": "<?php echo $pageUrl; ?>",
        "dateModified": "<?php echo date('Y-m-d'); ?>"
    }
    </script>
    
    <!-- Redirect to actual app page after meta tags are read -->
    <script>
        // Redirect to the actual app page after a short delay
        // This allows crawlers to read meta tags while redirecting users
        <?php 
        $redirectUrl = $baseUrl . '/app';
        if (!empty($tutorialId)) {
            $redirectUrl .= '/knowledge-base/' . urlencode($tutorialId);
        } elseif (!empty($path)) {
            $redirectUrl .= '/' . $path;
        }
        ?>
        setTimeout(function() {
            window.location.href = '<?php echo $redirectUrl; ?>';
        }, 100);
    </script>
</head>
<body>
    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1><?php echo htmlspecialchars($pageData['name']); ?></h1>
        <p><?php echo htmlspecialchars($pageData['description']); ?></p>
        <p>Redirecting to DataStatPro...</p>
        <p><a href="<?php echo $baseUrl . '/app' . (!empty($tutorialId) ? '/knowledge-base/' . urlencode($tutorialId) : (!empty($path) ? '/' . $path : '')); ?>">Click here if you are not redirected automatically</a></p>
    </div>
</body>
</html>