# Interactive Statistical Advisor

## Overview

The Interactive Statistical Advisor is a redesigned, engaging deployment of the existing Analysis Index content that transforms the static method browser into a dynamic, intelligent guide for statistical analysis discovery and navigation.

## Key Features

### 🎯 **No Duplication - Analysis Index Integration**
- **Leverages Existing Content**: Uses all Analysis Index categories and methods
- **Unified Data Source**: Single source of truth for analysis methods
- **Consistent Information**: Same descriptions, paths, and metadata
- **Simplified Architecture**: No separate database tables needed

### 🚀 **Four Interactive View Modes**

#### 1. Smart Recommendations
- **AI-Powered Suggestions**: Context-aware recommendations based on loaded datasets
- **Data-Driven Logic**: Analyzes variable types and suggests appropriate methods
- **Progressive Disclosure**: Shows most relevant methods first
- **Visual Priority**: Clear visual hierarchy for recommendations

#### 2. Guided Workflow
- **Step-by-Step Process**: Structured analysis workflow
- **Visual Progress**: Stepper component showing analysis stages
- **Educational Approach**: Teaches proper analysis sequence
- **Interactive Navigation**: Jump between workflow steps

#### 3. Explore Methods
- **Enhanced Analysis Index**: Interactive version of existing content
- **Expandable Categories**: Smooth accordion-style navigation
- **Access Control Integration**: Visual indicators for tier restrictions
- **Method Filtering**: Show only accessible methods option

#### 4. Search & Filter
- **Intelligent Search**: Search across method names, descriptions, and categories
- **Real-time Results**: Instant filtering as user types
- **Comprehensive Coverage**: Searches all Analysis Index content
- **Category Context**: Shows which category each method belongs to

## Architecture

### Component Structure

```typescript
InteractiveStatisticalAdvisor
├── SmartRecommendationsView
├── GuidedWorkflowView  
├── ExploreMethodsView
├── SearchFilterView
└── InteractiveMethodCard
```

### Data Integration

```typescript
// Uses existing Analysis Index options
import { descriptiveStatsOptions } from '../DescriptiveStats/DescriptiveStatsOptions';
import { inferentialStatsOptions } from '../InferentialStats/InferentialStatsOptions';
import { correlationAnalysisOptions } from '../CorrelationAnalysis/CorrelationAnalysisOptions';
// ... all other Analysis Index options

// Creates unified category structure
const categoryGroups: CategoryGroup[] = [
  {
    name: 'Data Management',
    icon: <DataManagementIcon />,
    color: '#795548',
    options: dataManagementOptions, // Direct from Analysis Index
    description: 'Import, clean, and prepare your data for analysis',
    accessLevel: 'guest'
  },
  // ... other categories
];
```

## Smart Recommendations Logic

### Dataset Analysis
```typescript
const smartRecommendations = useMemo(() => {
  if (!currentDataset || !datasetAnalysis) {
    // Default recommendations for new users
    return defaultRecommendations;
  }

  const { variableAnalysis } = datasetAnalysis;
  const numericVars = variableAnalysis?.filter(v => v.type === 'numeric').length || 0;
  const categoricalVars = variableAnalysis?.filter(v => v.type === 'categorical').length || 0;

  // Generate contextual recommendations
  const recommendations = [];
  
  if (numericVars > 0) {
    recommendations.push({
      category: 'Recommended for Your Data',
      methods: descriptiveStatsOptions, // From Analysis Index
      reason: `You have ${numericVars} numeric variable(s) - start with descriptive statistics`
    });
  }
  
  // ... more intelligent logic
}, [currentDataset, datasetAnalysis]);
```

### Recommendation Categories
- **Getting Started**: For users without datasets
- **Recommended for Your Data**: Based on variable types
- **Group Comparisons**: When both numeric and categorical variables exist
- **Relationships**: When multiple numeric variables are present

## Access Control Integration

### Tier-Based Access
```typescript
const canAccessCategory = useCallback((accessLevel: string): boolean => {
  if (isGuest) return accessLevel === 'guest';
  if (!user) return accessLevel === 'guest';
  
  const tierHierarchy = { guest: 0, standard: 1, edu: 1, pro: 2, edu_pro: 2 };
  const requiredTierLevel = tierHierarchy[accessLevel] || 0;
  const userTierLevel = tierHierarchy[effectiveTier] || 0;
  
  return userTierLevel >= requiredTierLevel;
}, [isGuest, user, effectiveTier]);
```

### Visual Indicators
- **User Tier Badges**: Show current subscription level
- **PRO Labels**: Clear indicators for premium features
- **Access Counters**: Show accessible vs total methods
- **Upgrade Prompts**: Direct paths to subscription upgrades

## Interactive Method Cards

### Enhanced Features
- **Hover Effects**: Smooth animations and elevation changes
- **Expandable Details**: Learn more functionality
- **Bookmark System**: Save favorite methods
- **Direct Launch**: One-click navigation to analysis tools
- **Access Control**: Visual restrictions for premium features

### Card Variants
- **Compact Mode**: For recommendations and guided workflow
- **Full Mode**: For explore and search views
- **Category Context**: Shows parent category when needed

## User Experience Enhancements

### Smooth Transitions
- **Fade Animations**: Staggered appearance of recommendations
- **Zoom Effects**: Interactive feedback on card interactions
- **Progress Indicators**: Visual workflow completion status

### Contextual Help
- **Tooltips**: Explanatory text for view modes
- **Alerts**: Contextual information based on user state
- **Guidance**: Clear next steps and recommendations

### Responsive Design
- **Mobile Optimized**: Works well on all screen sizes
- **Grid Layouts**: Adaptive card arrangements
- **Touch Friendly**: Optimized for touch interactions

## Integration Benefits

### For Users
- **Familiar Content**: Same trusted Analysis Index information
- **Enhanced Discovery**: Multiple ways to find the right analysis
- **Intelligent Guidance**: Smart recommendations based on data
- **Seamless Navigation**: Direct launch to analysis tools

### For Developers
- **No Duplication**: Single source of truth for analysis methods
- **Simplified Maintenance**: Updates to Analysis Index automatically reflected
- **Consistent Data**: Same paths, descriptions, and metadata
- **Reduced Complexity**: No separate database schema needed

## Future Enhancements

### Planned Features
- **Usage Analytics**: Track method selection patterns
- **Personalized Recommendations**: Learn from user behavior
- **Workflow Templates**: Save and share analysis sequences
- **Integration Improvements**: Even tighter Analysis Index coupling

### Scalability
- **Dynamic Loading**: Load method details on demand
- **Caching Strategy**: Optimize performance for large method catalogs
- **Search Optimization**: Enhanced search algorithms
- **Accessibility**: Full keyboard navigation and screen reader support

## Demo and Testing

### Interactive Demo
The `InteractiveAdvisorDemo.tsx` component provides comprehensive testing:
- **User Type Switching**: Test all subscription tiers
- **Dataset Scenarios**: Multiple mock datasets for testing
- **Real-time Updates**: See recommendations change with data
- **Access Control Validation**: Verify tier restrictions work correctly

### Test Scenarios
1. **Guest User**: Limited access, upgrade prompts
2. **Standard User**: Intermediate access, Pro upgrade prompts  
3. **Pro User**: Full access, no restrictions
4. **Educational User**: Academic tier access levels
5. **Dataset Loading**: Smart recommendations based on data characteristics

The Interactive Statistical Advisor successfully transforms the static Analysis Index into an engaging, intelligent guide that enhances rather than duplicates existing functionality, providing users with multiple intuitive ways to discover and navigate to the perfect statistical analysis for their research needs.
