-- Statistical Analysis Advisor Tables
-- This migration creates tables to support the Statistical Analysis Advisor feature

-- Table to store user preferences for statistical analysis advisor
CREATE TABLE IF NOT EXISTS statistical_advisor_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  preference_key VARCHAR(100) NOT NULL,
  preference_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, preference_key)
);

-- Table to store analysis recommendations and user interactions
CREATE TABLE IF NOT EXISTS analysis_recommendations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  dataset_id UUID, -- Optional reference to user_datasets if available
  session_id VARCHAR(100), -- To group related recommendations in a session
  decision_path JSONB NOT NULL, -- Store the decision tree path taken
  recommended_method VARCHAR(100) NOT NULL,
  method_details JSONB NOT NULL, -- Store the full method details
  user_feedback JSONB, -- Store user feedback (helpful, not helpful, etc.)
  implementation_language VARCHAR(20), -- r, python, spss
  was_implemented BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table to store statistical method usage analytics
CREATE TABLE IF NOT EXISTS statistical_method_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  method_id VARCHAR(100) NOT NULL,
  method_name VARCHAR(200) NOT NULL,
  method_category VARCHAR(100) NOT NULL,
  usage_count INTEGER DEFAULT 1,
  success_rate DECIMAL(5,2), -- Percentage of successful implementations
  avg_user_rating DECIMAL(3,2), -- Average user rating (1-5)
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(method_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_statistical_advisor_preferences_user_id 
  ON statistical_advisor_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_analysis_recommendations_user_id 
  ON analysis_recommendations(user_id);

CREATE INDEX IF NOT EXISTS idx_analysis_recommendations_session_id 
  ON analysis_recommendations(session_id);

CREATE INDEX IF NOT EXISTS idx_analysis_recommendations_method 
  ON analysis_recommendations(recommended_method);

CREATE INDEX IF NOT EXISTS idx_statistical_method_analytics_method_id 
  ON statistical_method_analytics(method_id);

-- Enable Row Level Security (RLS)
ALTER TABLE statistical_advisor_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE statistical_method_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for statistical_advisor_preferences
CREATE POLICY "Users can view their own statistical advisor preferences" 
  ON statistical_advisor_preferences FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own statistical advisor preferences" 
  ON statistical_advisor_preferences FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own statistical advisor preferences" 
  ON statistical_advisor_preferences FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own statistical advisor preferences" 
  ON statistical_advisor_preferences FOR DELETE 
  USING (auth.uid() = user_id);

-- RLS Policies for analysis_recommendations
CREATE POLICY "Users can view their own analysis recommendations" 
  ON analysis_recommendations FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own analysis recommendations" 
  ON analysis_recommendations FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own analysis recommendations" 
  ON analysis_recommendations FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own analysis recommendations" 
  ON analysis_recommendations FOR DELETE 
  USING (auth.uid() = user_id);

-- RLS Policies for statistical_method_analytics (read-only for users, admin can modify)
CREATE POLICY "Users can view statistical method analytics" 
  ON statistical_method_analytics FOR SELECT 
  TO authenticated 
  USING (true);

CREATE POLICY "Only service role can modify statistical method analytics" 
  ON statistical_method_analytics FOR ALL 
  TO service_role 
  USING (true);

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON statistical_advisor_preferences TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON analysis_recommendations TO authenticated;
GRANT SELECT ON statistical_method_analytics TO authenticated;

-- Grant full permissions to service role for analytics
GRANT ALL ON statistical_method_analytics TO service_role;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_statistical_advisor_preferences_updated_at 
  BEFORE UPDATE ON statistical_advisor_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analysis_recommendations_updated_at 
  BEFORE UPDATE ON analysis_recommendations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_statistical_method_analytics_updated_at 
  BEFORE UPDATE ON statistical_method_analytics 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data for statistical method analytics
INSERT INTO statistical_method_analytics (method_id, method_name, method_category) VALUES
('descriptive_single', 'Descriptive Statistics (Single Variable)', 'Descriptive'),
('frequency_table', 'Frequency Table & Proportions', 'Descriptive'),
('descriptive_multiple', 'Comprehensive Descriptive Analysis', 'Descriptive'),
('t_test', 'Independent Samples t-test', 'Comparison'),
('mann_whitney', 'Mann-Whitney U Test', 'Comparison'),
('chi_square', 'Chi-Square Test of Independence', 'Association'),
('correlation', 'Pearson Correlation', 'Association')
ON CONFLICT (method_id) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE statistical_advisor_preferences IS 'Stores user preferences for the Statistical Analysis Advisor feature';
COMMENT ON TABLE analysis_recommendations IS 'Stores analysis recommendations and user interactions with the advisor';
COMMENT ON TABLE statistical_method_analytics IS 'Tracks usage analytics for statistical methods';

COMMENT ON COLUMN statistical_advisor_preferences.preference_key IS 'Key for the preference (e.g., "default_language", "show_assumptions")';
COMMENT ON COLUMN statistical_advisor_preferences.preference_value IS 'JSON value for the preference';

COMMENT ON COLUMN analysis_recommendations.decision_path IS 'JSON array of the decision tree path taken by the user';
COMMENT ON COLUMN analysis_recommendations.method_details IS 'Complete details of the recommended statistical method';
COMMENT ON COLUMN analysis_recommendations.user_feedback IS 'User feedback on the recommendation (rating, comments, etc.)';

COMMENT ON COLUMN statistical_method_analytics.success_rate IS 'Percentage of users who successfully implemented this method';
COMMENT ON COLUMN statistical_method_analytics.avg_user_rating IS 'Average user rating for this method (1-5 scale)';