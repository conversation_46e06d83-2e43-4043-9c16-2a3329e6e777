import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Clear as ClearIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface InputData {
  truePositive: string;
  falseNegative: string;
  falsePositive: string;
  trueNegative: string;
  confidenceLevel: string;
  method: 'wilson' | 'exact';
}

interface DiagnosticResults {
  sensitivity: number;
  specificity: number;
  ppv: number;
  npv: number;
  positiveLR: number;
  negativeLR: number;
  accuracy: number;
  prevalence: number;
  sensitivityCI: [number, number];
  specificityCI: [number, number];
  ppvCI: [number, number];
  npvCI: [number, number];
  positiveLRCI: [number, number];
  negativeLRCI: [number, number];
  accuracyCI: [number, number];
  confidenceLevel: number;
  formula: string;
}

const DiagnosticTestCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [inputData, setInputData] = useState<InputData>({
    truePositive: '',
    falseNegative: '',
    falsePositive: '',
    trueNegative: '',
    confidenceLevel: '95',
    method: 'wilson'
  });
  const [results, setResults] = useState<DiagnosticResults | null>(null);
  const [error, setError] = useState<string>('');

  // Render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // Wilson score interval for proportions
  const wilsonCI = (x: number, n: number, alpha: number): [number, number] => {
    if (n === 0) return [0, 0];
    
    const z = getZCritical(alpha / 2);
    const p = x / n;
    const z2 = z * z;
    const denominator = 1 + z2 / n;
    const center = (p + z2 / (2 * n)) / denominator;
    const margin = z * Math.sqrt((p * (1 - p) + z2 / (4 * n)) / n) / denominator;
    
    return [Math.max(0, center - margin), Math.min(1, center + margin)];
  };

  // Exact (Clopper-Pearson) confidence interval
  const exactCI = (x: number, n: number, alpha: number): [number, number] => {
    if (n === 0) return [0, 0];
    if (x === 0) return [0, 1 - Math.pow(alpha, 1/n)];
    if (x === n) return [Math.pow(alpha, 1/n), 1];
    
    // Simplified approximation for exact CI
    const p = x / n;
    const z = getZCritical(alpha / 2);
    const margin = z * Math.sqrt(p * (1 - p) / n);
    
    return [Math.max(0, p - margin), Math.min(1, p + margin)];
  };

  // Get z-critical value
  const getZCritical = (alpha: number): number => {
    const zTable: { [key: string]: number } = {
      '0.10': 1.645,
      '0.05': 1.960,
      '0.025': 1.960,
      '0.01': 2.576,
      '0.005': 2.807,
      '0.001': 3.291
    };
    
    const alphaStr = alpha.toFixed(3);
    return zTable[alphaStr] || 1.96;
  };

  // Calculate confidence interval for likelihood ratios
  const likelihoodRatioCI = (lr: number, sensitivity: number, specificity: number, 
                            diseased: number, nonDiseased: number, alpha: number): [number, number] => {
    if (lr === 0 || lr === Infinity) return [0, Infinity];
    
    const logLR = Math.log(lr);
    const seSensitivity = Math.sqrt(sensitivity * (1 - sensitivity) / diseased);
    const seSpecificity = Math.sqrt(specificity * (1 - specificity) / nonDiseased);
    
    const seLR = Math.sqrt(
      Math.pow(seSensitivity / sensitivity, 2) + 
      Math.pow(seSpecificity / (1 - specificity), 2)
    );
    
    const z = getZCritical(alpha / 2);
    const margin = z * seLR;
    
    return [Math.exp(logLR - margin), Math.exp(logLR + margin)];
  };

  const calculateDiagnosticTestCI = () => {
    try {
      setError('');
      
      const tp = parseInt(inputData.truePositive);
      const fn = parseInt(inputData.falseNegative);
      const fp = parseInt(inputData.falsePositive);
      const tn = parseInt(inputData.trueNegative);
      const confidenceLevel = parseFloat(inputData.confidenceLevel);
      
      // Validation
      if (isNaN(tp) || isNaN(fn) || isNaN(fp) || isNaN(tn) || isNaN(confidenceLevel)) {
        throw new Error('Please enter valid numeric values for all fields.');
      }
      
      if (tp < 0 || fn < 0 || fp < 0 || tn < 0) {
        throw new Error('All values must be non-negative.');
      }
      
      if (confidenceLevel <= 0 || confidenceLevel >= 100) {
        throw new Error('Confidence level must be between 0 and 100.');
      }
      
      const diseased = tp + fn;
      const nonDiseased = fp + tn;
      const total = tp + fn + fp + tn;
      
      if (total === 0) {
        throw new Error('Total sample size cannot be zero.');
      }
      
      // Calculate basic measures
      const sensitivity = diseased > 0 ? tp / diseased : 0;
      const specificity = nonDiseased > 0 ? tn / nonDiseased : 0;
      const ppv = (tp + fp) > 0 ? tp / (tp + fp) : 0;
      const npv = (fn + tn) > 0 ? tn / (fn + tn) : 0;
      const accuracy = total > 0 ? (tp + tn) / total : 0;
      const prevalence = total > 0 ? diseased / total : 0;
      
      const positiveLR = specificity < 1 ? sensitivity / (1 - specificity) : Infinity;
      const negativeLR = sensitivity > 0 ? (1 - sensitivity) / specificity : 0;
      
      const alpha = (100 - confidenceLevel) / 100;
      
      // Calculate confidence intervals
      const ciMethod = inputData.method === 'wilson' ? wilsonCI : exactCI;
      
      const sensitivityCI = diseased > 0 ? ciMethod(tp, diseased, alpha) : [0, 0];
      const specificityCI = nonDiseased > 0 ? ciMethod(tn, nonDiseased, alpha) : [0, 0];
      const ppvCI = (tp + fp) > 0 ? ciMethod(tp, tp + fp, alpha) : [0, 0];
      const npvCI = (fn + tn) > 0 ? ciMethod(tn, fn + tn, alpha) : [0, 0];
      const accuracyCI = total > 0 ? ciMethod(tp + tn, total, alpha) : [0, 0];
      
      const positiveLRCI = likelihoodRatioCI(positiveLR, sensitivity, specificity, diseased, nonDiseased, alpha);
      const negativeLRCI = likelihoodRatioCI(negativeLR, sensitivity, specificity, diseased, nonDiseased, alpha);
      
      const calculatedResults: DiagnosticResults = {
        sensitivity,
        specificity,
        ppv,
        npv,
        positiveLR,
        negativeLR,
        accuracy,
        prevalence,
        sensitivityCI,
        specificityCI,
        ppvCI,
        npvCI,
        positiveLRCI,
        negativeLRCI,
        accuracyCI,
        confidenceLevel,
        formula: `\\text{Sensitivity} = \\frac{TP}{TP + FN}, \\quad \\text{Specificity} = \\frac{TN}{TN + FP} \\\\ \\text{PPV} = \\frac{TP}{TP + FP}, \\quad \\text{NPV} = \\frac{TN}{TN + FN} \\\\ \\text{LR+} = \\frac{\\text{Sensitivity}}{1 - \\text{Specificity}}, \\quad \\text{LR-} = \\frac{1 - \\text{Sensitivity}}{\\text{Specificity}}`
      };
      
      setResults(calculatedResults);
      setActiveTab(1); // Switch to Results tab
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    }
  };

  const clearAll = () => {
    setInputData({
      truePositive: '',
      falseNegative: '',
      falsePositive: '',
      trueNegative: '',
      confidenceLevel: '95',
      method: 'wilson'
    });
    setResults(null);
    setError('');
    setActiveTab(0);
  };

  const copyToClipboard = () => {
    if (!results) return;
    
    const formatCI = (ci: [number, number]) => `[${(ci[0] * 100).toFixed(2)}%, ${(ci[1] * 100).toFixed(2)}%]`;
    
    const text = `Diagnostic Test Confidence Interval Results:\n` +
      `Confidence Level: ${results.confidenceLevel}%\n\n` +
      `Sensitivity: ${(results.sensitivity * 100).toFixed(2)}% ${formatCI(results.sensitivityCI)}\n` +
      `Specificity: ${(results.specificity * 100).toFixed(2)}% ${formatCI(results.specificityCI)}\n` +
      `PPV: ${(results.ppv * 100).toFixed(2)}% ${formatCI(results.ppvCI)}\n` +
      `NPV: ${(results.npv * 100).toFixed(2)}% ${formatCI(results.npvCI)}\n` +
      `Accuracy: ${(results.accuracy * 100).toFixed(2)}% ${formatCI(results.accuracyCI)}\n` +
      `Positive LR: ${results.positiveLR.toFixed(2)} [${results.positiveLRCI[0].toFixed(2)}, ${results.positiveLRCI[1].toFixed(2)}]\n` +
      `Negative LR: ${results.negativeLR.toFixed(2)} [${results.negativeLRCI[0].toFixed(2)}, ${results.negativeLRCI[1].toFixed(2)}]\n` +
      `Prevalence: ${(results.prevalence * 100).toFixed(2)}%`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        Diagnostic Test Confidence Intervals
      </Typography>
      
      <Card>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!results} />
          <Tab label="Guide" />
        </Tabs>
        
        <TabPanel value={activeTab} index={0}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  2×2 Contingency Table
                </Typography>
                
                <TableContainer component={Paper} sx={{ mb: 3 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell></TableCell>
                        <TableCell align="center">Disease +</TableCell>
                        <TableCell align="center">Disease -</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>Test +</TableCell>
                        <TableCell align="center">TP</TableCell>
                        <TableCell align="center">FP</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Test -</TableCell>
                        <TableCell align="center">FN</TableCell>
                        <TableCell align="center">TN</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
                
                <TextField
                  fullWidth
                  label="True Positives (TP)"
                  type="number"
                  value={inputData.truePositive}
                  onChange={(e) => setInputData({ ...inputData, truePositive: e.target.value })}
                  margin="normal"
                  helperText="Test positive, disease present"
                />
                
                <TextField
                  fullWidth
                  label="False Negatives (FN)"
                  type="number"
                  value={inputData.falseNegative}
                  onChange={(e) => setInputData({ ...inputData, falseNegative: e.target.value })}
                  margin="normal"
                  helperText="Test negative, disease present"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="False Positives (FP)"
                  type="number"
                  value={inputData.falsePositive}
                  onChange={(e) => setInputData({ ...inputData, falsePositive: e.target.value })}
                  margin="normal"
                  helperText="Test positive, disease absent"
                  sx={{ mt: 8 }}
                />
                
                <TextField
                  fullWidth
                  label="True Negatives (TN)"
                  type="number"
                  value={inputData.trueNegative}
                  onChange={(e) => setInputData({ ...inputData, trueNegative: e.target.value })}
                  margin="normal"
                  helperText="Test negative, disease absent"
                />
                
                <TextField
                  fullWidth
                  label="Confidence Level (%)"
                  type="number"
                  value={inputData.confidenceLevel}
                  onChange={(e) => setInputData({ ...inputData, confidenceLevel: e.target.value })}
                  margin="normal"
                  helperText="Typically 90, 95, or 99"
                />
                
                <FormControl fullWidth margin="normal">
                  <InputLabel>CI Method</InputLabel>
                  <Select
                    value={inputData.method}
                    onChange={(e) => setInputData({ ...inputData, method: e.target.value as 'wilson' | 'exact' })}
                    label="CI Method"
                  >
                    <MenuItem value="wilson">Wilson Score</MenuItem>
                    <MenuItem value="exact">Exact (Clopper-Pearson)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={calculateDiagnosticTestCI}
                startIcon={<CalculateIcon />}
                size="large"
              >
                Calculate CI
              </Button>
              
              <Button
                variant="outlined"
                onClick={clearAll}
                startIcon={<ClearIcon />}
                size="large"
              >
                Clear All
              </Button>
            </Box>
          </CardContent>
        </TabPanel>
        
        <TabPanel value={activeTab} index={1}>
          {results && (
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2">
                  Diagnostic Test Results
                </Typography>
                <Tooltip title="Copy results to clipboard">
                  <IconButton onClick={copyToClipboard}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Measure</TableCell>
                      <TableCell align="center">Value</TableCell>
                      <TableCell align="center">{results.confidenceLevel}% CI</TableCell>
                      <TableCell align="center">Interpretation</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Sensitivity</TableCell>
                      <TableCell align="center">{(results.sensitivity * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">
                        [{(results.sensitivityCI[0] * 100).toFixed(2)}%, {(results.sensitivityCI[1] * 100).toFixed(2)}%]
                      </TableCell>
                      <TableCell>Probability of positive test given disease</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Specificity</TableCell>
                      <TableCell align="center">{(results.specificity * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">
                        [{(results.specificityCI[0] * 100).toFixed(2)}%, {(results.specificityCI[1] * 100).toFixed(2)}%]
                      </TableCell>
                      <TableCell>Probability of negative test given no disease</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>PPV</TableCell>
                      <TableCell align="center">{(results.ppv * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">
                        [{(results.ppvCI[0] * 100).toFixed(2)}%, {(results.ppvCI[1] * 100).toFixed(2)}%]
                      </TableCell>
                      <TableCell>Probability of disease given positive test</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>NPV</TableCell>
                      <TableCell align="center">{(results.npv * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">
                        [{(results.npvCI[0] * 100).toFixed(2)}%, {(results.npvCI[1] * 100).toFixed(2)}%]
                      </TableCell>
                      <TableCell>Probability of no disease given negative test</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Accuracy</TableCell>
                      <TableCell align="center">{(results.accuracy * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">
                        [{(results.accuracyCI[0] * 100).toFixed(2)}%, {(results.accuracyCI[1] * 100).toFixed(2)}%]
                      </TableCell>
                      <TableCell>Overall proportion of correct classifications</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Positive LR</TableCell>
                      <TableCell align="center">{results.positiveLR.toFixed(2)}</TableCell>
                      <TableCell align="center">
                        [{results.positiveLRCI[0].toFixed(2)}, {results.positiveLRCI[1].toFixed(2)}]
                      </TableCell>
                      <TableCell>How much positive test increases disease odds</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Negative LR</TableCell>
                      <TableCell align="center">{results.negativeLR.toFixed(2)}</TableCell>
                      <TableCell align="center">
                        [{results.negativeLRCI[0].toFixed(2)}, {results.negativeLRCI[1].toFixed(2)}]
                      </TableCell>
                      <TableCell>How much negative test decreases disease odds</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Prevalence</TableCell>
                      <TableCell align="center">{(results.prevalence * 100).toFixed(2)}%</TableCell>
                      <TableCell align="center">-</TableCell>
                      <TableCell>Proportion of population with disease</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              
              {/* Formula Display */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Formulas
                </Typography>
                <Box
                  dangerouslySetInnerHTML={{ __html: renderFormula(results.formula) }}
                  sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    '& .katex': { fontSize: '1.1em' }
                  }}
                />
              </Box>
            </CardContent>
          )}
        </TabPanel>
        
        <TabPanel value={activeTab} index={2}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Diagnostic Test Confidence Intervals Guide
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                When to Use
              </Typography>
              <Typography paragraph>
                Use diagnostic test confidence intervals to:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Assess the precision of diagnostic test performance measures" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Compare different diagnostic tests" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Evaluate test reliability and clinical utility" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Report test performance in research studies" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Key Measures
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Sensitivity" 
                    secondary="True positive rate: TP/(TP+FN)" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Specificity" 
                    secondary="True negative rate: TN/(TN+FP)" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="PPV (Positive Predictive Value)" 
                    secondary="Probability of disease given positive test: TP/(TP+FP)" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="NPV (Negative Predictive Value)" 
                    secondary="Probability of no disease given negative test: TN/(TN+FN)" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Likelihood Ratios" 
                    secondary="How much the test result changes the odds of disease" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                CI Methods
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Wilson Score" 
                    secondary="More accurate for small samples and extreme proportions" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Exact (Clopper-Pearson)" 
                    secondary="Conservative method that guarantees coverage probability" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Interpretation Guidelines
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Narrower CIs indicate more precise estimates" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Sensitivity and specificity are intrinsic test properties" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• PPV and NPV depend on disease prevalence" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Positive LR > 10 and Negative LR < 0.1 are clinically useful" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Consider the clinical context when interpreting results" />
                </ListItem>
              </List>
            </Box>
          </CardContent>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default DiagnosticTestCI;