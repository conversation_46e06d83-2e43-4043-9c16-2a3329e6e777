import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  SelectChangeEvent,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Chip,
  Stack,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  RadioGroup,
  Radio,
  Switch,
  Autocomplete,
  CircularProgress
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  ContentCopy as CopyIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Calculate as CalculateIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Clear as ClearIcon,
  Timeline as TimelineIcon,
  BarChart as BarChartIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Science as ScienceIcon,
  AutoAwesome as AutoAwesomeIcon,
  Description as TemplateIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useData } from '../../context/DataContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import VariableSelector from '../UI/VariableSelector';
import { DataType } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import { methodsTemplates } from '../../utils/methodsTemplates';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`methods-tabpanel-${index}`}
      aria-labelledby={`methods-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface CustomTemplate {
  id: string;
  name: string;
  category: string;
  template: string;
  variables: string[];
  description: string;
  created: string;
}

interface MethodsConfiguration {
  studyDesign: string;
  analysisTypes: string[];
  software: string;
  alphaLevel: number;
  powerAnalysis: boolean;
  effectSizes: boolean;
  multipleComparisons: string;
  missingDataHandling: string;
  assumptionTesting: boolean;
  variables: {
    dependent: string[];
    independent: string[];
    covariates: string[];
    moderators: string[];
  };
  sampleSize: number;
  studyPeriod: string;
  ethicsApproval: boolean;
  dataCollection: string;
  qualityControl: string[];
}

const STUDY_DESIGNS = [
  { value: 'experimental', label: 'Experimental/RCT' },
  { value: 'quasi_experimental', label: 'Quasi-experimental' },
  { value: 'cross_sectional', label: 'Cross-sectional' },
  { value: 'longitudinal', label: 'Longitudinal' },
  { value: 'case_control', label: 'Case-control' },
  { value: 'cohort', label: 'Cohort' },
  { value: 'mixed_methods', label: 'Mixed methods' },
  { value: 'systematic_review', label: 'Systematic review' },
  { value: 'meta_analysis', label: 'Meta-analysis' }
];

const ANALYSIS_TYPES = [
  { value: 'descriptive', label: 'Descriptive Statistics', category: 'Basic' },
  { value: 'ttest', label: 'T-tests', category: 'Parametric' },
  { value: 'anova', label: 'ANOVA', category: 'Parametric' },
  { value: 'ancova', label: 'ANCOVA', category: 'Parametric' },
  { value: 'manova', label: 'MANOVA', category: 'Parametric' },
  { value: 'regression', label: 'Linear Regression', category: 'Regression' },
  { value: 'logistic', label: 'Logistic Regression', category: 'Regression' },
  { value: 'multilevel', label: 'Multilevel Modeling', category: 'Advanced' },
  
  { value: 'survival', label: 'Survival Analysis', category: 'Advanced' },
  { value: 'nonparametric', label: 'Non-parametric Tests', category: 'Non-parametric' },
  { value: 'correlation', label: 'Correlation Analysis', category: 'Basic' },
  { value: 'factor_analysis', label: 'Factor Analysis', category: 'Multivariate' },
  { value: 'cluster_analysis', label: 'Cluster Analysis', category: 'Multivariate' },
  { value: 'time_series', label: 'Time Series Analysis', category: 'Advanced' }
];

const SOFTWARE_OPTIONS = [
  'DataStatPro', 'SPSS', 'R', 'SAS', 'Stata', 'Python', 'JASP', 'jamovi', 'Mplus', 'AMOS', 'HLM', 'MLwiN'
];

const MULTIPLE_COMPARISONS = [
  { value: 'none', label: 'No correction' },
  { value: 'bonferroni', label: 'Bonferroni correction' },
  { value: 'holm', label: 'Holm-Bonferroni' },
  { value: 'fdr', label: 'False Discovery Rate (FDR)' },
  { value: 'tukey', label: 'Tukey HSD' },
  { value: 'scheffe', label: 'Scheffé' }
];

const MISSING_DATA_METHODS = [
  { value: 'listwise', label: 'Listwise deletion' },
  { value: 'pairwise', label: 'Pairwise deletion' },
  { value: 'mean_imputation', label: 'Mean imputation' },
  { value: 'multiple_imputation', label: 'Multiple imputation' },
  { value: 'fiml', label: 'Full Information Maximum Likelihood (FIML)' },
  { value: 'em', label: 'Expectation-Maximization (EM)' }
];

const EnhancedStatisticalMethodsGenerator: React.FC = () => {
  const { canAccessProFeatures } = useAuth();
  const { datasets, currentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [configuration, setConfiguration] = useState<MethodsConfiguration>({
    studyDesign: '',
    analysisTypes: [],
    software: 'DataStatPro',
    alphaLevel: 0.05,
    powerAnalysis: true,
    effectSizes: true,
    multipleComparisons: 'none',
    missingDataHandling: 'listwise',
    assumptionTesting: true,
    variables: {
      dependent: [],
      independent: [],
      covariates: [],
      moderators: []
    },
    sampleSize: 0,
    studyPeriod: '',
    ethicsApproval: true,
    dataCollection: '',
    qualityControl: []
  });
  const [generatedText, setGeneratedText] = useState<string>('');
  const [customTemplates, setCustomTemplates] = useState<CustomTemplate[]>([
    {
      id: 'template_rct_1',
      name: 'Randomized Controlled Trial Template',
      category: 'Experimental',
      template: 'This randomized controlled trial employed a {study_design} to examine the effects of {intervention} on {dependent_variables}. Participants were randomly assigned to {groups} using {randomization_method}. The primary outcome was {primary_outcome}, measured using {measurement_instrument}. Secondary outcomes included {secondary_outcomes}. Statistical analyses were conducted using {software} with significance set at α = {alpha_level}.',
      variables: ['study_design', 'intervention', 'dependent_variables', 'groups', 'randomization_method', 'primary_outcome', 'measurement_instrument', 'secondary_outcomes', 'software', 'alpha_level'],
      description: 'Comprehensive template for randomized controlled trials with placeholders for key study elements.',
      created: new Date().toISOString()
    },
    {
      id: 'template_observational_1',
      name: 'Cross-sectional Study Template',
      category: 'Observational',
      template: 'This cross-sectional study examined associations between {independent_variables} and {dependent_variables} in a sample of {sample_description}. Data were collected using {data_collection_method} during {study_period}. Descriptive statistics were calculated for all variables. {analysis_methods} were performed to examine relationships between variables. All analyses were conducted using {software} with statistical significance set at p < {alpha_level}.',
      variables: ['independent_variables', 'dependent_variables', 'sample_description', 'data_collection_method', 'study_period', 'analysis_methods', 'software', 'alpha_level'],
      description: 'Template for cross-sectional observational studies focusing on associations between variables.',
      created: new Date().toISOString()
    },
    {
      id: 'template_regression_1',
      name: 'Multiple Regression Analysis Template',
      category: 'Regression',
      template: 'Multiple linear regression analysis was conducted to examine predictors of {dependent_variable}. The model included {independent_variables} as predictor variables{covariates_text}. Prior to analysis, assumptions of linearity, independence, homoscedasticity, and normality were assessed through {assumption_tests}. {missing_data_handling} was used to address missing data. The final model was: {dependent_variable} = β₀ + β₁({predictor_1}) + β₂({predictor_2}) + ... + ε. All analyses were performed using {software}.',
      variables: ['dependent_variable', 'independent_variables', 'covariates_text', 'assumption_tests', 'missing_data_handling', 'predictor_1', 'predictor_2', 'software'],
      description: 'Detailed template for multiple regression analyses with assumption testing and model specification.',
      created: new Date().toISOString()
    }
  ]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [templateDialogOpen, setTemplateDialogOpen] = useState<boolean>(false);
  const [editingTemplate, setEditingTemplate] = useState<CustomTemplate | null>(null);
  const [previewMode, setPreviewMode] = useState<boolean>(false);

  // Check if user can access this feature
  if (!canAccessProFeatures) {
    return (
      <PublicationReadyGate 
        featureName="Enhanced Statistical Methods Generator"
        description="Generate comprehensive, publication-ready statistical methods sections with advanced customization and templates."
        features={[
          "Comprehensive analysis type coverage (parametric, non-parametric, multivariate)",
          "Custom template creation and management",
          "Variable-specific method descriptions",
          "Multiple software package support",
          "Advanced options (power analysis, effect sizes, missing data)",
          "Study design-specific recommendations"
        ]}
      />
    );
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleConfigurationChange = (field: keyof MethodsConfiguration, value: any) => {
    setConfiguration(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const handleVariableChange = (type: keyof MethodsConfiguration['variables'], variables: string[]) => {
    setConfiguration(prev => ({
      ...prev,
      variables: {
        ...prev.variables,
        [type]: variables
      }
    }));
  };

  const generateMethodsSection = () => {
    if (!configuration.studyDesign || configuration.analysisTypes.length === 0) {
      setError('Please select a study design and at least one analysis type.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let methodsText = '';

      // Study Design Section
      methodsText += generateStudyDesignSection();
      methodsText += '\n\n';

      // Participants/Sample Section
      if (configuration.sampleSize > 0) {
        methodsText += generateParticipantsSection();
        methodsText += '\n\n';
      }

      // Data Collection Section
      if (configuration.dataCollection) {
        methodsText += generateDataCollectionSection();
        methodsText += '\n\n';
      }

      // Statistical Analysis Section
      methodsText += generateStatisticalAnalysisSection();
      methodsText += '\n\n';

      // Software and Significance Section
      methodsText += generateSoftwareSection();

      setGeneratedText(methodsText.trim());
      
      // Auto-navigate to Generated Text tab
      setActiveTab(1);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during generation.');
    } finally {
      setLoading(false);
    }
  };

  const generateStudyDesignSection = (): string => {
    const designLabels: { [key: string]: string } = {
      experimental: 'randomized controlled trial',
      quasi_experimental: 'quasi-experimental design',
      cross_sectional: 'cross-sectional design',
      longitudinal: 'longitudinal design',
      case_control: 'case-control design',
      cohort: 'cohort design',
      mixed_methods: 'mixed-methods approach',
      systematic_review: 'systematic review',
      meta_analysis: 'meta-analysis'
    };

    let text = `**Study Design**\n\n`;
    text += `This study employed a ${designLabels[configuration.studyDesign] || configuration.studyDesign}.`;
    
    if (configuration.ethicsApproval) {
      text += ` The study protocol was approved by the institutional review board, and all participants provided informed consent.`;
    }
    
    if (configuration.studyPeriod) {
      text += ` Data collection occurred ${configuration.studyPeriod}.`;
    }

    return text;
  };

  const generateParticipantsSection = (): string => {
    let text = `**Participants**\n\n`;
    text += `The study included ${configuration.sampleSize} participants.`;
    
    if (configuration.powerAnalysis) {
      text += ` Sample size was determined through power analysis to detect medium effect sizes (Cohen's d = 0.5) with 80% power at α = ${configuration.alphaLevel}.`;
    }
    
    return text;
  };

  const generateDataCollectionSection = (): string => {
    let text = `**Data Collection**\n\n`;
    text += configuration.dataCollection;
    
    if (configuration.qualityControl.length > 0) {
      text += ` Quality control measures included ${configuration.qualityControl.join(', ')}.`;
    }
    
    return text;
  };

  const generateStatisticalAnalysisSection = (): string => {
    let text = `**Statistical Analysis**\n\n`;
    
    // Assumption testing
    if (configuration.assumptionTesting) {
      text += `Prior to analysis, data were examined for normality, homogeneity of variance, and outliers. `;
    }
    
    // Missing data handling
    if (configuration.missingDataHandling !== 'none') {
      const missingDataText: { [key: string]: string } = {
        listwise: 'Missing data were handled using listwise deletion.',
        pairwise: 'Missing data were handled using pairwise deletion.',
        mean_imputation: 'Missing values were replaced using mean imputation.',
        multiple_imputation: 'Missing data were handled using multiple imputation with 5 imputations.',
        fiml: 'Missing data were handled using Full Information Maximum Likelihood (FIML).',
        em: 'Missing data were handled using the Expectation-Maximization (EM) algorithm.'
      };
      text += missingDataText[configuration.missingDataHandling] + ' ';
    }
    
    // Analysis descriptions
    configuration.analysisTypes.forEach((analysisType, index) => {
      text += generateAnalysisDescription(analysisType);
      if (index < configuration.analysisTypes.length - 1) {
        text += ' ';
      }
    });
    
    // Multiple comparisons
    if (configuration.multipleComparisons !== 'none') {
      const correctionText: { [key: string]: string } = {
        bonferroni: 'Bonferroni correction',
        holm: 'Holm-Bonferroni correction',
        fdr: 'False Discovery Rate (FDR) correction',
        tukey: 'Tukey HSD correction',
        scheffe: 'Scheffé correction'
      };
      text += ` Multiple comparisons were adjusted using ${correctionText[configuration.multipleComparisons]}.`;
    }
    
    // Effect sizes
    if (configuration.effectSizes) {
      text += ` Effect sizes were calculated and interpreted according to Cohen's conventions.`;
    }
    
    return text;
  };

  const generateAnalysisDescription = (analysisType: string): string => {
    const { dependent, independent, covariates, moderators } = configuration.variables;
    
    // Helper function to format variable lists
    const formatVariables = (vars: string[]) => vars.length > 0 ? vars.join(', ') : '';
    
    let description = '';
    
    if (analysisType === 'descriptive') {
      description = 'Descriptive statistics including means, standard deviations, frequencies, and percentages were calculated';
      if (dependent.length > 0 || independent.length > 0) {
        const allVars = [...dependent, ...independent].filter(Boolean);
        if (allVars.length > 0) {
          description += ` for ${formatVariables(allVars)}`;
        }
      }
      description += '.';
    } else if (analysisType === 'ttest') {
      description = 'Independent samples t-tests were conducted';
      if (dependent.length > 0) {
        description += ` to compare means of ${formatVariables(dependent)}`;
      }
      if (independent.length > 0) {
        description += ` between groups defined by ${formatVariables(independent)}`;
      }
      description += '.';
    } else if (analysisType === 'anova') {
      description = 'Analysis of variance (ANOVA) was performed';
      if (dependent.length > 0) {
        description += ` to examine group differences in ${formatVariables(dependent)}`;
      }
      if (independent.length > 0) {
        description += ` across groups of ${formatVariables(independent)}`;
      }
      description += '.';
    } else if (analysisType === 'ancova') {
      description = 'Analysis of covariance (ANCOVA) was conducted';
      if (dependent.length > 0) {
        description += ` to examine group differences in ${formatVariables(dependent)}`;
      }
      if (covariates.length > 0) {
        description += ` while controlling for ${formatVariables(covariates)}`;
      }
      description += '.';
    } else if (analysisType === 'regression') {
      description = 'Multiple linear regression analysis was performed';
      if (dependent.length > 0) {
        description += ` to predict ${formatVariables(dependent)}`;
      }
      if (independent.length > 0) {
        description += ` using ${formatVariables(independent)} as predictors`;
      }
      if (covariates.length > 0) {
        description += ` while controlling for ${formatVariables(covariates)}`;
      }
      description += '.';
    } else if (analysisType === 'logistic') {
      description = 'Logistic regression analysis was conducted';
      if (dependent.length > 0) {
        description += ` to model ${formatVariables(dependent)}`;
      }
      if (independent.length > 0) {
        description += ` using ${formatVariables(independent)} as predictors`;
      }
      description += '.';
    } else if (analysisType === 'correlation') {
      description = 'Correlation analysis was performed';
      if (dependent.length > 0 || independent.length > 0) {
        const allVars = [...dependent, ...independent].filter(Boolean);
        if (allVars.length > 0) {
          description += ` to examine relationships between ${formatVariables(allVars)}`;
        }
      }
      description += '.';
    } else if (analysisType === 'manova') {
      description = 'Multivariate analysis of variance (MANOVA) was used';
      if (dependent.length > 0) {
        description += ` to examine differences across multiple dependent variables (${formatVariables(dependent)})`;
      }
      if (independent.length > 0) {
        description += ` between groups of ${formatVariables(independent)}`;
      }
      description += '.';
    } else {
      // Default templates for other analysis types
      const templates: { [key: string]: string } = {
        multilevel: 'Multilevel modeling was employed to account for nested data structure.',
  
        survival: 'Survival analysis using Cox proportional hazards regression was performed.',
        nonparametric: 'Non-parametric tests were used when assumptions of normality were violated.',
        factor_analysis: 'Exploratory factor analysis was conducted to identify underlying factor structure.',
        cluster_analysis: 'Cluster analysis was performed to identify distinct groups within the data.',
        time_series: 'Time series analysis was conducted to examine temporal patterns and trends.'
      };
      
      description = templates[analysisType] || `${analysisType} analysis was performed.`;
    }
    
    return description;
  };

  const generateSoftwareSection = (): string => {
    let text = `**Software and Significance**\n\n`;
    text += `All analyses were conducted using ${configuration.software}.`;
    text += ` Statistical significance was set at α = ${configuration.alphaLevel}.`;
    
    return text;
  };

  const createCustomTemplate = () => {
    setEditingTemplate({
      id: '',
      name: '',
      category: '',
      template: '',
      variables: [],
      description: '',
      created: new Date().toISOString()
    });
    setTemplateDialogOpen(true);
  };

  const saveCustomTemplate = () => {
    if (!editingTemplate) return;
    
    const newTemplate = {
      ...editingTemplate,
      id: editingTemplate.id || `template_${Date.now()}`,
      created: editingTemplate.created || new Date().toISOString()
    };
    
    setCustomTemplates(prev => {
      const existing = prev.findIndex(t => t.id === newTemplate.id);
      if (existing >= 0) {
        const updated = [...prev];
        updated[existing] = newTemplate;
        return updated;
      } else {
        return [...prev, newTemplate];
      }
    });
    
    setTemplateDialogOpen(false);
    setEditingTemplate(null);
  };

  const clearAll = () => {
    setConfiguration({
      studyDesign: '',
      analysisTypes: [],
      software: 'SPSS',
      alphaLevel: 0.05,
      powerAnalysis: true,
      effectSizes: true,
      multipleComparisons: 'none',
      missingDataHandling: 'listwise',
      assumptionTesting: true,
      variables: {
        dependent: [],
        independent: [],
        covariates: [],
        moderators: []
      },
      sampleSize: 0,
      studyPeriod: '',
      ethicsApproval: true,
      dataCollection: '',
      qualityControl: []
    });
    setGeneratedText('');
    setError(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const exportAsWord = () => {
    // Create a simple HTML document for Word export
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Statistical Methods Section</title>
        <style>
          body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.5; }
          h1, h2, h3 { font-weight: bold; }
          p { margin: 0 0 12pt 0; }
        </style>
      </head>
      <body>
        ${generatedText.replace(/\*\*(.*?)\*\*/g, '<h3>$1</h3>').replace(/\n\n/g, '</p><p>').replace(/^/, '<p>').replace(/$/, '</p>')}
      </body>
      </html>
    `;
    
    const blob = new Blob([htmlContent], { type: 'application/msword' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'statistical_methods.doc';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <ScienceIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Enhanced Statistical Methods Generator
        </Typography>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Generate comprehensive, publication-ready statistical methods sections with advanced customization and templates.
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Configuration" icon={<TemplateIcon />} />
          <Tab label="Generated Text" icon={<AssessmentIcon />} />
          <Tab label="Custom Templates" icon={<EditIcon />} />
          <Tab label="Preview" icon={<InfoIcon />} />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            {/* Study Design */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Study Design</InputLabel>
                <Select
                  value={configuration.studyDesign}
                  onChange={(e) => handleConfigurationChange('studyDesign', e.target.value)}
                  label="Study Design"
                >
                  {STUDY_DESIGNS.map((design) => (
                    <MenuItem key={design.value} value={design.value}>
                      {design.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Software */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={SOFTWARE_OPTIONS}
                value={configuration.software}
                onChange={(event, newValue) => handleConfigurationChange('software', newValue || 'SPSS')}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Statistical Software"
                    margin="normal"
                    fullWidth
                  />
                )}
              />
            </Grid>

            {/* Analysis Types */}
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Analysis Types</InputLabel>
                <Select
                  multiple
                  value={configuration.analysisTypes}
                  onChange={(e) => handleConfigurationChange('analysisTypes', e.target.value)}
                  label="Analysis Types"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {(selected as string[]).map((value) => {
                        const analysis = ANALYSIS_TYPES.find(a => a.value === value);
                        return (
                          <Chip key={value} label={analysis?.label || value} size="small" />
                        );
                      })}
                    </Box>
                  )}
                >
                  {ANALYSIS_TYPES.map((analysis) => (
                    <MenuItem key={analysis.value} value={analysis.value}>
                      <Box>
                        <Typography variant="body1">{analysis.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {analysis.category}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Sample Size and Alpha Level */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Sample Size"
                type="number"
                value={configuration.sampleSize}
                onChange={(e) => handleConfigurationChange('sampleSize', parseInt(e.target.value) || 0)}
                margin="normal"
                inputProps={{ min: 0 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Alpha Level"
                type="number"
                value={configuration.alphaLevel}
                onChange={(e) => handleConfigurationChange('alphaLevel', parseFloat(e.target.value))}
                margin="normal"
                inputProps={{ min: 0.001, max: 0.1, step: 0.001 }}
              />
            </Grid>

            {/* Multiple Comparisons */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Multiple Comparisons Correction</InputLabel>
                <Select
                  value={configuration.multipleComparisons}
                  onChange={(e) => handleConfigurationChange('multipleComparisons', e.target.value)}
                  label="Multiple Comparisons Correction"
                >
                  {MULTIPLE_COMPARISONS.map((method) => (
                    <MenuItem key={method.value} value={method.value}>
                      {method.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Missing Data Handling */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Missing Data Handling</InputLabel>
                <Select
                  value={configuration.missingDataHandling}
                  onChange={(e) => handleConfigurationChange('missingDataHandling', e.target.value)}
                  label="Missing Data Handling"
                >
                  {MISSING_DATA_METHODS.map((method) => (
                    <MenuItem key={method.value} value={method.value}>
                      {method.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Variable Selection - Show when analysis types are selected */}
            {configuration.analysisTypes.length > 0 && currentDataset && (
              <>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Variable Selection
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Select specific variables for your analyses. These will be reflected in the generated narrative.
                  </Typography>
                </Grid>

                {/* Dependent Variables */}
                <Grid item xs={12} md={6}>
                  <VariableSelector
                    label="Dependent Variables"
                    datasetId={currentDataset.id}
                    value={configuration.variables.dependent}
                    onChange={(variables) => handleVariableChange('dependent', variables as string[])}
                    multiple={true}
                    helperText="Primary outcome variables for your analyses"
                  />
                </Grid>

                {/* Independent Variables */}
                <Grid item xs={12} md={6}>
                  <VariableSelector
                    label="Independent Variables"
                    datasetId={currentDataset.id}
                    value={configuration.variables.independent}
                    onChange={(variables) => handleVariableChange('independent', variables as string[])}
                    multiple={true}
                    helperText="Predictor or grouping variables"
                  />
                </Grid>

                {/* Covariates */}
                <Grid item xs={12} md={6}>
                  <VariableSelector
                    label="Covariates"
                    datasetId={currentDataset.id}
                    value={configuration.variables.covariates}
                    onChange={(variables) => handleVariableChange('covariates', variables as string[])}
                    multiple={true}
                    helperText="Control variables to adjust for"
                  />
                </Grid>

                {/* Moderators */}
                <Grid item xs={12} md={6}>
                  <VariableSelector
                    label="Moderators"
                    datasetId={currentDataset.id}
                    value={configuration.variables.moderators}
                    onChange={(variables) => handleVariableChange('moderators', variables as string[])}
                    multiple={true}
                    helperText="Variables that may moderate relationships"
                  />
                </Grid>
              </>
            )}

            {/* Study Period */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Study Period (e.g., 'between January and March 2024')"
                value={configuration.studyPeriod}
                onChange={(e) => handleConfigurationChange('studyPeriod', e.target.value)}
                margin="normal"
                multiline
                rows={2}
              />
            </Grid>

            {/* Data Collection */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Data Collection Description"
                value={configuration.dataCollection}
                onChange={(e) => handleConfigurationChange('dataCollection', e.target.value)}
                margin="normal"
                multiline
                rows={3}
                placeholder="Describe your data collection procedures, instruments, and protocols..."
              />
            </Grid>

            {/* Options */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Additional Options
              </Typography>
              <Stack direction="row" spacing={2} flexWrap="wrap">
                <FormControlLabel
                  control={
                    <Switch
                      checked={configuration.powerAnalysis}
                      onChange={(e) => handleConfigurationChange('powerAnalysis', e.target.checked)}
                    />
                  }
                  label="Include Power Analysis"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={configuration.effectSizes}
                      onChange={(e) => handleConfigurationChange('effectSizes', e.target.checked)}
                    />
                  }
                  label="Include Effect Sizes"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={configuration.assumptionTesting}
                      onChange={(e) => handleConfigurationChange('assumptionTesting', e.target.checked)}
                    />
                  }
                  label="Include Assumption Testing"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={configuration.ethicsApproval}
                      onChange={(e) => handleConfigurationChange('ethicsApproval', e.target.checked)}
                    />
                  }
                  label="Include Ethics Approval"
                />
              </Stack>
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  onClick={generateMethodsSection}
                  disabled={loading || !configuration.studyDesign || configuration.analysisTypes.length === 0}
                  startIcon={loading ? <CircularProgress size={20} /> : <AutoAwesomeIcon />}
                >
                  {loading ? 'Generating...' : 'Generate Methods Section'}
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearAll}
                  startIcon={<ClearIcon />}
                >
                  Clear All
                </Button>
              </Box>
            </Grid>

            {error && (
              <Grid item xs={12}>
                <Alert severity="error">{error}</Alert>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {generatedText ? (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Generated Methods Section</Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CopyIcon />}
                    onClick={() => copyToClipboard(generatedText)}
                  >
                    Copy Text
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<DownloadIcon />}
                    onClick={exportAsWord}
                  >
                    Export Word
                  </Button>
                  <AddToResultsButton
                    resultData={{
                      title: `Enhanced Statistical Methods Section (${configuration.studyDesign || 'Custom'} Study)`,
                      type: 'other' as const,
                      component: 'EnhancedStatisticalMethodsGenerator',
                      data: {
                        methodsText: generatedText,
                        configuration: configuration,
                        studyDesign: configuration.studyDesign,
                        analysisTypes: configuration.analysisTypes,
                        software: configuration.software,
                        wordCount: generatedText.trim().split(/\s+/).filter(word => word.length > 0).length,
                        timestamp: new Date().toISOString()
                      }
                    }}
                    size="small"
                    onSuccess={() => {
                      console.log('Enhanced methods section successfully added to Results Manager!');
                    }}
                  />
                </Box>
              </Box>

              <Paper 
                elevation={1} 
                sx={{ 
                  p: 3, 
                  bgcolor: theme.palette.background.default,
                  fontFamily: 'serif',
                  lineHeight: 1.6,
                  '& h3': {
                    fontWeight: 'bold',
                    mt: 2,
                    mb: 1
                  }
                }}
              >
                <Typography 
                  component="div" 
                  sx={{ whiteSpace: 'pre-line' }}
                  dangerouslySetInnerHTML={{
                    __html: generatedText
                      .replace(/\*\*(.*?)\*\*/g, '<h3>$1</h3>')
                      .replace(/\n\n/g, '</p><p>')
                      .replace(/^/, '<p>')
                      .replace(/$/, '</p>')
                  }}
                />
              </Paper>
            </Box>
          ) : (
            <Alert severity="info">
              No methods section generated yet. Use the Configuration tab to set up your analysis and generate the text.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">Custom Templates</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={createCustomTemplate}
              >
                Create Template
              </Button>
            </Box>

            {customTemplates.length > 0 ? (
              <Grid container spacing={2}>
                {customTemplates.map((template) => (
                  <Grid item xs={12} md={6} key={template.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {template.name}
                        </Typography>
                        <Chip label={template.category} size="small" sx={{ mb: 1 }} />
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {template.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Created: {new Date(template.created).toLocaleDateString()}
                        </Typography>
                        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            startIcon={<EditIcon />}
                            onClick={() => {
                              setEditingTemplate(template);
                              setTemplateDialogOpen(true);
                            }}
                          >
                            Edit
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Alert severity="info">
                No custom templates created yet. Click "Create Template" to add your own method templates.
              </Alert>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" gutterBottom>
            Methods Section Guidelines
          </Typography>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Essential Components</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText primary="Study design and rationale" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Participant characteristics and recruitment" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Data collection procedures and instruments" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Statistical analysis plan" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Software used and significance levels" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Best Practices</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText primary="Be specific about statistical tests and their assumptions" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Justify sample size with power analysis when possible" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Address missing data handling explicitly" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Include effect size measures and interpretations" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Mention multiple comparisons corrections if applicable" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">Common Mistakes to Avoid</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText primary="Vague descriptions of statistical procedures" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Omitting assumption testing or violation handling" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Not addressing multiple comparisons" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Insufficient detail about data preprocessing" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Missing software version information" />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
        </TabPanel>
      </Paper>

      {/* Custom Template Dialog */}
      <Dialog 
        open={templateDialogOpen} 
        onClose={() => setTemplateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingTemplate?.id ? 'Edit Template' : 'Create Custom Template'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Template Name"
                value={editingTemplate?.name || ''}
                onChange={(e) => setEditingTemplate(prev => prev ? { ...prev, name: e.target.value } : null)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Category"
                value={editingTemplate?.category || ''}
                onChange={(e) => setEditingTemplate(prev => prev ? { ...prev, category: e.target.value } : null)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={editingTemplate?.description || ''}
                onChange={(e) => setEditingTemplate(prev => prev ? { ...prev, description: e.target.value } : null)}
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Template Text"
                value={editingTemplate?.template || ''}
                onChange={(e) => setEditingTemplate(prev => prev ? { ...prev, template: e.target.value } : null)}
                multiline
                rows={8}
                placeholder="Enter your template text here. Use {variable} syntax for placeholders..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTemplateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={saveCustomTemplate}
            variant="contained"
            disabled={!editingTemplate?.name || !editingTemplate?.template}
          >
            Save Template
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedStatisticalMethodsGenerator;