import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  SelectChangeEvent,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  FormControlLabel,
  Checkbox,
  Tooltip,
  Chip,
  Stack
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  Pie<PERSON>hart as ProportionIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import rehypeStringify from 'rehype-stringify';
import rehypeKatex from 'rehype-katex';
import remarkRehype from 'remark-rehype';
import SocialShare from '../UI/SocialShare';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`proportion-ci-tabpanel-${index}`}
      aria-labelledby={`proportion-ci-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface ProportionCIResult {
  method: string;
  lowerBound: number;
  upperBound: number;
  confidenceLevel: number;
  sampleSize: number;
  successes: number;
  proportion: number;
  formula: string;
  description: string;
  reference: string;
}

interface CalculationData {
  successes: number;
  sampleSize: number;
  confidenceLevel: number;
  method: 'wald' | 'wilson' | 'exact' | 'agresti-coull';
}

const CI_METHODS = [
  { value: 'wald', label: 'Wald (Normal Approximation)', description: 'Standard normal approximation method' },
  { value: 'wilson', label: 'Wilson Score', description: 'Recommended for small samples or extreme proportions' },
  { value: 'exact', label: 'Exact (Clopper-Pearson)', description: 'Conservative exact method using binomial distribution' },
  { value: 'agresti-coull', label: 'Agresti-Coull', description: 'Adjusted Wald method with better coverage' }
];

const SingleProportionCI: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<CalculationData>({
    successes: 0,
    sampleSize: 0,
    confidenceLevel: 95,
    method: 'wilson'
  });
  const [results, setResults] = useState<ProportionCIResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showAllMethods, setShowAllMethods] = useState<boolean>(false);

  // Function to render mathematical formulas with KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const file = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$${formula}$`);
      return String(file);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleInputChange = (field: keyof CalculationData, value: any) => {
    setCalculationData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const calculateWaldCI = (p: number, n: number, alpha: number): [number, number] => {
    const z = getZScore(alpha);
    const se = Math.sqrt((p * (1 - p)) / n);
    const margin = z * se;
    return [Math.max(0, p - margin), Math.min(1, p + margin)];
  };

  const calculateWilsonCI = (x: number, n: number, alpha: number): [number, number] => {
    const z = getZScore(alpha);
    const z2 = z * z;
    const p = x / n;
    
    const denominator = 1 + z2 / n;
    const center = (p + z2 / (2 * n)) / denominator;
    const margin = (z / denominator) * Math.sqrt((p * (1 - p)) / n + z2 / (4 * n * n));
    
    return [Math.max(0, center - margin), Math.min(1, center + margin)];
  };

  const calculateAgrestiCoullCI = (x: number, n: number, alpha: number): [number, number] => {
    const z = getZScore(alpha);
    const z2 = z * z;
    const nTilde = n + z2;
    const pTilde = (x + z2 / 2) / nTilde;
    const se = Math.sqrt((pTilde * (1 - pTilde)) / nTilde);
    const margin = z * se;
    
    return [Math.max(0, pTilde - margin), Math.min(1, pTilde + margin)];
  };

  const calculateExactCI = (x: number, n: number, alpha: number): [number, number] => {
    // Simplified exact calculation using beta distribution approximation
    // For a more precise implementation, you would use the beta distribution quantiles
    const alphaHalf = alpha / 2;
    
    let lower = 0;
    let upper = 1;
    
    if (x > 0) {
      // Beta(x, n-x+1) for lower bound
      lower = betaInverse(alphaHalf, x, n - x + 1);
    }
    
    if (x < n) {
      // Beta(x+1, n-x) for upper bound
      upper = betaInverse(1 - alphaHalf, x + 1, n - x);
    }
    
    return [lower, upper];
  };

  const getZScore = (confidenceLevel: number): number => {
    const alpha = (100 - confidenceLevel) / 100;
    // Common z-scores for confidence levels
    const zScores: { [key: number]: number } = {
      90: 1.645,
      95: 1.96,
      99: 2.576,
      99.9: 3.291
    };
    return zScores[confidenceLevel] || 1.96;
  };

  const betaInverse = (p: number, a: number, b: number): number => {
    // Simplified beta inverse function - in practice, you'd use a more sophisticated implementation
    // This is a rough approximation
    if (a === 1 && b === 1) return p;
    if (a === 1) return 1 - Math.pow(1 - p, 1 / b);
    if (b === 1) return Math.pow(p, 1 / a);
    
    // For other cases, use a simple approximation
    const mean = a / (a + b);
    const variance = (a * b) / ((a + b) * (a + b) * (a + b + 1));
    const std = Math.sqrt(variance);
    
    // Normal approximation to beta
    const z = getZScore(95); // Use 95% for approximation
    if (p < 0.5) {
      return Math.max(0, mean - z * std);
    } else {
      return Math.min(1, mean + z * std);
    }
  };

  const calculateProportionCI = () => {
    const { successes, sampleSize, confidenceLevel, method } = calculationData;
    
    if (sampleSize <= 0) {
      setError('Sample size must be greater than 0.');
      return;
    }
    
    if (successes < 0 || successes > sampleSize) {
      setError('Number of successes must be between 0 and sample size.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const newResults: ProportionCIResult[] = [];
      const p = successes / sampleSize;
      const alpha = (100 - confidenceLevel) / 100;
      
      const methodsToCalculate = showAllMethods ? CI_METHODS : [CI_METHODS.find(m => m.value === method)!];
      
      for (const methodInfo of methodsToCalculate) {
        let ci: [number, number];
        let formula: string;
        let description: string;
        let reference: string;
        
        switch (methodInfo.value) {
          case 'wald':
            ci = calculateWaldCI(p, sampleSize, alpha);
            formula = 'p \\pm z_{\\alpha/2} \\sqrt{\\frac{p(1-p)}{n}}';
            description = 'Standard normal approximation using sample proportion';
            reference = 'Wald, A. (1943). Tests of statistical hypotheses concerning several parameters.';
            break;
            
          case 'wilson':
            ci = calculateWilsonCI(successes, sampleSize, alpha);
            formula = '\\frac{p + \\frac{z^2}{2n} \\pm z\\sqrt{\\frac{p(1-p)}{n} + \\frac{z^2}{4n^2}}}{1 + \\frac{z^2}{n}}';
            description = 'Score-based method with better coverage properties';
            reference = 'Wilson, E.B. (1927). Probable inference, the law of succession, and statistical inference.';
            break;
            
          case 'exact':
            ci = calculateExactCI(successes, sampleSize, alpha);
            formula = 'F^{-1}_{Beta}(\\alpha/2; x, n-x+1), F^{-1}_{Beta}(1-\\alpha/2; x+1, n-x)';
            description = 'Exact confidence interval using binomial distribution';
            reference = 'Clopper, C.J. & Pearson, E.S. (1934). The use of confidence or fiducial limits.';
            break;
            
          case 'agresti-coull':
            ci = calculateAgrestiCoullCI(successes, sampleSize, alpha);
            formula = '\\tilde{p} \\pm z_{\\alpha/2} \\sqrt{\\frac{\\tilde{p}(1-\\tilde{p})}{\\tilde{n}}}';
            description = 'Adjusted Wald method with added successes and failures';
            reference = 'Agresti, A. & Coull, B.A. (1998). Approximate is better than "exact".';
            break;
            
          default:
            continue;
        }
        
        newResults.push({
          method: methodInfo.label,
          lowerBound: ci[0],
          upperBound: ci[1],
          confidenceLevel,
          sampleSize,
          successes,
          proportion: p,
          formula,
          description,
          reference
        });
      }
      
      setResults(newResults);
      // Automatically navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError('An error occurred during calculation. Please check your inputs.');
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setCalculationData({
      successes: 0,
      sampleSize: 0,
      confidenceLevel: 95,
      method: 'wilson'
    });
    setResults([]);
    setError(null);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (results.length === 0) return '';
    
    const result = results[0];
    return `Single Proportion Confidence Interval Analysis\n\n` +
           `Sample Size: ${result.sampleSize}\n` +
           `Number of Successes: ${result.successes}\n` +
           `Sample Proportion: ${result.proportion.toFixed(2)}\n` +
           `Confidence Level: ${result.confidenceLevel}%\n` +
           `Method: ${result.method}\n` +
           `Confidence Interval: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n\n` +
           `Interpretation: We are ${result.confidenceLevel}% confident that the true population proportion lies between ${(result.lowerBound * 100).toFixed(2)}% and ${(result.upperBound * 100).toFixed(2)}%.`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <ProportionIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Single Proportion Confidence Interval
        </Typography>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Calculate confidence intervals for a single population proportion using various statistical methods.
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<ProportionIcon />} />
          <Tab label="Method Guide" icon={<InfoIcon />} />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Sample Size (n)"
                type="number"
                value={calculationData.sampleSize}
                onChange={(e) => handleInputChange('sampleSize', parseInt(e.target.value) || 0)}
                inputProps={{ min: 1 }}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Number of Successes"
                type="number"
                value={calculationData.successes}
                onChange={(e) => handleInputChange('successes', parseInt(e.target.value) || 0)}
                inputProps={{ min: 0 }}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Confidence Level (%)"
                type="number"
                value={calculationData.confidenceLevel}
                onChange={(e) => handleInputChange('confidenceLevel', parseFloat(e.target.value) || 95)}
                inputProps={{ min: 50, max: 99.9, step: 0.1 }}
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Method</InputLabel>
                <Select
                  value={calculationData.method}
                  onChange={(e) => handleInputChange('method', e.target.value)}
                  label="Method"
                >
                  {CI_METHODS.map((method) => (
                    <MenuItem key={method.value} value={method.value}>
                      <Box>
                        <Typography variant="body1">{method.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {method.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={showAllMethods}
                    onChange={(e) => setShowAllMethods(e.target.checked)}
                  />
                }
                label="Calculate using all methods for comparison"
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  onClick={calculateProportionCI}
                  disabled={loading || calculationData.sampleSize <= 0}
                  startIcon={<CalculateIcon />}
                >
                  {loading ? 'Calculating...' : 'Calculate CI'}
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearAll}
                  startIcon={<ClearIcon />}
                >
                  Clear All
                </Button>
              </Box>
            </Grid>

            {error && (
              <Grid item xs={12}>
                <Alert severity="error">{error}</Alert>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {results.length > 0 ? (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Confidence Interval Results</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<CopyIcon />}
                  onClick={() => copyToClipboard(generateReport())}
                >
                  Copy Report
                </Button>
              </Box>

              {results.map((result, index) => (
                <Card key={index} sx={{ mb: 2, bgcolor: theme.palette.action.hover }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Typography variant="h6" color="primary">
                        {result.method}
                      </Typography>
                      <Chip
                        label={`${result.confidenceLevel}% CI`}
                        color="primary"
                        size="small"
                      />
                    </Box>
                    
                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Sample Proportion</Typography>
                        <Typography variant="h6">{result.proportion.toFixed(2)}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Sample Size</Typography>
                        <Typography variant="h6">{result.sampleSize}</Typography>
                      </Grid>
                    </Grid>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>Confidence Interval</Typography>
                    <Typography variant="h4" sx={{ mb: 2, fontWeight: 'bold' }}>
                      [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                    </Typography>
                    
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {result.description}
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5, display: 'block' }}>
                        Formula:
                      </Typography>
                      <Box 
                        dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                        sx={{ 
                          '& .katex': { fontSize: '0.875rem' },
                          '& .katex-display': { margin: '0.5em 0' }
                        }}
                      />
                    </Box>
                    
                    <Divider sx={{ my: 1 }} />
                    
                    <Typography variant="caption" color="text.secondary">
                      Reference: {result.reference}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
              
              <Box sx={{ mt: 3 }}>
                <SocialShare 
                  url={window.location.href}
                  title="Single Proportion Confidence Interval Results"
                  description={generateReport()}
                />
              </Box>
            </Box>
          ) : (
            <Alert severity="info">
              No results yet. Use the Calculator tab to compute confidence intervals.
            </Alert>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            Confidence Interval Methods for Proportions
          </Typography>
          
          <Grid container spacing={3}>
            {CI_METHODS.map((method, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="primary" gutterBottom>
                      {method.label}
                    </Typography>
                    <Typography variant="body2" paragraph>
                      {method.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {method.value === 'wald' && 'Best for: Large samples (np ≥ 5 and n(1-p) ≥ 5)'}
                      {method.value === 'wilson' && 'Best for: Small samples or extreme proportions (recommended)'}
                      {method.value === 'exact' && 'Best for: When exact coverage is required (conservative)'}
                      {method.value === 'agresti-coull' && 'Best for: Improved coverage over Wald method'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default SingleProportionCI;