-- Fix get_all_users function - Ensure exact type matching and include override status
CREATE OR REPLACE FUNCTION public.get_all_users(
  page_size INTEGER DEFAULT 50,
  page_offset INTEGER DEFAULT 0,
  search_term TEXT DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  email TEXT,
  username TEXT,
  full_name TEXT,
  institution TEXT,
  country TEXT,
  avatar_url TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_admin BOOLEAN,
  accounttype TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  last_sign_in_at TIMESTAMP WITH TIME ZONE,
  override_status TEXT -- New field for override status
)
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '30s'
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Return query with join to get override status
  RETURN QUERY
  SELECT 
    p.id,
    COALESCE(p.username, 'No email available')::TEXT as email,
    COALESCE(p.username, '')::TEXT as username,
    COALESCE(p.full_name, '')::TEXT as full_name,
    COALESCE(p.institution, '')::TEXT as institution,
    COALESCE(p.country, '')::TEXT as country,
    COALESCE(p.avatar_url, '')::TEXT as avatar_url,
    COALESCE(p.updated_at, NOW()) as updated_at,
    COALESCE(p.is_admin, false) as is_admin,
    COALESCE(p.accounttype, 'standard')::TEXT as accounttype,
    COALESCE(p.updated_at, NOW()) as created_at,
    NULL::TIMESTAMP WITH TIME ZONE as last_sign_in_at,
    CASE
      WHEN so.id IS NOT NULL AND so.end_date >= NOW() THEN 'active'
      ELSE 'none'
    END::TEXT as override_status
  FROM public.profiles p
  LEFT JOIN public.subscription_overrides so ON p.id = so.user_id AND so.end_date >= NOW()
  WHERE (
    search_term IS NULL OR 
    search_term = '' OR
    COALESCE(p.full_name, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.username, '') ILIKE '%' || search_term || '%' OR
    COALESCE(p.institution, '') ILIKE '%' || search_term || '%'
  )
  ORDER BY p.updated_at DESC NULLS LAST
  LIMIT page_size OFFSET page_offset;
END;
$$;
