import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  Calculate as CalculateIcon,
  TrendingUp as MeanIcon,
  PieChart as ProportionIcon,
  CompareArrows as DifferenceIcon,
  ScatterPlot as CorrelationIcon,
  <PERSON><PERSON>hart as RegressionIcon,
  Timeline as SurvivalIcon,
  Assessment as DiagnosticIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';

interface CICalculatorOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: 'Mean' | 'Proportion' | 'Correlation' | 'Regression' | 'Diagnostic' | 'Survival' | 'Variance';
  color: string;
}

interface CICalculatorsOptionsProps {
  onNavigate: (path: string) => void;
}

export const ciCalculatorOptions: CICalculatorOption[] = [
  {
    name: 'Single Mean CI',
    shortDescription: 'Confidence interval for a single population mean',
    detailedDescription: 'Calculate confidence intervals for a single population mean using t-distribution or z-distribution depending on sample size and known/unknown population variance.',
    path: 'single-mean',
    icon: <CalculateIcon />,
    category: 'Mean',
    color: '#1976d2'
  },
  {
    name: 'Single Proportion CI',
    shortDescription: 'Confidence interval for a single population proportion',
    detailedDescription: 'Calculate confidence intervals for a single population proportion using normal approximation, exact binomial, or Wilson score methods.',
    path: 'single-proportion',
    icon: <ProportionIcon />,
    category: 'Proportion',
    color: '#388e3c'
  },
  {
    name: 'Difference of Means CI',
    shortDescription: 'Confidence interval for difference between two means',
    detailedDescription: 'Calculate confidence intervals for the difference between two independent population means, with options for equal or unequal variances.',
    path: 'difference-means',
    icon: <DifferenceIcon />,
    category: 'Mean',
    color: '#f57c00'
  },
  {
    name: 'Difference of Proportions CI',
    shortDescription: 'Confidence interval for difference between two proportions',
    detailedDescription: 'Calculate confidence intervals for the difference between two independent population proportions using various methods including Wald and Wilson score.',
    path: 'difference-proportions',
    icon: <DifferenceIcon />,
    category: 'Proportion',
    color: '#7b1fa2'
  },
  {
    name: 'Paired Difference CI',
    shortDescription: 'Confidence interval for paired mean differences',
    detailedDescription: 'Calculate confidence intervals for the mean of paired differences in matched samples or repeated measures designs.',
    path: 'paired-difference',
    icon: <DifferenceIcon />,
    category: 'Mean',
    color: '#d32f2f'
  },
  {
    name: 'Correlation CI',
    shortDescription: 'Confidence interval for correlation coefficient',
    detailedDescription: 'Calculate confidence intervals for Pearson correlation coefficient using Fisher\'s z-transformation or bootstrap methods.',
    path: 'correlation',
    icon: <CorrelationIcon />,
    category: 'Correlation',
    color: '#303f9f'
  },
  {
    name: 'Variance CI',
    shortDescription: 'Confidence interval for population variance',
    detailedDescription: 'Calculate confidence intervals for population variance or standard deviation using chi-square distribution.',
    path: 'variance',
    icon: <MeanIcon />,
    category: 'Variance',
    color: '#689f38'
  },
  {
    name: 'Ratio of Variances CI',
    shortDescription: 'Confidence interval for ratio of two variances',
    detailedDescription: 'Calculate confidence intervals for the ratio of two population variances using F-distribution.',
    path: 'ratio-variances',
    icon: <RegressionIcon />,
    category: 'Variance',
    color: '#512da8'
  },
  {
    name: 'Linear Regression CI',
    shortDescription: 'Confidence intervals for regression parameters',
    detailedDescription: 'Calculate confidence intervals for regression coefficients, predicted values, and prediction intervals in linear regression.',
    path: 'linear-regression',
    icon: <RegressionIcon />,
    category: 'Regression',
    color: '#1976d2'
  },
  {
    name: 'Logistic Regression CI',
    shortDescription: 'Confidence intervals for logistic regression parameters',
    detailedDescription: 'Calculate confidence intervals for odds ratios and regression coefficients in logistic regression models.',
    path: 'logistic-regression',
    icon: <RegressionIcon />,
    category: 'Regression',
    color: '#388e3c'
  },
  {
    name: 'Diagnostic Test CI',
    shortDescription: 'Confidence intervals for diagnostic test accuracy',
    detailedDescription: 'Calculate confidence intervals for sensitivity, specificity, positive and negative predictive values, and likelihood ratios.',
    path: 'diagnostic-test',
    icon: <DiagnosticIcon />,
    category: 'Diagnostic',
    color: '#f57c00'
  },
  {
    name: 'Survival Analysis CI',
    shortDescription: 'Confidence intervals for survival analysis',
    detailedDescription: 'Calculate confidence intervals for survival probabilities, hazard ratios, and median survival times in survival analysis.',
    path: 'survival-analysis',
    icon: <SurvivalIcon />,
    category: 'Survival',
    color: '#7b1fa2'
  },
];

const CICalculatorsOptions: React.FC<CICalculatorsOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  // Generate structured data for confidence interval calculators
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Calculate Confidence Intervals for Statistical Inference",
      "description": "Comprehensive guide to calculating confidence intervals for means, proportions, differences, correlations, regression parameters, and diagnostic test accuracy measures.",
      "totalTime": "PT15M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Sample data with descriptive statistics (mean, standard deviation, sample size)"
        },
        {
          "@type": "HowToSupply",
          "name": "Confidence level specification (typically 95%)"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Confidence Interval Calculator Suite"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Select appropriate CI method",
          "text": "Choose the correct confidence interval calculator based on your data type: single mean, proportion, difference between groups, correlation, or regression parameters"
        },
        {
          "@type": "HowToStep",
          "name": "Input sample statistics",
          "text": "Enter sample size, mean, standard deviation, or proportion values depending on the selected method"
        },
        {
          "@type": "HowToStep",
          "name": "Set confidence level",
          "text": "Specify desired confidence level (90%, 95%, 99%) for interval estimation"
        },
        {
          "@type": "HowToStep",
          "name": "Calculate and interpret",
          "text": "Compute confidence interval bounds and interpret results in context of population parameter estimation"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Confidence Interval Results",
        "description": "Statistical confidence intervals with lower and upper bounds, margin of error, and interpretation guidelines"
      }
    };
  };

  const categories = ['All', 'Mean', 'Proportion', 'Correlation', 'Regression', 'Diagnostic', 'Survival', 'Variance'];
  
  const filteredOptions = selectedCategory === 'All' 
    ? ciCalculatorOptions 
    : ciCalculatorOptions.filter(option => option.category === selectedCategory);

  const handleNavigate = (path: string) => {
    onNavigate(path);
  };

  return (
    <>
      <Helmet>
        <title>Confidence Interval Calculators | DataStatPro - Statistical Inference & CI Analysis</title>
        <meta name="description" content="Professional confidence interval calculators for statistical inference: Single Mean, Proportion, Difference Tests, Correlation, Regression Parameters, Diagnostic Tests with AI-powered interpretation and multiple CI methods." />
        <meta name="keywords" content="confidence intervals, statistical inference, CI calculators, mean confidence interval, proportion CI, difference between means, correlation CI, regression CI, diagnostic test CI, margin of error, statistical significance" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Statistical inference and confidence interval estimation" />
        <meta name="ai:methods" content="T-distribution, Z-distribution, Wilson Score, Exact Binomial, Bootstrap" />
        <meta name="ai:parameters" content="Mean, Proportion, Difference, Correlation, Regression Coefficients" />
        
        {/* Structured data for CI calculators */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for confidence intervals */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What does a 95% confidence interval mean?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "A 95% confidence interval means that if we repeated the sampling process many times, 95% of the calculated intervals would contain the true population parameter. It provides a range of plausible values for the parameter."
                }
              },
              {
                "@type": "Question",
                "name": "When should I use t-distribution vs z-distribution for CI?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Use t-distribution when sample size is small (n<30) or population standard deviation is unknown. Use z-distribution when sample size is large (n≥30) and population standard deviation is known."
                }
              },
              {
                "@type": "Question",
                "name": "How do I interpret overlapping confidence intervals?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Overlapping confidence intervals suggest that the difference between groups may not be statistically significant, but formal hypothesis testing is needed for definitive conclusions. Non-overlapping CIs generally indicate significant differences."
                }
              },
              {
                "@type": "Question",
                "name": "What's the difference between Wilson Score and Wald method for proportions?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Wilson Score method performs better for small samples and extreme proportions (near 0 or 1), providing more accurate coverage. Wald method is simpler but can give poor results with small samples or extreme proportions."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg">
      <Paper 
        elevation={0} 
        sx={{ 
          p: 4, 
          mb: 4, 
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
          borderRadius: 3
        }}
      >
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              mx: 'auto',
              mb: 2,
              bgcolor: theme.palette.primary.main,
              fontSize: '2rem'
            }}
            itemProp="image"
          >
            <CalculateIcon fontSize="large" />
          </Avatar>
          <Typography 
            variant="h3" 
            component="h1" 
            gutterBottom 
            fontWeight="bold"
            itemProp="name"
          >
            Confidence Interval Calculators - Statistical Inference & CI Analysis
          </Typography>
          <Typography 
            variant="h6" 
            color="text.secondary" 
            sx={{ maxWidth: 800, mx: 'auto' }}
            itemProp="description"
          >
            Professional confidence interval calculators for statistical inference: Single Mean, Proportion, 
            Difference Tests, Correlation, Regression Parameters with AI-powered interpretation and multiple CI methods.
          </Typography>
          
          {/* AI-friendly feature overview */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: alpha(theme.palette.info.main, 0.05), borderRadius: 2, maxWidth: 900, mx: 'auto' }}>
            <Typography variant="body2" color="text.secondary">
              <strong>CI Methods Available:</strong> T-distribution • Z-distribution • Wilson Score • Exact Binomial • 
              Bootstrap • Regression Parameters • Diagnostic Test Accuracy • Survival Analysis
            </Typography>
          </Box>
        </Box>

        {/* Category Filter */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4, flexWrap: 'wrap', gap: 1 }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              color={selectedCategory === category ? 'primary' : 'default'}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              sx={{ 
                fontSize: '0.9rem',
                fontWeight: selectedCategory === category ? 'bold' : 'normal'
              }}
            />
          ))}
        </Box>
      </Paper>

      <Grid container spacing={3}>
        {filteredOptions.map((option) => (
          <Grid item xs={12} sm={6} md={4} key={option.name}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                },
                border: `2px solid ${alpha(option.color, 0.1)}`,
                borderRadius: 2,
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                action={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={option.category}
                      size="small"
                      sx={{
                        bgcolor: alpha(option.color, 0.1),
                        color: option.color,
                        fontWeight: 'bold',
                        fontSize: '0.75rem'
                      }}
                      itemProp="category"
                    />
                    <Tooltip title="More information">
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                }
                title={
                  <Typography 
                    variant="h6" 
                    component="h2" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    itemProp="description"
                  >
                    {option.shortDescription}
                  </Typography>
                }
                sx={{ pb: 1 }}
              />
              
              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
                
                <Box sx={{ mt: 'auto', pt: 2 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    onClick={(e) => {
                      e.stopPropagation();
                      handleNavigate(option.path);
                    }}
                    sx={{
                      bgcolor: option.color,
                      '&:hover': {
                        bgcolor: alpha(option.color, 0.8),
                      },
                      fontWeight: 'bold',
                      py: 1.5,
                    }}
                    endIcon={<LaunchIcon />}
                    aria-label={`Open ${option.name} calculator`}
                    itemProp="potentialAction"
                  >
                    Open Calculator
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Statistics */}
      <Box sx={{ mt: 6, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          {filteredOptions.length} confidence interval calculator{filteredOptions.length !== 1 ? 's' : ''} available
          {selectedCategory !== 'All' && ` in ${selectedCategory} category`}
        </Typography>
      </Box>
      </Container>
    </>
  );
};

export default CICalculatorsOptions;