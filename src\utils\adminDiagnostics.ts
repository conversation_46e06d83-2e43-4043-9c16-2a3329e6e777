import { supabase } from './supabaseClient';

/**
 * Comprehensive diagnostic tool for admin dashboard issues
 * Run this after deploying the fix migration to verify all issues are resolved
 */
export const runAdminDiagnostics = async () => {
  console.log('🔍 Starting Admin Dashboard Diagnostics...\n');
  
  const results = {
    userStatistics: { success: false, error: null, data: null },
    userManagement: { success: false, error: null, data: null },
    subscriptionOverrides: { success: false, error: null, data: null },
    materializedView: { success: false, error: null, data: null }
  };

  // Test 1: User Statistics Accuracy
  console.log('1️⃣ Testing User Statistics...');
  try {
    const { data: stats, error: statsError } = await supabase.rpc('get_user_statistics');
    
    if (statsError) {
      throw statsError;
    }
    
    console.log('✅ User Statistics Result:', stats);
    console.log(`📊 Total Users: ${stats.total_users} (Expected: 2139)`);
    
    if (stats.total_users === 2139) {
      console.log('✅ User count is CORRECT!');
    } else {
      console.log(`⚠️ User count mismatch. Got ${stats.total_users}, expected 2139`);
    }
    
    results.userStatistics = { success: true, error: null, data: stats };
  } catch (err: any) {
    console.error('❌ User Statistics Error:', err);
    results.userStatistics = { success: false, error: err.message, data: null };
  }

  // Test 2: User Management Function
  console.log('\n2️⃣ Testing User Management Function...');
  try {
    const { data: users, error: usersError } = await supabase.rpc('get_all_users', {
      page_size: 5,
      page_offset: 0,
      search_term: null
    });
    
    if (usersError) {
      throw usersError;
    }
    
    console.log('✅ User Management Function Works!');
    console.log(`📊 Returned ${users?.length || 0} users`);
    
    if (users && users.length > 0) {
      console.log('📋 Sample User Structure:', {
        id: users[0].id ? '✅' : '❌',
        email: users[0].email ? '✅' : '❌',
        username: users[0].username ? '✅' : '❌',
        full_name: users[0].full_name !== undefined ? '✅' : '❌',
        institution: users[0].institution !== undefined ? '✅' : '❌',
        country: users[0].country !== undefined ? '✅' : '❌',
        avatar_url: users[0].avatar_url !== undefined ? '✅' : '❌',
        updated_at: users[0].updated_at ? '✅' : '❌',
        is_admin: users[0].is_admin !== undefined ? '✅' : '❌',
        accounttype: users[0].accounttype ? '✅' : '❌',
        created_at: users[0].created_at ? '✅' : '❌',
        last_sign_in_at: users[0].last_sign_in_at !== undefined ? '✅' : '❌'
      });
    }
    
    results.userManagement = { success: true, error: null, data: users };
  } catch (err: any) {
    console.error('❌ User Management Error:', err);
    console.error('Error Details:', {
      message: err.message,
      details: err.details,
      hint: err.hint,
      code: err.code
    });
    results.userManagement = { success: false, error: err.message, data: null };
  }

  // Test 3: Subscription Override Functions
  console.log('\n3️⃣ Testing Subscription Override Functions...');
  try {
    // Test get_all_active_overrides
    const { data: activeOverrides, error: activeError } = await supabase.rpc('get_all_active_overrides', {
      page_size: 10,
      page_offset: 0
    });
    
    if (activeError) {
      throw new Error(`get_all_active_overrides: ${activeError.message}`);
    }
    
    console.log('✅ get_all_active_overrides works!');
    console.log(`📊 Active Overrides: ${activeOverrides?.length || 0}`);

    // Test get_override_audit_trail
    const { data: auditTrail, error: auditError } = await supabase.rpc('get_override_audit_trail', {
      target_user_id: null,
      page_size: 10,
      page_offset: 0
    });
    
    if (auditError) {
      throw new Error(`get_override_audit_trail: ${auditError.message}`);
    }
    
    console.log('✅ get_override_audit_trail works!');
    console.log(`📊 Audit Trail Records: ${auditTrail?.length || 0}`);

    // Test get_expiring_overrides
    const { data: expiringOverrides, error: expiringError } = await supabase.rpc('get_expiring_overrides', {
      days_ahead: 7
    });
    
    if (expiringError) {
      throw new Error(`get_expiring_overrides: ${expiringError.message}`);
    }
    
    console.log('✅ get_expiring_overrides works!');
    console.log(`📊 Expiring Overrides: ${expiringOverrides?.length || 0}`);
    
    results.subscriptionOverrides = { success: true, error: null, data: {
      active: activeOverrides?.length || 0,
      audit: auditTrail?.length || 0,
      expiring: expiringOverrides?.length || 0
    }};
  } catch (err: any) {
    console.error('❌ Subscription Override Error:', err);
    results.subscriptionOverrides = { success: false, error: err.message, data: null };
  }

  // Test 4: Materialized View Status
  console.log('\n4️⃣ Testing Materialized View...');
  try {
    const { data: cacheData, error: cacheError } = await supabase
      .from('user_stats_cache')
      .select('*')
      .limit(1);
    
    if (cacheError) {
      throw cacheError;
    }
    
    if (cacheData && cacheData.length > 0) {
      console.log('✅ Materialized View Accessible!');
      console.log('📊 Cache Data:', cacheData[0]);
      console.log(`📊 Cache Total Users: ${cacheData[0].total_users}`);
      console.log(`📊 Cache Last Updated: ${cacheData[0].last_updated}`);
      
      results.materializedView = { success: true, error: null, data: cacheData[0] };
    } else {
      throw new Error('Materialized view is empty');
    }
  } catch (err: any) {
    console.error('❌ Materialized View Error:', err);
    results.materializedView = { success: false, error: err.message, data: null };
  }

  // Summary
  console.log('\n📋 DIAGNOSTIC SUMMARY:');
  console.log('='.repeat(50));
  
  const allTests = [
    { name: 'User Statistics', result: results.userStatistics },
    { name: 'User Management', result: results.userManagement },
    { name: 'Subscription Overrides', result: results.subscriptionOverrides },
    { name: 'Materialized View', result: results.materializedView }
  ];
  
  let passedTests = 0;
  allTests.forEach(test => {
    const status = test.result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${test.name}`);
    if (test.result.success) passedTests++;
  });
  
  console.log('='.repeat(50));
  console.log(`📊 Overall Result: ${passedTests}/${allTests.length} tests passed`);
  
  if (passedTests === allTests.length) {
    console.log('🎉 ALL ISSUES RESOLVED! Admin dashboard should be fully functional.');
  } else {
    console.log('⚠️ Some issues remain. Check the errors above for details.');
  }
  
  return results;
};

/**
 * Quick test for user count accuracy
 */
export const checkUserCount = async () => {
  console.log('🔍 Quick User Count Check...');
  
  try {
    // Direct count from profiles table
    const { count: directCount, error: directError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });
    
    if (directError) {
      throw directError;
    }
    
    console.log(`📊 Direct Profile Count: ${directCount}`);
    
    // Count from materialized view
    const { data: cacheData, error: cacheError } = await supabase
      .from('user_stats_cache')
      .select('total_users')
      .limit(1);
    
    if (cacheError) {
      throw cacheError;
    }
    
    const cacheCount = cacheData?.[0]?.total_users || 0;
    console.log(`📊 Cache Count: ${cacheCount}`);
    
    // Count from function
    const { data: functionData, error: functionError } = await supabase.rpc('get_user_statistics');
    
    if (functionError) {
      throw functionError;
    }
    
    const functionCount = functionData?.total_users || 0;
    console.log(`📊 Function Count: ${functionCount}`);
    
    if (directCount === cacheCount && cacheCount === functionCount) {
      console.log('✅ All counts match! User statistics are accurate.');
    } else {
      console.log('⚠️ Count mismatch detected. May need to refresh materialized view.');
    }
    
    return { directCount, cacheCount, functionCount };
  } catch (err: any) {
    console.error('❌ User count check failed:', err);
    return null;
  }
};

/**
 * Force refresh the materialized view
 */
export const refreshUserStatsCache = async () => {
  console.log('🔄 Refreshing user statistics cache...');
  
  try {
    const { data, error } = await supabase.rpc('refresh_user_stats_cache');
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Cache refreshed successfully!');
    
    // Check the new count
    await checkUserCount();
    
    return true;
  } catch (err: any) {
    console.error('❌ Failed to refresh cache:', err);
    return false;
  }
};
