import React, { useState, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  Paper,
  useTheme,
  alpha,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface FigureCaption {
  id: string;
  figureNumber: string;
  title: string;
  description: string;
  methodology?: string;
  sampleSize?: string;
  statisticalTest?: string;
  significance?: string;
  abbreviations?: string;
  notes?: string;
  journalStyle: JournalStyle;
  figureType: FigureType;
  createdAt: Date;
}

type JournalStyle = 'apa' | 'nature' | 'science' | 'nejm' | 'jama' | 'bmj' | 'plos' | 'cell' | 'custom';
type FigureType = 'chart' | 'graph' | 'plot' | 'diagram' | 'image' | 'table' | 'flowchart' | 'other';

const FigureCaptionGenerator: React.FC = () => {
  const theme = useTheme();
  const [captions, setCaptions] = useState<FigureCaption[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCaption, setEditingCaption] = useState<FigureCaption | null>(null);
  const [autoGenerate, setAutoGenerate] = useState(false);
  const [newCaption, setNewCaption] = useState<Partial<FigureCaption>>({
    figureNumber: '1',
    title: '',
    description: '',
    journalStyle: 'apa',
    figureType: 'chart',
  });

  const journalStyleTemplates = {
    apa: {
      name: 'APA 7th Edition',
      format: 'Figure {number}. {title}. {description}',
      example: 'Figure 1. Mean reaction times by condition. Error bars represent standard error of the mean.',
    },
    nature: {
      name: 'Nature',
      format: 'Figure {number} | {title}. {description}',
      example: 'Figure 1 | Experimental design and results. a, Schematic of experimental setup. b, Results showing significant differences.',
    },
    science: {
      name: 'Science',
      format: 'Fig. {number}. {title}. {description}',
      example: 'Fig. 1. Experimental results. (A) Control condition. (B) Treatment condition showing significant effect.',
    },
    nejm: {
      name: 'New England Journal of Medicine',
      format: 'Figure {number}. {title}\n{description}',
      example: 'Figure 1. Patient Outcomes by Treatment Group\nKaplan-Meier curves showing survival probability over time.',
    },
    jama: {
      name: 'JAMA',
      format: 'Figure {number}. {title}\n{description}',
      example: 'Figure 1. Study Flow Diagram\nFlow of participants through each stage of the randomized trial.',
    },
    bmj: {
      name: 'BMJ',
      format: 'Figure {number} {title}. {description}',
      example: 'Figure 1 Risk of bias assessment. Summary of risk of bias for included studies.',
    },
    plos: {
      name: 'PLOS ONE',
      format: 'Fig {number}. {title}.\n{description}',
      example: 'Fig 1. Experimental design and main results.\nA) Schematic representation. B) Quantitative analysis.',
    },
    cell: {
      name: 'Cell',
      format: 'Figure {number}. {title}\n{description}',
      example: 'Figure 1. Molecular Mechanism of Action\n(A) Western blot analysis. (B) Quantification of protein levels.',
    },
    custom: {
      name: 'Custom Format',
      format: 'Figure {number}: {title} - {description}',
      example: 'Figure 1: Study Results - Comparison of treatment groups showing statistical significance.',
    },
  };

  const figureTypeTemplates = {
    chart: 'Bar chart showing {description} with error bars representing {statistical measure}.',
    graph: 'Line graph depicting {description} over {time period/condition}.',
    plot: 'Scatter plot illustrating the relationship between {variable 1} and {variable 2}.',
    diagram: 'Schematic diagram showing {process/structure/relationship}.',
    image: 'Representative image showing {subject/condition} with {scale/magnification}.',
    table: 'Summary table presenting {data type} organized by {grouping variable}.',
    flowchart: 'Flow diagram illustrating {process/methodology/decision tree}.',
    other: 'Visual representation of {description}.',
  };

  const generateCaption = useCallback((caption: Partial<FigureCaption>): string => {
    const template = journalStyleTemplates[caption.journalStyle || 'apa'];
    let formatted = template.format
      .replace('{number}', caption.figureNumber || '1')
      .replace('{title}', caption.title || 'Title')
      .replace('{description}', caption.description || 'Description');

    // Add methodology if provided
    if (caption.methodology) {
      formatted += ` Methodology: ${caption.methodology}.`;
    }

    // Add sample size if provided
    if (caption.sampleSize) {
      formatted += ` n = ${caption.sampleSize}.`;
    }

    // Add statistical information
    if (caption.statisticalTest) {
      formatted += ` Statistical analysis: ${caption.statisticalTest}.`;
    }

    if (caption.significance) {
      formatted += ` ${caption.significance}.`;
    }

    // Add abbreviations if provided
    if (caption.abbreviations) {
      formatted += ` Abbreviations: ${caption.abbreviations}.`;
    }

    // Add notes if provided
    if (caption.notes) {
      formatted += ` ${caption.notes}`;
    }

    return formatted;
  }, []);

  const autoGenerateDescription = (figureType: FigureType) => {
    const template = figureTypeTemplates[figureType];
    setNewCaption(prev => ({
      ...prev,
      description: template,
    }));
  };

  const addCaption = () => {
    if (newCaption.title && newCaption.description) {
      const caption: FigureCaption = {
        id: Date.now().toString(),
        figureNumber: newCaption.figureNumber || '1',
        title: newCaption.title || '',
        description: newCaption.description || '',
        methodology: newCaption.methodology,
        sampleSize: newCaption.sampleSize,
        statisticalTest: newCaption.statisticalTest,
        significance: newCaption.significance,
        abbreviations: newCaption.abbreviations,
        notes: newCaption.notes,
        journalStyle: newCaption.journalStyle || 'apa',
        figureType: newCaption.figureType || 'chart',
        createdAt: new Date(),
      };
      
      if (editingCaption) {
        setCaptions(prev => prev.map(c => c.id === editingCaption.id ? caption : c));
        setEditingCaption(null);
      } else {
        setCaptions(prev => [...prev, caption]);
      }
      
      setNewCaption({
        figureNumber: (parseInt(newCaption.figureNumber || '1') + 1).toString(),
        title: '',
        description: '',
        journalStyle: newCaption.journalStyle || 'apa',
        figureType: 'chart',
      });
      setDialogOpen(false);
    }
  };

  const deleteCaption = (id: string) => {
    setCaptions(prev => prev.filter(c => c.id !== id));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const exportCaptions = () => {
    const captionText = captions
      .map(caption => generateCaption(caption))
      .join('\n\n');
    
    const blob = new Blob([captionText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'figure_captions.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
            Figure Caption Generator
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create professional figure captions formatted for different academic journals
          </Typography>
        </Box>

        {/* Quick Actions */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setDialogOpen(true)}
                  >
                    New Caption
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={exportCaptions}
                    disabled={captions.length === 0}
                  >
                    Export All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Caption Statistics
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Chip label={`${captions.length} Captions`} color="primary" variant="outlined" />
                  <Chip 
                    label={`${new Set(captions.map(c => c.journalStyle)).size} Journal Styles`} 
                    color="secondary" 
                    variant="outlined" 
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Journal Style Guide */}
        <Card elevation={2} sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <HelpIcon color="primary" />
              Journal Style Guide
            </Typography>
            <Grid container spacing={2}>
              {Object.entries(journalStyleTemplates).map(([key, style]) => (
                <Grid item xs={12} md={6} lg={4} key={key}>
                  <Paper 
                    elevation={1} 
                    sx={{ 
                      p: 2, 
                      height: '100%',
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      {style.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                      Format: {style.format}
                    </Typography>
                    <Typography variant="body2" sx={{ fontStyle: 'italic', fontSize: '0.875rem' }}>
                      {style.example}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Captions List */}
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Generated Captions
            </Typography>
            
            {captions.length === 0 ? (
              <Alert severity="info">
                No captions created yet. Click "New Caption" to get started.
              </Alert>
            ) : (
              <List>
                {captions.map((caption, index) => (
                  <React.Fragment key={caption.id}>
                    <ListItem alignItems="flex-start">
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              Figure {caption.figureNumber}: {caption.title}
                            </Typography>
                            <Chip size="small" label={journalStyleTemplates[caption.journalStyle].name} variant="outlined" />
                            <Chip size="small" label={caption.figureType} color="secondary" variant="outlined" />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2,
                                bgcolor: alpha(theme.palette.primary.main, 0.05),
                                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                borderRadius: 1,
                                mb: 1,
                              }}
                            >
                              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                                {generateCaption(caption)}
                              </Typography>
                            </Paper>
                            <Typography variant="caption" color="text.secondary">
                              Created: {caption.createdAt.toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Copy caption">
                            <IconButton
                              size="small"
                              onClick={() => copyToClipboard(generateCaption(caption))}
                            >
                              <CopyIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit caption">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setEditingCaption(caption);
                                setNewCaption(caption);
                                setDialogOpen(true);
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete caption">
                            <IconButton
                              size="small"
                              onClick={() => deleteCaption(caption.id)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < captions.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>

        {/* Add/Edit Caption Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingCaption ? 'Edit Figure Caption' : 'Create New Figure Caption'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Figure Number"
                  value={newCaption.figureNumber || ''}
                  onChange={(e) => setNewCaption(prev => ({ ...prev, figureNumber: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Journal Style</InputLabel>
                  <Select
                    value={newCaption.journalStyle || 'apa'}
                    label="Journal Style"
                    onChange={(e) => setNewCaption(prev => ({ ...prev, journalStyle: e.target.value as JournalStyle }))}
                  >
                    {Object.entries(journalStyleTemplates).map(([key, style]) => (
                      <MenuItem key={key} value={key}>{style.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Figure Type</InputLabel>
                  <Select
                    value={newCaption.figureType || 'chart'}
                    label="Figure Type"
                    onChange={(e) => {
                      const figureType = e.target.value as FigureType;
                      setNewCaption(prev => ({ ...prev, figureType }));
                      if (autoGenerate) {
                        autoGenerateDescription(figureType);
                      }
                    }}
                  >
                    <MenuItem value="chart">Chart</MenuItem>
                    <MenuItem value="graph">Graph</MenuItem>
                    <MenuItem value="plot">Plot</MenuItem>
                    <MenuItem value="diagram">Diagram</MenuItem>
                    <MenuItem value="image">Image</MenuItem>
                    <MenuItem value="table">Table</MenuItem>
                    <MenuItem value="flowchart">Flowchart</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Figure Title *"
                  value={newCaption.title || ''}
                  onChange={(e) => setNewCaption(prev => ({ ...prev, title: e.target.value }))}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={autoGenerate}
                        onChange={(e) => setAutoGenerate(e.target.checked)}
                      />
                    }
                    label="Auto-generate description template"
                  />
                  {autoGenerate && (
                    <Button
                      size="small"
                      startIcon={<AutoAwesomeIcon />}
                      onClick={() => autoGenerateDescription(newCaption.figureType || 'chart')}
                    >
                      Generate
                    </Button>
                  )}
                </Box>
                <TextField
                  fullWidth
                  label="Description *"
                  value={newCaption.description || ''}
                  onChange={(e) => setNewCaption(prev => ({ ...prev, description: e.target.value }))}
                  multiline
                  rows={3}
                  required
                />
              </Grid>
              
              {/* Advanced Options */}
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">Advanced Options</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Methodology"
                          value={newCaption.methodology || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, methodology: e.target.value }))}
                          placeholder="e.g., Two-way ANOVA"
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Sample Size"
                          value={newCaption.sampleSize || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, sampleSize: e.target.value }))}
                          placeholder="e.g., 150"
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Statistical Test"
                          value={newCaption.statisticalTest || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, statisticalTest: e.target.value }))}
                          placeholder="e.g., t-test, ANOVA"
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Significance"
                          value={newCaption.significance || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, significance: e.target.value }))}
                          placeholder="e.g., *p < 0.05, **p < 0.01"
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Abbreviations"
                          value={newCaption.abbreviations || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, abbreviations: e.target.value }))}
                          placeholder="e.g., SEM, standard error of the mean"
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Additional Notes"
                          value={newCaption.notes || ''}
                          onChange={(e) => setNewCaption(prev => ({ ...prev, notes: e.target.value }))}
                          multiline
                          rows={2}
                          placeholder="Any additional information..."
                        />
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Grid>
              
              {/* Preview */}
              {newCaption.title && newCaption.description && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Preview:
                  </Typography>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                      borderRadius: 1,
                    }}
                  >
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                      {generateCaption(newCaption)}
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => {
              setDialogOpen(false);
              setEditingCaption(null);
              setNewCaption({
                figureNumber: '1',
                title: '',
                description: '',
                journalStyle: 'apa',
                figureType: 'chart',
              });
            }}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={addCaption}
              disabled={!newCaption.title || !newCaption.description}
            >
              {editingCaption ? 'Update' : 'Create'} Caption
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </PublicationReadyGate>
  );
};

export default FigureCaptionGenerator;