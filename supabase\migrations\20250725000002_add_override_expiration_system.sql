-- Subscription Override Expiration System Migration
-- This migration adds automatic expiration handling for subscription overrides
-- Date: 2025-07-25

-- Enhanced function to get effective user tier (considering active overrides)
CREATE OR REPLACE FUNCTION public.get_effective_user_tier(target_user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  base_tier TEXT;
  override_tier TEXT;
BEGIN
  -- Get base tier from profiles
  SELECT COALESCE(accounttype, 'standard') INTO base_tier
  FROM public.profiles
  WHERE id = target_user_id;
  
  -- Check for active override
  SELECT so.override_tier INTO override_tier
  FROM public.subscription_overrides so
  WHERE so.user_id = target_user_id 
    AND so.is_active = true 
    AND so.end_date > now()
  ORDER BY so.created_at DESC
  LIMIT 1;
  
  -- Return override tier if active, otherwise base tier
  RETURN COALESCE(override_tier, base_tier, 'standard');
END;
$$;

-- Function to check if user has specific tier access (considering overrides)
CREATE OR REPLACE FUNCTION public.user_has_tier_access(
  target_user_id UUID,
  required_tier TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  effective_tier TEXT;
  tier_hierarchy INTEGER;
  required_hierarchy INTEGER;
BEGIN
  -- Get effective tier (base tier or override)
  effective_tier := public.get_effective_user_tier(target_user_id);
  
  -- Define tier hierarchy (higher number = higher tier)
  tier_hierarchy := CASE effective_tier
    WHEN 'guest' THEN 0
    WHEN 'standard' THEN 1
    WHEN 'edu' THEN 2
    WHEN 'edu_pro' THEN 3
    WHEN 'pro' THEN 3
    ELSE 0
  END;
  
  required_hierarchy := CASE required_tier
    WHEN 'guest' THEN 0
    WHEN 'standard' THEN 1
    WHEN 'edu' THEN 2
    WHEN 'edu_pro' THEN 3
    WHEN 'pro' THEN 3
    ELSE 0
  END;
  
  RETURN tier_hierarchy >= required_hierarchy;
END;
$$;

-- Enhanced expiration function with logging
CREATE OR REPLACE FUNCTION public.expire_subscription_overrides_with_logging()
RETURNS TABLE (
  expired_count INTEGER,
  expired_user_ids UUID[],
  execution_time TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  expired_count_var INTEGER;
  expired_users UUID[];
BEGIN
  -- Get list of users whose overrides will expire
  SELECT ARRAY_AGG(user_id) INTO expired_users
  FROM public.subscription_overrides 
  WHERE is_active = true AND end_date <= now();
  
  -- Expire the overrides
  UPDATE public.subscription_overrides 
  SET is_active = false, updated_at = now()
  WHERE is_active = true AND end_date <= now();
  
  GET DIAGNOSTICS expired_count_var = ROW_COUNT;
  
  -- Return results
  RETURN QUERY SELECT 
    expired_count_var,
    COALESCE(expired_users, ARRAY[]::UUID[]),
    now();
END;
$$;

-- Function to get users whose overrides will expire soon
CREATE OR REPLACE FUNCTION public.get_expiring_overrides(days_ahead INTEGER DEFAULT 7)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  user_email TEXT,
  user_full_name TEXT,
  override_tier TEXT,
  end_date TIMESTAMP WITH TIME ZONE,
  days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    so.id,
    so.user_id,
    u.email as user_email,
    p.full_name as user_full_name,
    so.override_tier,
    so.end_date,
    EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
  FROM public.subscription_overrides so
  JOIN auth.users u ON so.user_id = u.id
  LEFT JOIN public.profiles p ON so.user_id = p.id
  WHERE so.is_active = true 
    AND so.end_date > now()
    AND so.end_date <= now() + (days_ahead || ' days')::INTERVAL
  ORDER BY so.end_date ASC;
END;
$$;

-- Function to extend an existing override
CREATE OR REPLACE FUNCTION public.extend_subscription_override(
  override_id UUID,
  additional_months INTEGER,
  reason_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_end_date TIMESTAMP WITH TIME ZONE;
  new_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Only allow admins to call this function
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate additional months
  IF additional_months < 1 OR additional_months > 12 THEN
    RAISE EXCEPTION 'Additional months must be between 1 and 12';
  END IF;
  
  -- Get current end date
  SELECT end_date INTO current_end_date
  FROM public.subscription_overrides
  WHERE id = override_id AND is_active = true;
  
  IF current_end_date IS NULL THEN
    RAISE EXCEPTION 'Override not found or not active';
  END IF;
  
  -- Calculate new end date
  new_end_date := current_end_date + (additional_months || ' months')::INTERVAL;
  
  -- Update the override
  UPDATE public.subscription_overrides 
  SET 
    end_date = new_end_date,
    reason = CASE 
      WHEN reason_param IS NOT NULL THEN 
        COALESCE(reason, '') || ' | Extended: ' || reason_param
      ELSE 
        COALESCE(reason, '') || ' | Extended by ' || additional_months || ' months'
    END,
    updated_at = now()
  WHERE id = override_id;
  
  RETURN FOUND;
END;
$$;

-- Create a view for easy override status checking
CREATE OR REPLACE VIEW public.user_override_status AS
SELECT 
  u.id as user_id,
  u.email,
  p.full_name,
  p.accounttype as base_tier,
  so.override_tier,
  so.end_date as override_end_date,
  CASE 
    WHEN so.id IS NOT NULL THEN so.override_tier
    ELSE p.accounttype
  END as effective_tier,
  so.is_active as has_active_override,
  EXTRACT(DAY FROM (so.end_date - now()))::INTEGER as days_remaining
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
LEFT JOIN public.subscription_overrides so ON u.id = so.user_id 
  AND so.is_active = true 
  AND so.end_date > now();

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_effective_user_tier(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.user_has_tier_access(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.expire_subscription_overrides_with_logging() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_expiring_overrides(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.extend_subscription_override(UUID, INTEGER, TEXT) TO authenticated;
GRANT SELECT ON public.user_override_status TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION public.get_effective_user_tier(UUID) IS 'Gets the effective tier for a user, considering active overrides';
COMMENT ON FUNCTION public.user_has_tier_access(UUID, TEXT) IS 'Checks if a user has access to a specific tier level, considering overrides';
COMMENT ON FUNCTION public.expire_subscription_overrides_with_logging() IS 'Expires overrides with detailed logging for monitoring';
COMMENT ON FUNCTION public.get_expiring_overrides(INTEGER) IS 'Gets overrides that will expire within specified days. Admin only.';
COMMENT ON FUNCTION public.extend_subscription_override(UUID, INTEGER, TEXT) IS 'Extends an existing override by additional months. Admin only.';
COMMENT ON VIEW public.user_override_status IS 'Convenient view showing each user''s current override status and effective tier';
