# PWA and Service Worker Configuration
RewriteEngine On

# Enable HTTPS redirect (recommended for PWA)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Service Worker files - serve with proper headers and no caching
<FilesMatch "\.(js)$">
    <IfModule mod_headers.c>
        # Service Worker files should not be cached
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
        # Allow service worker to control all pages
        Header set Service-Worker-Allowed "/"
    </IfModule>
</FilesMatch>

# PWA Manifest file
<FilesMatch "manifest\.json$">
    <IfModule mod_headers.c>
        Header set Content-Type "application/manifest+json"
        Header set Cache-Control "public, max-age=86400"
    </IfModule>
</FilesMatch>

# Static assets caching (CSS, JS, images)
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "public, max-age=31536000, immutable"
    </IfModule>
</FilesMatch>

# HTML files - no caching to ensure fresh content
<FilesMatch "\.html$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </IfModule>
</FilesMatch>

# Security headers for PWA
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy (adjusted for YouTube embeds and PWA functionality)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; media-src 'self' https:; frame-src 'self' https://www.youtube.com https://youtube.com; object-src 'none'; base-uri 'self'; form-action 'self';"
</IfModule>

# Social Media Crawler Detection and Meta Tag Handling
# Detect social media crawlers and serve appropriate meta tags

# Handle knowledge base tutorials specifically for crawlers (must come first)
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/knowledge-base/(.+)$
RewriteRule ^app/knowledge-base/(.+)$ /social-meta.php?tutorial=$1 [L,QSA]

# Handle main knowledge-base page for crawlers
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/knowledge-base/?$
RewriteRule ^app/knowledge-base/?$ /social-meta.php?path=knowledge-base [L,QSA]

# Handle other app routes for crawlers
RewriteCond %{HTTP_USER_AGENT} (facebookexternalhit|Twitterbot|LinkedInBot|WhatsApp|Slackbot|TelegramBot) [NC]
RewriteCond %{REQUEST_URI} ^/app/(.*)$
RewriteRule ^app/(.*)$ /social-meta.php?path=$1 [L,QSA]

# SPA Routing - Redirect all requests to index.html for React Router
<IfModule mod_rewrite.c>
    # Handle Angular and React Router
    # Skip real files and directories
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # Skip API endpoints and service worker
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteCond %{REQUEST_URI} !^/sw\.js
    RewriteCond %{REQUEST_URI} !^/workbox-
    RewriteCond %{REQUEST_URI} !^/manifest\.json
    RewriteCond %{REQUEST_URI} !^/robots\.txt
    RewriteCond %{REQUEST_URI} !^/sitemap\.xml
    RewriteCond %{REQUEST_URI} !^/social-meta\.php
    
    # Skip asset files
    RewriteCond %{REQUEST_URI} !\.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot|json|xml|txt)$
    
    # Redirect to index.html
    RewriteRule . /index.html [L]
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the "ea-php81" package as the default "PHP" programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php81 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit