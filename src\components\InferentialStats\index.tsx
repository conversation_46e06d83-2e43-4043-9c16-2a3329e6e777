import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  useTheme,
  alpha
} from '@mui/material';
import {
  Science as ScienceIcon,
  BarChart as BarChartIcon,
  Balance as BalanceIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import SocialShareWidget from '../UI/SocialShareWidget';
import { TabPanel } from '../UI';
import TTests from './TTests';
import ANOVA from './ANOVA';
import NonParametricTests from './NonParametricTests';
import Assumption<PERSON>hecker from './AssumptionChecker';



interface InferentialStatsProps {
  initialTab?: string;
}

const InferentialStats: React.FC<InferentialStatsProps> = ({ initialTab = '' }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [showCards, setShowCards] = useState<boolean>(true);
  
  // Map tab names to indices
  const tabNameToIndex: Record<string, number> = {
    'ttest': 0,
    'anova': 1,
    'nonparametric': 2,
    'assumptions': 3
  };
  
  // Set initial tab based on URL or prop
  useEffect(() => {
    if (initialTab && tabNameToIndex[initialTab] !== undefined) {
      setActiveTab(tabNameToIndex[initialTab]);
      // Hide cards if a specific test is selected via initialTab
      if (initialTab !== '') {
        setShowCards(false);
      }
    }
  }, [initialTab]);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // Show cards when switching between main tabs
    setShowCards(true);
  };
  
  // Function to hide cards when a specific test is selected
  const handleTestSelection = () => {
    setShowCards(false);
  };
  
  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible={true}
      />
      {activeTab === 0 && showCards && (
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} lg={3}>
              <Card 
                elevation={1}
                sx={{ 
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  borderLeft: `4px solid ${theme.palette.primary.main}`,
                  height: '100%',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: theme.shadows[3], cursor: 'pointer' }
                }}
                onClick={() => {
                  handleTestSelection();
                }}
              >
                <CardActionArea sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="subtitle1" color="primary" gutterBottom sx={{ fontWeight: 'medium' }}>
                      t-Tests
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Compare means between groups or against a known value. Use for comparing:
                    </Typography>
                    <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                      <li>One group against a known value</li>
                      <li>Two independent groups</li>
                      <li>Paired measurements (before/after)</li>
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} lg={3}>
              <Card 
                elevation={1}
                sx={{ 
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  height: '100%',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: theme.shadows[3] }
                }}
              >
                <CardActionArea 
                  sx={{ height: '100%', p: 1 }}
                  onClick={() => setActiveTab(1)}
                >
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                      ANOVA Tests
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Compare means across multiple groups or factors. Use for:
                    </Typography>
                    <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                      <li>One-way ANOVA (3+ groups)</li>
                      <li>Two-way ANOVA (2 factors)</li>
                      <li>Repeated measures designs</li>
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} lg={3}>
              <Card 
                elevation={1}
                sx={{ 
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  height: '100%',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: theme.shadows[3] }
                }}
              >
                <CardActionArea 
                  sx={{ height: '100%', p: 1 }}
                  onClick={() => setActiveTab(2)}
                >
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                      Non-parametric Tests
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Alternatives when assumptions are violated:
                    </Typography>
                    <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                      <li>Mann-Whitney U (vs. independent t-test)</li>
                      <li>Wilcoxon (vs. paired t-test)</li>
                      <li>Kruskal-Wallis (vs. ANOVA)</li>
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} lg={3}>
              <Card 
                elevation={1}
                sx={{ 
                  backgroundColor: alpha(theme.palette.background.default, 0.5),
                  height: '100%',
                  transition: 'all 0.2s',
                  '&:hover': { boxShadow: theme.shadows[3] }
                }}
              >
                <CardActionArea 
                  sx={{ height: '100%', p: 1 }}
                  onClick={() => setActiveTab(3)}
                >
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                      Assumption Checker
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Verify if your data meets test requirements:
                    </Typography>
                    <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                      <li>Normality tests</li>
                      <li>Homogeneity of variance</li>
                      <li>Sphericity & other assumptions</li>
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
            
          </Grid>
        </Box>
      )}
      
      <Paper 
        elevation={1} 
        sx={{ 
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderRadius: 2
        }}
      >
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          aria-label="inferential statistics tabs"
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Tab 
            icon={<TrendingUpIcon />} 
            iconPosition="start" 
            label="t-Tests" 
          />
          <Tab 
            icon={<BarChartIcon />} 
            iconPosition="start" 
            label="ANOVA" 
          />
          <Tab 
            icon={<BalanceIcon />} 
            iconPosition="start" 
            label="Non-parametric Tests" 
          />
          <Tab 
            icon={<CheckCircleOutlineIcon />} 
            iconPosition="start" 
            label="Assumption Checker" 
          />
        </Tabs>
        
        <Box sx={{ flex: 1, overflow: 'auto', p: 3, display: 'flex', flexDirection: 'column', height: '100%' }}>
          {activeTab === 0 && (
            <Box sx={{ flex: 1, height: '100%' }}>
              <TTests />
            </Box>
          )}
          
          {activeTab === 1 && (
            <Box sx={{ flex: 1, height: '100%' }}>
              <ANOVA />
            </Box>
          )}
          
          {activeTab === 2 && (
            <Box sx={{ flex: 1, height: '100%' }}>
              <NonParametricTests />
            </Box>
          )}
          
          {activeTab === 3 && (
            <Box sx={{ flex: 1, height: '100%' }}>
              <AssumptionChecker />
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default InferentialStats;
