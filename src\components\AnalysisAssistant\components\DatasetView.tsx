import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Grid,
  Box,
  Card,
  CardContent,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  Alert,
  AlertTitle,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  <PERSON><PERSON>hart as BarChartIcon,
  <PERSON><PERSON>hart as TableChartIcon,
  Help as HelpIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { ViewMode } from '../utils/types';

interface DatasetViewProps {
  changeViewMode: (mode: ViewMode) => void;
  datasetAnalysis: any;
  variableInsights: any;
  smartRecommendations: any[];
}

/**
 * Dataset view component for the AnalysisAssistant
 * Displays dataset information, variable insights, and smart recommendations
 */
const DatasetView: React.FC<DatasetViewProps> = ({
  changeViewMode,
  datasetAnalysis,
  variableInsights,
  smartRecommendations
}) => {
  if (!datasetAnalysis) {
    return (
      <Paper elevation={0} sx={{ p: 3, height: '100%' }}>
        <Alert severity="info">
          <AlertTitle>No Dataset Loaded</AlertTitle>
          Please load a dataset to view its analysis.
        </Alert>
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Button variant="contained" color="primary" onClick={() => changeViewMode('welcome')}>
            Back to Welcome
          </Button>
        </Box>
      </Paper>
    );
  }

  const {
    numericVariables = [],
    categoricalVariables = [],
    dateVariables = [],
    missingDataPatterns = [],
    outliers = [],
    distributionAssessments = []
  } = variableInsights || {};

  const dataQualityIssues = [
    ...missingDataPatterns.map((pattern: any) => ({
      type: 'missing',
      variable: pattern.variable,
      details: `${pattern.missingPercentage.toFixed(1)}% missing values`
    })),
    ...outliers.map((outlier: any) => ({
      type: 'outlier',
      variable: outlier.variable,
      details: `${outlier.outlierCount} outliers detected`
    })),
    ...distributionAssessments
      .filter((assessment: any) => assessment.skewness > 1 || assessment.skewness < -1)
      .map((assessment: any) => ({
        type: 'skewed',
        variable: assessment.variable,
        details: `Skewed distribution (${assessment.skewness.toFixed(2)})`
      }))
  ];

  return (
    <Paper elevation={0} sx={{ p: 3, height: '100%' }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Dataset Overview
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" paragraph>
          Explore your dataset and get insights for analysis
        </Typography>

        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Dataset Summary
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary="Observations"
                      secondary={datasetAnalysis.rowCount || 'N/A'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Variables"
                      secondary={datasetAnalysis.columnCount || 'N/A'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Numeric Variables"
                      secondary={numericVariables.length || 'N/A'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Categorical Variables"
                      secondary={categoricalVariables.length || 'N/A'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Date Variables"
                      secondary={dateVariables.length || 'N/A'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Missing Data"
                      secondary={
                        datasetAnalysis.missingDataPercentage
                          ? `${datasetAnalysis.missingDataPercentage.toFixed(1)}%`
                          : 'N/A'
                      }
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Data Quality Issues
                  <Tooltip title="These issues may affect your analysis results">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <HelpIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>

                {dataQualityIssues.length === 0 ? (
                  <Alert severity="success" sx={{ mt: 1 }}>
                    <AlertTitle>Good News!</AlertTitle>
                    No significant data quality issues detected.
                  </Alert>
                ) : (
                  <List dense>
                    {dataQualityIssues.slice(0, 5).map((issue, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {issue.type === 'missing' && <WarningIcon color="warning" fontSize="small" sx={{ mr: 1 }} />}
                              {issue.type === 'outlier' && <InfoIcon color="info" fontSize="small" sx={{ mr: 1 }} />}
                              {issue.type === 'skewed' && <InfoIcon color="info" fontSize="small" sx={{ mr: 1 }} />}
                              {issue.variable}
                            </Box>
                          }
                          secondary={issue.details}
                        />
                      </ListItem>
                    ))}
                    {dataQualityIssues.length > 5 && (
                      <ListItem>
                        <ListItemText
                          primary={`${dataQualityIssues.length - 5} more issues...`}
                          secondary="Click 'Data Quality Assessment' to see all"
                        />
                      </ListItem>
                    )}
                  </List>
                )}

                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    size="small"
                    startIcon={<TableChartIcon />}
                    onClick={() => changeViewMode('dataQuality')}
                  >
                    Data Quality Assessment
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          Variable Insights
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Numeric Variables
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {numericVariables.length > 0 ? (
                numericVariables.map((variable: any, index: number) => (
                  <Chip
                    key={index}
                    label={variable.name}
                    color="primary"
                    variant="outlined"
                    onClick={() => {}}
                  />
                ))
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No numeric variables detected
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Categorical Variables
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {categoricalVariables.length > 0 ? (
                categoricalVariables.map((variable: any, index: number) => (
                  <Chip
                    key={index}
                    label={variable.name}
                    color="secondary"
                    variant="outlined"
                    onClick={() => {}}
                  />
                ))
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No categorical variables detected
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Date Variables
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {dateVariables.length > 0 ? (
                dateVariables.map((variable: any, index: number) => (
                  <Chip
                    key={index}
                    label={variable.name}
                    color="info"
                    variant="outlined"
                    onClick={() => {}}
                  />
                ))
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No date variables detected
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Divider sx={{ my: 4 }} />

      <Box>
        <Typography variant="h5" gutterBottom>
          Smart Recommendations
        </Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          Based on your dataset, here are some analyses you might want to consider:
        </Typography>

        <Grid container spacing={2}>
          {smartRecommendations && smartRecommendations.length > 0 ? (
            smartRecommendations.slice(0, 3).map((recommendation, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BarChartIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="h3">
                        {recommendation.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      {recommendation.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Chip
                        icon={<CheckCircleIcon />}
                        label={`${Math.round(recommendation.confidence * 100)}% match`}
                        color="success"
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))
          ) : (
            <Grid item xs={12}>
              <Alert severity="info">
                No recommendations available yet. Try asking a specific question to get personalized suggestions.
              </Alert>
            </Grid>
          )}
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => changeViewMode('query')}
          >
            Ask a Question
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default DatasetView;