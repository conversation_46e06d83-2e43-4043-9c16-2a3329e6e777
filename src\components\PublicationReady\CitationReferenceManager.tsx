import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Checkbox,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Divider,
  Paper,
  useTheme,
  alpha,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  ContentCopy as CopyIcon,
  Edit as EditIcon,
  School as ScholarIcon,
  Science as PubMedIcon,
  ExpandMore as ExpandMoreIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';

interface Reference {
  id: string;
  title: string;
  authors: string;
  journal: string;
  year: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  pmid?: string;
  url?: string;
  type: 'journal' | 'book' | 'conference' | 'website' | 'other';
}

type CitationStyle = 'apa' | 'ama' | 'vancouver' | 'harvard' | 'chicago' | 'mla';
type ExportFormat = 'txt' | 'ris' | 'bibtex' | 'endnote' | 'json' | 'csv';

const CitationReferenceManager: React.FC = () => {
  const theme = useTheme();
  const [references, setReferences] = useState<Reference[]>([]);
  const [citationStyle, setCitationStyle] = useState<CitationStyle>('apa');
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [bulkImportOpen, setBulkImportOpen] = useState(false);
  const [editingReference, setEditingReference] = useState<Reference | null>(null);
  const [searchTab, setSearchTab] = useState(0); // 0: PubMed, 1: Google Scholar
  const [bulkText, setBulkText] = useState('');
  const [searchResults, setSearchResults] = useState<Reference[]>([]);
  const [selectedResults, setSelectedResults] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalResults, setTotalResults] = useState(0);
  const [resultsPerPage] = useState(20);
  const [exportFormat, setExportFormat] = useState<ExportFormat>('txt');
  const [newReference, setNewReference] = useState<Partial<Reference>>({
    type: 'journal',
    title: '',
    authors: '',
    journal: '',
    year: '',
  });

  const formatCitation = useCallback((ref: Reference, style: CitationStyle): string => {
    const { authors, year, title, journal, volume, issue, pages, doi } = ref;
    
    switch (style) {
      case 'apa':
        return `${authors} (${year}). ${title}. ${journal}${volume ? `, ${volume}` : ''}${issue ? `(${issue})` : ''}${pages ? `, ${pages}` : ''}${doi ? `. https://doi.org/${doi}` : ''}.`;
      
      case 'ama':
        return `${authors}. ${title}. ${journal}. ${year}${volume ? `;${volume}` : ''}${issue ? `(${issue})` : ''}${pages ? `:${pages}` : ''}${doi ? `. doi:${doi}` : ''}.`;
      
      case 'vancouver':
        return `${authors}. ${title}. ${journal}. ${year}${volume ? `;${volume}` : ''}${issue ? `(${issue})` : ''}${pages ? `:${pages}` : ''}${doi ? `. Available from: https://doi.org/${doi}` : ''}.`;
      
      case 'harvard':
        return `${authors}, ${year}. ${title}. ${journal}${volume ? `, ${volume}` : ''}${issue ? `(${issue})` : ''}${pages ? `, pp. ${pages}` : ''}${doi ? `. Available at: https://doi.org/${doi}` : ''}.`;
      
      case 'chicago':
        return `${authors}. "${title}." ${journal}${volume ? ` ${volume}` : ''}${issue ? `, no. ${issue}` : ''} (${year})${pages ? `: ${pages}` : ''}${doi ? `. https://doi.org/${doi}` : ''}.`;
      
      case 'mla':
        return `${authors}. "${title}." ${journal}${volume ? `, vol. ${volume}` : ''}${issue ? `, no. ${issue}` : ''}, ${year}${pages ? `, pp. ${pages}` : ''}${doi ? `. Web. doi:${doi}` : ''}.`;
      
      default:
        return `${authors} (${year}). ${title}. ${journal}.`;
    }
  }, []);

  // Reset pagination when search tab changes
  useEffect(() => {
    setCurrentPage(1);
    setSearchResults([]);
    setSelectedResults(new Set());
    setTotalResults(0);
  }, [searchTab]);

  const searchPubMed = async (query: string, page: number = 1) => {
    setIsSearching(true);
    
    // Ensure page is a valid number
    const validPage = isNaN(page) || page < 1 ? 1 : Math.floor(page);
    
    if (validPage === 1) {
      setSearchResults([]);
      setSelectedResults(new Set());
    }
    try {
      const retstart = (validPage - 1) * resultsPerPage;
      // PubMed E-utilities API search with pagination - using CORS proxy
      const searchUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(`https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=${encodeURIComponent(query)}&retmax=${resultsPerPage}&retstart=${retstart}&retmode=json`)}`;
      
      const searchResponse = await fetch(searchUrl);
      const proxyData = await searchResponse.json();
      const searchData = JSON.parse(proxyData.contents);
      
      // Set total results count for pagination
      if (validPage === 1 && searchData.esearchresult?.count) {
        setTotalResults(parseInt(searchData.esearchresult.count));
      }
      
      if (searchData.esearchresult?.idlist?.length > 0) {
        const ids = searchData.esearchresult.idlist.join(',');
        const summaryUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(`https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id=${ids}&retmode=json`)}`;
        
        const summaryResponse = await fetch(summaryUrl);
        const summaryProxyData = await summaryResponse.json();
        const summaryData = JSON.parse(summaryProxyData.contents);
        
        const results: Reference[] = [];
        
        for (const id of searchData.esearchresult.idlist) {
          const article = summaryData.result[id];
          if (article) {
            const authors = article.authors?.map((author: any) => author.name).join(', ') || 'Unknown';
            const year = article.pubdate ? article.pubdate.split(' ')[0] : 'Unknown';
            
            results.push({
              id: `pubmed_${id}`,
              title: article.title || 'Unknown Title',
              authors: authors,
              journal: article.fulljournalname || article.source || 'Unknown Journal',
              year: year,
              volume: article.volume || '',
              issue: article.issue || '',
              pages: article.pages || '',
              doi: article.elocationid?.startsWith('doi:') ? article.elocationid.replace('doi:', '') : '',
              pmid: id,
              type: 'journal',
            });
          }
        }
        
        // Append results for pagination or replace for new search
        if (validPage === 1) {
          setSearchResults(results);
        } else {
          setSearchResults(prev => [...prev, ...results]);
        }
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('PubMed search failed:', error);
      // Fallback to mock data if API fails
      const mockResults: Reference[] = [
        {
          id: Date.now().toString(),
          title: `Research Article: ${query}`,
          authors: 'Smith, J. A., Johnson, B. C.',
          journal: 'Journal of Medical Research',
          year: '2023',
          volume: '45',
          issue: '3',
          pages: '123-135',
          doi: '10.1234/jmr.2023.45.3.123',
          pmid: '12345678',
          type: 'journal',
        },
      ];
      setSearchResults(mockResults);
    } finally {
      setIsSearching(false);
    }
  };

  const searchGoogleScholar = async (query: string, page: number = 1) => {
    setIsSearching(true);
    if (page === 1) {
      setSearchResults([]);
      setSelectedResults(new Set());
    }
    try {
      // Use CrossRef API directly for academic papers (no CORS issues)
      const retstart = (page - 1) * resultsPerPage;
      const fallbackResults = await searchScholarFallback(query, page);
      
      // Append results for pagination or replace for new search
      if (page === 1) {
        setSearchResults(fallbackResults);
      } else {
        setSearchResults(prev => [...prev, ...fallbackResults]);
      }
    } catch (error) {
      console.error('Google Scholar search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = (page: number = 1) => {
    if (!searchQuery.trim()) return;
    
    // Ensure page is a valid number
    const validPage = isNaN(page) || page < 1 ? 1 : Math.floor(page);
    
    if (validPage === 1) {
      setCurrentPage(1);
      setTotalResults(0); // Reset total results for new search
    }
    
    if (searchTab === 0) {
      searchPubMed(searchQuery, validPage);
    } else {
      searchGoogleScholar(searchQuery, validPage);
    }
  };

  const handleLoadMore = () => {
    // Ensure currentPage is a valid number before incrementing
    const validCurrentPage = isNaN(currentPage) || currentPage < 1 ? 1 : currentPage;
    const nextPage = validCurrentPage + 1;
    setCurrentPage(nextPage);
    handleSearch(nextPage);
  };

  const handleSelectResult = (resultId: string) => {
    const newSelected = new Set(selectedResults);
    if (newSelected.has(resultId)) {
      newSelected.delete(resultId);
    } else {
      newSelected.add(resultId);
    }
    setSelectedResults(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedResults.size === searchResults.length) {
      setSelectedResults(new Set());
    } else {
      setSelectedResults(new Set(searchResults.map(r => r.id)));
    }
  };

  const handleBulkAddSelected = () => {
    const selectedRefs = searchResults.filter(result => selectedResults.has(result.id));
    setReferences(prev => [...prev, ...selectedRefs]);
    setSelectedResults(new Set());
  };

  const searchScholarFallback = async (query: string, page: number = 1): Promise<Reference[]> => {
    // Alternative approach using CrossRef API for academic papers
    try {
      const offset = (page - 1) * resultsPerPage;
      const crossRefUrl = `https://api.crossref.org/works?query=${encodeURIComponent(query)}&rows=${resultsPerPage}&offset=${offset}`;
      const response = await fetch(crossRefUrl);
      
      if (!response.ok) {
        throw new Error('CrossRef API failed');
      }
      
      const data = await response.json();
      const results: Reference[] = [];
      
      // Set total results count for pagination
      if (page === 1 && data.message?.['total-results']) {
        setTotalResults(data.message['total-results']);
      }
      
      if (data.message?.items) {
        for (const item of data.message.items) {
          const authors = item.author?.map((author: any) => 
            `${author.family || ''}, ${author.given || ''}`.trim().replace(/^,\s*|,\s*$/g, '')
          ).join(', ') || 'Unknown';
          
          const year = item.published?.['date-parts']?.[0]?.[0]?.toString() || 
                      item.created?.['date-parts']?.[0]?.[0]?.toString() || 'Unknown';
          
          const journal = item['container-title']?.[0] || item.publisher || 'Unknown';
          
          results.push({
            id: `crossref_${Date.now()}_${results.length}`,
            title: item.title?.[0] || 'Untitled',
            authors: authors,
            journal: journal,
            year: year,
            volume: item.volume,
            issue: item.issue,
            pages: item.page,
            doi: item.DOI,
            url: item.URL,
            type: item.type === 'book' ? 'book' : 
                  item.type === 'proceedings-article' ? 'conference' : 'journal',
          });
        }
      }
      
      return results;
    } catch (error) {
      console.error('CrossRef fallback failed:', error);
      // Final fallback: return empty array
      return [];
    }
  };

  const addSearchResult = (result: Reference) => {
    setReferences(prev => [...prev, { ...result, id: Date.now().toString() }]);
  };

  const parseBulkReferences = (text: string): Reference[] => {
    const lines = text.split('\n').filter(line => line.trim());
    const references: Reference[] = [];
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) return;
      
      // Try to parse common citation formats
      let title = '', authors = '', journal = '', year = '', doi = '';
      
      // Basic parsing for common formats
      if (trimmedLine.includes('(') && trimmedLine.includes(')')) {
        const yearMatch = trimmedLine.match(/\((\d{4})\)/);
        if (yearMatch) {
          year = yearMatch[1];
          const beforeYear = trimmedLine.substring(0, trimmedLine.indexOf(yearMatch[0])).trim();
          const afterYear = trimmedLine.substring(trimmedLine.indexOf(yearMatch[0]) + yearMatch[0].length).trim();
          
          authors = beforeYear;
          
          // Extract title and journal from after year
          const parts = afterYear.split('.');
          if (parts.length >= 2) {
            title = parts[0].trim();
            journal = parts[1].trim();
          } else {
            title = afterYear;
          }
        }
      }
      
      // Extract DOI if present
      const doiMatch = trimmedLine.match(/doi[:\s]*(10\.\d+\/[^\s]+)/i);
      if (doiMatch) {
        doi = doiMatch[1];
      }
      
      // Fallback: use the entire line as title if parsing fails
      if (!title && !authors) {
        title = trimmedLine;
        authors = 'Unknown';
      }
      
      references.push({
        id: `bulk_${Date.now()}_${index}`,
        title: title || trimmedLine,
        authors: authors || 'Unknown',
        journal: journal || 'Unknown',
        year: year || 'Unknown',
        doi: doi,
        type: 'journal',
      });
    });
    
    return references;
  };

  const handleBulkImport = () => {
    if (!bulkText.trim()) return;
    
    const parsedReferences = parseBulkReferences(bulkText);
    setReferences(prev => [...prev, ...parsedReferences]);
    setBulkText('');
    setBulkImportOpen(false);
  };

  const addReference = () => {
    if (newReference.title && newReference.authors) {
      const reference: Reference = {
        id: Date.now().toString(),
        title: newReference.title || '',
        authors: newReference.authors || '',
        journal: newReference.journal || '',
        year: newReference.year || '',
        volume: newReference.volume,
        issue: newReference.issue,
        pages: newReference.pages,
        doi: newReference.doi,
        pmid: newReference.pmid,
        url: newReference.url,
        type: newReference.type || 'journal',
      };
      
      setReferences(prev => [...prev, reference]);
      setNewReference({ type: 'journal', title: '', authors: '', journal: '', year: '' });
      setDialogOpen(false);
    }
  };

  const deleteReference = (id: string) => {
    setReferences(prev => prev.filter(ref => ref.id !== id));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatReferenceForExport = (ref: Reference, format: ExportFormat): string => {
    switch (format) {
      case 'ris':
        return `TY  - JOUR
TI  - ${ref.title}
AU  - ${ref.authors.split(', ').join('\nAU  - ')}
JO  - ${ref.journal}
PY  - ${ref.year}
${ref.volume ? `VL  - ${ref.volume}\n` : ''}${ref.issue ? `IS  - ${ref.issue}\n` : ''}${ref.pages ? `SP  - ${ref.pages.split('-')[0]}\nEP  - ${ref.pages.split('-')[1] || ref.pages.split('-')[0]}\n` : ''}${ref.doi ? `DO  - ${ref.doi}\n` : ''}${ref.pmid ? `AN  - ${ref.pmid}\n` : ''}${ref.url ? `UR  - ${ref.url}\n` : ''}ER  -`;
      
      case 'bibtex':
        const key = `${ref.authors.split(',')[0].replace(/\s+/g, '').toLowerCase()}${ref.year}`;
        return `@article{${key},
  title={${ref.title}},
  author={${ref.authors}},
  journal={${ref.journal}},
  year={${ref.year}},
${ref.volume ? `  volume={${ref.volume}},\n` : ''}${ref.issue ? `  number={${ref.issue}},\n` : ''}${ref.pages ? `  pages={${ref.pages}},\n` : ''}${ref.doi ? `  doi={${ref.doi}},\n` : ''}${ref.pmid ? `  pmid={${ref.pmid}},\n` : ''}${ref.url ? `  url={${ref.url}},\n` : ''}
}`;
      
      case 'endnote':
        return `%0 Journal Article
%T ${ref.title}
%A ${ref.authors.split(', ').join('\n%A ')}
%J ${ref.journal}
%D ${ref.year}
${ref.volume ? `%V ${ref.volume}\n` : ''}${ref.issue ? `%N ${ref.issue}\n` : ''}${ref.pages ? `%P ${ref.pages}\n` : ''}${ref.doi ? `%R ${ref.doi}\n` : ''}${ref.pmid ? `%M ${ref.pmid}\n` : ''}${ref.url ? `%U ${ref.url}\n` : ''}`;
      
      case 'json':
        return JSON.stringify(ref, null, 2);
      
      case 'csv':
        return `"${ref.title}","${ref.authors}","${ref.journal}","${ref.year}","${ref.volume || ''}","${ref.issue || ''}","${ref.pages || ''}","${ref.doi || ''}","${ref.pmid || ''}","${ref.url || ''}","${ref.type}"`;
      
      case 'txt':
      default:
        return formatCitation(ref, citationStyle);
    }
  };

  const getExportMimeType = (format: ExportFormat): string => {
    switch (format) {
      case 'ris': return 'application/x-research-info-systems';
      case 'bibtex': return 'application/x-bibtex';
      case 'endnote': return 'application/x-endnote-refer';
      case 'json': return 'application/json';
      case 'csv': return 'text/csv';
      case 'txt':
      default: return 'text/plain';
    }
  };

  const getExportFileExtension = (format: ExportFormat): string => {
    switch (format) {
      case 'ris': return 'ris';
      case 'bibtex': return 'bib';
      case 'endnote': return 'enw';
      case 'json': return 'json';
      case 'csv': return 'csv';
      case 'txt':
      default: return 'txt';
    }
  };

  const exportBibliography = () => {
    let content: string;
    
    if (exportFormat === 'csv') {
      // Add CSV header
      const header = '"Title","Authors","Journal","Year","Volume","Issue","Pages","DOI","PMID","URL","Type"';
      const rows = references.map(ref => formatReferenceForExport(ref, exportFormat));
      content = [header, ...rows].join('\n');
    } else if (exportFormat === 'json') {
      // Export as JSON array
      content = JSON.stringify(references, null, 2);
    } else {
      // For other formats, join with appropriate separators
      const separator = exportFormat === 'ris' ? '\n\n' : 
                       exportFormat === 'bibtex' ? '\n\n' : 
                       exportFormat === 'endnote' ? '\n\n' : '\n\n';
      content = references
        .map(ref => formatReferenceForExport(ref, exportFormat))
        .join(separator);
    }
    
    const mimeType = getExportMimeType(exportFormat);
    const fileExtension = getExportFileExtension(exportFormat);
    
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bibliography_${exportFormat === 'txt' ? citationStyle : exportFormat}.${fileExtension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <PublicationReadyGate>
      <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 1400, mx: 'auto' }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
            Citation & Reference Manager
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your research references and generate formatted citations in multiple academic styles
          </Typography>
        </Box>

        {/* Search and Add Section */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={8}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Search Academic Databases
                </Typography>
                
                <Tabs value={searchTab} onChange={(e, newValue) => setSearchTab(newValue)} sx={{ mb: 2 }}>
                  <Tab icon={<PubMedIcon />} label="PubMed" />
                  <Tab icon={<ScholarIcon />} label="Google Scholar" />
                </Tabs>
                
                {/* Search Method Information */}
                {searchTab === 0 ? (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <strong>PubMed Search:</strong> Access to NCBI's biomedical literature database via CORS proxy. 
                    Search by keywords, DOI, or PMID for accurate medical and life science publications.
                  </Alert>
                ) : (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <strong>Google Scholar Search:</strong> Searches academic papers across disciplines using CrossRef API. 
                    Provides access to scholarly publications, conference papers, and academic content from multiple publishers.
                  </Alert>
                )}
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    fullWidth
                    placeholder={searchTab === 0 ? "Enter keywords, DOI, or PMID..." : "Enter search terms for Google Scholar..."}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button
                    variant="contained"
                    startIcon={isSearching ? <CircularProgress size={20} /> : <SearchIcon />}
                    onClick={handleSearch}
                    disabled={isSearching || !searchQuery.trim()}
                  >
                    Search
                  </Button>
                </Box>
                
                {/* Search Results */}
                {searchResults.length > 0 && (
                  <Box sx={{ mt: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle2">
                        Search Results ({searchResults.length}{totalResults > searchResults.length ? ` of ${totalResults}` : ''})
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={handleSelectAll}
                        >
                          {selectedResults.size === searchResults.length ? 'Deselect All' : 'Select All'}
                        </Button>
                        {selectedResults.size > 0 && (
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleBulkAddSelected}
                          >
                            Add Selected ({selectedResults.size})
                          </Button>
                        )}
                      </Box>
                    </Box>
                    <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {searchResults.map((result) => (
                        <ListItem key={result.id} divider>
                          <ListItemIcon>
                            <Checkbox
                              checked={selectedResults.has(result.id)}
                              onChange={() => handleSelectResult(result.id)}
                              size="small"
                            />
                          </ListItemIcon>
                          <ListItemText
                            primary={result.title}
                            secondary={
                              <>
                                {result.authors} ({result.year})<br/>
                                <em>{result.journal}</em>
                                {result.doi && (
                                  <><br/><span style={{ color: '#1976d2', fontSize: '0.75rem' }}>DOI: {result.doi}</span></>
                                )}
                              </>
                            }
                            primaryTypographyProps={{ variant: 'subtitle2', sx: { fontWeight: 500 } }}
                            secondaryTypographyProps={{ variant: 'body2', color: 'text.secondary', component: 'div' }}
                          />
                          <ListItemSecondaryAction>
                            <Button
                              size="small"
                              variant="outlined"
                              startIcon={<AddIcon />}
                              onClick={() => addSearchResult(result)}
                            >
                              Add
                            </Button>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                    {/* Load More Button */}
                    {totalResults > searchResults.length && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                        <Button
                          variant="outlined"
                          onClick={handleLoadMore}
                          disabled={isSearching}
                        >
                          {isSearching ? 'Loading...' : `Load More (${totalResults - searchResults.length} remaining)`}
                        </Button>
                      </Box>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Manual Entry
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={() => setDialogOpen(true)}
                    >
                      Add Single Reference
                    </Button>
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<UploadIcon />}
                      onClick={() => setBulkImportOpen(true)}
                    >
                      Bulk Import References
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Citation Style and Export */}
        <Card elevation={2} sx={{ mb: 4 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Citation Style</InputLabel>
                  <Select
                    value={citationStyle}
                    label="Citation Style"
                    onChange={(e) => setCitationStyle(e.target.value as CitationStyle)}
                  >
                    <MenuItem value="apa">APA 7th Edition</MenuItem>
                    <MenuItem value="ama">AMA Style</MenuItem>
                    <MenuItem value="vancouver">Vancouver Style</MenuItem>
                    <MenuItem value="harvard">Harvard Style</MenuItem>
                    <MenuItem value="chicago">Chicago Style</MenuItem>
                    <MenuItem value="mla">MLA Style</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Chip
                  label={`${references.length} References`}
                  color="primary"
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Grid container spacing={1}>
                  <Grid item xs={12}>
                     <FormControl fullWidth size="small">
                       <InputLabel>Export Format</InputLabel>
                       <Select
                         value={exportFormat}
                         label="Export Format"
                         onChange={(e) => setExportFormat(e.target.value as ExportFormat)}
                       >
                         <MenuItem value="txt">Text ({citationStyle.toUpperCase()}) - Formatted citations</MenuItem>
                          <MenuItem value="ris">RIS Format (.ris) - Reference Manager</MenuItem>
                          <MenuItem value="bibtex">BibTeX (.bib) - LaTeX/Academic</MenuItem>
                          <MenuItem value="endnote">EndNote (.enw) - EndNote Compatible</MenuItem>
                          <MenuItem value="json">JSON (.json) - Structured data</MenuItem>
                          <MenuItem value="csv">CSV (.csv) - Spreadsheet format</MenuItem>
                       </Select>
                     </FormControl>
                     {exportFormat !== 'txt' && (
                       <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                         {exportFormat === 'ris' && 'Compatible with Zotero, Mendeley, and other reference managers'}
                         {exportFormat === 'bibtex' && 'For use with LaTeX documents and academic writing'}
                         {exportFormat === 'endnote' && 'Import directly into EndNote reference manager'}
                         {exportFormat === 'json' && 'Machine-readable format for data processing'}
                         {exportFormat === 'csv' && 'Open in Excel, Google Sheets, or other spreadsheet applications'}
                       </Typography>
                     )}
                   </Grid>
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      startIcon={<DownloadIcon />}
                      onClick={exportBibliography}
                      disabled={references.length === 0}
                    >
                      Export Bibliography
                    </Button>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* References List */}
        <Card elevation={2}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Reference Library
            </Typography>
            
            {references.length === 0 ? (
              <Alert severity="info">
                No references added yet. Search PubMed or add references manually to get started.
              </Alert>
            ) : (
              <List>
                {references.map((ref, index) => (
                  <React.Fragment key={ref.id}>
                    <ListItem alignItems="flex-start">
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              [{index + 1}] {ref.title}
                            </Typography>
                            <Chip size="small" label={ref.type} variant="outlined" />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {ref.authors} ({ref.year}). {ref.journal}
                            </Typography>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2,
                                bgcolor: alpha(theme.palette.primary.main, 0.05),
                                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                borderRadius: 1,
                              }}
                            >
                              <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                                {formatCitation(ref, citationStyle)}
                              </Typography>
                            </Paper>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => copyToClipboard(formatCitation(ref, citationStyle))}
                            title="Copy citation"
                          >
                            <CopyIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => {
                              setEditingReference(ref);
                              setNewReference(ref);
                              setDialogOpen(true);
                            }}
                            title="Edit reference"
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => deleteReference(ref.id)}
                            title="Delete reference"
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < references.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>

        {/* Add/Edit Reference Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingReference ? 'Edit Reference' : 'Add New Reference'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Reference Type</InputLabel>
                  <Select
                    value={newReference.type || 'journal'}
                    label="Reference Type"
                    onChange={(e) => setNewReference(prev => ({ ...prev, type: e.target.value as Reference['type'] }))}
                  >
                    <MenuItem value="journal">Journal Article</MenuItem>
                    <MenuItem value="book">Book</MenuItem>
                    <MenuItem value="conference">Conference Paper</MenuItem>
                    <MenuItem value="website">Website</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Year"
                  value={newReference.year || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, year: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title *"
                  value={newReference.title || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, title: e.target.value }))}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Authors *"
                  value={newReference.authors || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, authors: e.target.value }))}
                  placeholder="Last, F. M., Last, F. M."
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Journal/Publisher"
                  value={newReference.journal || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, journal: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Volume"
                  value={newReference.volume || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, volume: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Issue"
                  value={newReference.issue || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, issue: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Pages"
                  value={newReference.pages || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, pages: e.target.value }))}
                  placeholder="123-135"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="DOI"
                  value={newReference.doi || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, doi: e.target.value }))}
                  placeholder="10.1234/journal.2023.123"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="PMID"
                  value={newReference.pmid || ''}
                  onChange={(e) => setNewReference(prev => ({ ...prev, pmid: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => {
              setDialogOpen(false);
              setEditingReference(null);
              setNewReference({ type: 'journal', title: '', authors: '', journal: '', year: '' });
            }}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={addReference}
              disabled={!newReference.title || !newReference.authors}
            >
              {editingReference ? 'Update' : 'Add'} Reference
            </Button>
          </DialogActions>
        </Dialog>

        {/* Bulk Import Dialog */}
        <Dialog open={bulkImportOpen} onClose={() => setBulkImportOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            Bulk Import References
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Paste your references below. The system will attempt to parse common citation formats automatically.
              Each reference should be on a separate line.
            </Typography>
            
            <Accordion sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">Supported Formats & Examples</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" component="div">
                  <strong>APA Format:</strong><br />
                  Smith, J. A. (2023). Title of the article. Journal Name, 45(3), 123-135. doi:10.1234/journal.2023.123<br /><br />
                  
                  <strong>Vancouver Format:</strong><br />
                  Smith JA, Johnson BC. Title of the article. Journal Name. 2023;45(3):123-135.<br /><br />
                  
                  <strong>Simple Format:</strong><br />
                  Title of the article by Author Name in Journal Name (2023)<br /><br />
                  
                  <strong>Note:</strong> The parser will extract available information and you can edit individual references later if needed.
                </Typography>
              </AccordionDetails>
            </Accordion>
            
            <TextField
              fullWidth
              multiline
              rows={12}
              placeholder="Paste your references here, one per line..."
              value={bulkText}
              onChange={(e) => setBulkText(e.target.value)}
              variant="outlined"
            />
            
            {bulkText.trim() && (
              <Alert severity="info" sx={{ mt: 2 }}>
                {bulkText.split('\n').filter(line => line.trim()).length} references detected
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => {
              setBulkImportOpen(false);
              setBulkText('');
            }}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleBulkImport}
              disabled={!bulkText.trim()}
            >
              Import References
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </PublicationReadyGate>
  );
};

export default CitationReferenceManager;