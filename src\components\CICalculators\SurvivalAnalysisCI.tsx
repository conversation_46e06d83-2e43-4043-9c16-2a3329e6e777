import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Tabs,
  Tab,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Clear as ClearIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface InputData {
  events: string;
  totalSample: string;
  survivalProbability: string;
  confidenceLevel: string;
  method: 'greenwood' | 'exponential' | 'loglog';
  analysisType: 'kaplan-meier' | 'hazard-ratio';
  hazardRatio?: string;
  logHazardSE?: string;
}

interface SurvivalResults {
  survivalProbability: number;
  survivalCI: [number, number];
  hazardRatio?: number;
  hazardRatioCI?: [number, number];
  standardError: number;
  confidenceLevel: number;
  method: string;
  analysisType: string;
  formula: string;
}

const SurvivalAnalysisCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [inputData, setInputData] = useState<InputData>({
    events: '',
    totalSample: '',
    survivalProbability: '',
    confidenceLevel: '95',
    method: 'greenwood',
    analysisType: 'kaplan-meier'
  });
  const [results, setResults] = useState<SurvivalResults | null>(null);
  const [error, setError] = useState<string>('');

  // Render formula using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      return `<p>Error rendering formula: ${formula}</p>`;
    }
  };

  // Get z-critical value
  const getZCritical = (alpha: number): number => {
    const zTable: { [key: string]: number } = {
      '0.10': 1.645,
      '0.05': 1.960,
      '0.025': 1.960,
      '0.01': 2.576,
      '0.005': 2.807,
      '0.001': 3.291
    };
    
    const alphaStr = alpha.toFixed(2);
    return zTable[alphaStr] || 1.96;
  };

  // Greenwood's formula for Kaplan-Meier standard error
  const greenwoodSE = (survivalProb: number, events: number, totalSample: number): number => {
    // Simplified Greenwood formula approximation
    const variance = survivalProb * survivalProb * (events / (totalSample * (totalSample - events)));
    return Math.sqrt(variance);
  };

  // Exponential model standard error
  const exponentialSE = (survivalProb: number, events: number): number => {
    if (survivalProb <= 0 || survivalProb >= 1) return 0;
    const logSurvival = Math.log(survivalProb);
    return Math.abs(logSurvival) / Math.sqrt(events);
  };

  // Log-log transformation confidence interval
  const logLogCI = (survivalProb: number, se: number, alpha: number): [number, number] => {
    if (survivalProb <= 0 || survivalProb >= 1) return [0, 1];
    
    const z = getZCritical(alpha / 2);
    const logLogSurvival = Math.log(-Math.log(survivalProb));
    const logLogSE = se / (survivalProb * Math.abs(Math.log(survivalProb)));
    
    const lowerLogLog = logLogSurvival - z * logLogSE;
    const upperLogLog = logLogSurvival + z * logLogSE;
    
    const lower = Math.exp(-Math.exp(upperLogLog));
    const upper = Math.exp(-Math.exp(lowerLogLog));
    
    return [Math.max(0, lower), Math.min(1, upper)];
  };

  // Linear confidence interval
  const linearCI = (survivalProb: number, se: number, alpha: number): [number, number] => {
    const z = getZCritical(alpha / 2);
    const margin = z * se;
    
    return [Math.max(0, survivalProb - margin), Math.min(1, survivalProb + margin)];
  };

  // Hazard ratio confidence interval
  const hazardRatioCI = (hr: number, logHRSE: number, alpha: number): [number, number] => {
    const z = getZCritical(alpha / 2);
    const logHR = Math.log(hr);
    const margin = z * logHRSE;
    
    return [Math.exp(logHR - margin), Math.exp(logHR + margin)];
  };

  const calculateSurvivalCI = () => {
    try {
      setError('');
      
      const events = parseInt(inputData.events);
      const totalSample = parseInt(inputData.totalSample);
      const survivalProb = parseFloat(inputData.survivalProbability);
      const confidenceLevel = parseFloat(inputData.confidenceLevel);
      
      // Validation
      if (isNaN(events) || isNaN(totalSample) || isNaN(survivalProb) || isNaN(confidenceLevel)) {
        throw new Error('Please enter valid numeric values for all required fields.');
      }
      
      if (events < 0 || totalSample <= 0) {
        throw new Error('Events must be non-negative and total sample must be positive.');
      }
      
      if (events > totalSample) {
        throw new Error('Number of events cannot exceed total sample size.');
      }
      
      if (survivalProb < 0 || survivalProb > 1) {
        throw new Error('Survival probability must be between 0 and 1.');
      }
      
      if (confidenceLevel <= 0 || confidenceLevel >= 100) {
        throw new Error('Confidence level must be between 0 and 100.');
      }
      
      const alpha = (100 - confidenceLevel) / 100;
      let standardError: number;
      let survivalCI: [number, number];
      
      // Calculate standard error based on method
      if (inputData.method === 'greenwood') {
        standardError = greenwoodSE(survivalProb, events, totalSample);
        survivalCI = inputData.analysisType === 'kaplan-meier' 
          ? logLogCI(survivalProb, standardError, alpha)
          : linearCI(survivalProb, standardError, alpha);
      } else if (inputData.method === 'exponential') {
        standardError = exponentialSE(survivalProb, events);
        survivalCI = linearCI(survivalProb, standardError, alpha);
      } else { // log-log
        standardError = greenwoodSE(survivalProb, events, totalSample);
        survivalCI = logLogCI(survivalProb, standardError, alpha);
      }
      
      let calculatedResults: SurvivalResults = {
        survivalProbability: survivalProb,
        survivalCI,
        standardError,
        confidenceLevel,
        method: inputData.method,
        analysisType: inputData.analysisType,
        formula: inputData.analysisType === 'hazard-ratio' 
          ? `\\text{HR} = \\frac{\\lambda_1}{\\lambda_2}, \\quad \\text{CI} = \\exp(\\ln(\\text{HR}) \\pm z_{\\alpha/2} \\cdot \\text{SE}(\\ln(\\text{HR})))`
          : `\\hat{S}(t) = \\prod_{i: t_i \\leq t} \\left(1 - \\frac{d_i}{n_i}\\right), \\quad \\text{SE}(\\hat{S}(t)) = \\hat{S}(t) \\sqrt{\\sum_{i: t_i \\leq t} \\frac{d_i}{n_i(n_i - d_i)}}`
      };
      
      // Handle hazard ratio if provided
      if (inputData.analysisType === 'hazard-ratio' && inputData.hazardRatio && inputData.logHazardSE) {
        const hr = parseFloat(inputData.hazardRatio);
        const logHRSE = parseFloat(inputData.logHazardSE);
        
        if (isNaN(hr) || isNaN(logHRSE)) {
          throw new Error('Please enter valid numeric values for hazard ratio and log hazard SE.');
        }
        
        if (hr <= 0) {
          throw new Error('Hazard ratio must be positive.');
        }
        
        if (logHRSE <= 0) {
          throw new Error('Log hazard standard error must be positive.');
        }
        
        calculatedResults.hazardRatio = hr;
        calculatedResults.hazardRatioCI = hazardRatioCI(hr, logHRSE, alpha);
      }
      
      setResults(calculatedResults);
      setActiveTab(1); // Switch to Results tab
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during calculation.');
    }
  };

  const clearAll = () => {
    setInputData({
      events: '',
      totalSample: '',
      survivalProbability: '',
      confidenceLevel: '95',
      method: 'greenwood',
      analysisType: 'kaplan-meier'
    });
    setResults(null);
    setError('');
    setActiveTab(0);
  };

  const copyToClipboard = () => {
    if (!results) return;
    
    let text = `Survival Analysis Confidence Interval Results:\n` +
      `Confidence Level: ${results.confidenceLevel}%\n` +
      `Method: ${results.method}\n` +
      `Analysis Type: ${results.analysisType}\n\n` +
      `Survival Probability: ${(results.survivalProbability * 100).toFixed(2)}%\n` +
      `${results.confidenceLevel}% CI: [${(results.survivalCI[0] * 100).toFixed(2)}%, ${(results.survivalCI[1] * 100).toFixed(2)}%]\n` +
      `Standard Error: ${results.standardError.toFixed(2)}`;
    
    if (results.hazardRatio && results.hazardRatioCI) {
      text += `\n\nHazard Ratio: ${results.hazardRatio.toFixed(2)}\n` +
        `${results.confidenceLevel}% CI: [${results.hazardRatioCI[0].toFixed(2)}, ${results.hazardRatioCI[1].toFixed(2)}]`;
    }
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        Survival Analysis Confidence Intervals
      </Typography>
      
      <Card>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!results} />
          <Tab label="Guide" />
        </Tabs>
        
        <TabPanel value={activeTab} index={0}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Analysis Type</InputLabel>
                  <Select
                    value={inputData.analysisType}
                    onChange={(e) => setInputData({ ...inputData, analysisType: e.target.value as 'kaplan-meier' | 'hazard-ratio' })}
                    label="Analysis Type"
                  >
                    <MenuItem value="kaplan-meier">Kaplan-Meier Survival</MenuItem>
                    <MenuItem value="hazard-ratio">Hazard Ratio</MenuItem>
                  </Select>
                </FormControl>
                
                <TextField
                  fullWidth
                  label="Number of Events"
                  type="number"
                  value={inputData.events}
                  onChange={(e) => setInputData({ ...inputData, events: e.target.value })}
                  margin="normal"
                  helperText="Number of observed events (deaths, failures, etc.)"
                />
                
                <TextField
                  fullWidth
                  label="Total Sample Size"
                  type="number"
                  value={inputData.totalSample}
                  onChange={(e) => setInputData({ ...inputData, totalSample: e.target.value })}
                  margin="normal"
                  helperText="Total number of subjects in the study"
                />
                
                <TextField
                  fullWidth
                  label="Survival Probability"
                  type="number"
                  value={inputData.survivalProbability}
                  onChange={(e) => setInputData({ ...inputData, survivalProbability: e.target.value })}
                  margin="normal"
                  helperText="Survival probability at time t (0 to 1)"
                  inputProps={{ min: 0, max: 1, step: 0.001 }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Confidence Level (%)"
                  type="number"
                  value={inputData.confidenceLevel}
                  onChange={(e) => setInputData({ ...inputData, confidenceLevel: e.target.value })}
                  margin="normal"
                  helperText="Typically 90, 95, or 99"
                  sx={{ mt: 2 }}
                />
                
                <FormControl fullWidth margin="normal">
                  <InputLabel>CI Method</InputLabel>
                  <Select
                    value={inputData.method}
                    onChange={(e) => setInputData({ ...inputData, method: e.target.value as 'greenwood' | 'exponential' | 'loglog' })}
                    label="CI Method"
                  >
                    <MenuItem value="greenwood">Greenwood Formula</MenuItem>
                    <MenuItem value="exponential">Exponential Model</MenuItem>
                    <MenuItem value="loglog">Log-Log Transformation</MenuItem>
                  </Select>
                </FormControl>
                
                {inputData.analysisType === 'hazard-ratio' && (
                  <>
                    <TextField
                      fullWidth
                      label="Hazard Ratio"
                      type="number"
                      value={inputData.hazardRatio || ''}
                      onChange={(e) => setInputData({ ...inputData, hazardRatio: e.target.value })}
                      margin="normal"
                      helperText="Estimated hazard ratio"
                      inputProps={{ min: 0, step: 0.001 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Log Hazard Standard Error"
                      type="number"
                      value={inputData.logHazardSE || ''}
                      onChange={(e) => setInputData({ ...inputData, logHazardSE: e.target.value })}
                      margin="normal"
                      helperText="Standard error of log hazard ratio"
                      inputProps={{ min: 0, step: 0.001 }}
                    />
                  </>
                )}
              </Grid>
            </Grid>
            
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                onClick={calculateSurvivalCI}
                startIcon={<CalculateIcon />}
                size="large"
              >
                Calculate CI
              </Button>
              
              <Button
                variant="outlined"
                onClick={clearAll}
                startIcon={<ClearIcon />}
                size="large"
              >
                Clear All
              </Button>
            </Box>
          </CardContent>
        </TabPanel>
        
        <TabPanel value={activeTab} index={1}>
          {results && (
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" component="h2">
                  Survival Analysis Results
                </Typography>
                <Tooltip title="Copy results to clipboard">
                  <IconButton onClick={copyToClipboard}>
                    <CopyIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Analysis Details
                    </Typography>
                    <Typography><strong>Method:</strong> {results.method}</Typography>
                    <Typography><strong>Analysis Type:</strong> {results.analysisType}</Typography>
                    <Typography><strong>Confidence Level:</strong> {results.confidenceLevel}%</Typography>
                    <Typography><strong>Standard Error:</strong> {results.standardError.toFixed(2)}</Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                      Survival Probability
                    </Typography>
                    <Typography variant="h4" color="primary">
                      {(results.survivalProbability * 100).toFixed(2)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {results.confidenceLevel}% CI: [{(results.survivalCI[0] * 100).toFixed(2)}%, {(results.survivalCI[1] * 100).toFixed(2)}%]
                    </Typography>
                  </Paper>
                </Grid>
                
                {results.hazardRatio && results.hazardRatioCI && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Hazard Ratio Analysis
                      </Typography>
                      <Typography variant="h4" color="secondary">
                        {results.hazardRatio.toFixed(2)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {results.confidenceLevel}% CI: [{results.hazardRatioCI[0].toFixed(2)}, {results.hazardRatioCI[1].toFixed(2)}]
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {results.hazardRatio > 1 
                          ? 'Increased hazard compared to reference group'
                          : results.hazardRatio < 1 
                          ? 'Decreased hazard compared to reference group'
                          : 'No difference in hazard compared to reference group'
                        }
                      </Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Interpretation
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText 
                      primary="Survival Probability" 
                      secondary={`There is a ${(results.survivalProbability * 100).toFixed(1)}% probability of survival at the specified time point.`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Confidence Interval" 
                      secondary={`We are ${results.confidenceLevel}% confident that the true survival probability lies between ${(results.survivalCI[0] * 100).toFixed(1)}% and ${(results.survivalCI[1] * 100).toFixed(1)}%.`}
                    />
                  </ListItem>
                  {results.hazardRatio && (
                    <ListItem>
                      <ListItemText 
                        primary="Hazard Ratio" 
                        secondary={`The hazard is ${results.hazardRatio.toFixed(2)} times ${results.hazardRatio > 1 ? 'higher' : 'lower'} compared to the reference group.`}
                      />
                    </ListItem>
                  )}
                </List>
              </Box>
              
              {/* Formula Display */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Formulas
                </Typography>
                <Box
                  dangerouslySetInnerHTML={{ __html: renderFormula(results.formula) }}
                  sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    '& .katex': { fontSize: '1.1em' }
                  }}
                />
              </Box>
            </CardContent>
          )}
        </TabPanel>
        
        <TabPanel value={activeTab} index={2}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Survival Analysis Confidence Intervals Guide
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                When to Use
              </Typography>
              <Typography paragraph>
                Use survival analysis confidence intervals to:
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Estimate uncertainty in survival probabilities" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Compare survival between different groups" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Assess the precision of Kaplan-Meier estimates" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Report hazard ratios with confidence intervals" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                CI Methods
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Greenwood Formula" 
                    secondary="Standard method for Kaplan-Meier survival estimates" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Exponential Model" 
                    secondary="Assumes exponential survival distribution" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Log-Log Transformation" 
                    secondary="Provides better coverage for extreme survival probabilities" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Key Concepts
              </Typography>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Survival Function" 
                    secondary="S(t) = Probability of surviving beyond time t" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Hazard Function" 
                    secondary="Instantaneous risk of event at time t" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Censoring" 
                    secondary="Incomplete observation of event times" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Hazard Ratio" 
                    secondary="Ratio of hazards between two groups" 
                  />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Assumptions
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Independent observations" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Non-informative censoring" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Proportional hazards (for hazard ratios)" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Adequate sample size for stable estimates" />
                </ListItem>
              </List>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Interpretation Guidelines
              </Typography>
              <List>
                <ListItem>
                  <ListItemText primary="• Narrower CIs indicate more precise estimates" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Consider clinical significance, not just statistical significance" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Hazard ratio = 1 indicates no difference between groups" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="• Log-log transformation often preferred for extreme probabilities" />
                </ListItem>
              </List>
            </Box>
          </CardContent>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default SurvivalAnalysisCI;