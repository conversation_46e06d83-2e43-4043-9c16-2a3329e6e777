import React from 'react';
import { Box, Container } from '@mui/material';
import DataVisualizationOptions from '../components/Visualization/DataVisualizationOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';

interface DataVisualizationPageProps {
  onNavigate: (path: string) => void;
}

const DataVisualizationPage: React.FC<DataVisualizationPageProps> = ({ onNavigate }) => {
  return (
    <Container maxWidth="lg">
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible={true}
      />
      <Box sx={{ mt: 4, mb: 4 }}>
        <DataVisualizationOptions onNavigate={onNavigate} />
      </Box>
    </Container>
  );
};

export default DataVisualizationPage;
