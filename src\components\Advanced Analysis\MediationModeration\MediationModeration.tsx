import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  FormControlLabel,
  Checkbox,
  TextField,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Timeline as TimelineIcon,
  Help as HelpIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  AccountTree as AccountTreeIcon,
  ArrowRightAlt as ArrowRightAltIcon,
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType, Column, VariableRole } from '../../../types';
import { mediationModerationService, MediationResults, ModerationResults } from '../../../utils/services/mediationModerationService';

// Define the structure for analysis results used in the component
interface SimpleSlopeData {
  slope: number;
  se: number;
  t_value: number;
  p_value: number;
  ci_lower: number;
  ci_upper: number;
}

interface ExtendedMediationResults extends MediationResults {
  // UI-specific data
  xColumn: Column;
  yColumn: Column;
  mediatorColumn: Column;
  covariateColumns?: Column[];
  xName: string;
  yName: string;
  mediatorName: string;
  covariateNames?: string[];
}

interface ExtendedModerationResults extends ModerationResults {
  // UI-specific data
  xColumn: Column;
  yColumn: Column;
  moderatorColumn: Column;
  covariateColumns?: Column[];
  xName: string;
  yName: string;
  moderatorName: string;
  covariateNames?: string[];
  simple_slopes: {
    [key: string]: SimpleSlopeData;
  };
}

type AnalysisType = 'mediation' | 'moderation';

const MediationModeration: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();

  // State for analysis type selection
  const [analysisType, setAnalysisType] = useState<AnalysisType>('mediation');

  // State for variable selection
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [independentVariable, setIndependentVariable] = useState<string>('');
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [mediatorVariable, setMediatorVariable] = useState<string>('');
  const [moderatorVariable, setModeratorVariable] = useState<string>('');
  const [covariateVariables, setCovariateVariables] = useState<string[]>([]);
  const [confInterval, setConfInterval] = useState<number>(0.95);
  const [bootstrapSamples, setBootstrapSamples] = useState<number>(1000);

  // State for display options
  const [displayOptions, setDisplayOptions] = useState({
    showPathDiagram: true,
    showStandardizedCoefficients: true,
    showBootstrapResults: true,
    showEffectDecomposition: true,
  });

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [initializingPython, setInitializingPython] = useState<boolean>(false);
  const [pythonReady, setPythonReady] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [mediationResults, setMediationResults] = useState<ExtendedMediationResults | null>(null);
  const [moderationResults, setModerationResults] = useState<ExtendedModerationResults | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  // Initialize Python environment
  const initializePython = useCallback(async () => {
    if (pythonReady || initializingPython) return;

    setInitializingPython(true);
    try {
      await mediationModerationService.initialize();
      setPythonReady(true);
    } catch (error) {
      console.error('Failed to initialize Python environment:', error);
      setError('Failed to initialize Python environment. Please refresh the page and try again.');
    } finally {
      setInitializingPython(false);
    }
  }, [pythonReady, initializingPython]);

  // Initialize Python on component mount
  useEffect(() => {
    initializePython();
  }, [initializePython]);

  // Load saved results from localStorage on component mount
  useEffect(() => {
    const savedMediationResults = localStorage.getItem('mediation_results');
    const savedModerationResults = localStorage.getItem('moderation_results');
    
    if (savedMediationResults) {
      try {
        const parsedResults = JSON.parse(savedMediationResults);
        setMediationResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved mediation results:', error);
        localStorage.removeItem('mediation_results');
      }
    }
    
    if (savedModerationResults) {
      try {
        const parsedResults = JSON.parse(savedModerationResults);
        setModerationResults(parsedResults);
      } catch (error) {
        console.error('Error parsing saved moderation results:', error);
        localStorage.removeItem('moderation_results');
      }
    }
  }, []);

  // Get numeric columns for all variables
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Get available columns for covariates (numeric only, excluding selected main variables)
  const availableCovariateColumns = numericColumns.filter(
    col => col.id !== independentVariable && 
           col.id !== dependentVariable && 
           col.id !== mediatorVariable && 
           col.id !== moderatorVariable
  );

  // Handle analysis type change
  const handleAnalysisTypeChange = (
    _event: React.MouseEvent<HTMLElement>,
    newType: AnalysisType | null
  ) => {
    if (newType !== null) {
      setAnalysisType(newType);
      setMediatorVariable('');
      setModeratorVariable('');
      setMediationResults(null);
      setModerationResults(null);
      localStorage.removeItem('mediation_results');
      localStorage.removeItem('moderation_results');
    }
  };

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setIndependentVariable('');
    setDependentVariable('');
    setMediatorVariable('');
    setModeratorVariable('');
    setCovariateVariables([]);
    setMediationResults(null);
    setModerationResults(null);

    localStorage.removeItem('mediation_results');
    localStorage.removeItem('moderation_results');

    const selectedDataset = datasets.find(dataset => dataset.id === newDatasetId);
    if (selectedDataset) {
      setCurrentDataset(selectedDataset);
    }
  };

  // Handle variable selection changes
  const handleIndependentVariableChange = (event: SelectChangeEvent<string>) => {
    setIndependentVariable(event.target.value);
    setMediationResults(null);
    setModerationResults(null);
    localStorage.removeItem('mediation_results');
    localStorage.removeItem('moderation_results');
  };

  const handleDependentVariableChange = (event: SelectChangeEvent<string>) => {
    setDependentVariable(event.target.value);
    setMediationResults(null);
    setModerationResults(null);
    localStorage.removeItem('mediation_results');
    localStorage.removeItem('moderation_results');
  };

  const handleMediatorVariableChange = (event: SelectChangeEvent<string>) => {
    setMediatorVariable(event.target.value);
    setMediationResults(null);
    localStorage.removeItem('mediation_results');
  };

  const handleModeratorVariableChange = (event: SelectChangeEvent<string>) => {
    setModeratorVariable(event.target.value);
    setModerationResults(null);
    localStorage.removeItem('moderation_results');
  };

  const handleCovariateVariablesChange = (event: SelectChangeEvent<typeof covariateVariables>) => {
    const value = event.target.value;
    const newVars = typeof value === 'string' ? value.split(',') : value;
    setCovariateVariables(newVars);
    setMediationResults(null);
    setModerationResults(null);
    localStorage.removeItem('mediation_results');
    localStorage.removeItem('moderation_results');
  };

  // Handle confidence interval change
  const handleConfIntervalChange = (event: SelectChangeEvent<number>) => {
    setConfInterval(Number(event.target.value));
    setMediationResults(null);
    setModerationResults(null);
    localStorage.removeItem('mediation_results');
    localStorage.removeItem('moderation_results');
  };

  // Handle bootstrap samples change
  const handleBootstrapSamplesChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    if (!isNaN(value) && value >= 100 && value <= 10000) {
      setBootstrapSamples(value);
      setMediationResults(null);
      setModerationResults(null);
      localStorage.removeItem('mediation_results');
      localStorage.removeItem('moderation_results');
    }
  };

  // Handle display option change
  const handleDisplayOptionChange = (option: keyof typeof displayOptions) => {
    setDisplayOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

// Run analysis
const runAnalysis = async () => {
  if (!currentDataset || !independentVariable || !dependentVariable) {
    setError('Please select independent and dependent variables.');
    return;
  }

  if (analysisType === 'mediation' && !mediatorVariable) {
    setError('Please select a mediator variable for mediation analysis.');
    return;
  }

  if (analysisType === 'moderation' && !moderatorVariable) {
    setError('Please select a moderator variable for moderation analysis.');
    return;
  }

  if (!pythonReady) {
    setError('Python environment is not ready. Please wait for initialization to complete.');
    return;
  }

  setLoading(true);
  setError(null);
  setMediationResults(null);
  setModerationResults(null);

  try {
    // Get column objects
    const xColumn = currentDataset.columns.find(col => col.id === independentVariable);
    const yColumn = currentDataset.columns.find(col => col.id === dependentVariable);
    const mColumn = currentDataset.columns.find(col => col.id === mediatorVariable);
    const wColumn = currentDataset.columns.find(col => col.id === moderatorVariable);
    const covColumns = covariateVariables
      .map(id => currentDataset.columns.find(col => col.id === id))
      .filter(Boolean) as Column[];

    if (!xColumn || !yColumn || (analysisType === 'mediation' && !mColumn) || 
        (analysisType === 'moderation' && !wColumn)) {
      throw new Error('Selected variables not found in dataset.');
    }

    // Prepare data arrays
    const xData: number[] = [];
    const yData: number[] = [];
    const mediatorData: number[] = [];
    const moderatorData: number[] = [];
    const covariateData: { [key: string]: number[] } = {};

    // Initialize covariate arrays
    covColumns.forEach(col => {
      covariateData[col.id] = [];
    });

    // Process each row - collect only valid rows
    for (let i = 0; i < currentDataset.data.length; i++) {
      const row = currentDataset.data[i];
      const xVal = row[xColumn.name];
      const yVal = row[yColumn.name];

      // Check if required values are valid numbers
      if (typeof xVal !== 'number' || isNaN(xVal) ||
          typeof yVal !== 'number' || isNaN(yVal)) {
        continue;
      }

      // Check mediator/moderator value
      if (analysisType === 'mediation' && mColumn) {
        const mVal = row[mColumn.name];
        if (typeof mVal !== 'number' || isNaN(mVal)) continue;
        mediatorData.push(mVal);
      } else if (analysisType === 'moderation' && wColumn) {
        const wVal = row[wColumn.name];
        if (typeof wVal !== 'number' || isNaN(wVal)) continue;
        moderatorData.push(wVal);
      }

      // Check covariate values
      let allCovariatesValid = true;
      const currentCovValues: { [key: string]: number } = {};
      
      for (const col of covColumns) {
        const val = row[col.name];
        if (typeof val !== 'number' || isNaN(val)) {
          allCovariatesValid = false;
          break;
        }
        currentCovValues[col.id] = val;
      }

      if (!allCovariatesValid) continue;

      // All values are valid, add to arrays
      xData.push(xVal);
      yData.push(yVal);
      
      // Add covariate values
      covColumns.forEach(col => {
        covariateData[col.id].push(currentCovValues[col.id]);
      });
    }

    if (xData.length < 20) {
      throw new Error('Not enough valid data rows for analysis. Need at least 20 valid rows.');
    }

    // Prepare data for service with correct structure
    const analysisData = {
      x: xData,
      y: yData,
      mediator: analysisType === 'mediation' ? mediatorData : undefined,
      moderator: analysisType === 'moderation' ? moderatorData : undefined,
      covariates: Object.keys(covariateData).length > 0 ? covariateData : undefined,
      confidence_level: confInterval,
      bootstrap_samples: bootstrapSamples
    };

    // Run analysis using service
    if (analysisType === 'mediation') {
      const results = await mediationModerationService.runMediationAnalysis(analysisData);
      
      const extendedResults: ExtendedMediationResults = {
        ...results,
        xColumn,
        yColumn,
        mediatorColumn: mColumn!,
        covariateColumns: covColumns.length > 0 ? covColumns : undefined,
        xName: xColumn.name,
        yName: yColumn.name,
        mediatorName: mColumn!.name,
        covariateNames: covColumns.map(col => col.name)
      };

      setMediationResults(extendedResults);
      localStorage.setItem('mediation_results', JSON.stringify(extendedResults));
    } else {
      const results = await mediationModerationService.runModerationAnalysis(analysisData);
      
      const extendedResults: ExtendedModerationResults = {
        ...results,
        xColumn,
        yColumn,
        moderatorColumn: wColumn!,
        covariateColumns: covColumns.length > 0 ? covColumns : undefined,
        xName: xColumn.name,
        yName: yColumn.name,
        moderatorName: wColumn!.name,
        covariateNames: covColumns.map(col => col.name)
      };

      setModerationResults(extendedResults);
      localStorage.setItem('moderation_results', JSON.stringify(extendedResults));
    }

  } catch (err) {
    console.error('Analysis error:', err);
    setError(`Error in analysis: ${err instanceof Error ? err.message : String(err)}`);
  } finally {
    setLoading(false);
  }
};

  // Generate interpretation for mediation
  const getMediationInterpretation = () => {
    if (!mediationResults) return '';

    const { total_effect, direct_effect, indirect_effect, proportion_mediated,
            total_effect_pvalue, direct_effect_pvalue, indirect_effect_pvalue,
            sobel_test_statistic, sobel_test_pvalue, bootstrap_ci_indirect,
            path_a, path_b, path_c, path_a_pvalue, path_b_pvalue, 
            xName, yName, mediatorName, n_observations, r_squared } = mediationResults;

    let interpretation = '';

    // Overall model interpretation
    interpretation += `A mediation analysis was conducted to examine whether ${mediatorName} mediates the relationship between ${xName} and ${yName}. `;
    interpretation += `The analysis was based on ${n_observations} observations.\n\n`;

    // Model fit
    interpretation += `Model Fit:\n`;
    interpretation += `• The models explained ${(r_squared.model_1 * 100).toFixed(1)}% of the variance in ${mediatorName} `;
    interpretation += `and ${(r_squared.model_2 * 100).toFixed(1)}% of the variance in ${yName}.\n\n`;

    // Path analysis
    interpretation += `Path Analysis:\n`;
    interpretation += `• Path a (${xName} → ${mediatorName}): β = ${path_a.toFixed(2)}, `;
            interpretation += path_a_pvalue < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${path_a_pvalue.toFixed(4)} (not significant)\n`;

            interpretation += `• Path b (${mediatorName} → ${yName}, controlling for ${xName}): β = ${path_b.toFixed(2)}, `;
            interpretation += path_b_pvalue < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${path_b_pvalue.toFixed(4)} (not significant)\n`;

            interpretation += `• Path c (total effect of ${xName} → ${yName}): β = ${path_c.toFixed(2)}, `;
            interpretation += total_effect_pvalue < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${total_effect_pvalue.toFixed(4)} (not significant)\n`;

            interpretation += `• Path c' (direct effect of ${xName} → ${yName}, controlling for ${mediatorName}): β = ${direct_effect.toFixed(2)}, `;
            interpretation += direct_effect_pvalue < 0.05 ? 'p < 0.05 (significant)\n\n' : `p = ${direct_effect_pvalue.toFixed(4)} (not significant)\n\n`;

    // Mediation effects
    interpretation += `Mediation Effects:\n`;
    interpretation += `• Indirect effect (ab): ${indirect_effect.toFixed(2)}\n`;
            interpretation += `• Bootstrap 95% CI: [${bootstrap_ci_indirect[0].toFixed(2)}, ${bootstrap_ci_indirect[1].toFixed(2)}]\n`;
            interpretation += `• Sobel test: z = ${sobel_test_statistic.toFixed(2)}, `;
            interpretation += sobel_test_pvalue < 0.05 ? 'p < 0.05\n' : `p = ${sobel_test_pvalue.toFixed(4)}\n`;

    // Proportion mediated
    if (Math.abs(total_effect) > 0.001) {
      interpretation += `• Proportion mediated: ${(proportion_mediated * 100).toFixed(1)}%\n\n`;
    } else {
      interpretation += `• Proportion mediated: Cannot be calculated (total effect ≈ 0)\n\n`;
    }

    // Conclusion
    interpretation += `Conclusion:\n`;
    const ciExcludesZero = bootstrap_ci_indirect[0] > 0 || bootstrap_ci_indirect[1] < 0;
    
    if (ciExcludesZero && indirect_effect_pvalue < 0.05) {
      interpretation += `There is significant evidence of mediation. ${mediatorName} significantly mediates the relationship between ${xName} and ${yName}. `;
      
      if (direct_effect_pvalue >= 0.05) {
        interpretation += `This appears to be full mediation, as the direct effect is no longer significant when controlling for the mediator.`;
      } else {
        interpretation += `This appears to be partial mediation, as the direct effect remains significant when controlling for the mediator.`;
      }
    } else {
      interpretation += `There is no significant evidence of mediation. ${mediatorName} does not significantly mediate the relationship between ${xName} and ${yName}.`;
    }

    return interpretation;
  };

  // Generate interpretation for moderation
  const getModerationInterpretation = () => {
    if (!moderationResults) return '';

    const { main_effect_x, main_effect_moderator, interaction_effect,
            main_effect_x_pvalue, main_effect_moderator_pvalue, interaction_pvalue,
            simple_slopes, r_squared, f_statistic, f_pvalue,
            xName, yName, moderatorName, n_observations } = moderationResults;

    let interpretation = '';

    // Overall model interpretation
    interpretation += `A moderation analysis was conducted to examine whether ${moderatorName} moderates the relationship between ${xName} and ${yName}. `;
    interpretation += `The analysis was based on ${n_observations} observations.\n\n`;

    // Model fit
    interpretation += `Model Fit:\n`;
    interpretation += `• R² = ${r_squared.toFixed(2)} (${(r_squared * 100).toFixed(1)}% of variance explained)\n`;
            interpretation += `• F(${df1}, ${df2}) = ${f_statistic.toFixed(2)}, `;
            interpretation += f_pvalue < 0.001 ? 'p < 0.001\n\n' : `p = ${f_pvalue.toFixed(4)}\n\n`;

    // Main effects and interaction
    interpretation += `Effects:\n`;
    interpretation += `• Main effect of ${xName}: β = ${main_effect_x.toFixed(2)}, `;
            interpretation += main_effect_x_pvalue < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${main_effect_x_pvalue.toFixed(4)} (not significant)\n`;

            interpretation += `• Main effect of ${moderatorName}: β = ${main_effect_moderator.toFixed(2)}, `;
            interpretation += main_effect_moderator_pvalue < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${main_effect_moderator_pvalue.toFixed(4)} (not significant)\n`;

            interpretation += `• Interaction effect (${xName} × ${moderatorName}): β = ${interaction_effect.toFixed(2)}, `;
            interpretation += interaction_pvalue < 0.05 ? 'p < 0.05 (significant)\n\n' : `p = ${interaction_pvalue.toFixed(4)} (not significant)\n\n`;

    // Simple slopes
    if (simple_slopes && Object.keys(simple_slopes).length > 0) {
      interpretation += `Simple Slopes Analysis:\n`;
      interpretation += `The effect of ${xName} on ${yName} at different levels of ${moderatorName}:\n`;
      
      Object.entries(simple_slopes).forEach(([level, data]: [string, SimpleSlopeData]) => {
        interpretation += `• At ${level}: β = ${data.slope.toFixed(2)}, `;
              interpretation += data.p_value < 0.05 ? 'p < 0.05 (significant)\n' : `p = ${data.p_value.toFixed(4)} (not significant)\n`;
      });
      interpretation += '\n';
    }

    // Conclusion
    interpretation += `Conclusion:\n`;
    
    if (interaction_pvalue < 0.05) {
      interpretation += `There is a significant moderation effect. ${moderatorName} significantly moderates the relationship between ${xName} and ${yName}. `;
      interpretation += `This means that the effect of ${xName} on ${yName} varies depending on the level of ${moderatorName}.`;
      
      if (interaction_effect > 0) {
        interpretation += ` As ${moderatorName} increases, the positive effect of ${xName} on ${yName} becomes stronger.`;
      } else {
        interpretation += ` As ${moderatorName} increases, the positive effect of ${xName} on ${yName} becomes weaker (or more negative).`;
      }
    } else {
      interpretation += `There is no significant moderation effect. ${moderatorName} does not significantly moderate the relationship between ${xName} and ${yName}. `;
      interpretation += `The effect of ${xName} on ${yName} does not vary significantly across different levels of ${moderatorName}.`;
    }

    return interpretation;
  };

  // Render path diagram for mediation
  const renderMediationPathDiagram = () => {
    if (!mediationResults) return null;

    const { path_a, path_b, direct_effect, path_a_pvalue, path_b_pvalue, direct_effect_pvalue } = mediationResults;

    return (
      <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <svg width="800" height="500" viewBox="0 0 500 300">
          {/* Nodes */}
          <rect x="50" y="120" width="100" height="60" fill={theme.palette.primary.light} stroke={theme.palette.primary.main} strokeWidth="2" rx="5" />
          <text x="100" y="155" textAnchor="middle" fill="white" fontWeight="bold" fontSize="10">{mediationResults.xName}</text>
          
          <rect x="350" y="120" width="100" height="60" fill={theme.palette.secondary.light} stroke={theme.palette.secondary.main} strokeWidth="2" rx="5" />
          <text x="400" y="155" textAnchor="middle" fill="white" fontWeight="bold" fontSize="10">{mediationResults.yName}</text>
          
          <rect x="200" y="30" width="100" height="60" fill={theme.palette.info.light} stroke={theme.palette.info.main} strokeWidth="2" rx="5" />
          <text x="250" y="65" textAnchor="middle" fill="white" fontWeight="bold" fontSize="10">{mediationResults.mediatorName}</text>
          
          {/* Path a: X -> M */}
          <line x1="150" y1="140" x2="200" y2="80" stroke={path_a_pvalue < 0.05 ? theme.palette.success.main : theme.palette.grey[500]} strokeWidth="2" markerEnd="url(#arrowhead)" />
          <text x="175" y="110" textAnchor="middle" fill={theme.palette.text.primary} fontSize="10">
            a = {path_a.toFixed(2)}
            {path_a_pvalue < 0.05 && '*'}
          </text>
          
          {/* Path b: M -> Y */}
          <line x1="300" y1="80" x2="350" y2="140" stroke={path_b_pvalue < 0.05 ? theme.palette.success.main : theme.palette.grey[500]} strokeWidth="2" markerEnd="url(#arrowhead)" />
          <text x="310" y="100" fill={theme.palette.text.primary} fontSize="10">
            b = {path_b.toFixed(2)}
            {path_b_pvalue < 0.05 && '*'}
          </text>
          
          {/* Path c': X -> Y (direct) */}
          <line x1="150" y1="160" x2="350" y2="160" stroke={direct_effect_pvalue < 0.05 ? theme.palette.success.main : theme.palette.grey[500]} strokeWidth="2" markerEnd="url(#arrowhead)" />
          <text x="250" y="180" textAnchor="middle" fill={theme.palette.text.primary} fontSize="10">
            c' = {direct_effect.toFixed(2)}
            {direct_effect_pvalue < 0.05 && '*'}
          </text>
          
          {/* Arrow marker */}
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill={theme.palette.text.primary} />
            </marker>
          </defs>
        </svg>
        
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          * indicates p {'<'} 0.05
        </Typography>
      </Box>
    );
  };

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Mediation and Moderation Analysis
      </Typography>

      {/* Python Initialization Status */}
      {initializingPython && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="body2" gutterBottom>
              Initializing Python environment for statistical analysis...
            </Typography>
            <LinearProgress sx={{ mt: 1 }} />
          </Box>
        </Alert>
      )}

      {!pythonReady && !initializingPython && (
        <Alert severity="warning" sx={{ mb: 2 }} action={
          <Button color="inherit" size="small" onClick={initializePython} startIcon={<RefreshIcon />}>
            Retry
          </Button>
        }>
          Python environment not ready. Analysis requires Python libraries to be loaded.
        </Alert>
      )}

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Analysis Type and Variables
        </Typography>

        {/* Analysis Type Selection */}
        <Box display="flex" justifyContent="center" mb={3}>
          <ToggleButtonGroup
            value={analysisType}
            exclusive
            onChange={handleAnalysisTypeChange}
            aria-label="analysis type"
          >
            <ToggleButton value="mediation" aria-label="mediation analysis">
              <Box display="flex" alignItems="center" gap={1}>
                <AccountTreeIcon />
                <Typography>Mediation Analysis</Typography>
              </Box>
            </ToggleButton>
            <ToggleButton value="moderation" aria-label="moderation analysis">
              <Box display="flex" alignItems="center" gap={1}>
                <ShowChartIcon />
                <Typography>Moderation Analysis</Typography>
              </Box>
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>

        {/* Analysis Description */}
        <Alert severity="info" sx={{ mb: 2 }}>
          {analysisType === 'mediation' ? (
            <Box>
              <Typography variant="body2" fontWeight="bold" gutterBottom>
                Mediation Analysis
              </Typography>
              <Typography variant="body2">
                Tests whether a mediator variable (M) explains the relationship between an independent variable (X) and dependent variable (Y).
                Path: X → M → Y
              </Typography>
            </Box>
          ) : (
            <Box>
              <Typography variant="body2" fontWeight="bold" gutterBottom>
                Moderation Analysis
              </Typography>
              <Typography variant="body2">
                Tests whether the relationship between X and Y depends on the level of a moderator variable (W).
                Examines the interaction effect: X × W → Y
              </Typography>
            </Box>
          )}
        </Alert>

        <Grid container spacing={2}>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="independent-variable-label">Independent Variable (X)</InputLabel>
              <Select
                labelId="independent-variable-label"
                id="independent-variable"
                value={independentVariable}
                label="Independent Variable (X)"
                onChange={handleIndependentVariableChange}
                disabled={!currentDataset}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.filter(col => col.id !== dependentVariable && col.id !== mediatorVariable && col.id !== moderatorVariable).map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dependent-variable-label">Dependent Variable (Y)</InputLabel>
              <Select
                labelId="dependent-variable-label"
                id="dependent-variable"
                value={dependentVariable}
                label="Dependent Variable (Y)"
                onChange={handleDependentVariableChange}
                disabled={!currentDataset}
              >
                {numericColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numeric variables available
                  </MenuItem>
                ) : (
                  numericColumns.filter(col => col.id !== independentVariable && col.id !== mediatorVariable && col.id !== moderatorVariable).map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            {analysisType === 'mediation' ? (
              <FormControl fullWidth margin="normal">
                <InputLabel id="mediator-variable-label">Mediator Variable (M)</InputLabel>
                <Select
                  labelId="mediator-variable-label"
                  id="mediator-variable"
                  value={mediatorVariable}
                  label="Mediator Variable (M)"
                  onChange={handleMediatorVariableChange}
                  disabled={!currentDataset}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.filter(col => col.id !== independentVariable && col.id !== dependentVariable).map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            ) : (
              <FormControl fullWidth margin="normal">
                <InputLabel id="moderator-variable-label">Moderator Variable (W)</InputLabel>
                <Select
                  labelId="moderator-variable-label"
                  id="moderator-variable"
                  value={moderatorVariable}
                  label="Moderator Variable (W)"
                  onChange={handleModeratorVariableChange}
                  disabled={!currentDataset}
                >
                  {numericColumns.length === 0 ? (
                    <MenuItem value="" disabled>
                      No numeric variables available
                    </MenuItem>
                  ) : (
                    numericColumns.filter(col => col.id !== independentVariable && col.id !== dependentVariable).map(column => (
                      <MenuItem key={column.id} value={column.id}>
                        {column.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            )}
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="covariate-variables-label">Covariates (Optional)</InputLabel>
              <Select
                labelId="covariate-variables-label"
                id="covariate-variables"
                multiple
                value={covariateVariables}
                label="Covariates (Optional)"
                onChange={handleCovariateVariablesChange}
                disabled={!currentDataset}
                renderValue={(selected) => {
                  const selectedNames = selected.map(id => {
                    const column = availableCovariateColumns.find(col => col.id === id);
                    return column ? column.name : '';
                  }).filter(Boolean);
                  return selectedNames.join(', ');
                }}
              >
                {availableCovariateColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No suitable variables available
                  </MenuItem>
                ) : (
                  availableCovariateColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
              <Typography variant="caption" color="text.secondary">
                Select control variables to include in the analysis.
              </Typography>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="conf-interval-label">Confidence Level</InputLabel>
              <Select
                labelId="conf-interval-label"
                id="conf-interval"
                value={confInterval}
                label="Confidence Level"
                onChange={handleConfIntervalChange}
              >
                <MenuItem value={0.90}>90%</MenuItem>
                <MenuItem value={0.95}>95%</MenuItem>
                <MenuItem value={0.99}>99%</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              margin="normal"
              label="Bootstrap Samples"
              type="number"
              value={bootstrapSamples}
              onChange={handleBootstrapSamplesChange}
              inputProps={{ min: 100, max: 10000, step: 100 }}
              helperText="Number of bootstrap samples (100-10000)"
            />
          </Grid>
        </Grid>

        <Box mt={2}>
          <Typography variant="subtitle2" gutterBottom>
            Display Options
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showPathDiagram}
                    onChange={() => handleDisplayOptionChange('showPathDiagram')}
                  />
                }
                label="Show path diagram (mediation only)"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showStandardizedCoefficients}
                    onChange={() => handleDisplayOptionChange('showStandardizedCoefficients')}
                  />
                }
                label="Show standardized coefficients"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showBootstrapResults}
                    onChange={() => handleDisplayOptionChange('showBootstrapResults')}
                  />
                }
                label="Show bootstrap results"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={displayOptions.showEffectDecomposition}
                    onChange={() => handleDisplayOptionChange('showEffectDecomposition')}
                  />
                }
                label="Show effect decomposition"
              />
            </Grid>
          </Grid>
        </Box>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AssessmentIcon />}
            onClick={runAnalysis}
            disabled={loading || !pythonReady || !independentVariable || !dependentVariable || 
                     (analysisType === 'mediation' && !mediatorVariable) ||
                     (analysisType === 'moderation' && !moderatorVariable)}
          >
            {loading ? 'Running Analysis...' : `Run ${analysisType === 'mediation' ? 'Mediation' : 'Moderation'} Analysis`}
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {(mediationResults || moderationResults) && !loading && (
        <>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="analysis tabs">
                <Tab label="Results Summary" />
                <Tab label="Interpretation" />
                </Tabs>
            </Box>

            {/* Results Summary Tab */}
            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  {analysisType === 'mediation' ? 'Mediation Analysis Results' : 'Moderation Analysis Results'}
                </Typography>

                {analysisType === 'mediation' && mediationResults && (
                  <>
                    {displayOptions.showPathDiagram && renderMediationPathDiagram()}

                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Model Information
                        </Typography>

                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Independent Variable (X)</TableCell>
                                <TableCell>{mediationResults.xName}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Dependent Variable (Y)</TableCell>
                                <TableCell>{mediationResults.yName}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>Mediator Variable (M)</TableCell>
                                <TableCell>{mediationResults.mediatorName}</TableCell>
                              </TableRow>
                              {mediationResults.covariateNames && mediationResults.covariateNames.length > 0 && (
                                <TableRow>
                                  <TableCell>Covariates</TableCell>
                                  <TableCell>{mediationResults.covariateNames.join(', ')}</TableCell>
                                </TableRow>
                              )}
                              <TableRow>
                                <TableCell>Number of Observations</TableCell>
                                <TableCell>{mediationResults.n_observations}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>R² (M ~ X)</TableCell>
                                <TableCell>{mediationResults.r_squared.model_1.toFixed(2)}</TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>R² (Y ~ X + M)</TableCell>
                                <TableCell>{mediationResults.r_squared.model_2.toFixed(2)}</TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Path Coefficients
                        </Typography>

                        <TableContainer>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Path</TableCell>
                                <TableCell align="right">Coefficient</TableCell>
                                <TableCell align="right">SE</TableCell>
                                <TableCell align="right">p-value</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              <TableRow>
                                <TableCell>a (X → M)</TableCell>
                                <TableCell align="right">{mediationResults.path_a.toFixed(2)}</TableCell>
                                <TableCell align="right">{mediationResults.path_a_se.toFixed(2)}</TableCell>
                                <TableCell align="right">
                                  {mediationResults.path_a_pvalue < 0.001 ? '< 0.001' : mediationResults.path_a_pvalue.toFixed(4)}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>b (M → Y)</TableCell>
                                <TableCell align="right">{mediationResults.path_b.toFixed(2)}</TableCell>
                                <TableCell align="right">{mediationResults.path_b_se.toFixed(2)}</TableCell>
                                <TableCell align="right">
                                  {mediationResults.path_b_pvalue < 0.001 ? '< 0.001' : mediationResults.path_b_pvalue.toFixed(4)}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>c (Total Effect)</TableCell>
                                <TableCell align="right">{mediationResults.path_c.toFixed(2)}</TableCell>
                                <TableCell align="right">{mediationResults.total_effect_se.toFixed(2)}</TableCell>
                                <TableCell align="right">
                                  {mediationResults.total_effect_pvalue < 0.001 ? '< 0.001' : mediationResults.total_effect_pvalue.toFixed(4)}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell>c' (Direct Effect)</TableCell>
                                <TableCell align="right">{mediationResults.direct_effect.toFixed(2)}</TableCell>
                                <TableCell align="right">{mediationResults.direct_effect_se.toFixed(2)}</TableCell>
                                <TableCell align="right">
                                  {mediationResults.direct_effect_pvalue < 0.001 ? '< 0.001' : mediationResults.direct_effect_pvalue.toFixed(4)}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>

                        {displayOptions.showEffectDecomposition && (
                          <Box mt={2}>
                            <Typography variant="subtitle2" gutterBottom>
                              Effect Decomposition
                            </Typography>
                            <TableContainer>
                              <Table size="small">
                                <TableBody>
                                  <TableRow>
                                    <TableCell>Total Effect (c)</TableCell>
                                    <TableCell align="right">{mediationResults.total_effect.toFixed(2)}</TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell>Direct Effect (c')</TableCell>
                                    <TableCell align="right">{mediationResults.direct_effect.toFixed(3)}</TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell>Indirect Effect (ab)</TableCell>
                                    <TableCell align="right">{mediationResults.indirect_effect.toFixed(3)}</TableCell>
                                  </TableRow>
                                  <TableRow>
                                    <TableCell>Proportion Mediated</TableCell>
                                    <TableCell align="right">
                                      {Math.abs(mediationResults.total_effect) > 0.001  
                                        ? `${(mediationResults.proportion_mediated * 100).toFixed(1)}%`
                                        : 'N/A'
                                      }
                                    </TableCell>
                                  </TableRow>
                                </TableBody>
                              </Table>
                            </TableContainer>
                          </Box>
                        )}
                      </Grid>

                      {displayOptions.showBootstrapResults && (
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" gutterBottom>
                            Mediation Analysis Results
                          </Typography>
                          <TableContainer>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>Test</TableCell>
                                  <TableCell align="right">Estimate</TableCell>
                                  <TableCell align="right">SE</TableCell>
                                  <TableCell align="right">95% CI</TableCell>
                                  <TableCell align="right">p-value</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                <TableRow>
                                  <TableCell>Indirect Effect (Bootstrap)</TableCell>
                                  <TableCell align="right">{mediationResults.indirect_effect.toFixed(3)}</TableCell>
                                  <TableCell align="right">{mediationResults.indirect_effect_se.toFixed(3)}</TableCell>
                                  <TableCell align="right">
                                    [{mediationResults.bootstrap_ci_indirect[0].toFixed(3)}, {mediationResults.bootstrap_ci_indirect[1].toFixed(3)}]
                                  </TableCell>
                                  <TableCell align="right">
                                    {mediationResults.indirect_effect_pvalue < 0.001 ? '< 0.001' : mediationResults.indirect_effect_pvalue.toFixed(3)}
                                  </TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Sobel Test</TableCell>
                                  <TableCell align="right">{mediationResults.indirect_effect.toFixed(2)}</TableCell>
                                  <TableCell align="right">-</TableCell>
                                  <TableCell align="right">
                                    z = {mediationResults.sobel_test_statistic.toFixed(2)}
                                  </TableCell>
                                  <TableCell align="right">
                                    {mediationResults.sobel_test_pvalue < 0.001 ? '< 0.001' : mediationResults.sobel_test_pvalue.toFixed(4)}
                                  </TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </Grid>
                      )}

                      {displayOptions.showStandardizedCoefficients && mediationResults.standardized_coefficients && (
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" gutterBottom>
                            Standardized Coefficients
                          </Typography>
                          <TableContainer>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>Path</TableCell>
                                  <TableCell align="right">Standardized β</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                <TableRow>
                                  <TableCell>a (X → M)</TableCell>
                                  <TableCell align="right">{mediationResults.standardized_coefficients.path_a.toFixed(2)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>b (M → Y)</TableCell>
                                  <TableCell align="right">{mediationResults.standardized_coefficients.path_b.toFixed(2)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>c' (Direct Effect)</TableCell>
                                  <TableCell align="right">{mediationResults.standardized_coefficients.direct_effect.toFixed(2)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>ab (Indirect Effect)</TableCell>
                                  <TableCell align="right">{mediationResults.standardized_coefficients.indirect_effect.toFixed(2)}</TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </Grid>
                      )}
                    </Grid>
                  </>
                )}

                {analysisType === 'moderation' && moderationResults && (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Model Information
                      </Typography>

                      <TableContainer>
                        <Table size="small">
                          <TableBody>
                            <TableRow>
                              <TableCell>Independent Variable (X)</TableCell>
                              <TableCell>{moderationResults.xName}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Dependent Variable (Y)</TableCell>
                              <TableCell>{moderationResults.yName}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Moderator Variable (W)</TableCell>
                              <TableCell>{moderationResults.moderatorName}</TableCell>
                            </TableRow>
                            {moderationResults.covariateNames && moderationResults.covariateNames.length > 0 && (
                              <TableRow>
                                <TableCell>Covariates</TableCell>
                                <TableCell>{moderationResults.covariateNames.join(', ')}</TableCell>
                              </TableRow>
                            )}
                            <TableRow>
                              <TableCell>Number of Observations</TableCell>
                              <TableCell>{moderationResults.n_observations}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>R²</TableCell>
                              <TableCell>{moderationResults.r_squared.toFixed(2)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Adjusted R²</TableCell>
                              <TableCell>{moderationResults.adjusted_r_squared.toFixed(2)}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>F-statistic</TableCell>
                              <TableCell>
                                {moderationResults.f_statistic.toFixed(2)} 
                                {moderationResults.f_pvalue < 0.001 ? ' (p < 0.001)' : ` (p = ${moderationResults.f_pvalue.toFixed(4)})`}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Regression Coefficients
                      </Typography>

                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Effect</TableCell>
                              <TableCell align="right">Coefficient</TableCell>
                              <TableCell align="right">SE</TableCell>
                              <TableCell align="right">p-value</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell>Intercept</TableCell>
                              <TableCell align="right">{moderationResults.intercept.toFixed(2)}</TableCell>
                              <TableCell align="right">{moderationResults.intercept_se.toFixed(2)}</TableCell>
                              <TableCell align="right">
                                {moderationResults.intercept_pvalue < 0.001 ? '< 0.001' : moderationResults.intercept_pvalue.toFixed(4)}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Main Effect (X)</TableCell>
                              <TableCell align="right">{moderationResults.main_effect_x.toFixed(2)}</TableCell>
                              <TableCell align="right">{moderationResults.main_effect_x_se.toFixed(2)}</TableCell>
                              <TableCell align="right">
                                {moderationResults.main_effect_x_pvalue < 0.001 ? '< 0.001' : moderationResults.main_effect_x_pvalue.toFixed(4)}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>Main Effect (W)</TableCell>
                              <TableCell align="right">{moderationResults.main_effect_moderator.toFixed(2)}</TableCell>
                              <TableCell align="right">{moderationResults.main_effect_moderator_se.toFixed(2)}</TableCell>
                              <TableCell align="right">
                                {moderationResults.main_effect_moderator_pvalue < 0.001 ? '< 0.001' : moderationResults.main_effect_moderator_pvalue.toFixed(4)}
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>
                                <Box display="flex" alignItems="center">
                                  Interaction (X × W)
                                  {moderationResults.interaction_pvalue < 0.05 && (
                                    <Chip label="Significant" size="small" color="success" sx={{ ml: 1 }} />
                                  )}
                                </Box>
                              </TableCell>
                              <TableCell align="right">{moderationResults.interaction_effect.toFixed(2)}</TableCell>
                              <TableCell align="right">{moderationResults.interaction_se.toFixed(2)}</TableCell>
                              <TableCell align="right">
                                {moderationResults.interaction_pvalue < 0.001 ? '< 0.001' : moderationResults.interaction_pvalue.toFixed(4)}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {displayOptions.showStandardizedCoefficients && moderationResults.standardized_coefficients && (
                        <Box mt={2}>
                          <Typography variant="subtitle2" gutterBottom>
                            Standardized Coefficients
                          </Typography>
                          <TableContainer>
                            <Table size="small">
                              <TableBody>
                                <TableRow>
                                  <TableCell>Main Effect (X)</TableCell>
                                  <TableCell align="right">{moderationResults.standardized_coefficients.main_effect_x.toFixed(2)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Main Effect (W)</TableCell>
                                  <TableCell align="right">{moderationResults.standardized_coefficients.main_effect_moderator.toFixed(2)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Interaction (X × W)</TableCell>
                                  <TableCell align="right">{moderationResults.standardized_coefficients.interaction.toFixed(2)}</TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </Box>
                      )}
                    </Grid>

                    {moderationResults.simple_slopes && Object.keys(moderationResults.simple_slopes).length > 0 && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          Simple Slopes Analysis
                        </Typography>
                        <TableContainer>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>Moderator Level</TableCell>
                                <TableCell align="right">Slope</TableCell>
                                <TableCell align="right">SE</TableCell>
                                <TableCell align="right">t-value</TableCell>
                                <TableCell align="right">p-value</TableCell>
                                <TableCell align="right">95% CI</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {Object.entries(moderationResults.simple_slopes).map(([level, data]: [string, SimpleSlopeData]) => (
                                <TableRow key={level}>
                                  <TableCell>{level}</TableCell>
                                  <TableCell align="right">{data.slope.toFixed(2)}</TableCell>
                                  <TableCell align="right">{data.se.toFixed(2)}</TableCell>
                                  <TableCell align="right">{data.t_value.toFixed(2)}</TableCell>
                                  <TableCell align="right">
                                    {data.p_value < 0.001 ? '< 0.001' : data.p_value.toFixed(4)}
                                  </TableCell>
                                  <TableCell align="right">
                                    [{data.ci_lower.toFixed(2)}, {data.ci_upper.toFixed(2)}]
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Grid>
                    )}

                    {displayOptions.showBootstrapResults && moderationResults.bootstrap_ci_interaction && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" gutterBottom>
                          Bootstrap Results
                        </Typography>
                        <TableContainer>
                          <Table size="small">
                            <TableBody>
                              <TableRow>
                                <TableCell>Interaction Effect Bootstrap CI</TableCell>
                                <TableCell align="right">
                                  [{moderationResults.bootstrap_ci_interaction[0].toFixed(2)}, {moderationResults.bootstrap_ci_interaction[1].toFixed(2)}]
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Grid>
                    )}
                  </Grid>
                )}
              </Box>
            )}

            {/* Interpretation Tab */}
            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Statistical Interpretation
                </Typography>

                {(!mediationResults && !moderationResults) ? (
                  <Alert severity="info">
                    Please run an analysis first to see the interpretation.
                  </Alert>
                ) : (
                  <Paper sx={{ p: 3, backgroundColor: theme.palette.grey[50] }}>
                    <Typography 
                      variant="body1" 
                      component="pre" 
                      sx={{ 
                        whiteSpace: 'pre-wrap',
                        fontFamily: 'inherit',
                        lineHeight: 1.6
                      }}
                    >
                      {analysisType === 'mediation' ? getMediationInterpretation() : getModerationInterpretation()}
                    </Typography>
                  </Paper>
                )}
              </Box>
            )}
          </Paper>
        </>
      )}
    </Box>
  );
};

export default MediationModeration;
