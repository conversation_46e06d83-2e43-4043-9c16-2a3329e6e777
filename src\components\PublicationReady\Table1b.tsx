import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  SelectChangeEvent,
  Chip,
  Snackbar,
} from '@mui/material';
import {
  Save as SaveIcon,
} from '@mui/icons-material';
import { useData } from '../../context/DataContext';
import { useResults } from '../../context/ResultsContext';
import AddToResultsButton from '../UI/AddToResultsButton';
import { DataType, Column } from '../../types';
import PublicationReadyGate from '../FeatureGates/PublicationReadyGate';
import {
  calculateMean,
  calculateStandardDeviation,
  calculateMedian,
  calculateQuartiles,
  calculateIQR,
} from '../../utils/stats/descriptive';
import { comprehensiveNormalityTest } from '../../utils/stats/normality';

interface Table1bResult {
  variableName: string;
  n: number;
  mean: number;
  standardDeviation: number;
  median: number;
  q1: number;
  q3: number;
  iqr: number;
  min: number;
  max: number;
  normalityPValue: number;
}

const Table1b: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const { addResult } = useResults();

  // State for selected dataset and variables
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [selectedVariables, setSelectedVariables] = useState<string[]>([]);

  // State for analysis results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [table1bResults, setTable1bResults] = useState<Table1bResult[] | null>(null);
  const [interpretation, setInterpretation] = useState<string | null>(null);

  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');

  // Get the currently selected dataset based on selectedDatasetId
  const selectedDataset = datasets.find(dataset => dataset.id === selectedDatasetId);

  // Get available numerical columns from the selected dataset
  const availableColumns = selectedDataset?.columns.filter(col => col.type === DataType.NUMERIC) || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    setSelectedVariables([]); // Clear selected variables when dataset changes
    setTable1bResults(null); // Clear results
    setInterpretation(null);

    // Update the current dataset in the DataContext
    const datasetToSet = datasets.find(dataset => dataset.id === newDatasetId);
    if (datasetToSet) {
      setCurrentDataset(datasetToSet);
    }
  };

  // Handle variable selection change
  const handleVariableChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedVariables(typeof value === 'string' ? value.split(',') : value);
    setTable1bResults(null); // Clear results when selection changes
    setInterpretation(null);
  };

  // Run Table 1b analysis
  const runTable1bAnalysis = () => {
    if (!selectedDataset || selectedVariables.length === 0) {
      setError('Please select a dataset and at least one numerical variable to analyze.');
      setTable1bResults(null);
      setInterpretation(null);
      return;
    }

    setLoading(true);
    setError(null);
    const results: Table1bResult[] = [];

    selectedVariables.forEach(variableId => {
      const column = availableColumns.find(col => col.id === variableId);

      if (!column) {
        console.error(`Column with ID ${variableId} not found in selected dataset.`);
        return; // Skip if column not found
      }

      const columnData = selectedDataset.data
        .map(row => row[column.name])
        .filter(val => typeof val === 'number' && !isNaN(val)) as number[];

      if (columnData.length > 0) {
        const mean = calculateMean(columnData);
        const standardDeviation = calculateStandardDeviation(columnData);
        const median = calculateMedian(columnData);
        const [q1, , q3] = calculateQuartiles(columnData);
        const iqr = calculateIQR(columnData);
        const min = Math.min(...columnData);
        const max = Math.max(...columnData);
        const normalityTestResult = comprehensiveNormalityTest(columnData, 0.05, ['auto']);
        const normalityTest = {
          isNormal: normalityTestResult.overallAssessment.isNormal,
          pValue: normalityTestResult.tests.shapiroWilk?.pValue ||
                  normalityTestResult.tests.kolmogorovSmirnov?.pValue ||
                  normalityTestResult.tests.jarqueBera?.pValue || NaN,
          statistic: normalityTestResult.tests.shapiroWilk?.statistic ||
                    normalityTestResult.tests.kolmogorovSmirnov?.statistic ||
                    normalityTestResult.tests.jarqueBera?.statistic || NaN
        };

        results.push({
          variableName: column.name,
          n: columnData.length,
          mean,
          standardDeviation,
          median,
          q1,
          q3,
          iqr,
          min,
          max,
          normalityPValue: normalityTest.pValue,
        });
      }
    });

    setTable1bResults(results);
    setInterpretation(generateInterpretation(results));
    setLoading(false);
  };

  // Function to generate contextual interpretation
  const generateInterpretation = (results: Table1bResult[]): string => {
    if (results.length === 0) return '';

    let interpretation = `Table 1b presents the descriptive statistics for ${results.length} numerical variable${results.length > 1 ? 's' : ''} from a dataset of ${results[0]?.n || 0} observations. `;
    interpretation += `The table provides both measures of central tendency and dispersion, tailored to the distribution of each variable.\n\n`;

    // Generate description for each variable
    results.forEach((result, index) => {
      const pValueText = result.normalityPValue < 0.001 ? 'less than 0.001' : result.normalityPValue.toFixed(4);
      const normalityStatement = result.normalityPValue >= 0.05 ? 'indicating that the distribution is normally distributed' : 'indicating that the distribution is not normal';

      interpretation += `**${result.variableName}:** The mean ${result.variableName.toLowerCase()} is ${result.mean.toFixed(2)} with a standard deviation of ${result.standardDeviation.toFixed(2)}. `;
      interpretation += `The median ${result.variableName.toLowerCase()} is ${result.median.toFixed(2)}, with the first quartile (Q1) at ${result.q1.toFixed(2)} and the third quartile (Q3) at ${result.q3.toFixed(2)}. `;
      interpretation += `The interquartile range (IQR) is ${result.iqr.toFixed(2)}, and the ${result.variableName.toLowerCase()} ranges from a minimum of ${result.min.toFixed(2)} to a maximum of ${result.max.toFixed(2)}. `;
      interpretation += `The p-value for normality is ${pValueText}, ${normalityStatement}.`;

      if (index < results.length - 1) {
        interpretation += `\n\n`;
      }
    });

    // Add overall summary
    interpretation += `\n\nOverall, the table provides a comprehensive overview of the central tendencies and dispersions of the variables, highlighting the normality or non-normality of their distributions.`;

    return interpretation;
  };



  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <PublicationReadyGate>
      <Box p={3}>
      <Typography variant="h5" gutterBottom>
        Table 1b: Comprehensive Descriptive Statistics
      </Typography>

      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Select Data and Numerical Variables
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
          This table provides comprehensive descriptive statistics (mean, standard deviation, median, quartiles, range, and normality tests)
          for multiple numerical variables in a publication-ready format.
        </Alert>

        <Grid container spacing={2}>
          {/* Dataset Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="dataset-select-label">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label"
                id="dataset-select"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
                disabled={datasets.length === 0}
              >
                {datasets.length === 0 ? (
                  <MenuItem value="" disabled>
                    No datasets available
                  </MenuItem>
                ) : (
                  datasets.map(dataset => (
                    <MenuItem key={dataset.id} value={dataset.id}>
                      {dataset.name} ({dataset.data.length} rows)
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>

          {/* Variable Selection */}
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="variable-select-label">Numerical Variables</InputLabel>
              <Select
                labelId="variable-select-label"
                id="variable-select"
                multiple
                value={selectedVariables}
                onChange={handleVariableChange}
                label="Numerical Variables"
                disabled={availableColumns.length === 0}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((id) => {
                      const column = availableColumns.find(col => col.id === id);
                      return (
                        <Chip key={id} label={column?.name || ''} size="small" />
                      );
                    })}
                  </Box>
                )}
              >
                {availableColumns.length === 0 ? (
                  <MenuItem value="" disabled>
                    No numerical variables available in selected dataset
                  </MenuItem>
                ) : (
                  availableColumns.map(column => (
                    <MenuItem key={column.id} value={column.id}>
                      {column.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={runTable1bAnalysis}
            disabled={loading || selectedVariables.length === 0 || !selectedDataset}
          >
            Generate Descriptive Statistics Table
          </Button>
        </Box>
      </Paper>

      {loading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {table1bResults && !loading && selectedDataset && (
        <Box>
          <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Table 1b. Descriptive Statistics for Numerical Variables
            </Typography>

            {/* Table Rendering */}
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Variable</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">N</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Mean</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">SD</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Median</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Q1</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Q3</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">IQR</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Min</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Max</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }} align="center">Normality (p-value)</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {table1bResults.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ fontWeight: 'bold' }}>{result.variableName}</TableCell>
                      <TableCell align="center">{result.n}</TableCell>
                      <TableCell align="center">{result.mean.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.standardDeviation.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.median.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.q1.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.q3.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.iqr.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.min.toFixed(2)}</TableCell>
                      <TableCell align="center">{result.max.toFixed(2)}</TableCell>
                      <TableCell align="center">
                        {result.normalityPValue < 0.001 ? '<0.001' : result.normalityPValue.toFixed(4)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Interpretation Section */}
          {interpretation && (
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Statistical Interpretation
              </Typography>
              <Typography variant="body1" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
                {interpretation}
              </Typography>
            </Paper>
          )}

          {/* Add to Results Manager Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <AddToResultsButton
              resultData={{
                title: `Table 1b - Comprehensive Descriptive Statistics (${selectedDataset.name})`,
                type: 'descriptive' as const,
                component: 'Table1b',
                data: {
                  dataset: selectedDataset.name,
                  variables: selectedVariables.map(id =>
                    availableColumns.find(col => col.id === id)?.name || id
                  ),
                  results: table1bResults,
                  interpretation: interpretation,
                  timestamp: new Date().toISOString(),
                  totalSampleSize: selectedDataset.data.length
                }
              }}
              onSuccess={() => {
                setSnackbarMessage('Results successfully added to Results Manager!');
                setSnackbarOpen(true);
              }}
              onError={(error) => {
                setSnackbarMessage(`Error adding results to Results Manager: ${error}`);
                setSnackbarOpen(true);
              }}
            />
          </Box>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
      />
    </Box>
    </PublicationReadyGate>
  );
};

export default Table1b;
