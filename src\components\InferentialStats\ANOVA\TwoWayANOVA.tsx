import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Alert,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Chip,
  Card,
  CardContent,
  IconButton,
  Collapse,
  Stack,
  useMediaQuery,
  alpha,
  LinearProgress,
  Tab,
  Tabs
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  Cell,
  ErrorBar,
  PieChart,
  Pie,
  RadialBarChart,
  RadialBar
} from 'recharts';
import {
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  TrendingUp as TrendingUpIcon,
  Functions as FunctionsIcon,
  Assessment as AssessmentIcon,
  Science as ScienceIcon,
  Description as DescriptionIcon,
  BuildCircle as BuildCircleIcon
} from '@mui/icons-material';
import { useData } from '../../../context/DataContext';
import { DataType } from '../../../types';
import { calculateMean, calculateStandardDeviation } from '@/utils/stats';
import { twoWayANOVA, prepareDataForANOVA, TwoWayANOVAData, ANOVATableRow, CellStats, MarginalStats, AssumptionsTests } from '@/utils/stats/inference/anova/twoWayANOVA';
import { useTableTheme, useStatisticalColors } from '../../../hooks';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`results-tabpanel-${index}`}
      aria-labelledby={`results-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

const TwoWayANOVA: React.FC = () => {
  const { datasets, currentDataset, setCurrentDataset } = useData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { getHeaderCellSx, getRowSx } = useTableTheme();
  const { significant, notSignificant } = useStatisticalColors();

  // State for test options
  const [selectedDatasetId, setSelectedDatasetId] = useState<string>(currentDataset?.id || '');
  const [dependentVariable, setDependentVariable] = useState<string>('');
  const [factorVariable1, setFactorVariable1] = useState<string>('');
  const [factorVariable2, setFactorVariable2] = useState<string>('');

  // State for results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<any | null>(null); // TODO: Create specific TwoWayANOVAResult interface
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    summary: true,
    anova: true,
    descriptive: true,
    posthoc: false,
    assumptions: false
  });
  const [tabValue, setTabValue] = useState(0);

  // Get numeric columns from current dataset
  const numericColumns = currentDataset?.columns.filter(
    col => col.type === DataType.NUMERIC
  ) || [];

  // Get categorical columns for factors
  const categoricalColumns = currentDataset?.columns.filter(
    col => col.type === DataType.CATEGORICAL || col.type === DataType.ORDINAL || col.type === DataType.BOOLEAN
  ) || [];

  // Handle dataset selection change
  const handleDatasetChange = (event: SelectChangeEvent<string>) => {
    const newDatasetId = event.target.value;
    setSelectedDatasetId(newDatasetId);
    const selectedDs = datasets.find(ds => ds.id === newDatasetId);
    if (selectedDs) {
        setCurrentDataset(selectedDs);
    }
    // Reset other selections
    setDependentVariable('');
    setFactorVariable1('');
    setFactorVariable2('');
    setResults(null);
    setError(null);
  };

  const handleDependentVariableChange = (event: SelectChangeEvent<string>) => {
    setDependentVariable(event.target.value);
    setResults(null);
  };

  const handleFactor1Change = (event: SelectChangeEvent<string>) => {
    setFactorVariable1(event.target.value);
    setResults(null);
  };

  const handleFactor2Change = (event: SelectChangeEvent<string>) => {
    setFactorVariable2(event.target.value);
    setResults(null);
  };

  const isFormValid = () => {
    return currentDataset && dependentVariable && factorVariable1 && factorVariable2 && factorVariable1 !== factorVariable2;
  };

  const handleExpandClick = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getEffectSizeInterpretation = (etaSquared: number): { label: string; color: string } => {
    if (etaSquared < 0.01) return { label: 'Negligible', color: theme.palette.grey[500] };
    if (etaSquared < 0.06) return { label: 'Small', color: theme.palette.info.main };
    if (etaSquared < 0.14) return { label: 'Medium', color: theme.palette.warning.main };
    return { label: 'Large', color: theme.palette.success.main };
  };

  const runTwoWayANOVA = () => {
    if (!isFormValid()) {
      setError('Please select a dataset, a dependent variable, and two different factor variables.');
      return;
    }
    setLoading(true);
    setError(null);
    setResults(null);

    setTimeout(() => {
      try {
        const dependentCol = currentDataset!.columns.find(col => col.id === dependentVariable)!;
        const factor1Col = currentDataset!.columns.find(col => col.id === factorVariable1)!;
        const factor2Col = currentDataset!.columns.find(col => col.id === factorVariable2)!;

        const dependentName = dependentCol.name;
        const factor1Name = factor1Col.name;
        const factor2Name = factor2Col.name;

        // Prepare data for the twoWayANOVA function
        const anovaInputData: TwoWayANOVAData[] = prepareDataForANOVA(
          currentDataset!.data,
          dependentName,
          factor1Name,
          factor2Name
        );

        // Run the actual Two-Way ANOVA calculation
        const anovaResults = twoWayANOVA(anovaInputData, factor1Name, factor2Name);

        const factor1Levels = [...new Set(anovaInputData.map(d => d.factorA))];
        const factor2Levels = [...new Set(anovaInputData.map(d => d.factorB))];

        // Extract ANOVA table rows for easier access
        const anovaRowFactor1 = anovaResults.anovaTable.find(row => row.source === factor1Name);
        const anovaRowFactor2 = anovaResults.anovaTable.find(row => row.source === factor2Name);
        const anovaRowInteraction = anovaResults.anovaTable.find(row => row.source === `${factor1Name} × ${factor2Name}`);
        const anovaRowError = anovaResults.anovaTable.find(row => row.source === 'Error');

        // Generate comprehensive interpretation
        const mainEffect1Sig = anovaRowFactor1?.p !== null && anovaRowFactor1?.p !== undefined && anovaRowFactor1.p < 0.05;
        const mainEffect2Sig = anovaRowFactor2?.p !== null && anovaRowFactor2?.p !== undefined && anovaRowFactor2.p < 0.05;
        const interactionSig = anovaRowInteraction?.p !== null && anovaRowInteraction?.p !== undefined && anovaRowInteraction.p < 0.05;

        let interpretation = {
          summary: `A ${factor1Levels.length} × ${factor2Levels.length} between-subjects ANOVA was conducted to examine the effects of ${factor1Name} and ${factor2Name} on ${dependentName}.`,
          mainEffects: {
            factor1: {
              significant: mainEffect1Sig,
              text: `The main effect of ${factor1Name} was ${mainEffect1Sig ? 'statistically significant' : 'not statistically significant'}, F(${anovaRowFactor1?.df}, ${anovaRowError?.df}) = ${anovaRowFactor1?.F?.toFixed(2)}, p = ${anovaRowFactor1?.p?.toFixed(4)}, η² = ${anovaRowFactor1?.etaSquared?.toFixed(2)}.`,
              effectSize: getEffectSizeInterpretation(anovaRowFactor1?.etaSquared || 0)
            },
            factor2: {
              significant: mainEffect2Sig,
              text: `The main effect of ${factor2Name} was ${mainEffect2Sig ? 'statistically significant' : 'not statistically significant'}, F(${anovaRowFactor2?.df}, ${anovaRowError?.df}) = ${anovaRowFactor2?.F?.toFixed(2)}, p = ${anovaRowFactor2?.p?.toFixed(4)}, η² = ${anovaRowFactor2?.etaSquared?.toFixed(2)}.`,
              effectSize: getEffectSizeInterpretation(anovaRowFactor2?.etaSquared || 0)
            }
          },
          interaction: {
            significant: interactionSig,
            text: `The interaction effect between ${factor1Name} and ${factor2Name} was ${interactionSig ? 'statistically significant' : 'not statistically significant'}, F(${anovaRowInteraction?.df}, ${anovaRowError?.df}) = ${anovaRowInteraction?.F?.toFixed(2)}, p = ${anovaRowInteraction?.p?.toFixed(4)}, η² = ${anovaRowInteraction?.etaSquared?.toFixed(2)}.`,
            effectSize: getEffectSizeInterpretation(anovaRowInteraction?.etaSquared || 0),
            interpretation: interactionSig 
              ? `The significant interaction suggests that the effect of ${factor1Name} on ${dependentName} differs depending on the level of ${factor2Name}. Simple main effects analysis is recommended to understand the nature of this interaction.`
              : `The non-significant interaction suggests that the effect of ${factor1Name} on ${dependentName} is consistent across levels of ${factor2Name}, and vice versa.`
          }
        };

        const finalResults = {
          dependentVariable: dependentName,
          factor1: factor1Name,
          factor2: factor2Name,
          factor1Levels: factor1Levels,
          factor2Levels: factor2Levels,
          anovaTable: anovaResults.anovaTable,
          descriptiveStats: anovaResults.descriptiveStats,
          marginalMeansFactor1: anovaResults.marginalMeansFactor1,
          marginalMeansFactor2: anovaResults.marginalMeansFactor2,
          interpretation: interpretation,
          assumptions: anovaResults.assumptions,
          totalN: anovaResults.totalN,
          timestamp: new Date(),
        };
        setResults(finalResults);
        setTabValue(0); // Reset to summary tab when new results are generated
      } catch (e) {
        const errorMessage = e instanceof Error ? e.message : 'An error occurred during Two-Way ANOVA calculation.';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }, 1500);
  };

  const downloadResults = () => {
    if (!results) return;
    
    // Prepare data for download
    const resultText = `Two-Way ANOVA Results
=====================================
Analysis Date: ${results.timestamp.toLocaleString()}
Dependent Variable: ${results.dependentVariable}
Factor 1: ${results.factor1}
Factor 2: ${results.factor2}
Total N: ${results.totalN}

ANOVA Summary Table
-------------------
${results.anovaTable.map((row: any) => 
  `${row.source}: F(${row.df || '-'}) = ${row.F?.toFixed(2) || '-'}, p = ${row.p < 0.001 ? '< 0.001' : row.p?.toFixed(4) || '-'}, η² = ${row.etaSquared?.toFixed(2) || '-'}`
).join('\n')}

Interpretation
--------------
${results.interpretation.summary}
${results.interpretation.mainEffects.factor1.text}
${results.interpretation.mainEffects.factor2.text}
${results.interpretation.interaction.text}
${results.interpretation.interaction.interpretation}

Descriptive Statistics
---------------------
${results.descriptiveStats.map((stat: any) => 
  `${results.factor1}: ${stat.factor1Level}, ${results.factor2}: ${stat.factor2Level} - Mean: ${stat.mean.toFixed(2)}, SD: ${stat.sd.toFixed(2)}, N: ${stat.N}`
).join('\n')}
`;
    
    const blob = new Blob([resultText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `two-way-anova-results-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box>
      <Paper elevation={0} sx={{ p: 3, mb: 3, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
        <Stack direction="row" alignItems="center" spacing={2} mb={3}>
          <ScienceIcon sx={{ color: theme.palette.primary.main, fontSize: 28 }} />
          <Typography variant="h6" fontWeight="bold">Two-Way ANOVA Configuration</Typography>
        </Stack>

        {/* Info notice */}
        <Alert severity="info" icon={<InfoIcon />} sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Two-Way ANOVA:</strong> This component performs a complete two-way analysis of variance
            on your data, calculating descriptive statistics, F-statistics, p-values, and effect sizes.
          </Typography>
        </Alert>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="dataset-select-label-twoway">Dataset</InputLabel>
              <Select
                labelId="dataset-select-label-twoway"
                value={selectedDatasetId}
                label="Dataset"
                onChange={handleDatasetChange}
              >
                {datasets.map(ds => (
                  <MenuItem key={ds.id} value={ds.id}>
                    {ds.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={8}>
            {currentDataset && (
              <Alert severity="info" sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                Dataset contains {currentDataset.data.length} observations
              </Alert>
            )}
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 1 }} />
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="dependent-variable-label">Dependent Variable</InputLabel>
              <Select
                labelId="dependent-variable-label"
                value={dependentVariable}
                label="Dependent Variable"
                onChange={handleDependentVariableChange}
                disabled={!currentDataset}
              >
                {numericColumns.map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <FunctionsIcon sx={{ fontSize: 18, color: theme.palette.text.secondary }} />
                      <span>{column.name}</span>
                    </Stack>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="factor-1-label">Factor 1 (Between-Subjects)</InputLabel>
              <Select
                labelId="factor-1-label"
                value={factorVariable1}
                label="Factor 1 (Between-Subjects)"
                onChange={handleFactor1Change}
                disabled={!currentDataset}
              >
                {categoricalColumns.filter(c => c.id !== factorVariable2).map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="factor-2-label">Factor 2 (Between-Subjects)</InputLabel>
              <Select
                labelId="factor-2-label"
                value={factorVariable2}
                label="Factor 2 (Between-Subjects)"
                onChange={handleFactor2Change}
                disabled={!currentDataset}
              >
                {categoricalColumns.filter(c => c.id !== factorVariable1).map(column => (
                  <MenuItem key={column.id} value={column.id}>
                    {column.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => {
              setDependentVariable('');
              setFactorVariable1('');
              setFactorVariable2('');
              setResults(null);
              setError(null);
            }}
            disabled={loading}
          >
            Clear
          </Button>
          <Button
            variant="contained"
            onClick={runTwoWayANOVA}
            disabled={!isFormValid() || loading}
            startIcon={loading ? <CircularProgress size={20} /> : <AssessmentIcon />}
          >
            {loading ? 'Running Analysis...' : 'Run Two-Way ANOVA'}
          </Button>
        </Box>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} icon={<ErrorIcon />}>
          {error}
        </Alert>
      )}

      {loading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
        </Box>
      )}

      {results && (
        <Paper elevation={0} sx={{ p: 3, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Typography variant="h6" fontWeight="bold">Analysis Results</Typography>
            <Stack direction="row" spacing={1}>
              <Chip
                label="Statistical analysis results"
                size="small"
                color="success"
                variant="outlined"
              />
              <IconButton onClick={downloadResults} color="primary" title="Download Results">
                <DownloadIcon />
              </IconButton>
            </Stack>
          </Stack>

          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="results tabs">
              <Tab label="Summary" icon={<DescriptionIcon />} iconPosition="start" />
              <Tab label="ANOVA Table" icon={<FunctionsIcon />} iconPosition="start" />
              <Tab label="Descriptive Stats" icon={<AssessmentIcon />} iconPosition="start" />
              <Tab label="Visualizations" icon={<TrendingUpIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            {/* Summary Tab */}
            <Grid container spacing={3}>
              {/* Key Results Cards */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <InfoIcon fontSize="small" />
                  Key Findings
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Card variant="outlined" sx={{ height: '100%' }}>
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Main Effect: {results.factor1}
                        </Typography>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          {results.interpretation.mainEffects.factor1.significant ? (
                            <CheckCircleIcon sx={{ color: theme.palette.success.main }} />
                          ) : (
                            <WarningIcon sx={{ color: theme.palette.grey[400] }} />
                          )}
                          <Typography variant="h6" fontWeight="bold">
                            {results.interpretation.mainEffects.factor1.significant ? 'Significant' : 'Not Significant'}
                          </Typography>
                        </Stack>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          F({results.anovaTable[0].df}, {results.anovaTable[3].df}) = {results.anovaTable[0].F?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2">
                          p = {results.anovaTable[0].p?.toFixed(3)}
                        </Typography>
                        <Chip
                          label={`Effect Size: ${results.interpretation.mainEffects.factor1.effectSize.label}`}
                          size="small"
                          sx={{
                            mt: 1,
                            bgcolor: alpha(results.interpretation.mainEffects.factor1.effectSize.color, 0.1),
                            color: results.interpretation.mainEffects.factor1.effectSize.color
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Card variant="outlined" sx={{ height: '100%' }}>
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Main Effect: {results.factor2}
                        </Typography>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          {results.interpretation.mainEffects.factor2.significant ? (
                            <CheckCircleIcon sx={{ color: theme.palette.success.main }} />
                          ) : (
                            <WarningIcon sx={{ color: theme.palette.grey[400] }} />
                          )}
                          <Typography variant="h6" fontWeight="bold">
                            {results.interpretation.mainEffects.factor2.significant ? 'Significant' : 'Not Significant'}
                          </Typography>
                        </Stack>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          F({results.anovaTable[1].df}, {results.anovaTable[3].df}) = {results.anovaTable[1].F?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2">
                          p = {results.anovaTable[1].p?.toFixed(3)}
                        </Typography>
                        <Chip
                          label={`Effect Size: ${results.interpretation.mainEffects.factor2.effectSize.label}`}
                          size="small"
                          sx={{
                            mt: 1,
                            bgcolor: alpha(results.interpretation.mainEffects.factor2.effectSize.color, 0.1),
                            color: results.interpretation.mainEffects.factor2.effectSize.color
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Card variant="outlined" sx={{ height: '100%' }}>
                      <CardContent>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Interaction Effect
                        </Typography>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          {results.interpretation.interaction.significant ? (
                            <CheckCircleIcon sx={{ color: theme.palette.success.main }} />
                          ) : (
                            <WarningIcon sx={{ color: theme.palette.grey[400] }} />
                          )}
                          <Typography variant="h6" fontWeight="bold">
                            {results.interpretation.interaction.significant ? 'Significant' : 'Not Significant'}
                          </Typography>
                        </Stack>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          F({results.anovaTable[2].df}, {results.anovaTable[3].df}) = {results.anovaTable[2].F?.toFixed(2)}
                        </Typography>
                        <Typography variant="body2">
                          p = {results.anovaTable[2].p?.toFixed(3)}
                        </Typography>
                        <Chip
                          label={`Effect Size: ${results.interpretation.interaction.effectSize.label}`}
                          size="small"
                          sx={{
                            mt: 1,
                            bgcolor: alpha(results.interpretation.interaction.effectSize.color, 0.1),
                            color: results.interpretation.interaction.effectSize.color
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Grid>

              {/* Interpretation */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <InfoIcon fontSize="small" />
                  Statistical Interpretation
                </Typography>
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 3, 
                    bgcolor: alpha(theme.palette.primary.main, 0.02),
                    borderColor: theme.palette.primary.main,
                    borderStyle: 'dashed'
                  }}
                >
                  <Stack spacing={2}>
                    <Typography variant="body1">{results.interpretation.summary}</Typography>
                    <Divider />
                    <Typography variant="body2">{results.interpretation.mainEffects.factor1.text}</Typography>
                    <Typography variant="body2">{results.interpretation.mainEffects.factor2.text}</Typography>
                    <Typography variant="body2">{results.interpretation.interaction.text}</Typography>
                    <Alert severity={results.interpretation.interaction.significant ? "warning" : "info"} sx={{ mt: 2 }}>
                      {results.interpretation.interaction.interpretation}
                    </Alert>
                  </Stack>
                </Paper>
              </Grid>

              {/* Assumptions Check */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                      <Typography variant="subtitle1" fontWeight="bold">
                        Assumptions Check
                      </Typography>
                      <IconButton 
                        onClick={() => handleExpandClick('assumptions')}
                        size="small"
                      >
                        {expandedSections.assumptions ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </Stack>
                    <Collapse in={expandedSections.assumptions}>
                      <Stack spacing={2} sx={{ mt: 2 }}>
                        <Stack direction="row" alignItems="center" spacing={2}>
                          {results.assumptions.normality.passed ? (
                            <CheckCircleIcon color="success" />
                          ) : (
                            <ErrorIcon color="error" />
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Normality ({results.assumptions.normality.test})
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              W = {results.assumptions.normality.statistic.toFixed(3)}, p = {results.assumptions.normality.p.toFixed(3)}
                            </Typography>
                          </Box>
                        </Stack>
                        <Stack direction="row" alignItems="center" spacing={2}>
                          {results.assumptions.homogeneity.passed ? (
                            <CheckCircleIcon color="success" />
                          ) : (
                            <ErrorIcon color="error" />
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Homogeneity of Variance ({results.assumptions.homogeneity.test})
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              F = {results.assumptions.homogeneity.statistic.toFixed(3)}, p = {results.assumptions.homogeneity.p.toFixed(3)}
                            </Typography>
                          </Box>
                        </Stack>
                        <Stack direction="row" alignItems="center" spacing={2}>
                          <CheckCircleIcon color="success" />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Independence of Observations
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {results.assumptions.independence.note}
                            </Typography>
                          </Box>
                        </Stack>
                      </Stack>
                    </Collapse>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {/* ANOVA Table Tab */}
            <Alert severity="info" sx={{ mb: 2 }}>
              The ANOVA table shows the complete analysis results including sum of squares (SS), degrees of freedom (df), mean squares (MS), F-statistics, p-values, and effect sizes (η²).
            </Alert>
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell sx={getHeaderCellSx()}>Source of Variation</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>SS</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>df</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>MS</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>F</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>p-value</TableCell>
                    <TableCell align="right" sx={getHeaderCellSx()}>η²</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {results.anovaTable.map((row: any) => (
                    <TableRow
                      key={row.source}
                      sx={getRowSx(row.p !== null && row.p < 0.05)}
                    >
                      <TableCell component="th" scope="row" sx={{ fontWeight: row.source === 'Total' ? 'bold' : 'normal' }}>
                        {row.source}
                      </TableCell>
                      <TableCell align="right">{row.SS?.toFixed(2)}</TableCell>
                      <TableCell align="right">{row.df}</TableCell>
                      <TableCell align="right">{row.MS?.toFixed(2) ?? '-'}</TableCell>
                      <TableCell align="right">{row.F?.toFixed(2) ?? '-'}</TableCell>
                      <TableCell 
                        align="right"
                        sx={{ 
                          fontWeight: row.p !== null && row.p < 0.05 ? 'bold' : 'normal',
                          color: row.p !== null && row.p < 0.05 ? theme.palette.success.main : 'inherit'
                        }}
                      >
                        {row.p === null ? '-' : (row.p < 0.001 ? '< 0.001' : row.p.toFixed(4))}
                      </TableCell>
                      <TableCell align="right">
                        {row.etaSquared ? (
                          <Tooltip title={`Effect size: ${getEffectSizeInterpretation(row.etaSquared).label}`}>
                            <span>{row.etaSquared.toFixed(2)}</span>
                          </Tooltip>
                        ) : '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                <strong>Note:</strong> η² (eta squared) represents the proportion of variance explained by each factor. 
                Effect sizes: Small (0.01), Medium (0.06), Large (0.14).
              </Typography>
            </Alert>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            {/* Descriptive Statistics Tab */}
            <Alert severity="info" sx={{ mb: 2 }}>
              This section shows descriptive statistics for all factor level combinations and marginal means.
            </Alert>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Cell Means and Standard Deviations
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.08) }}>
                        <TableCell sx={{ fontWeight: 'bold' }}>{results.factor1}</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>{results.factor2}</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>N</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>Mean</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SD</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SE</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>95% CI</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {results.descriptiveStats.map((stat: any, index: number) => (
                        <TableRow key={index} sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.02) } }}>
                          <TableCell>{stat.factor1Level}</TableCell>
                          <TableCell>{stat.factor2Level}</TableCell>
                          <TableCell align="right">{stat.N}</TableCell>
                          <TableCell align="right">{isNaN(stat.mean) ? '-' : stat.mean.toFixed(2)}</TableCell>
                          <TableCell align="right">{isNaN(stat.sd) ? '-' : stat.sd.toFixed(2)}</TableCell>
                          <TableCell align="right">{isNaN(stat.se) ? '-' : stat.se.toFixed(2)}</TableCell>
                          <TableCell align="right">
                            {isNaN(stat.ci95Lower) ? '-' : `[${stat.ci95Lower.toFixed(2)}, ${stat.ci95Upper.toFixed(2)}]`}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Marginal Means - {results.factor1}
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.08) }}>
                        <TableCell sx={{ fontWeight: 'bold' }}>{results.factor1}</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>N</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>Mean</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SD</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SE</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(results.marginalMeansFactor1).map(([level, stats]: [string, any]) => (
                        <TableRow key={level}>
                          <TableCell>{level}</TableCell>
                          <TableCell align="right">{stats.n}</TableCell>
                          <TableCell align="right">{stats.mean.toFixed(2)}</TableCell>
                          <TableCell align="right">{stats.sd.toFixed(2)}</TableCell>
                          <TableCell align="right">{stats.se.toFixed(2)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Marginal Means - {results.factor2}
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.08) }}>
                        <TableCell sx={{ fontWeight: 'bold' }}>{results.factor2}</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>N</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>Mean</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SD</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>SE</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(results.marginalMeansFactor2).map(([level, stats]: [string, any]) => (
                        <TableRow key={level}>
                          <TableCell>{level}</TableCell>
                          <TableCell align="right">{stats.n}</TableCell>
                          <TableCell align="right">{stats.mean.toFixed(2)}</TableCell>
                          <TableCell align="right">{stats.sd.toFixed(2)}</TableCell>
                          <TableCell align="right">{stats.se.toFixed(2)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            {/* Visualizations Tab */}
            <Grid container spacing={3}>
              <Grid item xs={12} lg={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Interaction Plot
                    </Typography>
                    <Box sx={{ height: 400, mt: 2 }}>
                      {(() => {
                        const factor1Levels = [...new Set(results.descriptiveStats.map((s: CellStats) => String(s.factor1Level)))] as string[];
                        const factor2Levels = [...new Set(results.descriptiveStats.map((s: CellStats) => String(s.factor2Level)))] as string[];
                        
                        const plotData = factor1Levels.map((f1Level) => {
                          const dataPoint: { name: string; [key: string]: any } = { name: f1Level };
                          factor2Levels.forEach((f2Level) => {
                            const stat = results.descriptiveStats.find((s: CellStats) => 
                              String(s.factor1Level) === f1Level && String(s.factor2Level) === f2Level
                            );
                            dataPoint[f2Level] = stat && !isNaN(stat.mean) ? stat.mean : undefined;
                            dataPoint[`${f2Level}_error`] = stat && !isNaN(stat.se) ? stat.se * 1.96 : undefined;
                          });
                          return dataPoint;
                        });
                        
                        const colors = [
                          theme.palette.primary.main,
                          theme.palette.secondary.main,
                          theme.palette.error.main,
                          theme.palette.warning.main
                        ];

                        return (
                          <ResponsiveContainer width="100%" height="100%">
                            <LineChart data={plotData} margin={{ top: 5, right: 30, left: 20, bottom: 40 }}>
                              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                              <XAxis 
                                dataKey="name" 
                                label={{ 
                                  value: results.factor1, 
                                  position: 'insideBottom', 
                                  offset: -10,
                                  style: { fontWeight: 'bold' }
                                }} 
                                padding={{ left: 20, right: 20 }}
                              />
                              <YAxis 
                                label={{ 
                                  value: `Mean ${results.dependentVariable}`, 
                                  angle: -90, 
                                  position: 'insideLeft',
                                  style: { fontWeight: 'bold' }
                                }} 
                              />
                              <RechartsTooltip 
                                contentStyle={{
                                  backgroundColor: theme.palette.background.paper,
                                  border: `1px solid ${theme.palette.divider}`,
                                  borderRadius: 4
                                }}
                              />
                              <Legend 
                                wrapperStyle={{ paddingTop: '20px' }}
                                iconType="line"
                              />
                              {factor2Levels.map((f2Level, index) => (
                                <Line 
                                  key={f2Level}
                                  type="monotone" 
                                  dataKey={f2Level}
                                  stroke={colors[index % colors.length]} 
                                  strokeWidth={2}
                                  dot={{ r: 4 }}
                                  activeDot={{ r: 6 }} 
                                  name={`${results.factor2}: ${f2Level}`}
                                />
                              ))}
                            </LineChart>
                          </ResponsiveContainer>
                        );
                      })()}
                    </Box>
                    {results.interpretation.interaction.significant && (
                      <Alert severity="warning" sx={{ mt: 2 }}>
                        The lines are not parallel, indicating a significant interaction effect.
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} lg={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Marginal Means Comparison
                    </Typography>
                    <Box sx={{ height: 400, mt: 2 }}>
                      {(() => {
                        const marginalData = [
                          ...Object.entries(results.marginalMeansFactor1).map(([level, stats]) => ({
                            factor: results.factor1,
                            level: level,
                            mean: (stats as MarginalStats).mean,
                            se: (stats as MarginalStats).se
                          })),
                          ...Object.entries(results.marginalMeansFactor2).map(([level, stats]) => ({
                            factor: results.factor2,
                            level: level,
                            mean: (stats as MarginalStats).mean,
                            se: (stats as MarginalStats).se
                          }))
                        ];

                        return (
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={marginalData} margin={{ top: 20, right: 30, left: 20, bottom: 40 }}>
                              <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                              <XAxis 
                                dataKey="level"
                                label={{ 
                                  value: 'Factor Levels', 
                                  position: 'insideBottom', 
                                  offset: -10,
                                  style: { fontWeight: 'bold' }
                                }}
                              />
                              <YAxis 
                                label={{ 
                                  value: `Mean ${results.dependentVariable}`, 
                                  angle: -90, 
                                  position: 'insideLeft',
                                  style: { fontWeight: 'bold' }
                                }}
                              />
                              <RechartsTooltip 
                                contentStyle={{
                                  backgroundColor: theme.palette.background.paper,
                                  border: `1px solid ${theme.palette.divider}`,
                                  borderRadius: 4
                                }}
                              />
                              <Bar dataKey="mean" fill={theme.palette.primary.main}>
                                {marginalData.map((entry: { factor: string; level: string; mean: number; se: number }, index: number) => (
                                  <Cell 
                                    key={`cell-${index}`} 
                                    fill={entry.factor === results.factor1 ? theme.palette.primary.main : theme.palette.secondary.main} 
                                  />
                                ))}
                                <ErrorBar dataKey="se" width={4} stroke={theme.palette.grey[700]} />
                              </Bar>
                            </BarChart>
                          </ResponsiveContainer>
                        );
                      })()}
                    </Box>
                    <Stack direction="row" spacing={2} sx={{ mt: 2, justifyContent: 'center' }}>
                      <Chip 
                        label={results.factor1} 
                        sx={{ bgcolor: theme.palette.primary.main, color: 'white' }}
                      />
                      <Chip 
                        label={results.factor2} 
                        sx={{ bgcolor: theme.palette.secondary.main, color: 'white' }}
                      />
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Effect Size Visualization (Variance Explained)
                    </Typography>
                    <Box sx={{ height: 300, mt: 2 }}>
                      {(() => {
                        const effectSizeData = results.anovaTable
                          .filter((row: ANOVATableRow) => row.etaSquared !== null && row.etaSquared !== undefined)
                          .map((row: ANOVATableRow) => ({
                            name: row.source,
                            value: row.etaSquared! * 100,
                            fill: getEffectSizeInterpretation(row.etaSquared!).color
                          }));

                        // Add unexplained variance
                        const totalExplained = effectSizeData.reduce((sum: number, item: { value: number }) => sum + item.value, 0);
                        effectSizeData.push({
                          name: 'Unexplained',
                          value: 100 - totalExplained,
                          fill: theme.palette.grey[300]
                        });

                        return (
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Pie
                                data={effectSizeData}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                dataKey="value"
                                label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                              >
                                {effectSizeData.map((entry: { fill: string }, index: number) => (
                                  <Cell key={`cell-${index}`} fill={entry.fill} />
                                ))}
                              </Pie>
                              <RechartsTooltip 
                                formatter={(value: any) => `${value.toFixed(1)}%`}
                                contentStyle={{
                                  backgroundColor: theme.palette.background.paper,
                                  border: `1px solid ${theme.palette.divider}`,
                                  borderRadius: 4
                                }}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        );
                      })()}
                    </Box>
                    <Stack direction="row" spacing={2} sx={{ mt: 2, justifyContent: 'center' }}>
                      <Chip label="Negligible: < 1%" size="small" sx={{ bgcolor: theme.palette.grey[500], color: 'white' }} />
                      <Chip label="Small: 1-6%" size="small" sx={{ bgcolor: theme.palette.info.main, color: 'white' }} />
                      <Chip label="Medium: 6-14%" size="small" sx={{ bgcolor: theme.palette.warning.main, color: 'white' }} />
                      <Chip label="Large: > 14%" size="small" sx={{ bgcolor: theme.palette.success.main, color: 'white' }} />
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Analysis performed on: {results.timestamp.toLocaleString()}
            </Typography>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default TwoWayANOVA;
