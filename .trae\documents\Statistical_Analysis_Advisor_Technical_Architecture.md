# Statistical Analysis Advisor - Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[Analysis Assistant Component]
    C --> D[Statistical Analysis Advisor Tab]
    D --> E[Decision Tree Engine]
    D --> F[Recommendation Service]
    D --> G[Dataset Context Provider]
    E --> H[Question Logic Manager]
    F --> I[Method Ranking Algorithm]
    G --> J[Data Analysis Service]
    B --> K[Supabase SDK]
    K --> L[Supabase Service]
    
    subgraph "Frontend Layer"
        B
        C
        D
        E
        F
        G
    end
    
    subgraph "Service Layer"
        H
        I
        J
    end
    
    subgraph "Data Layer (Provided by Supabase)"
        L
    end
```

## 2. Technology Description

* **Frontend**: React\@18 + TypeScript\@5 + Material-UI\@5 + Vite\@5

* **State Management**: React Context API + Custom Hooks

* **Data Processing**: Custom statistical analysis utilities

* **Backend**: Supabase (PostgreSQL database, authentication)

* **Styling**: Material-UI sx prop + Custom theme

## 3. Route Definitions

| Route                             | Purpose                                          |
| --------------------------------- | ------------------------------------------------ |
| /analysis-assistant               | Main Analysis Assistant page with tab navigation |
| /analysis-assistant?tab=advisor   | Direct link to Statistical Analysis Advisor tab  |
| /analysis-assistant?tab=assistant | AI Analysis Assistant tab (existing)             |
| /analysis-assistant?tab=quality   | AI Data Quality Assistant tab (existing)         |

## 4. Component Architecture

### 4.1 Core Components

**StatisticalAnalysisAdvisor Component**

```typescript
interface StatisticalAnalysisAdvisorProps {
  currentDataset?: Dataset;
  onNavigateToTool: (toolPath: string, config?: AnalysisConfig) => void;
}

interface AdvisorState {
  currentStep: number;
  responses: QuestionResponse[];
  recommendations: MethodRecommendation[];
  isProcessing: boolean;
  selectedMethod?: MethodRecommendation;
}
```

**DecisionTreeEngine Component**

```typescript
interface DecisionTreeEngineProps {
  questions: Question[];
  currentStep: number;
  responses: QuestionResponse[];
  onResponseChange: (response: QuestionResponse) => void;
  onNext: () => void;
  onPrevious: () => void;
}

interface Question {
  id: string;
  type: 'single-choice' | 'multiple-choice' | 'scale' | 'text';
  title: string;
  description?: string;
  options?: QuestionOption[];
  validation?: ValidationRule[];
  conditionalLogic?: ConditionalRule[];
}
```

**RecommendationEngine Component**

```typescript
interface RecommendationEngineProps {
  responses: QuestionResponse[];
  datasetContext?: DatasetContext;
  onRecommendationsGenerated: (recommendations: MethodRecommendation[]) => void;
}

interface MethodRecommendation {
  id: string;
  name: string;
  category: AnalysisCategory;
  suitabilityScore: number;
  confidence: number;
  reasoning: string[];
  prerequisites: string[];
  assumptions: string[];
  toolPath: string;
  configTemplate?: AnalysisConfig;
}
```

### 4.2 Data Context Integration

**DatasetContextProvider**

```typescript
interface DatasetContext {
  hasDataset: boolean;
  totalRows: number;
  totalColumns: number;
  variables: VariableInfo[];
  sampleSize: SampleSizeAssessment;
  dataQuality: DataQualityMetrics;
  distributions: DistributionAnalysis[];
}

interface VariableInfo {
  name: string;
  type: 'numeric' | 'categorical' | 'ordinal' | 'binary' | 'date';
  detectedType: string;
  confidence: number;
  uniqueValues: number;
  missingCount: number;
  distribution?: DistributionStats;
}
```

## 5. Decision Tree Logic System

### 5.1 Question Flow Architecture

**Question Categories:**

```typescript
enum QuestionCategory {
  RESEARCH_OBJECTIVE = 'research_objective',
  DATA_CHARACTERISTICS = 'data_characteristics',
  VARIABLE_SELECTION = 'variable_selection',
  ANALYSIS_CONSTRAINTS = 'analysis_constraints',
  OUTPUT_PREFERENCES = 'output_preferences'
}

enum ResearchObjective {
  DESCRIPTIVE = 'descriptive',
  COMPARATIVE = 'comparative', 
  PREDICTIVE = 'predictive',
  EXPLORATORY = 'exploratory'
}
```

**Conditional Logic Engine:**

```typescript
interface ConditionalRule {
  condition: {
    questionId: string;
    operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
  };
  action: {
    type: 'show_question' | 'hide_question' | 'skip_to_step' | 'set_default';
    target: string;
    value?: any;
  };
}
```

### 5.2 Recommendation Algorithm

**Scoring System:**

```typescript
interface ScoringCriteria {
  dataTypeCompatibility: number; // 0-100
  sampleSizeAdequacy: number;   // 0-100
  assumptionsMet: number;       // 0-100
  objectiveAlignment: number;   // 0-100
  complexityMatch: number;      // 0-100
}

interface RecommendationAlgorithm {
  calculateSuitabilityScore(method: StatisticalMethod, context: AnalysisContext): number;
  rankMethods(methods: StatisticalMethod[], context: AnalysisContext): MethodRecommendation[];
  generateReasoning(method: StatisticalMethod, context: AnalysisContext): string[];
}
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    ADVISOR_SESSIONS ||--o{ QUESTION_RESPONSES : contains
    ADVISOR_SESSIONS ||--o{ METHOD_RECOMMENDATIONS : generates
    QUESTION_RESPONSES }|--|| QUESTIONS : answers
    METHOD_RECOMMENDATIONS }|--|| STATISTICAL_METHODS : recommends
    ADVISOR_SESSIONS }|--|| DATASETS : analyzes
    
    ADVISOR_SESSIONS {
        uuid id PK
        uuid user_id FK
        uuid dataset_id FK
        jsonb session_data
        timestamp created_at
        timestamp updated_at
        varchar status
    }
    
    QUESTION_RESPONSES {
        uuid id PK
        uuid session_id FK
        varchar question_id
        jsonb response_data
        timestamp created_at
    }
    
    METHOD_RECOMMENDATIONS {
        uuid id PK
        uuid session_id FK
        varchar method_id
        float suitability_score
        float confidence_score
        jsonb reasoning
        jsonb config_template
        boolean selected
        timestamp created_at
    }
    
    QUESTIONS {
        varchar id PK
        varchar category
        varchar type
        varchar title
        text description
        jsonb options
        jsonb validation_rules
        jsonb conditional_logic
        integer order_index
    }
    
    STATISTICAL_METHODS {
        varchar id PK
        varchar name
        varchar category
        text description
        jsonb prerequisites
        jsonb assumptions
        jsonb data_requirements
        varchar tool_path
        jsonb config_schema
    }
```

### 6.2 Data Definition Language

**Advisor Sessions Table**

```sql
-- Create advisor sessions table
CREATE TABLE advisor_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    dataset_id UUID,
    session_data JSONB NOT NULL DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'abandoned')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_advisor_sessions_user_id ON advisor_sessions(user_id);
CREATE INDEX idx_advisor_sessions_created_at ON advisor_sessions(created_at DESC);
CREATE INDEX idx_advisor_sessions_status ON advisor_sessions(status);

-- Row Level Security
ALTER TABLE advisor_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own advisor sessions" ON advisor_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own advisor sessions" ON advisor_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own advisor sessions" ON advisor_sessions
    FOR UPDATE USING (auth.uid() = user_id);
```

**Question Responses Table**

```sql
-- Create question responses table
CREATE TABLE question_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES advisor_sessions(id) ON DELETE CASCADE,
    question_id VARCHAR(100) NOT NULL,
    response_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_question_responses_session_id ON question_responses(session_id);
CREATE INDEX idx_question_responses_question_id ON question_responses(question_id);

-- Row Level Security
ALTER TABLE question_responses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage responses for own sessions" ON question_responses
    USING (EXISTS (
        SELECT 1 FROM advisor_sessions 
        WHERE advisor_sessions.id = question_responses.session_id 
        AND advisor_sessions.user_id = auth.uid()
    ));
```

**Method Recommendations Table**

```sql
-- Create method recommendations table
CREATE TABLE method_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES advisor_sessions(id) ON DELETE CASCADE,
    method_id VARCHAR(100) NOT NULL,
    suitability_score FLOAT NOT NULL CHECK (suitability_score >= 0 AND suitability_score <= 100),
    confidence_score FLOAT NOT NULL CHECK (confidence_score >= 0 AND confidence_score <= 100),
    reasoning JSONB NOT NULL DEFAULT '[]',
    config_template JSONB DEFAULT '{}',
    selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_method_recommendations_session_id ON method_recommendations(session_id);
CREATE INDEX idx_method_recommendations_method_id ON method_recommendations(method_id);
CREATE INDEX idx_method_recommendations_score ON method_recommendations(suitability_score DESC);

-- Row Level Security
ALTER TABLE method_recommendations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage recommendations for own sessions" ON method_recommendations
    USING (EXISTS (
        SELECT 1 FROM advisor_sessions 
        WHERE advisor_sessions.id = method_recommendations.session_id 
        AND advisor_sessions.user_id = auth.uid()
    ));
```

**Statistical Methods Reference Table**

```sql
-- Create statistical methods reference table
CREATE TABLE statistical_methods (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    prerequisites JSONB DEFAULT '[]',
    assumptions JSONB DEFAULT '[]',
    data_requirements JSONB NOT NULL,
    tool_path VARCHAR(200) NOT NULL,
    config_schema JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_statistical_methods_category ON statistical_methods(category);
CREATE INDEX idx_statistical_methods_name ON statistical_methods(name);

-- Grant permissions
GRANT SELECT ON statistical_methods TO anon;
GRANT SELECT ON statistical_methods TO authenticated;

-- Insert initial data
INSERT INTO statistical_methods (id, name, category, description, data_requirements, tool_path) VALUES
('DESC_CENTRAL_TENDENCY', 'Measures of Central Tendency', 'descriptive', 'Calculate mean, median, and mode', '{"numeric_variables": 1, "min_sample_size": 1}', '/stats/descriptive'),
('DESC_VARIABILITY', 'Measures of Variability', 'descriptive', 'Calculate standard deviation, variance, and range', '{"numeric_variables": 1, "min_sample_size": 2}', '/stats/descriptive'),
('TEST_ONE_SAMPLE_T', 'One-Sample t-test', 'parametric_tests', 'Test if sample mean differs from population mean', '{"numeric_variables": 1, "min_sample_size": 30, "normality_required": true}', '/stats/t-tests'),
('TEST_TWO_SAMPLE_T', 'Independent t-test', 'parametric_tests', 'Compare means between two independent groups', '{"numeric_variables": 1, "categorical_variables": 1, "min_sample_size": 30, "normality_required": true}', '/stats/t-tests'),
('TEST_CHI_SQUARE', 'Chi-Square Test', 'categorical_tests', 'Test association between categorical variables', '{"categorical_variables": 2, "min_expected_frequency": 5}', '/stats/chi-square'),
('REG_LINEAR', 'Linear Regression', 'regression', 'Model linear relationship between variables', '{"numeric_variables": 2, "min_sample_size": 50}', '/stats/regression'),
('REG_LOGISTIC', 'Logistic Regression', 'regression', 'Model binary outcomes', '{"numeric_variables": 1, "binary_outcome": 1, "min_sample_size": 100}', '/stats/logistic-regression'),
('MULTI_PCA', 'Principal Component Analysis', 'multivariate', 'Reduce dimensionality and identify patterns', '{"numeric_variables": 3, "min_sample_size": 100}', '/stats/pca'),
('MULTI_CLUSTER', 'Cluster Analysis', 'multivariate', 'Group similar observations', '{"numeric_variables": 2, "min_sample_size": 50}', '/stats/clustering');
```

## 7. Integration Points

### 7.1 Analysis Assistant Integration

**Tab Management:**

```typescript
// Update ViewMode type in AnalysisAssistant.tsx
type ViewMode = 'assistant' | 'quality' | 'advisor';

// Add new tab to ToggleButtonGroup
<ToggleButton value="advisor">
  <AssessmentIcon sx={{ mr: 0.5 }} />
  Statistical Analysis Advisor
</ToggleButton>
```

**Component Integration:**

```typescript
// Add conditional rendering for advisor tab
{viewMode === 'advisor' ? (
  <StatisticalAnalysisAdvisor
    currentDataset={currentDataset}
    datasetAnalysis={datasetAnalysis}
    onNavigateToTool={handleNavigateToTool}
  />
) : viewMode === 'assistant' ? (
  // Existing AI Assistant content
) : viewMode === 'quality' ? (
  // Existing Data Quality content
) : null}
```

### 7.2 Dataset Context Integration

**Real-time Data Analysis:**

```typescript
const useDatasetContext = (dataset?: Dataset) => {
  const [context, setContext] = useState<DatasetContext | null>(null);
  
  useEffect(() => {
    if (dataset) {
      analyzeDatasetForAdvisor(dataset).then(setContext);
    }
  }, [dataset]);
  
  return context;
};
```

### 7.3 Navigation Integration

**Tool Navigation Handler:**

```typescript
const handleNavigateToTool = (toolPath: string, config?: AnalysisConfig) => {
  // Save current advisor session
  saveAdvisorSession(currentSession);
  
  // Navigate to specified tool with pre-configured parameters
  navigate(toolPath, { state: { config, fromAdvisor: true } });
};
```

## 8. Performance Considerations

### 8.1 Optimization Strategies

* **Lazy Loading**: Load recommendation engine only when advisor tab is active

* **Memoization**: Cache dataset analysis results and question responses

* **Debounced Updates**: Prevent excessive re-calculations during user input

* **Progressive Enhancement**: Load basic functionality first, enhance with advanced features

### 8.2 Caching Strategy

* **Dataset Context**: Cache analysis results for 5 minutes

* **Method Recommendations**: Cache based on response hash

* **Question Definitions**: Cache statically with service worker

* **User Sessions**: Persist in localStorage for offline access

## 9. Security Considerations

### 9.1 Data Privacy

* **Session Isolation**: Each user can only access their own advisor sessions

* **Data Anonymization**: Statistical analysis doesn't store actual data values

* **Temporary Storage**: Clear sensitive data from memory after session ends

### 9.2 Input Validation

* **Response Validation**: Validate all user inputs against question schemas

* **SQL Injection Prevention**: Use parameterized queries for all database operations

* **XSS Protection**: Sanitize all user-generated content before display

## 10. Testing Strategy

### 10.1 Unit Testing

* **Decision Tree Logic**: Test question branching and conditional logic

* **Recommendation Algorithm**: Verify scoring calculations and ranking

* **Data Context Analysis**: Test variable type detection and validation

### 10.2 Integration Testing

* **Component Integration**: Test tab switching and data flow

* **Database Operations**: Test session persistence and retrieval

* **Navigation Flow**: Test tool navigation with pre-configured parameters

### 10.3 User Acceptance Testing

* **Wizard Completion**: Test full decision tree workflows

* **Recommendation Accuracy**: Validate method suggestions against expert knowledge

* **Mobile Responsiveness**: Test on various device sizes and orientations

