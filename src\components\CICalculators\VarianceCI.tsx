import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  BarChart as VarianceIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  Calculate as CalculateIcon,
  TrendingUp as TrendIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`variance-ci-tabpanel-${index}`}
      aria-labelledby={`variance-ci-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface VarianceCIResult {
  sampleVariance: number;
  n: number;
  confidenceLevel: number;
  degreesOfFreedom: number;
  chiSquareLower: number;
  chiSquareUpper: number;
  lowerBound: number;
  upperBound: number;
  standardDeviation: number;
  sdLowerBound: number;
  sdUpperBound: number;
  interpretation: string;
  formula: string;
}

const VarianceCI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [sampleVariance, setSampleVariance] = useState<string>('');
  const [n, setN] = useState<string>('');
  const [confidenceLevel, setConfidenceLevel] = useState<number>(95);
  const [result, setResult] = useState<VarianceCIResult | null>(null);
  const [error, setError] = useState<string>('');

  // Render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // Chi-square critical values (approximation for common cases)
  const getChiSquareCritical = (df: number, alpha: number): { lower: number; upper: number } => {
    // This is a simplified approximation. In practice, you'd use a more comprehensive table or function
    const criticalValues: { [key: string]: { [key: number]: { lower: number; upper: number } } } = {
      '90': {
        5: { lower: 1.145, upper: 11.070 },
        10: { lower: 3.940, upper: 18.307 },
        15: { lower: 7.261, upper: 24.996 },
        20: { lower: 10.851, upper: 31.410 },
        25: { lower: 14.611, upper: 37.652 },
        30: { lower: 18.493, upper: 43.773 },
        40: { lower: 26.509, upper: 55.758 },
        50: { lower: 34.764, upper: 67.505 },
        60: { lower: 43.188, upper: 79.082 },
        100: { lower: 74.222, upper: 124.342 }
      },
      '95': {
        5: { lower: 0.831, upper: 12.833 },
        10: { lower: 3.247, upper: 20.483 },
        15: { lower: 6.262, upper: 27.488 },
        20: { lower: 9.591, upper: 34.170 },
        25: { lower: 13.120, upper: 40.646 },
        30: { lower: 16.791, upper: 46.979 },
        40: { lower: 24.433, upper: 59.342 },
        50: { lower: 32.357, upper: 71.420 },
        60: { lower: 40.482, upper: 83.298 },
        100: { lower: 70.065, upper: 129.561 }
      },
      '99': {
        5: { lower: 0.412, upper: 16.750 },
        10: { lower: 2.156, upper: 25.188 },
        15: { lower: 4.601, upper: 32.801 },
        20: { lower: 7.434, upper: 39.997 },
        25: { lower: 10.520, upper: 46.928 },
        30: { lower: 13.787, upper: 53.672 },
        40: { lower: 20.707, upper: 66.766 },
        50: { lower: 27.991, upper: 79.490 },
        60: { lower: 35.534, upper: 91.952 },
        100: { lower: 61.918, upper: 140.169 }
      }
    };
    
    const levelStr = confidenceLevel.toString();
    if (!criticalValues[levelStr]) {
      // Default to 95% if level not found
      return criticalValues['95'][df] || criticalValues['95'][30];
    }
    
    // Find closest df
    const availableDfs = Object.keys(criticalValues[levelStr]).map(Number).sort((a, b) => a - b);
    let closestDf = availableDfs[0];
    
    for (const availableDf of availableDfs) {
      if (Math.abs(df - availableDf) < Math.abs(df - closestDf)) {
        closestDf = availableDf;
      }
    }
    
    return criticalValues[levelStr][closestDf];
  };

  const calculateVarianceCI = useCallback(() => {
    setError('');
    
    const varianceValue = parseFloat(sampleVariance);
    const nValue = parseInt(n);
    
    // Input validation
    if (isNaN(varianceValue) || isNaN(nValue)) {
      setError('Please enter valid numeric values for sample variance and sample size.');
      return;
    }
    
    if (varianceValue <= 0) {
      setError('Sample variance must be positive.');
      return;
    }
    
    if (nValue < 2) {
      setError('Sample size must be at least 2.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }
    
    const df = nValue - 1;
    const alpha = (100 - confidenceLevel) / 100;
    
    // Get chi-square critical values
    const chiSquareValues = getChiSquareCritical(df, alpha);
    const chiSquareLower = chiSquareValues.lower;
    const chiSquareUpper = chiSquareValues.upper;
    
    // Calculate confidence interval for variance
    const lowerBound = (df * varianceValue) / chiSquareUpper;
    const upperBound = (df * varianceValue) / chiSquareLower;
    
    // Calculate confidence interval for standard deviation
    const standardDeviation = Math.sqrt(varianceValue);
    const sdLowerBound = Math.sqrt(lowerBound);
    const sdUpperBound = Math.sqrt(upperBound);
    
    // Interpretation
    const interpretation = `We are ${confidenceLevel}% confident that the true population variance is between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}, and the true population standard deviation is between ${sdLowerBound.toFixed(2)} and ${sdUpperBound.toFixed(2)}.`;
    
    // Formula for variance confidence interval
    const formula = '\\frac{(n-1)s^2}{\\chi^2_{\\alpha/2,df}} \\leq \\sigma^2 \\leq \\frac{(n-1)s^2}{\\chi^2_{1-\\alpha/2,df}}';
    
    const calculationResult: VarianceCIResult = {
      sampleVariance: varianceValue,
      n: nValue,
      confidenceLevel,
      degreesOfFreedom: df,
      chiSquareLower,
      chiSquareUpper,
      lowerBound,
      upperBound,
      standardDeviation,
      sdLowerBound,
      sdUpperBound,
      interpretation,
      formula
    };
    
    setResult(calculationResult);
    setActiveTab(1); // Switch to Results tab
  }, [sampleVariance, n, confidenceLevel]);
  
  const clearAll = () => {
    setSampleVariance('');
    setN('');
    setConfidenceLevel(95);
    setResult(null);
    setError('');
    setActiveTab(0);
  };
  
  const copyToClipboard = () => {
    if (!result) return;
    
    const text = `Variance Confidence Interval Results:\n` +
      `Sample Variance: ${result.sampleVariance}\n` +
      `Sample Size (n): ${result.n}\n` +
      `Confidence Level: ${result.confidenceLevel}%\n` +
      `Degrees of Freedom: ${result.degreesOfFreedom}\n` +
      `${result.confidenceLevel}% CI for Variance: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n` +
        `${result.confidenceLevel}% CI for Standard Deviation: [${result.sdLowerBound.toFixed(2)}, ${result.sdUpperBound.toFixed(2)}]\n` +
        `Chi-square Lower: ${result.chiSquareLower.toFixed(2)}\n` +
        `Chi-square Upper: ${result.chiSquareUpper.toFixed(2)}\n` +
      `\nInterpretation: ${result.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <VarianceIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
        <Typography variant="h4" component="h1" fontWeight="bold">
          Variance Confidence Interval
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" icon={<CalculateIcon />} />
          <Tab label="Results" icon={<TrendIcon />} />
          <Tab label="Guide" icon={<InfoIcon />} />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Variance (s²)"
                      value={sampleVariance}
                      onChange={(e) => setSampleVariance(e.target.value)}
                      type="number"
                      inputProps={{ step: 0.001, min: 0 }}
                      helperText="Enter the sample variance (must be positive)"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Sample Size (n)"
                      value={n}
                      onChange={(e) => setN(e.target.value)}
                      type="number"
                      inputProps={{ step: 1, min: 2 }}
                      helperText="Number of observations"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={confidenceLevel}
                        onChange={(e) => setConfidenceLevel(e.target.value as number)}
                        label="Confidence Level"
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculateVarianceCI}
                    disabled={!sampleVariance || !n}
                    startIcon={<CalculateIcon />}
                  >
                    Calculate Confidence Interval
                  </Button>
                  
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  About This Calculator
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This calculator computes confidence intervals for population variance and standard deviation 
                  using the chi-square distribution. It assumes the data comes from a normal distribution.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result ? (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Confidence Interval Results
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                        <Typography variant="subtitle2" color="primary">
                          {result.confidenceLevel}% CI for Variance
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                        <Typography variant="subtitle2" color="secondary">
                          {result.confidenceLevel}% CI for Std Dev
                        </Typography>
                        <Typography variant="h5" fontWeight="bold">
                          [{result.sdLowerBound.toFixed(2)}, {result.sdUpperBound.toFixed(2)}]
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Calculation Details
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Variance</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.sampleVariance}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Size</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.n}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Degrees of Freedom</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.degreesOfFreedom}</Typography>
                    </Grid>
                    <Grid item xs={6} sm={3}>
                      <Typography variant="body2" color="text.secondary">Sample Std Dev</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.standardDeviation.toFixed(2)}</Typography>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Chi-square Critical Values
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">χ² Lower</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.chiSquareLower.toFixed(2)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">χ² Upper</Typography>
                      <Typography variant="body1" fontWeight="bold">{result.chiSquareUpper.toFixed(2)}</Typography>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Formula
                  </Typography>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: 'grey.50',
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'grey.200',
                      mb: 2
                    }}
                    dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                  />
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Interpretation
                  </Typography>
                  <Typography variant="body1">
                    {result.interpretation}
                  </Typography>
                  
                  <Box sx={{ mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={copyToClipboard}
                      startIcon={<CopyIcon />}
                    >
                      Copy Results
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Key Statistics
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Chip label="s²" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Variance" 
                        secondary={result.sampleVariance.toString()}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="s" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Std Dev" 
                        secondary={result.standardDeviation.toFixed(2)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="n" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Sample Size" 
                        secondary={result.n.toString()}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Chip label="df" size="small" /></ListItemIcon>
                      <ListItemText 
                        primary="Degrees of Freedom" 
                        secondary={result.degreesOfFreedom.toString()}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        ) : (
          <Alert severity="info">
            No results to display. Please go to the Calculator tab and perform a calculation.
          </Alert>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h5" gutterBottom>
                  Variance Confidence Intervals Guide
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  When to Use
                </Typography>
                <Typography variant="body1" paragraph>
                  Use variance confidence intervals when you want to estimate the range of 
                  plausible values for the true population variance or standard deviation based on your sample data. 
                  This is particularly useful for:
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="Quality control in manufacturing processes" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Assessing measurement precision" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Comparing variability between groups" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Risk assessment and financial modeling" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  How It Works
                </Typography>
                <Typography variant="body1" paragraph>
                  The confidence interval for variance is calculated using the chi-square distribution. 
                  The formula is:
                </Typography>
                <Typography variant="body1" paragraph sx={{ fontFamily: 'monospace', bgcolor: 'grey.100', p: 1 }}>
                  [(n-1)s² / χ²(α/2, n-1), (n-1)s² / χ²(1-α/2, n-1)]
                </Typography>
                <Typography variant="body1" paragraph>
                  Where s² is the sample variance, n is the sample size, and χ² represents 
                  the chi-square critical values.
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  How to Interpret
                </Typography>
                <Typography variant="body1" paragraph>
                  The confidence interval provides a range of values that likely contains the true 
                  population variance. For example, a 95% confidence interval means that if you 
                  repeated your study many times, 95% of the intervals would contain the true 
                  population variance.
                </Typography>
                <Typography variant="body1" paragraph>
                  Note that confidence intervals for variance are not symmetric around the sample 
                  variance due to the skewed nature of the chi-square distribution.
                </Typography>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Assumptions
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="The data comes from a normal distribution" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Observations are independent" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="The sample is randomly selected" />
                  </ListItem>
                </List>
                
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                  Important Notes
                </Typography>
                <List>
                  <ListItem>
                    <ListItemText primary="The chi-square test is sensitive to departures from normality" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Larger sample sizes provide more precise estimates" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="The interval for standard deviation is the square root of the variance interval bounds" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Reference
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Sample Size Guidelines:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="n ≥ 30: Good approximation" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="n < 30: Check normality" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="n < 10: Use with caution" />
                  </ListItem>
                </List>
                
                <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                  <strong>Common Confidence Levels:</strong>
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="90%: Less stringent" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="95%: Standard" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="99%: More stringent" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default VarianceCI;