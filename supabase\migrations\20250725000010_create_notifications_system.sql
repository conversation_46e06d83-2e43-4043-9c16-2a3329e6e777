-- Create Notifications System Migration
-- This migration creates the notifications tables and policies for the admin notification system
-- Date: 2025-07-25

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  priority INTEGER DEFAULT 0, -- Higher numbers = higher priority
  target_audience TEXT DEFAULT 'all' CHECK (target_audience IN ('all', 'pro', 'edu', 'standard', 'guest'))
);

-- Create user notification reads table
CREATE TABLE IF NOT EXISTS public.user_notification_reads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_id UUID REFERENCES public.notifications(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(user_id, notification_id)
);

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notification_reads ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can read active notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can manage notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can read own notification reads" ON public.user_notification_reads;
DROP POLICY IF EXISTS "Users can insert own notification reads" ON public.user_notification_reads;

-- RLS Policies for notifications table
-- All authenticated users can read active notifications
CREATE POLICY "Users can read active notifications" ON public.notifications
  FOR SELECT USING (
    is_active = true 
    AND (expires_at IS NULL OR expires_at > now())
  );

-- Only admins can insert/update/delete notifications
CREATE POLICY "Admins can manage notifications" ON public.notifications
  FOR ALL USING (
    auth.uid() IS NOT NULL AND 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND COALESCE(is_admin, false) = true
    )
  );

-- RLS Policies for user_notification_reads table
-- Users can only read their own notification read status
CREATE POLICY "Users can read own notification reads" ON public.user_notification_reads
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own notification reads
CREATE POLICY "Users can insert own notification reads" ON public.user_notification_reads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own notification reads
CREATE POLICY "Users can update own notification reads" ON public.user_notification_reads
  FOR UPDATE USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notifications_active_expires ON public.notifications(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_target_audience ON public.notifications(target_audience);
CREATE INDEX IF NOT EXISTS idx_user_notification_reads_user_id ON public.user_notification_reads(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notification_reads_notification_id ON public.user_notification_reads(notification_id);

-- Grant necessary permissions
GRANT SELECT ON public.notifications TO authenticated;
GRANT ALL ON public.notifications TO authenticated; -- Will be restricted by RLS
GRANT ALL ON public.user_notification_reads TO authenticated; -- Will be restricted by RLS

-- Insert sample notifications for testing
INSERT INTO public.notifications (title, message, type, priority, target_audience) VALUES
  ('Welcome to DataStatPro!', 'Thank you for using DataStatPro. Explore our powerful statistical analysis tools and features.', 'success', 2, 'all'),
  ('New Feature: Advanced ANOVA', 'We have added support for repeated measures ANOVA. Check it out in the Inferential Statistics section.', 'info', 1, 'pro'),
  ('Maintenance Notice', 'Scheduled maintenance on Sunday 2AM-4AM UTC. The app will remain accessible during this time.', 'warning', 0, 'all')
ON CONFLICT DO NOTHING;

-- Add helpful comments
COMMENT ON TABLE public.notifications IS 'Stores admin-created notifications for users';
COMMENT ON TABLE public.user_notification_reads IS 'Tracks which notifications users have read';
COMMENT ON COLUMN public.notifications.target_audience IS 'Defines which user types can see this notification';
COMMENT ON COLUMN public.notifications.priority IS 'Higher numbers appear first in notification lists';

-- Success message
SELECT 'Notifications system created successfully. Admin users can now add notifications.' as status;