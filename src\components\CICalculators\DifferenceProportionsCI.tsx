import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  TextField,
  Divider,
  Tabs,
  Tab,
  useTheme,
  Chip,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
  Info as InfoIcon,
  CompareArrows as DifferenceIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`difference-proportions-ci-tabpanel-${index}`}
      aria-labelledby={`difference-proportions-ci-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface DifferenceProportionsCIResult {
  method: string;
  lowerBound: number;
  upperBound: number;
  confidenceLevel: number;
  p1: number;
  p2: number;
  difference: number;
  standardError: number;
  interpretation: string;
  formula: string;
}

interface CalculationData {
  x1: number;
  n1: number;
  x2: number;
  n2: number;
  confidenceLevel: number;
  method: string;
}

const DifferenceProportionsCI: React.FC = () => {
  const theme = useTheme();
  
  const [activeTab, setActiveTab] = useState<number>(0);
  const [calculationData, setCalculationData] = useState<CalculationData>({
    x1: 0,
    n1: 0,
    x2: 0,
    n2: 0,
    confidenceLevel: 95,
    method: 'wald'
  });
  const [result, setResult] = useState<DifferenceProportionsCIResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const renderFormula = (formula: string): string => {
    try {
      const processor = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify);
      
      const result = processor.processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  const getZScore = (confidenceLevel: number): number => {
    const zScores: { [key: number]: number } = {
      90: 1.645,
      95: 1.96,
      99: 2.576,
      99.9: 3.291
    };
    return zScores[confidenceLevel] || 1.96;
  };

  const calculateWaldCI = (p1: number, p2: number, n1: number, n2: number, z: number) => {
    const difference = p1 - p2;
    const standardError = Math.sqrt((p1 * (1 - p1)) / n1 + (p2 * (1 - p2)) / n2);
    const margin = z * standardError;
    
    return {
      lowerBound: difference - margin,
      upperBound: difference + margin,
      standardError,
      method: 'Wald Method'
    };
  };

  const calculatePooledCI = (p1: number, p2: number, n1: number, n2: number, z: number) => {
    const difference = p1 - p2;
    const pooledP = (p1 * n1 + p2 * n2) / (n1 + n2);
    const standardError = Math.sqrt(pooledP * (1 - pooledP) * (1/n1 + 1/n2));
    const margin = z * standardError;
    
    return {
      lowerBound: difference - margin,
      upperBound: difference + margin,
      standardError,
      method: 'Pooled Variance Method'
    };
  };

  const calculateUnpooledCI = (p1: number, p2: number, n1: number, n2: number, z: number) => {
    const difference = p1 - p2;
    const se1 = Math.sqrt((p1 * (1 - p1)) / n1);
    const se2 = Math.sqrt((p2 * (1 - p2)) / n2);
    const standardError = Math.sqrt(se1 * se1 + se2 * se2);
    const margin = z * standardError;
    
    return {
      lowerBound: difference - margin,
      upperBound: difference + margin,
      standardError,
      method: 'Unpooled Variance Method'
    };
  };

  const calculateNewcombCI = (p1: number, p2: number, n1: number, n2: number, z: number) => {
    const difference = p1 - p2;
    
    // Newcombe's method uses Wilson score intervals
    const calculateWilsonBounds = (x: number, n: number, z: number) => {
      const p = x / n;
      const term1 = p + (z * z) / (2 * n);
      const term2 = z * Math.sqrt((p * (1 - p)) / n + (z * z) / (4 * n * n));
      const denominator = 1 + (z * z) / n;
      
      return {
        lower: (term1 - term2) / denominator,
        upper: (term1 + term2) / denominator
      };
    };
    
    const wilson1 = calculateWilsonBounds(calculationData.x1, n1, z);
    const wilson2 = calculateWilsonBounds(calculationData.x2, n2, z);
    
    const lowerBound = (p1 - p2) - Math.sqrt(Math.pow(p1 - wilson1.lower, 2) + Math.pow(wilson2.upper - p2, 2));
    const upperBound = (p1 - p2) + Math.sqrt(Math.pow(wilson1.upper - p1, 2) + Math.pow(p2 - wilson2.lower, 2));
    
    const standardError = Math.sqrt((p1 * (1 - p1)) / n1 + (p2 * (1 - p2)) / n2);
    
    return {
      lowerBound,
      upperBound,
      standardError,
      method: 'Newcombe Method (Wilson Score)'
    };
  };

  const calculateDifferenceProportionsCI = () => {
    const { x1, n1, x2, n2, confidenceLevel, method } = calculationData;
    
    if (n1 <= 0 || n2 <= 0) {
      setError('Sample sizes must be greater than 0.');
      return;
    }
    
    if (x1 < 0 || x2 < 0) {
      setError('Number of successes cannot be negative.');
      return;
    }
    
    if (x1 > n1 || x2 > n2) {
      setError('Number of successes cannot exceed sample size.');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const p1 = x1 / n1;
      const p2 = x2 / n2;
      const z = getZScore(confidenceLevel);
      
      let calculationResult;
      
      switch (method) {
        case 'wald':
          calculationResult = calculateWaldCI(p1, p2, n1, n2, z);
          break;
        case 'pooled':
          calculationResult = calculatePooledCI(p1, p2, n1, n2, z);
          break;
        case 'unpooled':
          calculationResult = calculateUnpooledCI(p1, p2, n1, n2, z);
          break;
        case 'newcombe':
          calculationResult = calculateNewcombCI(p1, p2, n1, n2, z);
          break;
        default:
          calculationResult = calculateWaldCI(p1, p2, n1, n2, z);
      }
      
      const difference = p1 - p2;
      
      let interpretation = `We are ${confidenceLevel}% confident that the true difference in population proportions (p₁ - p₂) lies between ${calculationResult.lowerBound.toFixed(2)} and ${calculationResult.upperBound.toFixed(2)}.`;
      
      if (calculationResult.lowerBound > 0) {
        interpretation += ' Since the entire interval is positive, we can conclude that p₁ > p₂.';
      } else if (calculationResult.upperBound < 0) {
        interpretation += ' Since the entire interval is negative, we can conclude that p₁ < p₂.';
      } else {
        interpretation += ' Since the interval contains 0, we cannot conclude there is a significant difference between the proportions.';
      }
      
      // Set formula based on method
      let formula = '';
      switch (method) {
        case 'wald':
          formula = '(\\hat{p}_1 - \\hat{p}_2) \\pm z_{\\alpha/2} \\sqrt{\\frac{\\hat{p}_1(1-\\hat{p}_1)}{n_1} + \\frac{\\hat{p}_2(1-\\hat{p}_2)}{n_2}}';
          break;
        case 'pooled':
          formula = '(\\hat{p}_1 - \\hat{p}_2) \\pm z_{\\alpha/2} \\sqrt{\\hat{p}(1-\\hat{p})\\left(\\frac{1}{n_1} + \\frac{1}{n_2}\\right)}';
          break;
        case 'unpooled':
          formula = '(\\hat{p}_1 - \\hat{p}_2) \\pm z_{\\alpha/2} \\sqrt{\\frac{\\hat{p}_1(1-\\hat{p}_1)}{n_1} + \\frac{\\hat{p}_2(1-\\hat{p}_2)}{n_2}}';
          break;
        case 'newcombe':
          formula = 'CI = (\\hat{p}_1 - \\hat{p}_2) \\pm \\sqrt{(\\hat{p}_1 - L_1)^2 + (U_2 - \\hat{p}_2)^2}';
          break;
        default:
          formula = '(\\hat{p}_1 - \\hat{p}_2) \\pm z_{\\alpha/2} \\sqrt{\\frac{\\hat{p}_1(1-\\hat{p}_1)}{n_1} + \\frac{\\hat{p}_2(1-\\hat{p}_2)}{n_2}}';
      }
      
      setResult({
        method: calculationResult.method,
        lowerBound: calculationResult.lowerBound,
        upperBound: calculationResult.upperBound,
        confidenceLevel,
        p1,
        p2,
        difference,
        standardError: calculationResult.standardError,
        interpretation,
        formula
      });
      
      // Automatically navigate to results tab after successful calculation
      setActiveTab(1);
    } catch (err) {
      setError('An error occurred during calculation. Please check your inputs.');
    } finally {
      setLoading(false);
    }
  };

  const clearAll = () => {
    setCalculationData({
      x1: 0,
      n1: 0,
      x2: 0,
      n2: 0,
      confidenceLevel: 95,
      method: 'wald'
    });
    setResult(null);
    setError(null);
    setActiveTab(0);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const generateReport = (): string => {
    if (!result) return '';
    
    return `Difference of Proportions Confidence Interval Analysis\n\n` +
           `Group 1: ${calculationData.x1}/${calculationData.n1} (p₁ = ${result.p1.toFixed(2)})\n` +
        `Group 2: ${calculationData.x2}/${calculationData.n2} (p₂ = ${result.p2.toFixed(2)})\n` +
        `Difference (p₁ - p₂): ${result.difference.toFixed(2)}\n` +
           `Confidence Level: ${result.confidenceLevel}%\n` +
           `Method: ${result.method}\n` +
           `Standard Error: ${result.standardError.toFixed(2)}\n` +
        `Confidence Interval: [${result.lowerBound.toFixed(2)}, ${result.upperBound.toFixed(2)}]\n\n` +
           `Interpretation: ${result.interpretation}`;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <DifferenceIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Difference of Proportions Confidence Interval
        </Typography>
      </Box>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Calculate confidence intervals for the difference between two population proportions.
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!result} />
          <Tab label="Guide" />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <InfoIcon sx={{ mr: 1 }} />
                  Input Parameters
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom color="primary">
                      Group 1
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Sample Size (n₁)"
                      value={calculationData.n1}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, n1: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 1, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Number of Successes (x₁)"
                      value={calculationData.x1}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, x1: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 0, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    {calculationData.n1 > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        Proportion: {(calculationData.x1 / calculationData.n1).toFixed(2)}
                      </Typography>
                    )}
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom color="secondary">
                      Group 2
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Sample Size (n₂)"
                      value={calculationData.n2}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, n2: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 1, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Number of Successes (x₂)"
                      value={calculationData.x2}
                      onChange={(e) => setCalculationData(prev => ({ ...prev, x2: parseInt(e.target.value) || 0 }))}
                      type="number"
                      inputProps={{ min: 0, step: 1 }}
                      sx={{ mb: 2 }}
                    />
                    
                    {calculationData.n2 > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        Proportion: {(calculationData.x2 / calculationData.n2).toFixed(2)}
                      </Typography>
                    )}
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Confidence Level</InputLabel>
                      <Select
                        value={calculationData.confidenceLevel}
                        label="Confidence Level"
                        onChange={(e) => setCalculationData(prev => ({ ...prev, confidenceLevel: Number(e.target.value) }))}
                      >
                        <MenuItem value={90}>90%</MenuItem>
                        <MenuItem value={95}>95%</MenuItem>
                        <MenuItem value={99}>99%</MenuItem>
                        <MenuItem value={99.9}>99.9%</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Method</InputLabel>
                      <Select
                        value={calculationData.method}
                        label="Method"
                        onChange={(e) => setCalculationData(prev => ({ ...prev, method: e.target.value }))}
                      >
                        <MenuItem value="wald">Wald Method</MenuItem>
                        <MenuItem value="pooled">Pooled Variance</MenuItem>
                        <MenuItem value="unpooled">Unpooled Variance</MenuItem>
                        <MenuItem value="newcombe">Newcombe (Wilson Score)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                
                {error && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                  </Alert>
                )}
                
                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    onClick={calculateDifferenceProportionsCI}
                    startIcon={<CalculateIcon />}
                    disabled={loading}
                    sx={{ flex: 1 }}
                  >
                    {loading ? 'Calculating...' : 'Calculate CI'}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={clearAll}
                    startIcon={<ClearIcon />}
                    sx={{ flex: 1 }}
                  >
                    Clear All
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Interval Results
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                    <Typography variant="subtitle2" color="primary">
                      {result.confidenceLevel}% Confidence Interval
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                    <Typography variant="subtitle2" color="secondary">
                      Difference (p₁ - p₂)
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.difference.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, bgcolor: 'info.50' }}>
                    <Typography variant="subtitle2" color="info.main">
                      Standard Error
                    </Typography>
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {result.standardError.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Group 1:</strong> {calculationData.x1}/{calculationData.n1} (p₁ = {result.p1.toFixed(2)})
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Group 2:</strong> {calculationData.x2}/{calculationData.n2} (p₂ = {result.p2.toFixed(2)})
                  </Typography>
                </Grid>
              </Grid>
              
              <Typography variant="subtitle2" gutterBottom>
                Method: {result.method}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Formula
              </Typography>
              <Box 
                sx={{ 
                  p: 2, 
                  bgcolor: 'grey.50', 
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'grey.200',
                  '& .katex': { fontSize: '1.1em' }
                }}
                dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
              />
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Interpretation
              </Typography>
              <Typography variant="body2">
                {result.interpretation}
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<CopyIcon />}
                onClick={() => copyToClipboard(generateReport())}
                sx={{ mt: 2 }}
              >
                Copy Results
              </Button>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Difference of Proportions Confidence Interval Guide
            </Typography>
            
            <Typography variant="body1" paragraph>
              A confidence interval for the difference of proportions helps you estimate the true difference 
              between two population proportions based on sample data.
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Available Methods:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• <strong>Wald Method:</strong> Simple and commonly used, but may perform poorly with small samples or extreme proportions</li>
              <li>• <strong>Pooled Variance:</strong> Assumes equal population proportions (null hypothesis)</li>
              <li>• <strong>Unpooled Variance:</strong> Does not assume equal proportions (more conservative)</li>
              <li>• <strong>Newcombe (Wilson Score):</strong> More accurate for small samples and extreme proportions</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Interpretation:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• If the interval contains 0: No significant difference between proportions</li>
              <li>• If the interval is entirely positive: Group 1 proportion is significantly larger</li>
              <li>• If the interval is entirely negative: Group 2 proportion is significantly larger</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Assumptions:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• Independent random samples from two populations</li>
              <li>• Each observation is classified as success or failure</li>
              <li>• Sample sizes should be reasonably large (np ≥ 5 and n(1-p) ≥ 5 for each group)</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Method Recommendations:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• Use <strong>Newcombe method</strong> for small samples or when proportions are close to 0 or 1</li>
              <li>• Use <strong>Wald method</strong> for large samples with moderate proportions (0.2 &lt; p &lt; 0.8)</li>
              <li>• Use <strong>Unpooled variance</strong> when you don't assume equal population proportions</li>
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>
    </Box>
  );
};

export default DifferenceProportionsCI;