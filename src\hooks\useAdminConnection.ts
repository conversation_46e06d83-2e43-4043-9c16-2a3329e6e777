import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../utils/supabaseClient';

interface ConnectionState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  retryCount: number;
  lastConnected: Date | null;
}

interface AdminConnectionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  enableRateLimit?: boolean;
}

/**
 * Custom hook for managing Supabase connections in admin dashboard
 * Provides connection stability, rate limiting, and error recovery
 */
export const useAdminConnection = (options: AdminConnectionOptions = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    timeout = 30000,
    enableRateLimit = true
  } = options;

  const [connectionState, setConnectionState] = useState<ConnectionState>({
    isConnected: false,
    isLoading: false,
    error: null,
    retryCount: 0,
    lastConnected: null
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const rateLimitRef = useRef<Map<string, number>>(new Map());

  // Rate limiting function
  const checkRateLimit = useCallback((functionName: string, maxCalls: number = 5): boolean => {
    if (!enableRateLimit) return true;

    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window
    const key = `${functionName}_${Math.floor(now / 60000)}`;
    
    const currentCalls = rateLimitRef.current.get(key) || 0;
    
    if (currentCalls >= maxCalls) {
      console.warn(`Rate limit exceeded for ${functionName}. Max ${maxCalls} calls per minute.`);
      return false;
    }
    
    rateLimitRef.current.set(key, currentCalls + 1);
    
    // Cleanup old entries
    for (const [k, _] of rateLimitRef.current.entries()) {
      const keyTime = parseInt(k.split('_').pop() || '0') * 60000;
      if (keyTime < windowStart) {
        rateLimitRef.current.delete(k);
      }
    }
    
    return true;
  }, [enableRateLimit]);

  // Enhanced database call wrapper with connection management
  const executeAdminFunction = useCallback(async <T>(
    functionName: string,
    params: any = {},
    retryCount: number = 0
  ): Promise<T> => {
    // Check rate limit
    if (!checkRateLimit(functionName)) {
      throw new Error(`Rate limit exceeded for ${functionName}. Please wait before retrying.`);
    }

    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    const timeoutId = setTimeout(() => {
      abortControllerRef.current?.abort();
    }, timeout);

    setConnectionState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      retryCount
    }));

    try {
      console.log(`🔄 Executing admin function: ${functionName}`, params);
      
      const { data, error } = await supabase.rpc(functionName, params);

      clearTimeout(timeoutId);

      if (error) {
        throw error;
      }

      setConnectionState(prev => ({
        ...prev,
        isConnected: true,
        isLoading: false,
        error: null,
        retryCount: 0,
        lastConnected: new Date()
      }));

      console.log(`✅ Admin function ${functionName} completed successfully`);
      return data;

    } catch (err: any) {
      clearTimeout(timeoutId);
      
      const isConnectionError = err.message?.includes('network') || 
                               err.message?.includes('connection') ||
                               err.message?.includes('timeout') ||
                               err.name === 'AbortError';

      console.error(`❌ Admin function ${functionName} failed:`, err);

      // Retry logic for connection errors
      if (isConnectionError && retryCount < maxRetries) {
        console.log(`🔄 Retrying ${functionName} (attempt ${retryCount + 1}/${maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, retryDelay * (retryCount + 1)));
        return executeAdminFunction(functionName, params, retryCount + 1);
      }

      setConnectionState(prev => ({
        ...prev,
        isConnected: false,
        isLoading: false,
        error: err.message || `Failed to execute ${functionName}`,
        retryCount
      }));

      throw err;
    }
  }, [checkRateLimit, timeout, maxRetries, retryDelay]);

  // Connection health check
  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.rpc('get_connection_stats');
      
      if (error) {
        console.warn('Connection health check failed:', error);
        return false;
      }

      console.log('📊 Connection stats:', data);
      return true;
    } catch (err) {
      console.warn('Connection health check error:', err);
      return false;
    }
  }, []);

  // Periodic connection monitoring
  useEffect(() => {
    const interval = setInterval(async () => {
      const isHealthy = await checkConnection();
      setConnectionState(prev => ({
        ...prev,
        isConnected: isHealthy
      }));
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [checkConnection]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    connectionState,
    executeAdminFunction,
    checkConnection,
    isConnected: connectionState.isConnected,
    isLoading: connectionState.isLoading,
    error: connectionState.error,
    retryCount: connectionState.retryCount
  };
};

export default useAdminConnection;
