# Supabase Migration Guide: Fixing User Datasets RLS Policies

This guide explains how to update your Supabase database and storage RLS policies to fix issues with saving user datasets in the DataStatPro application.

## Overview of Issues

Users may encounter the following errors when trying to save datasets to their accounts:

1. `GET https://[your-supabase-url].supabase.co/rest/v1/user_datasets?select=id%2Cfile_path&user_id=eq.[user-id]&dataset_name=eq.[dataset-name] 406 (Not Acceptable)`

2. `POST https://[your-supabase-url].supabase.co/storage/v1/object/userdatasets/[user-id]/[dataset-name].json 400 (Bad Request)`

3. `Error uploading new dataset file: {statusCode: '403', error: 'Unauthorized', message: 'new row violates row-level security policy'}`

These errors occur because:

1. The Row-Level Security (RLS) policies for the `user_datasets` table are not properly configured
2. The RLS policies for the `userdatasets` storage bucket are missing or improperly configured

## Step-by-Step Migration Guide

### 1. Access Your Supabase Project

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your DataStatPro project
3. Navigate to the SQL Editor

### 2. Apply the Migration Script

Copy and paste the following SQL script into the SQL Editor and run it:

```sql
-- Fix RLS policies for the user_datasets table
-- Drop the existing policy if it exists
DROP POLICY IF EXISTS "Authenticated users can manage their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can insert their own datasets" ON user_datasets;
DROP POLICY IF EXISTS "Users can view and modify their own datasets" ON user_datasets;

-- Create a more permissive policy for INSERT
CREATE POLICY "Users can insert their own datasets"
ON user_datasets FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Create a policy for SELECT, UPDATE, DELETE
CREATE POLICY "Users can view and modify their own datasets"
ON user_datasets FOR ALL
TO authenticated
USING (auth.uid() = user_id);

-- Enable RLS on the table (in case it was disabled)
ALTER TABLE user_datasets ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT ALL ON user_datasets TO authenticated;

-- Fix RLS policies for the userdatasets storage bucket
-- Note: We don't need to explicitly enable RLS on the bucket
-- Instead, we create policies on storage.objects for the specific bucket

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can upload their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own datasets" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own datasets" ON storage.objects;

-- Create policy for uploading datasets
CREATE POLICY "Users can upload their own datasets"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for viewing datasets
CREATE POLICY "Users can view their own datasets"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for updating datasets
CREATE POLICY "Users can update their own datasets"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Create policy for deleting datasets
CREATE POLICY "Users can delete their own datasets"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'userdatasets' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Grant necessary permissions to authenticated users for the userdatasets bucket
GRANT ALL ON storage.objects TO authenticated;
```

### 3. Verify the Changes

1. Navigate to the **Authentication** > **Policies** section in your Supabase dashboard
2. Verify that:
   - The `user_datasets` table has RLS enabled with the correct policies
   - The `userdatasets` storage bucket has RLS enabled with the correct policies

### 4. Test Dataset Saving

To ensure the changes are working correctly:

1. Log in to the DataStatPro application
2. Create or import a dataset
3. Try to save the dataset to your cloud account
4. Verify that the dataset is successfully saved without errors

## Understanding the RLS Policies

### User Datasets Table Policies

The RLS policies for the `user_datasets` table ensure that:

1. Users can only insert records with their own user_id
2. Users can only view, update, and delete their own records

### Storage Bucket Policies

The RLS policies for the `userdatasets` storage bucket ensure that:

1. Users can only upload files to their own folder (named with their user_id)
2. Users can only view, update, and delete files in their own folder

## Troubleshooting

If you encounter any issues after applying the migration:

1. Check the browser console for specific error messages
2. Verify that the RLS policies were correctly applied in the Supabase dashboard
3. Ensure that the `user_datasets` table and `userdatasets` storage bucket exist
4. Check that the file paths in the storage bucket follow the expected format: `[user-id]/[dataset-name].json`

If problems persist, you may need to check the Supabase logs for more detailed error information.