# DataStatPro Database Schema Documentation

## 📋 Overview

This document provides comprehensive documentation for the DataStatPro database schema. The application uses **Supabase** (PostgreSQL) as the backend database with Row Level Security (RLS) policies for data protection.

## 🏗️ Database Architecture

### Database Provider
- **Platform**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with JWT tokens
- **Security**: Row Level Security (RLS) policies
- **Real-time**: Supabase real-time subscriptions

### Schema Organization
- **public**: Application tables
- **auth**: Supabase authentication tables (managed by Supabase)
- **storage**: File storage tables (managed by Supabase)

## 📊 Table Definitions

### 1. profiles

**Purpose**: User profile information and account settings

```sql
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  username text,
  avatar_url text,
  updated_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  full_name text,
  institution text,
  country text,
  accounttype character varying DEFAULT 'standard'::character varying 
    CHECK (accounttype::text = ANY (ARRAY['standard'::character varying, 'pro'::character varying, 'edu'::character varying]::text[])),
  stripe_customer_id text,
  edu_subscription_type text 
    CHECK ((edu_subscription_type = ANY (ARRAY['free'::text, 'pro'::text])) OR edu_subscription_type IS NULL),
  is_admin boolean DEFAULT false,
  email text,
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `id`: Primary key, references auth.users(id)
- `accounttype`: User subscription tier (standard, pro, edu)
- `edu_subscription_type`: Educational subscription level
- `is_admin`: Administrative privileges flag
- `stripe_customer_id`: Stripe payment integration

**Business Rules**:
- Profile is automatically created when user signs up
- Account type determines feature access
- Admin users have elevated privileges

### 2. user_settings

**Purpose**: User preferences and application settings

```sql
CREATE TABLE public.user_settings (
  user_id uuid NOT NULL,
  notifications_enabled boolean NOT NULL DEFAULT true,
  email_notifications boolean NOT NULL DEFAULT true,
  mobile_notifications boolean NOT NULL DEFAULT false,
  theme_preference text NOT NULL DEFAULT 'system'::text,
  language_preference text NOT NULL DEFAULT 'en'::text,
  data_privacy boolean NOT NULL DEFAULT true,
  data_retention text NOT NULL DEFAULT 'standard'::text,
  two_factor_auth boolean NOT NULL DEFAULT false,
  analytics_consent boolean NOT NULL DEFAULT true,
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `theme_preference`: UI theme setting (light, dark, system)
- `language_preference`: Application language
- `data_privacy`: Privacy settings flag
- `data_retention`: Data retention policy preference

### 3. subscriptions

**Purpose**: Stripe subscription management

```sql
CREATE TABLE public.subscriptions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  stripe_customer_id text NOT NULL,
  stripe_subscription_id text UNIQUE,
  stripe_price_id text NOT NULL,
  status text NOT NULL,
  current_period_start timestamp with time zone,
  current_period_end timestamp with time zone,
  cancel_at_period_end boolean DEFAULT false,
  billing_cycle text NOT NULL DEFAULT 'monthly'::text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT subscriptions_pkey PRIMARY KEY (id),
  CONSTRAINT subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `stripe_subscription_id`: Unique Stripe subscription identifier
- `status`: Subscription status (active, canceled, past_due, etc.)
- `billing_cycle`: Billing frequency (monthly, yearly)

### 4. subscription_overrides

**Purpose**: Administrative subscription overrides

```sql
CREATE TABLE public.subscription_overrides (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  admin_id uuid NOT NULL,
  original_tier text NOT NULL 
    CHECK (original_tier = ANY (ARRAY['guest'::text, 'standard'::text, 'edu'::text, 'edu_pro'::text, 'pro'::text])),
  override_tier text NOT NULL 
    CHECK (override_tier = ANY (ARRAY['standard'::text, 'edu'::text, 'edu_pro'::text, 'pro'::text])),
  start_date timestamp with time zone NOT NULL DEFAULT now(),
  end_date timestamp with time zone NOT NULL,
  reason text,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT subscription_overrides_pkey PRIMARY KEY (id),
  CONSTRAINT subscription_overrides_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT subscription_overrides_admin_id_fkey FOREIGN KEY (admin_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `original_tier`: User's original subscription tier
- `override_tier`: Temporary override tier
- `admin_id`: Administrator who created the override
- `end_date`: When override expires

### 5. user_datasets

**Purpose**: User-uploaded datasets and data management

```sql
CREATE TABLE public.user_datasets (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  dataset_name character varying NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  file_path character varying,
  CONSTRAINT user_datasets_pkey PRIMARY KEY (id),
  CONSTRAINT user_datasets_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `dataset_name`: User-defined name for the dataset
- `file_path`: Storage path for dataset file

**Business Rules**:
- Users can upload and manage multiple datasets
- Datasets are private to each user
- File storage handled through Supabase Storage

### 6. user_projects

**Purpose**: User project management and organization

```sql
CREATE TABLE public.user_projects (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  project_name character varying NOT NULL,
  file_path text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_projects_pkey PRIMARY KEY (id),
  CONSTRAINT user_projects_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `project_name`: User-defined project name
- `file_path`: Storage path for project data

**Business Rules**:
- Projects organize related analyses and datasets
- Projects are private to each user
- Support for project sharing (future enhancement)

### 7. notifications

**Purpose**: System-wide notifications and announcements

```sql
CREATE TABLE public.notifications (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  title text NOT NULL,
  message text NOT NULL,
  type text DEFAULT 'info'::text 
    CHECK (type = ANY (ARRAY['info'::text, 'success'::text, 'warning'::text, 'error'::text])),
  is_active boolean DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  expires_at timestamp with time zone,
  created_by uuid,
  priority integer DEFAULT 0,
  target_audience text DEFAULT 'all'::text 
    CHECK (target_audience = ANY (ARRAY['all'::text, 'pro'::text, 'edu'::text, 'standard'::text, 'guest'::text])),
  CONSTRAINT notifications_pkey PRIMARY KEY (id),
  CONSTRAINT notifications_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `type`: Notification type (info, success, warning, error)
- `target_audience`: Which user types see the notification
- `priority`: Display priority (higher numbers = higher priority)
- `expires_at`: Optional expiration date

### 8. user_notification_reads

**Purpose**: Track which notifications users have read

```sql
CREATE TABLE public.user_notification_reads (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  notification_id uuid,
  read_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  CONSTRAINT user_notification_reads_pkey PRIMARY KEY (id),
  CONSTRAINT user_notification_reads_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id),
  CONSTRAINT user_notification_reads_notification_id_fkey FOREIGN KEY (notification_id) REFERENCES public.notifications(id)
);
```

**Business Rules**:
- Tracks read status per user per notification
- Prevents duplicate read records
- Used for notification badge counts

### 9. events

**Purpose**: Application analytics and user activity tracking

```sql
CREATE TABLE public.events (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid,
  event_type text NOT NULL 
    CHECK (event_type = ANY (ARRAY['app_open'::text, 'login'::text, 'signin'::text, 'sign_in'::text, 'signed_in'::text])),
  timestamp timestamp with time zone NOT NULL DEFAULT now(),
  details jsonb,
  CONSTRAINT events_pkey PRIMARY KEY (id),
  CONSTRAINT events_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `event_type`: Type of event being tracked
- `details`: JSON object with additional event data
- `timestamp`: When the event occurred

**Business Rules**:
- Used for analytics and usage tracking
- Respects user privacy settings
- JSONB format allows flexible event data

### 10. admin_function_calls

**Purpose**: Audit trail for administrative actions

```sql
CREATE TABLE public.admin_function_calls (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  function_name text NOT NULL,
  called_at timestamp with time zone DEFAULT now(),
  ip_address inet,
  CONSTRAINT admin_function_calls_pkey PRIMARY KEY (id),
  CONSTRAINT admin_function_calls_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
```

**Key Fields**:
- `function_name`: Name of administrative function called
- `ip_address`: IP address of the administrator
- `called_at`: Timestamp of the action

**Business Rules**:
- Provides audit trail for administrative actions
- Required for compliance and security
- Tracks IP addresses for security analysis

## 🔐 Security Implementation

### Row Level Security (RLS)

All tables implement Row Level Security policies to ensure data privacy:

1. **User Data Isolation**: Users can only access their own data
2. **Admin Access**: Administrators have elevated access where appropriate
3. **Public Data**: Some data (like notifications) may be publicly readable

### Authentication Integration

- **Supabase Auth**: Handles user authentication and JWT tokens
- **Foreign Key Constraints**: All user data linked to auth.users(id)
- **Automatic User Creation**: Profiles created via database triggers

### Data Privacy

- **Encryption**: Sensitive data encrypted at rest
- **Access Controls**: Strict access controls via RLS
- **Audit Trails**: Administrative actions logged
- **Data Retention**: Configurable data retention policies

## 📈 Performance Considerations

### Indexing Strategy

```sql
-- User lookup indexes
CREATE INDEX idx_profiles_user_id ON profiles(id);
CREATE INDEX idx_user_datasets_user_id ON user_datasets(user_id);
CREATE INDEX idx_user_projects_user_id ON user_projects(user_id);

-- Notification indexes
CREATE INDEX idx_notifications_active ON notifications(is_active, target_audience);
CREATE INDEX idx_notifications_expires ON notifications(expires_at) WHERE expires_at IS NOT NULL;

-- Event tracking indexes
CREATE INDEX idx_events_user_timestamp ON events(user_id, timestamp);
CREATE INDEX idx_events_type_timestamp ON events(event_type, timestamp);

-- Subscription indexes
CREATE INDEX idx_subscriptions_user_status ON subscriptions(user_id, status);
CREATE INDEX idx_subscription_overrides_active ON subscription_overrides(user_id, is_active);
```

### Query Optimization

1. **Efficient Joins**: Use appropriate join strategies
2. **Pagination**: Implement cursor-based pagination for large datasets
3. **Caching**: Cache frequently accessed data
4. **Connection Pooling**: Use connection pooling for better performance

## 🔄 Data Migration Strategy

### Migration Management

- **Supabase Migrations**: Use Supabase CLI for schema changes
- **Version Control**: All migrations tracked in version control
- **Rollback Support**: Implement rollback procedures
- **Testing**: Test migrations on staging environment

### Migration Files Location

```
supabase/
├── migrations/
│   ├── 20240101000000_initial_schema.sql
│   ├── 20240102000000_add_user_settings.sql
│   └── README.md
└── functions/
    ├── user_profile_trigger.sql
    └── notification_cleanup.sql
```

## 🔧 Database Functions and Triggers

### Automatic Profile Creation

```sql
-- Trigger to create profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, created_at, updated_at)
  VALUES (new.id, new.email, now(), now());
  
  INSERT INTO public.user_settings (user_id)
  VALUES (new.id);
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

### Notification Cleanup

```sql
-- Function to clean up expired notifications
CREATE OR REPLACE FUNCTION public.cleanup_expired_notifications()
RETURNS void AS $$
BEGIN
  UPDATE public.notifications 
  SET is_active = false 
  WHERE expires_at < now() AND is_active = true;
END;
$$ LANGUAGE plpgsql;
```

## 📊 Data Relationships

### Entity Relationship Diagram

```
auth.users (Supabase managed)
    |
    +-- profiles (1:1)
    |
    +-- user_settings (1:1)
    |
    +-- subscriptions (1:many)
    |
    +-- subscription_overrides (1:many)
    |
    +-- user_datasets (1:many)
    |
    +-- user_projects (1:many)
    |
    +-- user_notification_reads (1:many)
    |
    +-- events (1:many)
    |
    +-- admin_function_calls (1:many)

notifications (many:many with users via user_notification_reads)
```

### Key Relationships

1. **User Profile**: One-to-one relationship with auth.users
2. **User Data**: One-to-many relationships for datasets, projects, events
3. **Subscriptions**: Support for multiple subscriptions per user
4. **Notifications**: Many-to-many relationship via read tracking table

## 🔍 Monitoring and Maintenance

### Database Monitoring

1. **Performance Metrics**: Monitor query performance and slow queries
2. **Storage Usage**: Track database size and growth
3. **Connection Monitoring**: Monitor connection pool usage
4. **Error Tracking**: Log and monitor database errors

### Maintenance Tasks

1. **Regular Backups**: Automated daily backups
2. **Index Maintenance**: Regular index analysis and optimization
3. **Data Cleanup**: Periodic cleanup of expired data
4. **Security Audits**: Regular security policy reviews

## 🚀 Future Enhancements

### Planned Features

1. **Collaboration**: Shared projects and datasets
2. **Advanced Analytics**: Enhanced user behavior tracking
3. **Data Versioning**: Version control for datasets
4. **Export/Import**: Bulk data operations
5. **Integration APIs**: Third-party service integrations

### Scalability Considerations

1. **Read Replicas**: Implement read replicas for better performance
2. **Partitioning**: Consider table partitioning for large tables
3. **Caching Layer**: Implement Redis caching for frequently accessed data
4. **CDN Integration**: Use CDN for static assets and file storage

## 📞 Support and Resources

### Database Administration

- **Supabase Dashboard**: Web-based database management
- **CLI Tools**: Supabase CLI for migrations and management
- **Monitoring**: Built-in monitoring and alerting
- **Backup/Restore**: Automated backup and restore capabilities

### Documentation Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Database Design Best Practices](https://supabase.com/docs/guides/database/design)

---

**Note**: This schema documentation should be updated whenever database changes are made. All schema modifications should go through proper migration procedures and code review processes.