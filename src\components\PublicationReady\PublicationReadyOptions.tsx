import React, { useState } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Chip,
  Avatar,
  CardHeader,
  Divider,
  Tooltip,
  IconButton,
  useTheme,
  alpha,
  Paper,
  Container,
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
// Remove Link from react-router-dom
// import { Link } from 'react-router-dom';
import {
  TableChart as TableIcon,
  CompareArrows as CompareIcon, // Add back CompareArrowsIcon import
  Functions as SMDIcon,
  TrendingUp as RegressionIcon,
  Psychology as InterpretationIcon,
  AccountTree as FlowIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  CompareArrows as CompareArrowsIcon, // Ensure CompareArrowsIcon is imported
  Psychology as PsychologyIcon,
  Folder as ResultsIcon, // Icon for Results Manager
  Science as ScienceIcon, // Icon for Statistical Methods Generator
  Calculate as EffectSizeIcon, // Icon for Effect Size Calculator
  Speed as PowerIcon, // Icon for Power Analysis Calculator
  AutoAwesome as EnhancedIcon, // Icon for Enhanced Statistical Methods Generator
  TableView as Table3Icon, // Icon for Table 3 Generator
  Image as ImageIcon, // Icon for Image DPI Converter
  MenuBook as CitationIcon, // Icon for Citation & Reference Manager
  Title as CaptionIcon, // Icon for Figure Caption Generator
  PhotoLibrary as ExportIcon, // Icon for Multi-Format Figure Exporter
} from '@mui/icons-material';

interface PublicationOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  // route: string; // Route is no longer needed in this component
  path: string; // Use path for onNavigate
  icon: React.ReactNode;
  category: 'Tables' | 'Analysis' | 'Visualization';
  color: string;
}

interface PublicationReadyOptionsProps {
  onNavigate: (path: string) => void;
}

export const publicationReadyOptions: PublicationOption[] = [
  {
    name: 'Table 1',
    shortDescription: 'Descriptive statistics table for baseline characteristics',
    detailedDescription: 'Generate a comprehensive Table 1 showing descriptive statistics for all baseline characteristics of your study participants. This is the standard first table in most research papers that presents demographic and clinical characteristics.',
    // route: '/app/publication-ready/table1', // Remove route
    path: 'publication-ready/table1', // Use path for onNavigate
    icon: <TableIcon />,
    category: 'Tables',
    color: '#2196F3',
  },
  {
    name: 'Table 1a',
    shortDescription: 'Alternative descriptive statistics with advanced formatting',
    detailedDescription: 'Create an enhanced version of Table 1 with additional statistical measures, custom formatting options, and advanced grouping capabilities. Ideal for complex studies requiring detailed baseline comparisons.',
    // route: '/app/publication-ready/table1a', // Remove route
    path: 'publication-ready/table1a', // Use path for onNavigate
    icon: <TableIcon />,
    category: 'Tables',
    color: '#2196F3',
  },
  {
    name: 'Table 1b',
    shortDescription: 'Comprehensive descriptive statistics for numerical variables',
    detailedDescription: 'Generate a comprehensive descriptive statistics table specifically designed for multiple numerical variables. Includes mean, standard deviation, median, quartiles, range, and normality tests with intelligent interpretation of distribution characteristics and variability patterns.',
    path: 'publication-ready/table1b',
    icon: <TableIcon />,
    category: 'Tables',
    color: '#1976D2',
  },
  {
    name: 'Table 2',
    shortDescription: 'Comparative statistics and outcome analysis table',
    detailedDescription: 'Generate Table 2 for presenting primary and secondary outcomes with between-group comparisons. Includes statistical tests, p-values, confidence intervals, and effect sizes appropriate for your study design.',
    // route: '/app/publication-ready/table2', // Remove route
    path: 'publication-ready/table2', // Use path for onNavigate
    icon: <CompareArrowsIcon />,
    category: 'Tables',
    color: '#4CAF50',
  },
  {
    name: 'Table 3 Generator',
    shortDescription: 'Correlation matrix tables with APA formatting',
    detailedDescription: 'Generate publication-ready correlation matrices (Table 3) with descriptive statistics, significance testing, and proper APA formatting. Supports Pearson, Spearman, and Kendall correlations with customizable display options.',
    path: 'publication-ready/table3-generator',
    icon: <Table3Icon />,
    category: 'Tables',
    color: '#795548',
  },
  {
    name: 'Effect Size Analysis',
    shortDescription: 'Comprehensive effect size calculations for publication',
    detailedDescription: 'Calculate and present effect sizes including Cohen\'s d, Hedge\'s g, Eta squared, and Cramer\'s V with confidence intervals. Essential for meta-analyses, systematic reviews, and reporting effect sizes for both continuous and categorical outcomes.',
    path: 'publication-ready/smd-table',
    icon: <SMDIcon />,
    category: 'Analysis',
    color: '#FF9800',
  },
  {
    name: 'Regression Table',
    shortDescription: 'Formatted tables for regression analysis results',
    detailedDescription: 'Create publication-ready tables for linear, logistic, or Cox regression models. Automatically formats coefficients, odds ratios, hazard ratios, confidence intervals, and p-values according to journal standards.',
    // route: '/app/publication-ready/regression-table', // Remove route
    path: 'publication-ready/regression-table', // Use path for onNavigate
    icon: <RegressionIcon />,
    category: 'Analysis',
    color: '#9C27B0',
  },
  {
    name: 'Regression Interpretation',
    shortDescription: 'AI-assisted interpretation of regression results',
    detailedDescription: 'Get intelligent assistance in interpreting your regression analysis results. Provides plain-language explanations of coefficients, statistical significance, clinical significance, and suggests appropriate conclusions.',
    // route: '/app/publication-ready/regression-interpretation', // Remove route
    path: 'publication-ready/regression-interpretation', // Use path for onNavigate
    icon: <InterpretationIcon />,
    category: 'Analysis',
    color: '#607D8B',
  },
  {
    name: 'Flow Diagram',
    shortDescription: 'CONSORT-style participant flow diagrams',
    detailedDescription: 'Create professional participant flow diagrams following CONSORT, STROBE, or PRISMA guidelines. Visualize enrollment, randomization, follow-up, and analysis phases with customizable design options.',
    // route: '/app/publication-ready/flow-diagram', // Remove route
    path: 'publication-ready/flow-diagram', // Use path for onNavigate
    icon: <FlowIcon />,
    category: 'Visualization',
    color: '#00BCD4',
  },
  {
    name: 'Convert to APA',
    shortDescription: 'Convert raw data tables to APA-style format',
    detailedDescription: 'Transform your raw data tables into properly formatted APA-style tables for academic publications. This tool helps ensure your tables meet the strict guidelines of the APA 7th edition.',
    path: 'publication-ready/convert-to-apa',
    icon: <PsychologyIcon />,
    category: 'Tables',
    color: '#8BC34A',
  },
  {
    name: 'PostHoc Tests',
    shortDescription: 'Perform multiple comparisons after ANOVA',
    detailedDescription: 'Conduct various post-hoc tests (e.g., Tukey\'s HSD, Bonferroni, Holm) to identify specific group differences after a significant ANOVA result. Includes detailed tables and visualizations.',
    path: 'publication-ready/posthoc-tests',
    icon: <CompareArrowsIcon />,
    category: 'Analysis',
    color: '#FF5722', // A distinct color for PostHoc Tests
  },
  {
    name: 'Statistical Methods Generator',
    shortDescription: 'Generate publication-ready methods sections',
    detailedDescription: 'Automatically create comprehensive Statistical Methods sections based on your completed analyses. Select multiple analyses, customize the generated text, and export in various formats for publication.',
    path: 'publication-ready/statistical-methods',
    icon: <ScienceIcon />,
    category: 'Analysis',
    color: '#4CAF50', // Green color for Methods Generator
  },

  {
    name: 'Results Manager',
    shortDescription: 'Manage and export analysis results',
    detailedDescription: 'Organize, filter, and export your analysis results in various formats. Collect results from multiple analyses and create comprehensive reports for publication or presentation.',
    path: 'publication-ready/results-manager',
    icon: <ResultsIcon />,
    category: 'Analysis',
    color: '#9C27B0', // Purple color for Results Manager
  },

  {
    name: 'Power Analysis Calculator',
    shortDescription: 'Statistical power analysis and sample size determination',
    detailedDescription: 'Calculate statistical power, determine required sample sizes, and perform sensitivity analyses for various statistical tests. Essential for study planning and post-hoc power analysis.',
    path: 'publication-ready/power-analysis-calculator',
    icon: <PowerIcon />,
    category: 'Analysis',
    color: '#FF6F00', // Orange color for Power Analysis Calculator
  },
  {
    name: 'Enhanced Methods Generator',
    shortDescription: 'Advanced statistical methods section generator with custom templates',
    detailedDescription: 'Enhanced version of the statistical methods generator with expanded templates, custom template creation, AI-powered suggestions, and advanced formatting options for publication-ready methods sections.',
    path: 'publication-ready/enhanced-statistical-methods',
    icon: <EnhancedIcon />,
    category: 'Analysis',
    color: '#3F51B5', // Indigo color for Enhanced Methods Generator
  },
  {
    name: 'Enhanced Figure Processor',
    shortDescription: 'Complete figure processing: DPI conversion, format export, and figure combination',
    detailedDescription: 'Comprehensive figure processing tool that combines DPI conversion, multi-format export, and figure combination capabilities. Convert images to publication-ready formats with precise DPI control, export in multiple formats (PNG, JPEG, TIFF, PDF, SVG, EPS), and combine multiple figures into professional layouts. Includes journal-specific presets and advanced customization options.',
    path: 'publication-ready/enhanced-figure-processor',
    icon: <ImageIcon />,
    category: 'Visualization',
    color: '#E91E63', // Pink color for Enhanced Figure Processor
  },
  {
    name: 'Citation & Reference Manager',
    shortDescription: 'Manage references and generate formatted citations',
    detailedDescription: 'Organize your research references and generate properly formatted citations in multiple academic styles (APA, AMA, Vancouver, Harvard, Chicago, MLA). Search PubMed, add manual entries, and export complete bibliographies for your publications.',
    path: 'publication-ready/citation-reference-manager',
    icon: <CitationIcon />,
    category: 'Analysis',
    color: '#1565C0', // Blue color for Citation Manager
  },
  {
    name: 'Figure Caption Generator',
    shortDescription: 'Create professional figure captions for publications',
    detailedDescription: 'Generate publication-ready figure captions formatted for different academic journals. Includes templates for various journal styles (Nature, Science, NEJM, JAMA, BMJ, PLOS) with automatic formatting and statistical information integration.',
    path: 'publication-ready/figure-caption-generator',
    icon: <CaptionIcon />,
    category: 'Visualization',
    color: '#7B1FA2', // Purple color for Caption Generator
  },

];

const PublicationReadyOptions: React.FC<PublicationReadyOptionsProps> = ({ onNavigate }) => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  // Generate structured data for publication-ready tools
  const generateStructuredData = () => {
    return {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Create Publication-Ready Statistical Reports",
      "description": "Comprehensive guide to generating academic publication materials including APA tables, statistical reports, figures, and journal-ready documentation.",
      "totalTime": "PT30M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "Completed statistical analysis results"
        },
        {
          "@type": "HowToSupply",
          "name": "Journal formatting guidelines"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "DataStatPro Publication Suite"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Generate statistical tables",
          "text": "Create APA-formatted tables (Table 1, 2, 3) with descriptive statistics, correlations, and regression results"
        },
        {
          "@type": "HowToStep",
          "name": "Prepare analysis reports",
          "text": "Generate comprehensive statistical reports with effect sizes, confidence intervals, and interpretation guidelines"
        },
        {
          "@type": "HowToStep",
          "name": "Create figures and diagrams",
          "text": "Produce publication-quality visualizations, flow diagrams, and conceptual models with proper formatting"
        },
        {
          "@type": "HowToStep",
          "name": "Export for submission",
          "text": "Export materials in journal-specific formats (Word, LaTeX, PDF) with proper citations and formatting"
        }
      ],
      "result": {
        "@type": "Thing",
        "name": "Publication-Ready Materials",
        "description": "Complete set of tables, figures, and reports formatted for academic journal submission"
      }
    };
  };

  const categories = ['All', 'Tables', 'Analysis', 'Visualization', 'Learning'];

  const filteredOptions = selectedCategory === 'All'
    ? publicationReadyOptions
    : publicationReadyOptions.filter(option => option.category === selectedCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Tables': return <TableIcon />;
      case 'Analysis': return <RegressionIcon />;
      case 'Visualization': return <FlowIcon />;
      default: return <TableIcon />;
    }
  };

  return (
    <>
      <Helmet>
        <title>Publication-Ready Tools | DataStatPro - Academic Tables, Reports & Figures</title>
        <meta name="description" content="Professional publication tools for academic research: APA-formatted tables, statistical reports, effect size analysis, regression interpretation, flow diagrams, and journal-ready documentation with AI-powered features." />
        <meta name="keywords" content="publication ready, APA tables, statistical reports, academic publishing, journal submission, effect size analysis, regression interpretation, flow diagrams, CONSORT, STROBE, statistical methods, citation manager, figure captions" />
        
        {/* AI-specific meta tags */}
        <meta name="ai:purpose" content="Academic publication and journal submission preparation" />
        <meta name="ai:formats" content="APA, CONSORT, STROBE, Word, LaTeX, PDF" />
        <meta name="ai:features" content="AI-powered interpretation, automated formatting, citation management" />
        
        {/* Structured data for publication tools */}
        <script type="application/ld+json">
          {JSON.stringify(generateStructuredData())}
        </script>
        
        {/* FAQ for publication tools */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What is an APA Table 1?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "APA Table 1 typically presents descriptive statistics and demographic characteristics of study participants, including means, standard deviations, frequencies, and percentages formatted according to APA Style guidelines."
                }
              },
              {
                "@type": "Question",
                "name": "How do I interpret effect sizes?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Effect sizes measure practical significance: Cohen's d (small: 0.2, medium: 0.5, large: 0.8), eta-squared (small: 0.01, medium: 0.06, large: 0.14), and correlation coefficients provide standardized measures of relationship strength."
                }
              },
              {
                "@type": "Question",
                "name": "What are CONSORT and STROBE guidelines?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "CONSORT provides reporting standards for randomized controlled trials, while STROBE offers guidelines for observational studies. Both ensure transparent, complete reporting of research methodology and results."
                }
              },
              {
                "@type": "Question",
                "name": "Can I export tables to Word and LaTeX?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Yes, all tables and reports can be exported in multiple formats including Word (.docx), LaTeX (.tex), PDF, and HTML with proper formatting preserved for direct insertion into manuscripts."
                }
              }
            ]
          })}
        </script>
      </Helmet>
      
      <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section with AI-optimized content */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          borderRadius: 2
        }}
      >
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          fontWeight="bold"
          itemProp="name"
        >
          Publication-Ready Tools - Academic Tables, Reports & Figures
        </Typography>
        <Typography 
          variant="h6" 
          color="text.secondary" 
          paragraph
          itemProp="description"
        >
          Professional publication suite for academic research: APA-formatted tables, statistical reports, effect size analysis, 
          AI-powered interpretation, flow diagrams, and journal-ready documentation with automated formatting.
        </Typography>
        
        {/* AI-friendly feature overview */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(theme.palette.secondary.main, 0.05), borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Publication Tools:</strong> APA Tables (1-3) • Statistical Reports • Effect Size Analysis • 
            AI Interpretation • Flow Diagrams • Citation Manager • Multi-format Export (Word, LaTeX, PDF)
          </Typography>
        </Box>
        
        <Typography variant="body1" color="text.secondary">
          Create publication-quality materials that meet journal standards with AI-powered statistical interpretation, 
          automated APA formatting, and comprehensive documentation for reproducible research.
        </Typography>
      </Paper>

      {/* Category Filter */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Filter by Category
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'filled' : 'outlined'}
              color={selectedCategory === category ? 'primary' : 'default'}
              icon={category !== 'All' ? getCategoryIcon(category) : undefined}
              sx={{
                '&:hover': {
                  backgroundColor: selectedCategory === category
                    ? theme.palette.primary.dark
                    : alpha(theme.palette.primary.main, 0.1)
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Options Grid */}
      <Grid container spacing={3}>
        {filteredOptions.map((option, index) => (
          <Grid item xs={12} md={6} lg={4} key={option.name}>
            <Card
              elevation={2}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'all 0.3s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8],
                  '& .launch-button': {
                    backgroundColor: option.color,
                    color: 'white',
                  }
                }
              }}
            >
              <CardHeader
                avatar={
                  <Avatar
                    sx={{
                      bgcolor: option.color,
                      width: 48,
                      height: 48,
                    }}
                  >
                    {option.icon}
                  </Avatar>
                }
                title={
                  <Typography 
                    variant="h6" 
                    fontWeight="bold"
                    itemProp="name"
                  >
                    {option.name}
                  </Typography>
                }
                subheader={
                  <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={option.category}
                      size="small"
                      variant="outlined"
                      color="primary"
                      itemProp="category"
                    />
                  </Box>
                }
                action={
                  <Tooltip title="More information">
                    <IconButton size="small">
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />

              <CardContent sx={{ flexGrow: 1, pt: 0 }}>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  paragraph
                  itemProp="description"
                >
                  {option.shortDescription}
                </Typography>

                <Typography 
                  variant="body2" 
                  paragraph
                  itemProp="additionalProperty"
                >
                  {option.detailedDescription}
                </Typography>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  className="launch-button"
                  variant="outlined"
                  fullWidth
                  // component={Link} // Remove Link component
                  // to={option.route} // Remove to prop
                  onClick={() => onNavigate(option.path)} // Use onClick with onNavigate
                  endIcon={<LaunchIcon />}
                  sx={{
                    borderColor: option.color,
                    color: option.color,
                    fontWeight: 'bold',
                    '&:hover': {
                      borderColor: option.color,
                    }
                  }}
                  aria-label={`Generate ${option.name} for publication`}
                  itemProp="potentialAction"
                >
                  Launch {option.name}
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Help Section */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mt: 4,
          backgroundColor: alpha(theme.palette.info.main, 0.05),
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
          <InfoIcon color="info" />
          <Box>
            <Typography variant="h6" gutterBottom color="info.main">
              Need Help Choosing?
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>New to research publishing?</strong> Start with Table 1 or Flow Diagram
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Analyzing interventions?</strong> Use Table 2 for outcomes and SMD Table for effect sizes
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              • <strong>Complex modeling?</strong> Try Regression Table and Regression Interpretation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Systematic reviews?</strong> SMD Table and Flow Diagram are essential tools
            </Typography>
          </Box>
        </Box>
      </Paper>
      </Container>
    </>
  );
};

export default PublicationReadyOptions;
