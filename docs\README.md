# DataStatPro Documentation

## 📚 Documentation Overview

This directory contains comprehensive documentation for the DataStatPro statistical analysis web application. The documentation is organized to serve different audiences and use cases, particularly optimized for AI-assisted development and maintenance.

## 🎯 Quick Navigation

### For AI Assistants & Developers
- **[Technical Architecture](./TECHNICAL_ARCHITECTURE.md)** - Complete system architecture and design patterns
- **[API Reference](./API_REFERENCE.md)** - Comprehensive API documentation for all interfaces and functions
- **[Development Guide](./DEVELOPMENT_GUIDE.md)** - Practical development workflows and common tasks

### For Users
- **[User Guide](./USER_GUIDE.md)** - Complete feature documentation and tutorials
- **[Quick Start Guide](./QUICK_START.md)** - Get started with DataStatPro in minutes

### For System Administrators
- **[Build Process](./BUILD_PROCESS.md)** - Deployment and build configuration
- **[Admin Guide](./ADMIN_GUIDE.md)** - Administrative features and user management

## 🤖 AI Assistant Guidelines

When working with DataStatPro, AI assistants should:

1. **Start with Architecture**: Review `TECHNICAL_ARCHITECTURE.md` to understand the system structure
2. **Reference APIs**: Use `API_REFERENCE.md` for accurate interface definitions and function signatures
3. **Follow Patterns**: Implement new features following patterns in `DEVELOPMENT_GUIDE.md`
4. **Maintain Consistency**: Follow existing code conventions and architectural decisions

### Key Context for AI Interactions

#### Application Type
- **React 18 + TypeScript** web application
- **Material-UI (MUI)** for UI components
- **Context API** for state management
- **Vite** for build tooling
- **Progressive Web App (PWA)** with offline capabilities

#### Core Domains
- **Statistical Analysis**: Descriptive, inferential, correlation, regression
- **Data Visualization**: Interactive charts with Recharts and D3.js
- **Data Management**: Import/export, transformation, validation
- **Educational Tools**: Guided workflows, tutorials, calculators
- **User Management**: Authentication, subscriptions, cloud storage

#### Technology Stack
```typescript
// Core Technologies
React: "^18.2.0"
TypeScript: "^5.0.0"
"@mui/material": "^5.14.0"
"recharts": "^2.8.0"
"d3": "^7.8.0"

// Backend Integration
"@supabase/supabase-js": "^2.38.0"

// Statistical Computing
"jstat": "^1.9.6"
"mathjs": "^11.11.0"

// Build Tools
"vite": "^4.4.0"
"@vitejs/plugin-react": "^4.0.0"
```

## 📁 Documentation Structure

### Core Documentation
| File | Purpose | Audience |
|------|---------|----------|
| `README.md` | Documentation overview and navigation | All |
| `TECHNICAL_ARCHITECTURE.md` | System architecture and design patterns | Developers, AI |
| `API_REFERENCE.md` | Complete API documentation | Developers, AI |
| `DEVELOPMENT_GUIDE.md` | Development workflows and patterns | Developers, AI |

### User Documentation
| File | Purpose | Audience |
|------|---------|----------|
| `USER_GUIDE.md` | Complete user manual | End Users |
| `QUICK_START.md` | Getting started guide | New Users |
| `FEATURE_OVERVIEW.md` | Feature descriptions | All Users |

### Administrative Documentation
| File | Purpose | Audience |
|------|---------|----------|
| `BUILD_PROCESS.md` | Build and deployment | DevOps, Admins |
| `ADMIN_GUIDE.md` | Administrative features | Administrators |
| `DEPLOYMENT.md` | Deployment instructions | DevOps |

### Specialized Documentation
| File | Purpose | Audience |
|------|---------|----------|
| `STATISTICAL_METHODS.md` | Statistical method documentation | Researchers, Educators |
| `VISUALIZATION_GUIDE.md` | Chart and visualization guide | Data Analysts |
| `ACCESSIBILITY.md` | Accessibility features | All |
| `SECURITY.md` | Security considerations | Developers, Admins |

## 🔍 Key Application Concepts

### Data Model
```typescript
// Core data structures
Dataset {
  id: string
  name: string
  columns: Column[]
  rows: DataRow[]
  metadata?: DatasetMetadata
}

Column {
  id: string
  name: string
  type: DataType // NUMERIC, CATEGORICAL, ORDINAL, BINARY, DATE, TEXT
  role?: VariableRole // INDEPENDENT, DEPENDENT, CONTROL, etc.
  statistics?: ColumnStatistics
}
```

### Context Architecture
```typescript
// Four main contexts manage application state
AuthContext    // User authentication and profiles
DataContext    // Dataset management and operations
ThemeContext   // UI theming and preferences
ResultsContext // Analysis results and visualizations
```

### Component Organization
```
components/
├── Account/         # User account management
├── Admin/           # Administrative interfaces
├── Advanced/        # Advanced statistical analysis
├── Auth/            # Authentication components
├── Correlation/     # Correlation analysis
├── Dashboard/       # Main dashboard
├── DataManagement/ # Data import/export/editing
├── DescriptiveStats/# Descriptive statistics
├── EpiCalc/         # Epidemiological calculator
├── InferentialStats/# Inferential statistics
├── Layout/          # App layout components
├── SampleSize/      # Sample size calculators
├── UI/              # Reusable UI components
└── Visualization/   # Data visualization
```

## 🛠️ Development Workflows

### Adding New Features
1. **Statistical Methods**: Follow patterns in `utils/stats/`
2. **UI Components**: Use Material-UI and follow existing patterns
3. **Routes**: Register in appropriate route module
4. **Types**: Add TypeScript interfaces to `types/index.ts`
5. **Tests**: Write unit tests for calculations and components

### Common Tasks
- **New Statistical Test**: See `DEVELOPMENT_GUIDE.md` section "Adding a New Statistical Test"
- **New Visualization**: See `DEVELOPMENT_GUIDE.md` section "Adding a New Visualization Type"
- **Data Import Format**: See `DEVELOPMENT_GUIDE.md` section "Adding a New Data Import Format"

## 🧪 Testing Strategy

### Test Types
- **Unit Tests**: Individual functions and components
- **Integration Tests**: Context and service integration
- **Statistical Validation**: Accuracy of calculations
- **E2E Tests**: Complete user workflows

### Test Locations
```
__tests__/
├── components/     # Component tests
├── utils/          # Utility function tests
├── stats/          # Statistical calculation tests
└── integration/    # Integration tests
```

## 🔐 Security Considerations

### Authentication
- Supabase-based authentication with JWT tokens
- Row Level Security (RLS) for data protection
- Guest access with limited functionality

### Data Protection
- Client-side data validation
- Encrypted local storage for sensitive data
- GDPR-compliant user data management

## 📊 Performance Optimization

### Frontend
- Code splitting with React.lazy
- Memoization for expensive calculations
- Virtual scrolling for large datasets
- Progressive loading strategies

### Statistical Computing
- Web Workers for heavy calculations
- Result caching
- Streaming for large datasets

## 🚀 Deployment

### Build Process
```bash
npm run build     # Production build
npm run preview   # Preview production build
npm run deploy    # Deploy to production
```

### Environment Configuration
- Development: Local Supabase instance
- Staging: Cloud Supabase with test data
- Production: Cloud Supabase with live data

## 📱 Progressive Web App Features

### Offline Capabilities
- Statistical calculations work offline
- Data visualization available offline
- Local data storage and synchronization

### Mobile Optimization
- Responsive design for all screen sizes
- Touch-friendly interfaces
- Mobile-specific navigation patterns

## 🎓 Educational Features

### Learning Tools
- Step-by-step analysis tutorials
- Statistical method explanations
- Interactive examples and sample data
- "Which Test" decision tree

### Content Integration
- YouTube tutorial integration
- Daily statistical tips
- Method selection guidance

## 🔄 Data Flow Patterns

### Typical Analysis Workflow
```
Data Import → Validation → Analysis Selection → 
Computation → Results Display → Visualization → Export
```

### State Management Flow
```
User Action → Component → Context → Service → 
Utility → Result → UI Update
```

## 📈 Future Roadmap

### Planned Features
- Real-time collaboration
- Advanced machine learning integration
- Enhanced mobile app
- API for external integrations
- Advanced visualization capabilities

### Technical Improvements
- Performance optimization
- Enhanced testing coverage
- Improved accessibility
- Better error handling

---

## 📞 Support and Contribution

For questions about the documentation or suggestions for improvements:

1. Review existing documentation thoroughly
2. Check the `DEVELOPMENT_GUIDE.md` for common patterns
3. Refer to `API_REFERENCE.md` for technical details
4. Follow established conventions and patterns

This documentation is designed to provide comprehensive context for AI assistants and developers working with DataStatPro. Keep it updated as the application evolves.