<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Meta Test - DataStatPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 Social Media Meta Tag Test</h1>
    
    <div class="test-section">
        <h2>Test Tutorial Pages</h2>
        <p>Click these links to test the tutorial pages and their meta tags:</p>
        
        <a href="/app/knowledge-base/logistic-regression" class="test-link">Logistic Regression Tutorial</a>
        <a href="/app/knowledge-base/t-tests-and-alternatives" class="test-link">T-Tests Tutorial</a>
        <a href="/app/knowledge-base/anova-tests-and-alternatives" class="test-link">ANOVA Tutorial</a>
        <a href="/app/knowledge-base/correlation-linear-regression" class="test-link">Correlation Tutorial</a>
        <a href="/app/knowledge-base/sample-size-power-analysis" class="test-link">Sample Size Tutorial</a>
    </div>
    
    <div class="test-section">
        <h2>Social Media Sharing Test</h2>
        <p>Test these URLs with social media platform debuggers:</p>
        
        <div class="debug-info">
            <strong>Facebook Debugger:</strong><br>
            <a href="https://developers.facebook.com/tools/debug/" target="_blank">https://developers.facebook.com/tools/debug/</a><br><br>
            
            <strong>LinkedIn Post Inspector:</strong><br>
            <a href="https://www.linkedin.com/post-inspector/" target="_blank">https://www.linkedin.com/post-inspector/</a><br><br>
            
            <strong>Twitter Card Validator:</strong><br>
            <a href="https://cards-dev.twitter.com/validator" target="_blank">https://cards-dev.twitter.com/validator</a>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test URLs for Social Media</h2>
        <p>Copy these URLs and test them in the social media debuggers above:</p>
        
        <div class="debug-info">
            <strong>Logistic Regression:</strong><br>
            https://datastatpro.com/app/knowledge-base/logistic-regression<br><br>
            
            <strong>T-Tests Tutorial:</strong><br>
            https://datastatpro.com/app/knowledge-base/t-tests-and-alternatives<br><br>
            
            <strong>ANOVA Tutorial:</strong><br>
            https://datastatpro.com/app/knowledge-base/anova-tests-and-alternatives<br><br>
            
            <strong>Correlation Tutorial:</strong><br>
            https://datastatpro.com/app/knowledge-base/correlation-linear-regression<br><br>
            
            <strong>Sample Size Tutorial:</strong><br>
            https://datastatpro.com/app/knowledge-base/sample-size-power-analysis
        </div>
    </div>
    
    <div class="test-section">
        <h2>How It Works</h2>
        <p>The solution implements:</p>
        <ul>
            <li><strong>Crawler Detection:</strong> .htaccess detects social media crawlers (Facebook, LinkedIn, Twitter, etc.)</li>
            <li><strong>Dynamic Meta Tags:</strong> PHP script serves appropriate meta tags based on tutorial ID</li>
            <li><strong>User Redirection:</strong> Regular users are redirected to the React app</li>
            <li><strong>SEO Optimization:</strong> Each tutorial page has unique Open Graph and Twitter Card meta tags</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Expected Results</h2>
        <p>When testing with social media debuggers, you should see:</p>
        <ul>
            <li>✅ Correct page title for each tutorial</li>
            <li>✅ Unique description for each tutorial</li>
            <li>✅ Proper Open Graph image (DataStatPro logo)</li>
            <li>✅ Article type for tutorial pages</li>
            <li>✅ Structured data for educational resources</li>
        </ul>
    </div>
    
    <script>
        // Display current page info
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Social Meta Test Page Loaded');
            console.log('Current URL:', window.location.href);
            console.log('User Agent:', navigator.userAgent);
        });
    </script>
</body>
</html>