import React, { useState, useCallback, useContext, useMemo } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  Divider,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Tooltip,
  IconButton,
  Avatar,
  alpha,
  useTheme,
  TextField,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup,
  Grid,
  CardHeader,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Fade,
  Zoom,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  Psychology as PsychologyIcon,
  Launch as LaunchIcon,
  Login as LoginIcon,
  Upgrade as UpgradeIcon,
  School as SchoolIcon,
  Star as StarIcon,
  PlayArrow as PlayArrowIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  AutoAwesome as AutoAwesomeIcon,
  Search as ExploreIcon,
  Storage as DataManagementIcon,
  ShowChart as DescriptiveIcon,
  Science as InferentialIcon,
  CompareArrows as CorrelationIcon,
  Psychology as AdvancedIcon,
  BarChart as VisualizationIcon,
  Calculate as SampleSizeIcon,
  LocalHospital as EpiCalcIcon,
  Functions as CICalculatorIcon,
  Article as PublicationIcon,
  Lightbulb as LightbulbIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';

// Import Analysis Index options
import { descriptiveStatsOptions } from '../DescriptiveStats/DescriptiveStatsOptions';
import { inferentialStatsOptions } from '../InferentialStats/InferentialStatsOptions';
import { correlationAnalysisOptions } from '../CorrelationAnalysis/CorrelationAnalysisOptions';
import { advancedAnalysisOptions } from '../AdvancedAnalysisAliases/AdvancedAnalysisOptions';
import { epiCalcOptions } from '../EpiCalc/EpiCalcOptions';
import { sampleSizeCalculatorOptions } from '../SampleSizeCalculators/SampleSizeCalculatorsOptions';
import { ciCalculatorOptions } from '../CICalculators/CICalculatorsOptions';
import { dataVisualizationOptions } from '../Visualization/DataVisualizationOptions';
import { dataManagementOptions } from '../DataManagement/DataManagementOptions';
import { publicationReadyOptions } from '../PublicationReady/PublicationReadyOptions';

// Common interface for all analysis options (from Analysis Index)
interface AnalysisOption {
  name: string;
  shortDescription: string;
  detailedDescription: string;
  path: string;
  icon: React.ReactNode;
  category: string;
  color: string;
}

// Category structure that matches Analysis Index
interface CategoryGroup {
  name: string;
  icon: React.ReactNode;
  color: string;
  options: AnalysisOption[];
  description: string;
  accessLevel: 'guest' | 'standard' | 'pro';
}

interface ViewMode {
  mode: 'smart' | 'guided' | 'explore' | 'search';
  label: string;
  icon: React.ReactNode;
  description: string;
}

interface InteractiveStatisticalAdvisorProps {
  currentDataset?: any;
  datasetAnalysis?: any;
}

// View modes for different interaction styles
const VIEW_MODES: ViewMode[] = [
  { 
    mode: 'smart', 
    label: 'Smart Recommendations', 
    icon: <AutoAwesomeIcon />,
    description: 'AI-powered analysis suggestions based on your data'
  },
  { 
    mode: 'guided', 
    label: 'Guided Workflow', 
    icon: <TimelineIcon />,
    description: 'Step-by-step analysis guidance'
  },
  { 
    mode: 'explore', 
    label: 'Explore Methods', 
    icon: <ExploreIcon />,
    description: 'Browse all available analysis methods'
  },
  { 
    mode: 'search', 
    label: 'Search & Filter', 
    icon: <SearchIcon />,
    description: 'Find specific analysis methods'
  }
];

const InteractiveStatisticalAdvisor: React.FC<InteractiveStatisticalAdvisorProps> = ({
  currentDataset,
  datasetAnalysis
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const authContext = useContext(AuthContext);
  const { 
    user, 
    isGuest, 
    accountType, 
    effectiveTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    isEducationalUser,
    educationalTier
  } = authContext || {};

  // State management
  const [viewMode, setViewMode] = useState<'smart' | 'guided' | 'explore' | 'search'>('smart');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [bookmarkedMethods, setBookmarkedMethods] = useState<Set<string>>(new Set());
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['descriptive']));
  const [showOnlyAccessible, setShowOnlyAccessible] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<number>(0);

  const isAuthenticated = !!user;

  // Create hierarchical category groups using Analysis Index data
  const categoryGroups: CategoryGroup[] = useMemo(() => {
    const groups: CategoryGroup[] = [
      {
        name: 'Data Management',
        icon: <DataManagementIcon />,
        color: '#795548',
        options: dataManagementOptions,
        description: 'Import, clean, and prepare your data for analysis',
        accessLevel: 'guest'
      },
      {
        name: 'Descriptive Statistics',
        icon: <DescriptiveIcon />,
        color: '#4CAF50',
        options: descriptiveStatsOptions,
        description: 'Summarize and explore your data characteristics',
        accessLevel: 'guest'
      },
      {
        name: 'Inferential Statistics',
        icon: <InferentialIcon />,
        color: '#2196F3',
        options: inferentialStatsOptions,
        description: 'Test hypotheses and make statistical inferences',
        accessLevel: 'guest'
      },
      {
        name: 'Correlation Analysis',
        icon: <CorrelationIcon />,
        color: '#FF9800',
        options: correlationAnalysisOptions,
        description: 'Examine relationships between variables',
        accessLevel: 'standard'
      },
      {
        name: 'Advanced Analysis',
        icon: <AdvancedIcon />,
        color: '#9C27B0',
        options: advancedAnalysisOptions,
        description: 'Sophisticated statistical modeling and analysis',
        accessLevel: 'pro'
      },
      {
        name: 'Data Visualization',
        icon: <VisualizationIcon />,
        color: '#00BCD4',
        options: dataVisualizationOptions,
        description: 'Create charts and graphs to visualize your data',
        accessLevel: 'guest'
      },
      {
        name: 'Sample Size Calculator',
        icon: <SampleSizeIcon />,
        color: '#FF5722',
        options: sampleSizeCalculatorOptions,
        description: 'Calculate required sample sizes for studies',
        accessLevel: 'standard'
      },
      {
        name: 'Epi Calculator',
        icon: <EpiCalcIcon />,
        color: '#607D8B',
        options: epiCalcOptions,
        description: 'Epidemiological calculations and measures',
        accessLevel: 'standard'
      },
      {
        name: 'CI Calculator',
        icon: <CICalculatorIcon />,
        color: '#E91E63',
        options: ciCalculatorOptions,
        description: 'Calculate confidence intervals for various statistics',
        accessLevel: 'standard'
      },
      {
        name: 'Publication Ready',
        icon: <PublicationIcon />,
        color: '#3F51B5',
        options: publicationReadyOptions,
        description: 'Generate publication-quality tables and reports',
        accessLevel: 'pro'
      }
    ];

    return groups;
  }, []);

  // Helper function to check if user can access a category
  const canAccessCategory = useCallback((accessLevel: string): boolean => {
    if (isGuest) return accessLevel === 'guest';
    if (!user) return accessLevel === 'guest';
    
    const tierHierarchy = { guest: 0, standard: 1, edu: 1, pro: 2, edu_pro: 2 };
    const requiredTierLevel = tierHierarchy[accessLevel as keyof typeof tierHierarchy] || 0;
    const userTierLevel = tierHierarchy[effectiveTier as keyof typeof tierHierarchy] || 0;
    
    return userTierLevel >= requiredTierLevel;
  }, [isGuest, user, effectiveTier]);

  // Helper function to check if user can access a specific method
  const canAccessMethod = useCallback((methodPath: string): boolean => {
    // Pro features check
    if ((methodPath.startsWith('advanced-analysis') || methodPath.startsWith('publication-ready')) && !canAccessAdvancedAnalysis) {
      return false;
    }
    return true;
  }, [canAccessAdvancedAnalysis]);

  // Helper function to navigate to analysis
  const navigateToAnalysis = useCallback((path: string) => {
    if (canAccessMethod(path)) {
      navigate(`/app/${path}`);
    }
  }, [navigate, canAccessMethod]);

  // Helper function to toggle bookmark
  const toggleBookmark = useCallback((methodId: string) => {
    setBookmarkedMethods(prev => {
      const newSet = new Set(prev);
      if (newSet.has(methodId)) {
        newSet.delete(methodId);
      } else {
        newSet.add(methodId);
      }
      return newSet;
    });
  }, []);

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PsychologyIcon color="primary" />
            <Typography variant="h6">
              Interactive Statistical Advisor
            </Typography>
            <Chip 
              icon={<AutoAwesomeIcon />} 
              label="AI" 
              color="primary" 
              size="small" 
            />
          </Box>
          
          {/* User Tier Status */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isGuest ? (
              <Chip 
                label="Guest Access" 
                color="default" 
                size="small"
                icon={<ExploreIcon />}
              />
            ) : isEducationalUser ? (
              <Chip 
                icon={<SchoolIcon />} 
                label={educationalTier === 'pro' ? 'Educational Pro' : 'Educational'} 
                color="secondary" 
                size="small" 
              />
            ) : (
              <Chip 
                label={`${effectiveTier?.toUpperCase()} Account`}
                color={effectiveTier === 'pro' ? 'success' : effectiveTier === 'standard' ? 'primary' : 'default'}
                size="small"
                icon={effectiveTier === 'pro' ? <StarIcon /> : undefined}
              />
            )}
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Discover the perfect statistical analysis for your research with intelligent recommendations and interactive guidance.
        </Typography>

        {/* View Mode Toggle */}
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={(e, newMode) => newMode && setViewMode(newMode)}
          size="small"
          sx={{ mb: 2 }}
        >
          {VIEW_MODES.map((mode) => (
            <ToggleButton key={mode.mode} value={mode.mode}>
              <Tooltip title={mode.description}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {mode.icon}
                  <Typography variant="caption">
                    {mode.label}
                  </Typography>
                </Box>
              </Tooltip>
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>

      {/* Smart Recommendations Mode */}
      {viewMode === 'smart' && (
        <SmartRecommendationsView
          currentDataset={currentDataset}
          datasetAnalysis={datasetAnalysis}
          categoryGroups={categoryGroups}
          canAccessMethod={canAccessMethod}
          navigateToAnalysis={navigateToAnalysis}
          toggleBookmark={toggleBookmark}
          bookmarkedMethods={bookmarkedMethods}
        />
      )}

      {/* Guided Workflow Mode */}
      {viewMode === 'guided' && (
        <GuidedWorkflowView
          currentStep={currentStep}
          setCurrentStep={setCurrentStep}
          categoryGroups={categoryGroups}
          canAccessMethod={canAccessMethod}
          navigateToAnalysis={navigateToAnalysis}
        />
      )}

      {/* Explore Methods Mode */}
      {viewMode === 'explore' && (
        <ExploreMethodsView
          categoryGroups={categoryGroups}
          expandedCategories={expandedCategories}
          setExpandedCategories={setExpandedCategories}
          canAccessCategory={canAccessCategory}
          canAccessMethod={canAccessMethod}
          navigateToAnalysis={navigateToAnalysis}
          toggleBookmark={toggleBookmark}
          bookmarkedMethods={bookmarkedMethods}
          showOnlyAccessible={showOnlyAccessible}
          setShowOnlyAccessible={setShowOnlyAccessible}
        />
      )}

      {/* Search & Filter Mode */}
      {viewMode === 'search' && (
        <SearchFilterView
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          categoryGroups={categoryGroups}
          canAccessMethod={canAccessMethod}
          navigateToAnalysis={navigateToAnalysis}
          toggleBookmark={toggleBookmark}
          bookmarkedMethods={bookmarkedMethods}
        />
      )}
    </Box>
  );
};

// Smart Recommendations View Component
const SmartRecommendationsView: React.FC<{
  currentDataset?: any;
  datasetAnalysis?: any;
  categoryGroups: CategoryGroup[];
  canAccessMethod: (path: string) => boolean;
  navigateToAnalysis: (path: string) => void;
  toggleBookmark: (id: string) => void;
  bookmarkedMethods: Set<string>;
}> = ({
  currentDataset,
  datasetAnalysis,
  categoryGroups,
  canAccessMethod,
  navigateToAnalysis,
  toggleBookmark,
  bookmarkedMethods
}) => {
  const theme = useTheme();

  // Generate smart recommendations based on dataset
  const smartRecommendations = useMemo(() => {
    if (!currentDataset || !datasetAnalysis) {
      // Return default recommendations for new users
      return [
        {
          category: 'Getting Started',
          methods: categoryGroups.find(g => g.name === 'Data Management')?.options.slice(0, 2) || [],
          reason: 'Start by importing and exploring your data'
        },
        {
          category: 'Basic Analysis',
          methods: categoryGroups.find(g => g.name === 'Descriptive Statistics')?.options || [],
          reason: 'Understand your data with descriptive statistics'
        }
      ];
    }

    // Analyze dataset and provide contextual recommendations
    const recommendations = [];
    const { variableAnalysis } = datasetAnalysis;

    const numericVars = variableAnalysis?.filter((v: any) => v.type === 'numeric').length || 0;
    const categoricalVars = variableAnalysis?.filter((v: any) => v.type === 'categorical').length || 0;

    if (numericVars > 0) {
      recommendations.push({
        category: 'Recommended for Your Data',
        methods: categoryGroups.find(g => g.name === 'Descriptive Statistics')?.options || [],
        reason: `You have ${numericVars} numeric variable(s) - start with descriptive statistics`
      });
    }

    if (numericVars >= 1 && categoricalVars >= 1) {
      recommendations.push({
        category: 'Group Comparisons',
        methods: categoryGroups.find(g => g.name === 'Inferential Statistics')?.options.slice(0, 3) || [],
        reason: 'Compare groups using your numeric and categorical variables'
      });
    }

    if (numericVars >= 2) {
      recommendations.push({
        category: 'Relationships',
        methods: categoryGroups.find(g => g.name === 'Correlation Analysis')?.options || [],
        reason: 'Explore relationships between your numeric variables'
      });
    }

    return recommendations;
  }, [currentDataset, datasetAnalysis, categoryGroups]);

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <AutoAwesomeIcon color="primary" />
        Smart Recommendations
      </Typography>

      {!currentDataset ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Load a dataset to get personalized analysis recommendations, or explore the methods below to get started.
          </Typography>
        </Alert>
      ) : (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Dataset loaded:</strong> {currentDataset.name || 'Your data'} -
            {datasetAnalysis?.totalRows || 0} rows, {datasetAnalysis?.variableAnalysis?.length || 0} variables
          </Typography>
        </Alert>
      )}

      {smartRecommendations.map((recommendation, index) => (
        <Fade in timeout={300 * (index + 1)} key={recommendation.category}>
          <Paper elevation={2} sx={{ mb: 3, p: 2 }}>
            <Typography variant="h6" sx={{ mb: 1, color: 'primary.main' }}>
              {recommendation.category}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {recommendation.reason}
            </Typography>

            <Grid container spacing={2}>
              {recommendation.methods.slice(0, 3).map((method) => (
                <Grid item xs={12} md={4} key={method.name}>
                  <InteractiveMethodCard
                    method={method}
                    canAccess={canAccessMethod(method.path)}
                    onNavigate={navigateToAnalysis}
                    onBookmark={toggleBookmark}
                    isBookmarked={bookmarkedMethods.has(method.name)}
                    compact
                  />
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Fade>
      ))}
    </Box>
  );
};

// Guided Workflow View Component
const GuidedWorkflowView: React.FC<{
  currentStep: number;
  setCurrentStep: (step: number) => void;
  categoryGroups: CategoryGroup[];
  canAccessMethod: (path: string) => boolean;
  navigateToAnalysis: (path: string) => void;
}> = ({ currentStep, setCurrentStep, categoryGroups, canAccessMethod, navigateToAnalysis }) => {
  const workflowSteps = [
    {
      label: 'Prepare Your Data',
      description: 'Import and clean your dataset',
      category: 'Data Management',
      icon: <DataManagementIcon />
    },
    {
      label: 'Explore Your Data',
      description: 'Understand data characteristics',
      category: 'Descriptive Statistics',
      icon: <DescriptiveIcon />
    },
    {
      label: 'Test Hypotheses',
      description: 'Perform statistical tests',
      category: 'Inferential Statistics',
      icon: <InferentialIcon />
    },
    {
      label: 'Examine Relationships',
      description: 'Analyze variable relationships',
      category: 'Correlation Analysis',
      icon: <CorrelationIcon />
    },
    {
      label: 'Visualize Results',
      description: 'Create charts and graphs',
      category: 'Data Visualization',
      icon: <VisualizationIcon />
    }
  ];

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <TimelineIcon color="primary" />
        Guided Analysis Workflow
      </Typography>

      <Stepper activeStep={currentStep} orientation="vertical">
        {workflowSteps.map((step, index) => {
          const categoryGroup = categoryGroups.find(g => g.name === step.category);

          return (
            <Step key={step.label}>
              <StepLabel
                icon={
                  <Avatar sx={{
                    bgcolor: categoryGroup?.color || 'primary.main',
                    width: 32,
                    height: 32
                  }}>
                    {step.icon}
                  </Avatar>
                }
              >
                <Typography variant="subtitle1" fontWeight="medium">
                  {step.label}
                </Typography>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {step.description}
                </Typography>

                {categoryGroup && (
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    {categoryGroup.options.slice(0, 2).map((method) => (
                      <Grid item xs={12} sm={6} key={method.name}>
                        <InteractiveMethodCard
                          method={method}
                          canAccess={canAccessMethod(method.path)}
                          onNavigate={navigateToAnalysis}
                          onBookmark={() => {}}
                          isBookmarked={false}
                          compact
                        />
                      </Grid>
                    ))}
                  </Grid>
                )}

                <Box sx={{ mb: 1 }}>
                  <Button
                    variant="contained"
                    onClick={() => setCurrentStep(index + 1)}
                    sx={{ mt: 1, mr: 1 }}
                    disabled={index === workflowSteps.length - 1}
                  >
                    {index === workflowSteps.length - 1 ? 'Complete' : 'Continue'}
                  </Button>
                  <Button
                    disabled={index === 0}
                    onClick={() => setCurrentStep(index - 1)}
                    sx={{ mt: 1, mr: 1 }}
                  >
                    Back
                  </Button>
                </Box>
              </StepContent>
            </Step>
          );
        })}
      </Stepper>
    </Box>
  );
};

// Explore Methods View Component
const ExploreMethodsView: React.FC<{
  categoryGroups: CategoryGroup[];
  expandedCategories: Set<string>;
  setExpandedCategories: (categories: Set<string>) => void;
  canAccessCategory: (accessLevel: string) => boolean;
  canAccessMethod: (path: string) => boolean;
  navigateToAnalysis: (path: string) => void;
  toggleBookmark: (id: string) => void;
  bookmarkedMethods: Set<string>;
  showOnlyAccessible: boolean;
  setShowOnlyAccessible: (show: boolean) => void;
}> = ({
  categoryGroups,
  expandedCategories,
  setExpandedCategories,
  canAccessCategory,
  canAccessMethod,
  navigateToAnalysis,
  toggleBookmark,
  bookmarkedMethods,
  showOnlyAccessible,
  setShowOnlyAccessible
}) => {
  const theme = useTheme();

  const toggleCategoryExpansion = (categoryName: string) => {
    const newSet = new Set(expandedCategories);
    if (newSet.has(categoryName)) {
      newSet.delete(categoryName);
    } else {
      newSet.add(categoryName);
    }
    setExpandedCategories(newSet);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ExploreIcon color="primary" />
          Explore Analysis Methods
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={showOnlyAccessible}
              onChange={(e) => setShowOnlyAccessible(e.target.checked)}
              size="small"
            />
          }
          label="Show only accessible"
        />
      </Box>

      {categoryGroups.map((group) => {
        const hasAccess = canAccessCategory(group.accessLevel);
        const accessibleMethods = group.options.filter(method => canAccessMethod(method.path));
        const visibleMethods = showOnlyAccessible ? accessibleMethods : group.options;

        if (visibleMethods.length === 0) return null;

        return (
          <Accordion
            key={group.name}
            expanded={expandedCategories.has(group.name)}
            onChange={() => toggleCategoryExpansion(group.name)}
            sx={{
              mb: 2,
              '&:before': { display: 'none' },
              boxShadow: theme.shadows[2],
              opacity: hasAccess ? 1 : 0.7
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                bgcolor: alpha(group.color, 0.05),
                '&:hover': { bgcolor: alpha(group.color, 0.1) }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <Avatar sx={{
                  bgcolor: alpha(group.color, 0.15),
                  color: group.color,
                  width: 40,
                  height: 40
                }}>
                  {group.icon}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {group.name}
                    {!hasAccess && (
                      <Chip
                        label="PRO"
                        size="small"
                        color="warning"
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    )}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {group.description}
                  </Typography>
                </Box>
                <Chip
                  label={`${accessibleMethods.length}/${group.options.length}`}
                  size="small"
                  color={accessibleMethods.length === group.options.length ? 'success' : 'warning'}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ bgcolor: alpha(theme.palette.grey[50], 0.5) }}>
              <Grid container spacing={2}>
                {visibleMethods.map((method) => (
                  <Grid item xs={12} md={6} lg={4} key={method.name}>
                    <InteractiveMethodCard
                      method={method}
                      canAccess={canAccessMethod(method.path)}
                      onNavigate={navigateToAnalysis}
                      onBookmark={toggleBookmark}
                      isBookmarked={bookmarkedMethods.has(method.name)}
                    />
                  </Grid>
                ))}
              </Grid>
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

// Search & Filter View Component
const SearchFilterView: React.FC<{
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  categoryGroups: CategoryGroup[];
  canAccessMethod: (path: string) => boolean;
  navigateToAnalysis: (path: string) => void;
  toggleBookmark: (id: string) => void;
  bookmarkedMethods: Set<string>;
}> = ({
  searchQuery,
  setSearchQuery,
  categoryGroups,
  canAccessMethod,
  navigateToAnalysis,
  toggleBookmark,
  bookmarkedMethods
}) => {
  // Flatten all methods for searching
  const allMethods = useMemo(() => {
    return categoryGroups.flatMap(group =>
      group.options.map(method => ({
        ...method,
        categoryName: group.name,
        categoryColor: group.color
      }))
    );
  }, [categoryGroups]);

  // Filter methods based on search query
  const filteredMethods = useMemo(() => {
    if (!searchQuery.trim()) return allMethods;

    const query = searchQuery.toLowerCase();
    return allMethods.filter(method =>
      method.name.toLowerCase().includes(query) ||
      method.shortDescription.toLowerCase().includes(query) ||
      method.detailedDescription.toLowerCase().includes(query) ||
      method.categoryName.toLowerCase().includes(query)
    );
  }, [allMethods, searchQuery]);

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchIcon color="primary" />
        Search & Filter Methods
      </Typography>

      <TextField
        fullWidth
        placeholder="Search analysis methods, descriptions, or categories..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 3 }}
      />

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        {filteredMethods.length} method{filteredMethods.length !== 1 ? 's' : ''} found
        {searchQuery && ` for "${searchQuery}"`}
      </Typography>

      {filteredMethods.length > 0 ? (
        <Grid container spacing={2}>
          {filteredMethods.map((method) => (
            <Grid item xs={12} md={6} lg={4} key={method.name}>
              <InteractiveMethodCard
                method={method}
                canAccess={canAccessMethod(method.path)}
                onNavigate={navigateToAnalysis}
                onBookmark={toggleBookmark}
                isBookmarked={bookmarkedMethods.has(method.name)}
                showCategory
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info">
          <Typography variant="body2">
            No methods found matching your search criteria. Try different keywords or browse all methods.
          </Typography>
        </Alert>
      )}
    </Box>
  );
};

// Interactive Method Card Component
const InteractiveMethodCard: React.FC<{
  method: AnalysisOption & { categoryName?: string; categoryColor?: string };
  canAccess: boolean;
  onNavigate: (path: string) => void;
  onBookmark: (id: string) => void;
  isBookmarked: boolean;
  compact?: boolean;
  showCategory?: boolean;
}> = ({ method, canAccess, onNavigate, onBookmark, isBookmarked, compact = false, showCategory = false }) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        cursor: 'pointer',
        opacity: canAccess ? 1 : 0.7,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          boxShadow: theme.shadows[canAccess ? 6 : 2],
          transform: canAccess ? 'translateY(-2px)' : 'none'
        }
      }}
      onClick={() => canAccess && onNavigate(method.path)}
    >
      {/* Access Level Indicator */}
      {!canAccess && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            bgcolor: theme.palette.warning.main,
            color: 'white',
            px: 1,
            py: 0.25,
            borderRadius: 1,
            fontSize: '0.7rem',
            fontWeight: 'bold',
            zIndex: 1
          }}
        >
          PRO
        </Box>
      )}

      <CardHeader
        avatar={
          <Avatar
            sx={{
              bgcolor: method.color,
              width: compact ? 36 : 48,
              height: compact ? 36 : 48,
            }}
          >
            {method.icon}
          </Avatar>
        }
        title={
          <Box>
            <Typography
              variant={compact ? "subtitle2" : "h6"}
              fontWeight="bold"
              sx={{ color: canAccess ? 'inherit' : 'text.secondary' }}
            >
              {method.name}
            </Typography>
            {showCategory && method.categoryName && (
              <Chip
                label={method.categoryName}
                size="small"
                sx={{
                  mt: 0.5,
                  bgcolor: alpha(method.categoryColor || method.color, 0.1),
                  color: method.categoryColor || method.color,
                  fontSize: '0.7rem'
                }}
              />
            )}
          </Box>
        }
        action={
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onBookmark(method.name);
            }}
          >
            {isBookmarked ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
          </IconButton>
        }
        sx={{ pb: 1 }}
      />

      <CardContent sx={{ pt: 0, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 2,
            fontSize: compact ? '0.8rem' : '0.875rem',
            display: '-webkit-box',
            WebkitLineClamp: compact ? 2 : 3,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
        >
          {method.shortDescription}
        </Typography>

        {!compact && (
          <>
            <Button
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                setExpanded(!expanded);
              }}
              sx={{ mb: 1, alignSelf: 'flex-start' }}
            >
              {expanded ? 'Show Less' : 'Learn More'}
            </Button>

            <Fade in={expanded}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {method.detailedDescription}
                </Typography>
              </Box>
            </Fade>
          </>
        )}

        <Box sx={{ mt: 'auto', pt: 2 }}>
          {canAccess ? (
            <Button
              variant="contained"
              fullWidth
              startIcon={<LaunchIcon />}
              onClick={(e) => {
                e.stopPropagation();
                onNavigate(method.path);
              }}
              sx={{
                bgcolor: method.color,
                '&:hover': {
                  bgcolor: alpha(method.color, 0.8)
                }
              }}
            >
              Launch Analysis
            </Button>
          ) : (
            <Button
              variant="outlined"
              fullWidth
              startIcon={<UpgradeIcon />}
              color="warning"
              onClick={(e) => {
                e.stopPropagation();
                // Navigate to pricing page
              }}
            >
              Upgrade Required
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default InteractiveStatisticalAdvisor;
