import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Autocomplete,
  Slider,
  FormHelperText,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';
import { useAuth } from '../../context/AuthContext';

interface OverrideData {
  id: string;
  user_id: string;
  user_email: string;
  user_full_name: string;
  admin_email: string;
  original_tier: string;
  override_tier: string;
  start_date: string;
  end_date: string;
  reason: string;
  days_remaining: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface UserOption {
  id: string;
  email: string;
  full_name: string;
  accounttype: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`override-tabpanel-${index}`}
      aria-labelledby={`override-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SubscriptionOverrides: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [activeOverrides, setActiveOverrides] = useState<OverrideData[]>([]);
  const [overrideHistory, setOverrideHistory] = useState<OverrideData[]>([]);
  const [expiringOverrides, setExpiringOverrides] = useState<OverrideData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Create Override Dialog State
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserOption | null>(null);
  const [overrideTier, setOverrideTier] = useState<string>('');
  const [duration, setDuration] = useState<number>(1);
  const [reason, setReason] = useState<string>('');
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);

  // Extend Override Dialog State
  const [extendDialogOpen, setExtendDialogOpen] = useState(false);
  const [selectedOverride, setSelectedOverride] = useState<OverrideData | null>(null);
  const [extensionMonths, setExtensionMonths] = useState<number>(1);
  const [extensionReason, setExtensionReason] = useState<string>('');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const fetchActiveOverrides = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_all_active_overrides', {
        page_size: 100,
        page_offset: 0
      });

      if (error) throw error;
      setActiveOverrides(data || []);
    } catch (err: any) {
      console.error('Error fetching active overrides:', err);
      setError(err.message);
    }
  }, []);

  const fetchOverrideHistory = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_override_audit_trail', {
        target_user_id: null,
        page_size: 100,
        page_offset: 0
      });

      if (error) throw error;
      setOverrideHistory(data || []);
    } catch (err: any) {
      console.error('Error fetching override history:', err);
      setError(err.message);
    }
  }, []);

  const fetchExpiringOverrides = useCallback(async () => {
    try {
      const { data, error } = await supabase.rpc('get_expiring_overrides', {
        days_ahead: 7
      });

      if (error) throw error;
      setExpiringOverrides(data || []);
    } catch (err: any) {
      console.error('Error fetching expiring overrides:', err);
      setError(err.message);
    }
  }, []);

  const fetchUsers = useCallback(async (searchTerm: string = '') => {
    setUserSearchLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_all_users', {
        page_size: 50,
        page_offset: 0,
        search_term: searchTerm || null
      });

      if (error) throw error;
      
      // Filter out users who already have active overrides
      const activeUserIds = new Set(activeOverrides.map(o => o.user_id));
      const availableUsers = (data || []).filter((user: any) => 
        !activeUserIds.has(user.id) && user.accounttype !== 'pro'
      );
      
      setUserOptions(availableUsers);
    } catch (err: any) {
      console.error('Error fetching users:', err);
      setError(err.message);
    } finally {
      setUserSearchLoading(false);
    }
  }, [activeOverrides]);

  const handleCreateOverride = async () => {
    if (!selectedUser || !overrideTier || duration < 1) {
      setError('Please fill in all required fields');
      return;
    }

    setCreateLoading(true);
    try {
      const { data, error } = await supabase.rpc('create_subscription_override', {
        target_user_id: selectedUser.id,
        override_tier_param: overrideTier,
        duration_months: duration,
        reason_param: reason || null
      });

      if (error) throw error;

      setSuccess(`Override created successfully for ${selectedUser.email}`);
      setCreateDialogOpen(false);
      resetCreateForm();
      await fetchActiveOverrides();
    } catch (err: any) {
      console.error('Error creating override:', err);
      setError(err.message);
    } finally {
      setCreateLoading(false);
    }
  };

  const handleExpireOverride = async (overrideId: string) => {
    if (!confirm('Are you sure you want to expire this override?')) return;

    try {
      setLoading(true);
      console.log('Expiring override with ID:', overrideId);
      
      const { data, error } = await supabase.rpc('expire_subscription_override', {
        override_id: overrideId
      });

      if (error) {
        console.error('RPC Error:', error);
        throw error;
      }

      console.log('Override expired successfully:', data);
      setSuccess('Override expired successfully');
      
      // Refresh all data
      await Promise.all([
        fetchActiveOverrides(),
        fetchOverrideHistory(),
        fetchExpiringOverrides()
      ]);
    } catch (err: any) {
      console.error('Error expiring override:', err);
      setError(`Failed to expire override: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleExtendOverride = async () => {
    if (!selectedOverride || extensionMonths < 1) {
      setError('Please specify extension duration');
      return;
    }

    try {
      const { error } = await supabase.rpc('extend_subscription_override', {
        override_id: selectedOverride.id,
        additional_months: extensionMonths,
        reason_param: extensionReason || null
      });

      if (error) throw error;

      setSuccess('Override extended successfully');
      setExtendDialogOpen(false);
      setSelectedOverride(null);
      setExtensionMonths(1);
      setExtensionReason('');
      await fetchActiveOverrides();
    } catch (err: any) {
      console.error('Error extending override:', err);
      setError(err.message);
    }
  };

  const resetCreateForm = () => {
    setSelectedUser(null);
    setOverrideTier('');
    setDuration(1);
    setReason('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'pro': return 'primary';
      case 'edu_pro': return 'secondary';
      case 'edu': return 'info';
      case 'standard': return 'default';
      default: return 'default';
    }
  };

  const getTierLabel = (tier: string) => {
    switch (tier) {
      case 'pro': return 'Pro';
      case 'edu_pro': return 'Educational Pro';
      case 'edu': return 'Educational';
      case 'standard': return 'Standard';
      case 'guest': return 'Guest';
      default: return tier;
    }
  };

  const getValidOverrideTiers = (originalTier: string) => {
    switch (originalTier) {
      case 'standard':
        return ['edu', 'edu_pro', 'pro'];
      case 'edu':
        return ['edu_pro', 'pro'];
      case 'edu_pro':
        return ['pro'];
      default:
        return [];
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchActiveOverrides(),
        fetchOverrideHistory(),
        fetchExpiringOverrides()
      ]);
      setLoading(false);
    };

    loadData();
  }, [fetchActiveOverrides, fetchOverrideHistory, fetchExpiringOverrides]);

  useEffect(() => {
    if (createDialogOpen) {
      fetchUsers();
    }
  }, [createDialogOpen, fetchUsers]);

  // Clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Subscription Overrides
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Override
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Overrides
              </Typography>
              <Typography variant="h4">
                {activeOverrides.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Expiring Soon (7 days)
              </Typography>
              <Typography variant="h4" color="warning.main">
                {expiringOverrides.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total History
              </Typography>
              <Typography variant="h4">
                {overrideHistory.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Active Overrides" />
          <Tab label="Expiring Soon" />
          <Tab label="History" />
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Original Tier</TableCell>
                  <TableCell>Override Tier</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Days Remaining</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {activeOverrides.map((override) => (
                  <TableRow key={override.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {override.user_full_name || 'No name'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {override.user_email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getTierLabel(override.original_tier)} 
                        size="small" 
                        color={getTierColor(override.original_tier) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getTierLabel(override.override_tier)} 
                        size="small" 
                        color={getTierColor(override.override_tier) as any}
                      />
                    </TableCell>
                    <TableCell>{formatDate(override.end_date)}</TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        {override.days_remaining <= 7 ? (
                          <WarningIcon color="warning" sx={{ mr: 1 }} />
                        ) : (
                          <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                        )}
                        {override.days_remaining} days
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ maxWidth: 200 }}>
                        {override.reason || 'No reason provided'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Extend Override">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedOverride(override);
                            setExtendDialogOpen(true);
                          }}
                        >
                          <ScheduleIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Expire Override">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleExpireOverride(override.id)}
                          disabled={loading}
                        >
                          {loading ? <CircularProgress size={16} /> : <DeleteIcon />}
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
                {activeOverrides.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography color="text.secondary">
                        No active overrides found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Override Tier</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Days Remaining</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {expiringOverrides.map((override) => (
                  <TableRow key={override.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {override.user_full_name || 'No name'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {override.user_email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getTierLabel(override.override_tier)} 
                        size="small" 
                        color={getTierColor(override.override_tier) as any}
                      />
                    </TableCell>
                    <TableCell>{formatDate(override.end_date)}</TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <WarningIcon color="warning" sx={{ mr: 1 }} />
                        {override.days_remaining} days
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => {
                          setSelectedOverride(override);
                          setExtendDialogOpen(true);
                        }}
                      >
                        Extend
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {expiringOverrides.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <Typography color="text.secondary">
                        No overrides expiring soon
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Original Tier</TableCell>
                  <TableCell>Override Tier</TableCell>
                  <TableCell>Start Date</TableCell>
                  <TableCell>End Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Admin</TableCell>
                  <TableCell>Reason</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {overrideHistory.map((override) => (
                  <TableRow key={override.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {override.user_full_name || 'No name'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {override.user_email}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getTierLabel(override.original_tier)} 
                        size="small" 
                        color={getTierColor(override.original_tier) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getTierLabel(override.override_tier)} 
                        size="small" 
                        color={getTierColor(override.override_tier) as any}
                      />
                    </TableCell>
                    <TableCell>{formatDate(override.start_date)}</TableCell>
                    <TableCell>{formatDate(override.end_date)}</TableCell>
                    <TableCell>
                      <Chip 
                        label={override.is_active ? 'Active' : 'Expired'} 
                        size="small" 
                        color={override.is_active ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {override.admin_email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ maxWidth: 200 }}>
                        {override.reason || 'No reason provided'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                {overrideHistory.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      <Typography color="text.secondary">
                        No override history found
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>

      {/* Create Override Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Subscription Override</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Autocomplete
                  options={userOptions}
                  getOptionLabel={(option) => `${option.email} (${option.full_name || 'No name'}) - ${getTierLabel(option.accounttype)}`}
                  value={selectedUser}
                  onChange={(event, newValue) => {
                    setSelectedUser(newValue);
                    if (newValue) {
                      setOverrideTier(''); // Reset override tier when user changes
                    }
                  }}
                  loading={userSearchLoading}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select User"
                      required
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {userSearchLoading ? <CircularProgress color="inherit" size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>
              
              {selectedUser && (
                <Grid item xs={12}>
                  <FormControl fullWidth required>
                    <InputLabel>Override Tier</InputLabel>
                    <Select
                      value={overrideTier}
                      onChange={(e) => setOverrideTier(e.target.value)}
                      label="Override Tier"
                    >
                      {getValidOverrideTiers(selectedUser.accounttype).map((tier) => (
                        <MenuItem key={tier} value={tier}>
                          {getTierLabel(tier)}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>
                      Current tier: {getTierLabel(selectedUser.accounttype)}
                    </FormHelperText>
                  </FormControl>
                </Grid>
              )}
              
              <Grid item xs={12}>
                <Typography gutterBottom>Duration: {duration} month{duration !== 1 ? 's' : ''}</Typography>
                <Slider
                  value={duration}
                  onChange={(e, newValue) => setDuration(newValue as number)}
                  min={1}
                  max={12}
                  marks
                  valueLabelDisplay="auto"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Reason (Optional)"
                  multiline
                  rows={3}
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Explain why this override is being granted..."
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateOverride} 
            variant="contained"
            disabled={!selectedUser || !overrideTier || createLoading}
          >
            {createLoading ? <CircularProgress size={20} /> : 'Create Override'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Extend Override Dialog */}
      <Dialog open={extendDialogOpen} onClose={() => setExtendDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Extend Subscription Override</DialogTitle>
        <DialogContent>
          {selectedOverride && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                User: {selectedOverride.user_email}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Current End Date: {formatDate(selectedOverride.end_date)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Days Remaining: {selectedOverride.days_remaining}
              </Typography>
              
              <Typography gutterBottom>
                Extend by: {extensionMonths} month{extensionMonths !== 1 ? 's' : ''}
              </Typography>
              <Slider
                value={extensionMonths}
                onChange={(e, newValue) => setExtensionMonths(newValue as number)}
                min={1}
                max={12}
                marks
                valueLabelDisplay="auto"
                sx={{ mb: 3 }}
              />
              
              <TextField
                fullWidth
                label="Extension Reason (Optional)"
                multiline
                rows={3}
                value={extensionReason}
                onChange={(e) => setExtensionReason(e.target.value)}
                placeholder="Explain why this extension is being granted..."
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExtendDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleExtendOverride} variant="contained">
            Extend Override
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubscriptionOverrides;
