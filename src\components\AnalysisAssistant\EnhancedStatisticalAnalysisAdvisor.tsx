import React, { useState, useCallback, useContext, useEffect, useMemo } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Tooltip,
  IconButton,
  Breadcrumbs,
  Link,
  Badge,
  Stack,
  Avatar,
  alpha,
  useTheme,
  Skeleton,
  TextField,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup,
  FormControlLabel,
  Switch,
  Grid
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  Psychology as PsychologyIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Science as ScienceIcon,
  Analytics as AnalyticsIcon,
  Insights as InsightsIcon,
  TrendingUp as TrendingUpIcon,
  Compare as CompareIcon,
  Lightbulb as LightbulbIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Help as HelpIcon,
  Launch as LaunchIcon,
  Login as LoginIcon,
  Upgrade as UpgradeIcon,
  School as SchoolIcon,
  Star as StarIcon,
  PlayArrow as PlayArrowIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  FilterList as FilterListIcon,
  ViewList as ViewListIcon,
  AccountTree as AccountTreeIcon,
  AutoAwesome as AutoAwesomeIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Category as CategoryIcon,
  Explore as ExploreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useResults } from '../../context/ResultsContext';
import StatsCard from '../UI/StatsCard';
import { DataContextAnalyzer } from './utils/dataContextAnalyzer';
import EnhancedRecommendationCard from './components/EnhancedRecommendationCard';
import WorkflowProgress from './components/WorkflowProgress';

// Enhanced types for the tree-like interface
interface AnalysisMethod {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  description: string;
  assumptions: string[];
  dataRequirements: string[];
  interpretation: string;
  route: string;
  accessLevel: 'guest' | 'standard' | 'pro' | 'edu';
  icon: React.ReactNode;
  examples: string[];
  relatedMethods: string[];
  contextualRecommendations?: string[];
}

interface AnalysisCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  subcategories: AnalysisSubcategory[];
}

interface AnalysisSubcategory {
  id: string;
  name: string;
  description: string;
  methods: AnalysisMethod[];
}

interface ViewMode {
  mode: 'tree' | 'recommendations' | 'search';
  label: string;
  icon: React.ReactNode;
}

interface StatisticalAnalysisAdvisorProps {
  currentDataset?: any;
  datasetAnalysis?: any;
}

// Enhanced analysis methods data structure with comprehensive method definitions
const ANALYSIS_METHODS: AnalysisMethod[] = [
  // GUEST ACCESS METHODS (Free for all users)
  {
    id: 'descriptive_single',
    name: 'Descriptive Statistics',
    category: 'Descriptive Statistics',
    subcategory: 'Central Tendency & Variability',
    description: 'Summarize the central tendency, variability, and distribution shape of numeric variables.',
    assumptions: ['Variable should be numeric', 'No missing data assumptions'],
    dataRequirements: ['At least 10-20 observations recommended', 'Single or multiple numeric variables'],
    interpretation: 'Mean shows central tendency, standard deviation shows spread, skewness indicates distribution shape.',
    route: '/stats/descriptives',
    accessLevel: 'guest',
    icon: <BarChartIcon />,
    examples: ['Calculate mean, median, mode', 'Measure standard deviation and variance', 'Assess data distribution shape'],
    relatedMethods: ['normality_tests', 'frequency_table']
  },
  {
    id: 'frequency_table',
    name: 'Frequency Tables',
    category: 'Descriptive Statistics',
    subcategory: 'Distribution Analysis',
    description: 'Examine the distribution of categorical or discrete numeric variables.',
    assumptions: ['Variable can be categorical or numeric', 'Finite number of unique values'],
    dataRequirements: ['At least 1 observation', 'Categorical or discrete numeric variable'],
    interpretation: 'Shows frequency and percentage of each category or value.',
    route: '/stats/frequencies',
    accessLevel: 'guest',
    icon: <ViewListIcon />,
    examples: ['Count categories in a variable', 'Calculate percentages', 'Create frequency distributions'],
    relatedMethods: ['crosstabs', 'chi_square']
  },
  {
    id: 'normality_tests',
    name: 'Normality Tests',
    category: 'Descriptive Statistics',
    subcategory: 'Distribution Testing',
    description: 'Test whether data follows a normal distribution using various statistical tests.',
    assumptions: ['Variable should be numeric', 'Continuous data preferred'],
    dataRequirements: ['At least 3 observations', 'Single numeric variable'],
    interpretation: 'P-value < 0.05 suggests data is not normally distributed.',
    route: '/stats/normality',
    accessLevel: 'guest',
    icon: <ShowChartIcon />,
    examples: ['Shapiro-Wilk test', 'Kolmogorov-Smirnov test', 'Anderson-Darling test'],
    relatedMethods: ['descriptive_single', 'independent_ttest']
  },
  {
    id: 'crosstabs',
    name: 'Cross-Tabulation',
    category: 'Descriptive Statistics',
    subcategory: 'Categorical Analysis',
    description: 'Examine relationships between two categorical variables using cross-tabulation tables.',
    assumptions: ['Variables should be categorical', 'Finite number of categories'],
    dataRequirements: ['At least 1 observation', 'Two categorical variables'],
    interpretation: 'Shows frequency distribution across category combinations.',
    route: '/stats/crosstabs',
    accessLevel: 'guest',
    icon: <CategoryIcon />,
    examples: ['Gender vs Department analysis', 'Treatment vs Outcome tables'],
    relatedMethods: ['frequency_table', 'chi_square']
  },
  {
    id: 'independent_ttest',
    name: 'Independent Samples t-test',
    category: 'Inferential Statistics',
    subcategory: 'Group Comparison',
    description: 'Compare means between two independent groups.',
    assumptions: ['Normal distribution', 'Independent observations', 'Equal variances (or use Welch correction)'],
    dataRequirements: ['At least 30 observations per group recommended', 'One numeric variable', 'One grouping variable with 2 levels'],
    interpretation: 'P-value < 0.05 suggests significant difference between group means.',
    route: '/inferential-stats/independent-samples-ttest',
    accessLevel: 'guest',
    icon: <CompareIcon />,
    examples: ['Compare test scores between two classes', 'Compare treatment vs control groups'],
    relatedMethods: ['mann_whitney', 'one_way_anova']
  },
  {
    id: 'paired_ttest',
    name: 'Paired Samples t-test',
    category: 'Inferential Statistics',
    subcategory: 'Paired Comparison',
    description: 'Compare means for paired observations or before/after measurements.',
    assumptions: ['Normal distribution of differences', 'Paired observations'],
    dataRequirements: ['At least 30 pairs recommended', 'Two related numeric variables'],
    interpretation: 'P-value < 0.05 suggests significant difference between paired measurements.',
    route: '/inferential-stats/paired-samples-ttest',
    accessLevel: 'guest',
    icon: <TimelineIcon />,
    examples: ['Before/after treatment comparison', 'Pre-test vs post-test scores'],
    relatedMethods: ['wilcoxon', 'independent_ttest']
  },
  {
    id: 'one_sample_ttest',
    name: 'One-Sample t-test',
    category: 'Inferential Statistics',
    subcategory: 'Single Sample Tests',
    description: 'Test if a sample mean differs significantly from a known population value.',
    assumptions: ['Normal distribution', 'Independent observations'],
    dataRequirements: ['At least 30 observations recommended', 'One numeric variable', 'Known population parameter'],
    interpretation: 'P-value < 0.05 suggests sample mean differs significantly from population value.',
    route: '/inferential-stats/one-sample-ttest',
    accessLevel: 'guest',
    icon: <TrendingUpIcon />,
    examples: ['Test if average height differs from national average', 'Compare sample mean to theoretical value'],
    relatedMethods: ['descriptive_single', 'independent_ttest']
  },

  // STANDARD ACCESS METHODS (Requires Standard account or higher)
  {
    id: 'mann_whitney',
    name: 'Mann-Whitney U Test',
    category: 'Inferential Statistics',
    subcategory: 'Non-parametric Tests',
    description: 'Non-parametric alternative to independent t-test for comparing two groups.',
    assumptions: ['Independent observations', 'Ordinal or continuous data'],
    dataRequirements: ['At least 10 observations per group', 'One numeric/ordinal variable', 'One grouping variable with 2 levels'],
    interpretation: 'P-value < 0.05 suggests significant difference in distributions between groups.',
    route: '/inferential-stats/mann-whitney-u-test',
    accessLevel: 'standard',
    icon: <CompareIcon />,
    examples: ['Compare groups when normality is violated', 'Analyze ordinal survey responses'],
    relatedMethods: ['independent_ttest', 'kruskal_wallis']
  },
  {
    id: 'one_way_anova',
    name: 'One-Way ANOVA',
    category: 'Inferential Statistics',
    subcategory: 'Multiple Group Comparison',
    description: 'Compare means across three or more independent groups.',
    assumptions: ['Normal distribution', 'Independent observations', 'Equal variances'],
    dataRequirements: ['At least 30 observations per group recommended', 'One numeric variable', 'One grouping variable with 3+ levels'],
    interpretation: 'P-value < 0.05 suggests at least one group mean differs significantly.',
    route: '/inferential-stats/one-way-anova',
    accessLevel: 'standard',
    icon: <BarChartIcon />,
    examples: ['Compare test scores across multiple schools', 'Analyze treatment effects with multiple conditions'],
    relatedMethods: ['independent_ttest', 'kruskal_wallis', 'two_way_anova']
  },
  {
    id: 'wilcoxon',
    name: 'Wilcoxon Signed-Rank Test',
    category: 'Inferential Statistics',
    subcategory: 'Non-parametric Tests',
    description: 'Non-parametric alternative to paired t-test for comparing paired observations.',
    assumptions: ['Paired observations', 'Symmetric distribution of differences'],
    dataRequirements: ['At least 10 pairs', 'Two related numeric/ordinal variables'],
    interpretation: 'P-value < 0.05 suggests significant difference between paired measurements.',
    route: '/inferential-stats/wilcoxon-signed-rank-test',
    accessLevel: 'standard',
    icon: <TimelineIcon />,
    examples: ['Before/after comparison with non-normal data', 'Paired ordinal responses'],
    relatedMethods: ['paired_ttest', 'mann_whitney']
  },
  {
    id: 'kruskal_wallis',
    name: 'Kruskal-Wallis Test',
    category: 'Inferential Statistics',
    subcategory: 'Non-parametric Tests',
    description: 'Non-parametric alternative to one-way ANOVA for comparing multiple groups.',
    assumptions: ['Independent observations', 'Similar distribution shapes'],
    dataRequirements: ['At least 10 observations per group', 'One numeric/ordinal variable', 'One grouping variable with 3+ levels'],
    interpretation: 'P-value < 0.05 suggests at least one group distribution differs significantly.',
    route: '/inferential-stats/kruskal-wallis-test',
    accessLevel: 'standard',
    icon: <BarChartIcon />,
    examples: ['Compare multiple groups with non-normal data', 'Analyze ordinal outcomes across groups'],
    relatedMethods: ['one_way_anova', 'mann_whitney']
  },

  // PRO ACCESS METHODS (Requires Pro subscription)
  {
    id: 'two_way_anova',
    name: 'Two-Way ANOVA',
    category: 'Inferential Statistics',
    subcategory: 'Factorial Analysis',
    description: 'Examine the effects of two factors and their interaction on a dependent variable.',
    assumptions: ['Normal distribution', 'Independent observations', 'Equal variances'],
    dataRequirements: ['At least 30 observations per cell recommended', 'One numeric variable', 'Two grouping variables'],
    interpretation: 'Tests main effects of each factor and their interaction effect.',
    route: '/inferential-stats/two-way-anova',
    accessLevel: 'pro',
    icon: <AnalyticsIcon />,
    examples: ['Analyze effects of treatment and gender on outcomes', 'Study interaction between factors'],
    relatedMethods: ['one_way_anova', 'repeated_measures_anova']
  },
  {
    id: 'repeated_measures_anova',
    name: 'Repeated Measures ANOVA',
    category: 'Inferential Statistics',
    subcategory: 'Repeated Measures',
    description: 'Compare means across multiple time points or conditions within the same subjects.',
    assumptions: ['Normal distribution', 'Sphericity', 'No missing data'],
    dataRequirements: ['At least 30 subjects recommended', 'Multiple measurements per subject'],
    interpretation: 'Tests if means change significantly across time points or conditions.',
    route: '/inferential-stats/repeated-measures-anova',
    accessLevel: 'pro',
    icon: <TimelineIcon />,
    examples: ['Analyze changes over time', 'Compare multiple treatments within subjects'],
    relatedMethods: ['paired_ttest', 'two_way_anova']
  }
];

// Analysis categories structure
const ANALYSIS_CATEGORIES: AnalysisCategory[] = [
  {
    id: 'descriptive',
    name: 'Descriptive Statistics',
    description: 'Explore and summarize your data characteristics',
    icon: <BarChartIcon />,
    color: '#2196F3',
    subcategories: [
      {
        id: 'central_tendency',
        name: 'Central Tendency & Variability',
        description: 'Measures of center and spread',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Central Tendency & Variability')
      },
      {
        id: 'distribution',
        name: 'Distribution Analysis',
        description: 'Examine data distributions and patterns',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Distribution Analysis')
      },
      {
        id: 'testing',
        name: 'Distribution Testing',
        description: 'Test distributional assumptions',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Distribution Testing')
      }
    ]
  },
  {
    id: 'inferential',
    name: 'Inferential Statistics',
    description: 'Test hypotheses and make statistical inferences',
    icon: <ScienceIcon />,
    color: '#4CAF50',
    subcategories: [
      {
        id: 'group_comparison',
        name: 'Group Comparison',
        description: 'Compare means between groups',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Group Comparison')
      }
    ]
  }
];

// View modes
const VIEW_MODES: ViewMode[] = [
  { mode: 'recommendations', label: 'Smart Recommendations', icon: <AutoAwesomeIcon /> },
  { mode: 'tree', label: 'Browse Methods', icon: <AccountTreeIcon /> },
  { mode: 'search', label: 'Search', icon: <SearchIcon /> }
];

// Access level requirements mapping - matches the method definitions
const ACCESS_REQUIREMENTS = {
  // Guest access methods (free for all users)
  descriptive_single: 'guest',
  frequency_table: 'guest',
  normality_tests: 'guest',
  crosstabs: 'guest',
  independent_ttest: 'guest',
  paired_ttest: 'guest',
  one_sample_ttest: 'guest',

  // Standard access methods (requires Standard account or higher)
  mann_whitney: 'standard',
  one_way_anova: 'standard',
  wilcoxon: 'standard',
  kruskal_wallis: 'standard',

  // Pro access methods (requires Pro subscription)
  two_way_anova: 'pro',
  repeated_measures_anova: 'pro'
};

const EnhancedStatisticalAnalysisAdvisor: React.FC<StatisticalAnalysisAdvisorProps> = ({
  currentDataset,
  datasetAnalysis
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { addResult } = useResults();
  const authContext = useContext(AuthContext);
  const {
    user,
    isGuest,
    accountType,
    effectiveTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    isEducationalUser,
    educationalTier
  } = authContext || {};

  // State management
  const [viewMode, setViewMode] = useState<'recommendations' | 'tree' | 'search'>('recommendations');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<AnalysisMethod | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [bookmarkedMethods, setBookmarkedMethods] = useState<Set<string>>(new Set());
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['descriptive']));
  const [showOnlyAccessible, setShowOnlyAccessible] = useState<boolean>(false);

  const isAuthenticated = !!user;

  // Enhanced helper function to check if user can access a specific method
  const canAccessMethod = useCallback((methodId: string): boolean => {
    const requiredLevel = ACCESS_REQUIREMENTS[methodId as keyof typeof ACCESS_REQUIREMENTS];
    if (!requiredLevel) {
      console.log(`⚠️ No access requirement found for method: ${methodId}, allowing access`);
      return true;
    }

    // Guest users can only access guest-level methods
    if (isGuest) {
      const hasAccess = requiredLevel === 'guest';
      console.log(`🔍 Guest Access Check - Method: ${methodId}, Required: ${requiredLevel}, Access: ${hasAccess}`);
      return hasAccess;
    }

    // Non-authenticated users same as guest
    if (!user) {
      const hasAccess = requiredLevel === 'guest';
      console.log(`🔍 Non-Auth Access Check - Method: ${methodId}, Required: ${requiredLevel}, Access: ${hasAccess}`);
      return hasAccess;
    }

    // Authenticated users - check tier hierarchy
    const tierHierarchy = {
      guest: 0,
      standard: 1,
      edu: 1,        // Educational free tier = standard level
      pro: 2,
      edu_pro: 2     // Educational pro tier = pro level
    };

    const requiredTierLevel = tierHierarchy[requiredLevel as keyof typeof tierHierarchy] || 0;
    const userTierLevel = tierHierarchy[effectiveTier as keyof typeof tierHierarchy] || 0;
    const hasAccess = userTierLevel >= requiredTierLevel;

    console.log(`🔍 Auth Access Check - Method: ${methodId}`, {
      requiredLevel,
      effectiveTier,
      requiredTierLevel,
      userTierLevel,
      hasAccess,
      accountType,
      isEducationalUser,
      educationalTier
    });

    return hasAccess;
  }, [isGuest, user, effectiveTier, accountType, isEducationalUser, educationalTier]);

  // Helper function to navigate to analysis
  const navigateToAnalysis = useCallback((method: AnalysisMethod) => {
    if (canAccessMethod(method.id)) {
      navigate(`/app${method.route}`);
    }
  }, [navigate, canAccessMethod]);

  // Helper function to toggle bookmark
  const toggleBookmark = useCallback((methodId: string) => {
    setBookmarkedMethods(prev => {
      const newSet = new Set(prev);
      if (newSet.has(methodId)) {
        newSet.delete(methodId);
      } else {
        newSet.add(methodId);
      }
      return newSet;
    });
  }, []);

  // Helper function to toggle category expansion
  const toggleCategoryExpansion = useCallback((categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  }, []);

  // Generate intelligent recommendations using DataContextAnalyzer
  const intelligentRecommendations = useMemo(() => {
    if (!currentDataset || !datasetAnalysis) return [];

    try {
      const analyzer = new DataContextAnalyzer(datasetAnalysis);
      const recommendations = analyzer.getRecommendations();

      // Map analyzer recommendations to our method structure
      return recommendations.map(rec => {
        const method = ANALYSIS_METHODS.find(m => m.id === rec.methodId);
        if (!method) return null;

        return {
          ...method,
          contextualRecommendations: rec.reasoning,
          priority: rec.priority,
          confidence: rec.confidence,
          estimatedTime: rec.estimatedTime,
          tips: rec.contextualTips
        };
      }).filter(Boolean);
    } catch (error) {
      console.error('Error generating intelligent recommendations:', error);
      return [];
    }
  }, [currentDataset, datasetAnalysis]);

  // Get data summary for context display
  const dataSummary = useMemo(() => {
    if (!currentDataset || !datasetAnalysis) return null;

    try {
      const analyzer = new DataContextAnalyzer(datasetAnalysis);
      return analyzer.getDataSummary();
    } catch (error) {
      console.error('Error generating data summary:', error);
      return null;
    }
  }, [currentDataset, datasetAnalysis]);

  // Filter methods based on search query and access level
  const filteredMethods = useMemo(() => {
    let methods = ANALYSIS_METHODS;

    if (searchQuery) {
      methods = methods.filter(method =>
        method.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        method.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        method.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (showOnlyAccessible) {
      methods = methods.filter(method => canAccessMethod(method.id));
    }

    return methods;
  }, [searchQuery, showOnlyAccessible, canAccessMethod]);

  // Enhanced Method card component with better access control visualization
  const MethodCard: React.FC<{ method: AnalysisMethod; showContext?: boolean; compact?: boolean }> = ({
    method,
    showContext = false,
    compact = false
  }) => {
    const hasAccess = canAccessMethod(method.id);
    const isBookmarked = bookmarkedMethods.has(method.id);
    const requiredLevel = ACCESS_REQUIREMENTS[method.id as keyof typeof ACCESS_REQUIREMENTS];

    const getAccessLevelColor = (level: string) => {
      switch (level) {
        case 'guest': return theme.palette.success.main;
        case 'standard': return theme.palette.primary.main;
        case 'pro': return theme.palette.warning.main;
        default: return theme.palette.grey[500];
      }
    };

    const getAccessLevelLabel = (level: string) => {
      switch (level) {
        case 'guest': return 'Free';
        case 'standard': return 'Standard+';
        case 'pro': return 'Pro Only';
        default: return level.toUpperCase();
      }
    };

    return (
      <Card
        sx={{
          mb: compact ? 1 : 2,
          position: 'relative',
          cursor: 'pointer',
          opacity: hasAccess ? 1 : 0.6,
          border: selectedMethod?.id === method.id ? `2px solid ${theme.palette.primary.main}` :
                  !hasAccess ? `1px solid ${alpha(theme.palette.warning.main, 0.3)}` : 'none',
          '&:hover': {
            boxShadow: theme.shadows[hasAccess ? 6 : 2],
            transform: hasAccess ? 'translateY(-2px)' : 'none',
            transition: 'all 0.2s ease-in-out'
          }
        }}
        onClick={() => setSelectedMethod(method)}
      >
        {/* Access Level Indicator */}
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            bgcolor: getAccessLevelColor(requiredLevel),
            color: 'white',
            px: 1,
            py: 0.25,
            borderRadius: 1,
            fontSize: '0.7rem',
            fontWeight: 'bold',
            zIndex: 1
          }}
        >
          {getAccessLevelLabel(requiredLevel)}
        </Box>

        {/* Restricted Method Overlay */}
        {!hasAccess && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: alpha(theme.palette.grey[500], 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 2,
              borderRadius: 1
            }}
          >
            <Box sx={{ textAlign: 'center', p: 2 }}>
              <UpgradeIcon sx={{ fontSize: 48, color: theme.palette.warning.main, mb: 1 }} />
              <Typography variant="subtitle2" color="warning.main">
                {requiredLevel === 'pro' ? 'Pro Feature' : 'Premium Feature'}
              </Typography>
              <Button
                variant="contained"
                size="small"
                color="warning"
                startIcon={<UpgradeIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate('/app#pricing');
                }}
                sx={{ mt: 1 }}
              >
                Upgrade
              </Button>
            </Box>
          </Box>
        )}

        <CardContent sx={{ position: 'relative' }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, flex: 1 }}>
              <Avatar sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                width: compact ? 36 : 44,
                height: compact ? 36 : 44,
                color: hasAccess ? theme.palette.primary.main : theme.palette.grey[400]
              }}>
                {method.icon}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant={compact ? "subtitle2" : "h6"} sx={{
                  fontSize: compact ? '1rem' : '1.1rem',
                  mb: 0.5,
                  color: hasAccess ? 'inherit' : 'text.secondary'
                }}>
                  {method.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{
                  fontSize: compact ? '0.8rem' : '0.875rem'
                }}>
                  {method.description}
                </Typography>
              </Box>
            </Box>

            {hasAccess && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleBookmark(method.id);
                  }}
                >
                  {isBookmarked ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
                </IconButton>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<PlayArrowIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToAnalysis(method);
                  }}
                >
                  Run
                </Button>
              </Box>
            )}
          </Box>

          {showContext && method.contextualRecommendations && (
            <Alert
              severity="info"
              sx={{ mt: 1, py: 0.5 }}
              icon={<TipsAndUpdatesIcon fontSize="small" />}
            >
              <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                {method.contextualRecommendations[0]}
              </Typography>
            </Alert>
          )}

          {!compact && (
            <Box sx={{ display: 'flex', gap: 1, mt: 1.5, flexWrap: 'wrap' }}>
              <Chip
                label={method.subcategory}
                size="small"
                variant="outlined"
                color="primary"
              />
              <Chip
                label={`${method.dataRequirements.length} requirements`}
                size="small"
                variant="outlined"
              />
              <Chip
                label={`${method.assumptions.length} assumptions`}
                size="small"
                variant="outlined"
              />
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PsychologyIcon color="primary" />
            <Typography variant="h6">
              Statistical Analysis Advisor
            </Typography>
            <Chip
              icon={<AutoAwesomeIcon />}
              label="AI"
              color="primary"
              size="small"
            />
          </Box>

          {/* User Tier Status */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {isGuest ? (
              <Chip
                label="Guest Access"
                color="default"
                size="small"
                icon={<ExploreIcon />}
              />
            ) : isEducationalUser ? (
              <Chip
                icon={<SchoolIcon />}
                label={educationalTier === 'pro' ? 'Educational Pro' : 'Educational'}
                color="secondary"
                size="small"
              />
            ) : (
              <Chip
                label={`${effectiveTier?.toUpperCase()} Account`}
                color={effectiveTier === 'pro' ? 'success' : effectiveTier === 'standard' ? 'primary' : 'default'}
                size="small"
                icon={effectiveTier === 'pro' ? <StarIcon /> : undefined}
              />
            )}
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Get intelligent recommendations for statistical analysis based on your data characteristics and research objectives.
        </Typography>

        {/* Access Level Information */}
        {isGuest && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Guest Access:</strong> You can access basic statistical methods.
              <Link component="button" onClick={() => navigate('/app#auth/login')} sx={{ ml: 1 }}>
                Sign in
              </Link> for more advanced features.
            </Typography>
          </Alert>
        )}

        {isAuthenticated && effectiveTier === 'standard' && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Standard Account:</strong> You have access to basic and intermediate methods.
              <Link component="button" onClick={() => navigate('/app#pricing')} sx={{ ml: 1 }}>
                Upgrade to Pro
              </Link> for advanced statistical analyses.
            </Typography>
          </Alert>
        )}

        {/* View Mode Toggle */}
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={(e, newMode) => newMode && setViewMode(newMode)}
          size="small"
          sx={{ mb: 2 }}
        >
          {VIEW_MODES.map((mode) => (
            <ToggleButton key={mode.mode} value={mode.mode}>
              {mode.icon}
              <Typography variant="caption" sx={{ ml: 0.5 }}>
                {mode.label}
              </Typography>
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>

      {/* Content based on view mode */}
      {viewMode === 'recommendations' && (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Smart Recommendations
            </Typography>
            {currentDataset && (
              <Chip
                icon={<CheckCircleIcon />}
                label={`Dataset: ${currentDataset.name || 'Loaded'}`}
                color="success"
                size="small"
              />
            )}
          </Box>

          {!currentDataset ? (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Load a dataset to get personalized analysis recommendations based on your data characteristics.
              </Typography>
            </Alert>
          ) : (
            <Box>
              {/* Data Summary */}
              {dataSummary && (
                <Paper elevation={1} sx={{ p: 2, mb: 3, bgcolor: alpha(theme.palette.info.main, 0.05) }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <InsightsIcon color="info" />
                    Data Overview
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {dataSummary.overview}
                  </Typography>

                  {dataSummary.strengths.length > 0 && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="success.main" fontWeight="medium">
                        Strengths:
                      </Typography>
                      {dataSummary.strengths.map((strength, index) => (
                        <Typography key={index} variant="caption" sx={{ display: 'block', ml: 1 }}>
                          • {strength}
                        </Typography>
                      ))}
                    </Box>
                  )}

                  {dataSummary.concerns.length > 0 && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="warning.main" fontWeight="medium">
                        Concerns:
                      </Typography>
                      {dataSummary.concerns.map((concern, index) => (
                        <Typography key={index} variant="caption" sx={{ display: 'block', ml: 1 }}>
                          • {concern}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Paper>
              )}

              {intelligentRecommendations.length > 0 ? (
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AutoAwesomeIcon color="primary" />
                    AI-Powered Recommendations
                  </Typography>
                  {intelligentRecommendations.map((method: any) => (
                    <EnhancedRecommendationCard
                      key={method.id}
                      method={method}
                      hasAccess={canAccessMethod(method.id)}
                      isBookmarked={bookmarkedMethods.has(method.id)}
                      onBookmarkToggle={toggleBookmark}
                      onRunAnalysis={navigateToAnalysis}
                      onViewDetails={setSelectedMethod}
                      showContext={true}
                      priority={method.priority}
                      confidence={method.confidence}
                      estimatedTime={method.estimatedTime}
                    />
                  ))}

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    All Available Methods
                  </Typography>
                  {ANALYSIS_METHODS.filter(m => !intelligentRecommendations.find((r: any) => r.id === m.id))
                    .map((method) => (
                      <MethodCard key={method.id} method={method} />
                    ))}
                </Box>
              ) : (
                <Box>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Unable to generate specific recommendations. Browse all available methods below.
                    </Typography>
                  </Alert>
                  {ANALYSIS_METHODS.map((method) => (
                    <MethodCard key={method.id} method={method} />
                  ))}
                </Box>
              )}
            </Box>
          )}
        </Box>
      )}

      {viewMode === 'tree' && (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6">
              Browse Analysis Methods
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showOnlyAccessible}
                    onChange={(e) => setShowOnlyAccessible(e.target.checked)}
                    size="small"
                  />
                }
                label="Show only accessible"
              />
              <Chip
                label={`${ANALYSIS_METHODS.filter(m => canAccessMethod(m.id)).length}/${ANALYSIS_METHODS.length} accessible`}
                size="small"
                color="info"
                variant="outlined"
              />
            </Box>
          </Box>

          {/* Access Level Legend */}
          <Paper elevation={1} sx={{ p: 2, mb: 3, bgcolor: alpha(theme.palette.grey[100], 0.5) }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Access Levels:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label="Guest" size="small" color="default" />
              <Chip label="Standard" size="small" color="primary" />
              <Chip label="Pro" size="small" color="success" />
              <Typography variant="caption" color="text.secondary" sx={{ ml: 1, alignSelf: 'center' }}>
                Your tier: <strong>{effectiveTier?.toUpperCase()}</strong>
              </Typography>
            </Box>
          </Paper>

          {ANALYSIS_CATEGORIES.map((category) => {
            const categoryMethods = category.subcategories.flatMap(sub => sub.methods);
            const accessibleMethods = categoryMethods.filter(method => canAccessMethod(method.id));
            const visibleMethods = showOnlyAccessible ? accessibleMethods : categoryMethods;

            if (visibleMethods.length === 0) return null;

            return (
              <Accordion
                key={category.id}
                expanded={expandedCategories.has(category.id)}
                onChange={() => toggleCategoryExpansion(category.id)}
                sx={{
                  mb: 2,
                  '&:before': { display: 'none' },
                  boxShadow: theme.shadows[2]
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    bgcolor: alpha(category.color, 0.05),
                    '&:hover': { bgcolor: alpha(category.color, 0.1) }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Avatar sx={{
                      bgcolor: alpha(category.color, 0.15),
                      color: category.color,
                      width: 40,
                      height: 40
                    }}>
                      {category.icon}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {category.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {category.description}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={`${accessibleMethods.length}/${categoryMethods.length}`}
                        size="small"
                        color={accessibleMethods.length === categoryMethods.length ? 'success' : 'warning'}
                        sx={{ mr: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        accessible
                      </Typography>
                    </Box>
                  </Box>
                </AccordionSummary>
                <AccordionDetails sx={{ bgcolor: alpha(theme.palette.grey[50], 0.5) }}>
                  {category.subcategories.map((subcategory) => {
                    const subMethods = subcategory.methods.filter(method =>
                      !showOnlyAccessible || canAccessMethod(method.id)
                    );

                    if (subMethods.length === 0) return null;

                    return (
                      <Box key={subcategory.id} sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {subcategory.name}
                          </Typography>
                          <Chip
                            label={`${subMethods.filter(m => canAccessMethod(m.id)).length}/${subMethods.length}`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {subcategory.description}
                        </Typography>

                        <Grid container spacing={2}>
                          {subMethods.map((method) => (
                            <Grid item xs={12} key={method.id}>
                              <MethodCard method={method} />
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    );
                  })}
                </AccordionDetails>
              </Accordion>
            );
          })}
        </Box>
      )}

      {viewMode === 'search' && (
        <Box>
          <TextField
            fullWidth
            placeholder="Search analysis methods..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {filteredMethods.length} method{filteredMethods.length !== 1 ? 's' : ''} found
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={showOnlyAccessible}
                  onChange={(e) => setShowOnlyAccessible(e.target.checked)}
                  size="small"
                />
              }
              label="Show only accessible"
            />
          </Box>

          {filteredMethods.length > 0 ? (
            filteredMethods.map((method) => (
              <MethodCard key={method.id} method={method} />
            ))
          ) : (
            <Alert severity="info">
              <Typography variant="body2">
                No methods found matching your search criteria.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {/* Method Details Panel */}
      {selectedMethod && (
        <Paper
          elevation={3}
          sx={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: 600,
            maxHeight: '80vh',
            overflow: 'auto',
            zIndex: 1300,
            p: 3
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h5">
              {selectedMethod.name}
            </Typography>
            <IconButton onClick={() => setSelectedMethod(null)}>
              <ArrowBackIcon />
            </IconButton>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {selectedMethod.description}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={6}>
                <StatsCard
                  title="Category"
                  value={selectedMethod.category}
                  description={selectedMethod.subcategory}
                  icon={selectedMethod.icon}
                  color="primary"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <StatsCard
                  title="Access Level"
                  value={selectedMethod.accessLevel.toUpperCase()}
                  description={canAccessMethod(selectedMethod.id) ? "You have access" : "Upgrade required"}
                  icon={canAccessMethod(selectedMethod.id) ? <CheckCircleIcon /> : <UpgradeIcon />}
                  color={canAccessMethod(selectedMethod.id) ? "success" : "warning"}
                />
              </Grid>
            </Grid>
          </Box>

          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Data Requirements</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.dataRequirements.map((req, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircleIcon color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={req} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Statistical Assumptions</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.assumptions.map((assumption, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <InfoIcon color="info" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={assumption} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Interpretation Guide</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                {selectedMethod.interpretation}
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.examples.map((example, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <LightbulbIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={example} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            {canAccessMethod(selectedMethod.id) ? (
              <Button
                variant="contained"
                startIcon={<PlayArrowIcon />}
                onClick={() => navigateToAnalysis(selectedMethod)}
                fullWidth
              >
                Run Analysis in DataStatPro
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<UpgradeIcon />}
                onClick={() => navigate('/app#pricing')}
                fullWidth
                color="warning"
              >
                Upgrade to Access
              </Button>
            )}
            <Button
              variant="outlined"
              startIcon={bookmarkedMethods.has(selectedMethod.id) ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              onClick={() => toggleBookmark(selectedMethod.id)}
            >
              {bookmarkedMethods.has(selectedMethod.id) ? 'Bookmarked' : 'Bookmark'}
            </Button>
          </Box>
        </Paper>
      )}

      {/* Backdrop for method details */}
      {selectedMethod && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1200
          }}
          onClick={() => setSelectedMethod(null)}
        />
      )}
    </Box>
  );
};

export default EnhancedStatisticalAnalysisAdvisor;
