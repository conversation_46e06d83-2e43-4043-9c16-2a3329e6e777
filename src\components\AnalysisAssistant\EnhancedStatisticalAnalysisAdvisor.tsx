import React, { useState, useCallback, useContext, useEffect, useMemo } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  Tooltip,
  IconButton,
  Breadcrumbs,
  Link,
  Badge,
  Stack,
  Avatar,
  alpha,
  useTheme,
  Skeleton,
  TextField,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup,
  FormControlLabel,
  Switch,
  Grid
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  TipsAndUpdates as TipsAndUpdatesIcon,
  Psychology as PsychologyIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  ShowChart as ShowChartIcon,
  Functions as FunctionsIcon,
  Science as ScienceIcon,
  Analytics as AnalyticsIcon,
  Insights as InsightsIcon,
  TrendingUp as TrendingUpIcon,
  Compare as CompareIcon,
  Lightbulb as LightbulbIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Help as HelpIcon,
  Launch as LaunchIcon,
  Login as LoginIcon,
  Upgrade as UpgradeIcon,
  School as SchoolIcon,
  Star as StarIcon,
  PlayArrow as PlayArrowIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  FilterList as FilterListIcon,
  ViewList as ViewListIcon,
  AccountTree as AccountTreeIcon,
  AutoAwesome as AutoAwesomeIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Category as CategoryIcon,
  Explore as ExploreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useResults } from '../../context/ResultsContext';
import StatsCard from '../UI/StatsCard';
import { DataContextAnalyzer } from './utils/dataContextAnalyzer';
import EnhancedRecommendationCard from './components/EnhancedRecommendationCard';
import WorkflowProgress from './components/WorkflowProgress';

// Enhanced types for the tree-like interface
interface AnalysisMethod {
  id: string;
  name: string;
  category: string;
  subcategory: string;
  description: string;
  assumptions: string[];
  dataRequirements: string[];
  interpretation: string;
  route: string;
  accessLevel: 'guest' | 'standard' | 'pro' | 'edu';
  icon: React.ReactNode;
  examples: string[];
  relatedMethods: string[];
  contextualRecommendations?: string[];
}

interface AnalysisCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  subcategories: AnalysisSubcategory[];
}

interface AnalysisSubcategory {
  id: string;
  name: string;
  description: string;
  methods: AnalysisMethod[];
}

interface ViewMode {
  mode: 'tree' | 'recommendations' | 'search';
  label: string;
  icon: React.ReactNode;
}

interface StatisticalAnalysisAdvisorProps {
  currentDataset?: any;
  datasetAnalysis?: any;
}

// Enhanced analysis methods data structure
const ANALYSIS_METHODS: AnalysisMethod[] = [
  {
    id: 'descriptive_single',
    name: 'Descriptive Statistics',
    category: 'Descriptive Statistics',
    subcategory: 'Central Tendency & Variability',
    description: 'Summarize the central tendency, variability, and distribution shape of numeric variables.',
    assumptions: ['Variable should be numeric', 'No missing data assumptions'],
    dataRequirements: ['At least 10-20 observations recommended', 'Single or multiple numeric variables'],
    interpretation: 'Mean shows central tendency, standard deviation shows spread, skewness indicates distribution shape.',
    route: '/stats/descriptives',
    accessLevel: 'guest',
    icon: <BarChartIcon />,
    examples: ['Calculate mean, median, mode', 'Measure standard deviation and variance', 'Assess data distribution shape'],
    relatedMethods: ['normality_tests', 'frequency_table']
  },
  {
    id: 'frequency_table',
    name: 'Frequency Tables',
    category: 'Descriptive Statistics',
    subcategory: 'Distribution Analysis',
    description: 'Examine the distribution of categorical or discrete numeric variables.',
    assumptions: ['Variable can be categorical or numeric', 'Finite number of unique values'],
    dataRequirements: ['At least 1 observation', 'Categorical or discrete numeric variable'],
    interpretation: 'Shows frequency and percentage of each category or value.',
    route: '/stats/frequencies',
    accessLevel: 'guest',
    icon: <ViewListIcon />,
    examples: ['Count categories in a variable', 'Calculate percentages', 'Create frequency distributions'],
    relatedMethods: ['crosstabs', 'chi_square']
  },
  {
    id: 'normality_tests',
    name: 'Normality Tests',
    category: 'Descriptive Statistics',
    subcategory: 'Distribution Testing',
    description: 'Test whether data follows a normal distribution using various statistical tests.',
    assumptions: ['Variable should be numeric', 'Continuous data preferred'],
    dataRequirements: ['At least 3 observations', 'Single numeric variable'],
    interpretation: 'P-value < 0.05 suggests data is not normally distributed.',
    route: '/stats/normality',
    accessLevel: 'guest',
    icon: <ShowChartIcon />,
    examples: ['Shapiro-Wilk test', 'Kolmogorov-Smirnov test', 'Anderson-Darling test'],
    relatedMethods: ['descriptive_single', 'independent_ttest']
  },
  {
    id: 'independent_ttest',
    name: 'Independent Samples t-test',
    category: 'Inferential Statistics',
    subcategory: 'Group Comparison',
    description: 'Compare means between two independent groups.',
    assumptions: ['Normal distribution', 'Independent observations', 'Equal variances (or use Welch correction)'],
    dataRequirements: ['At least 30 observations per group recommended', 'One numeric variable', 'One grouping variable with 2 levels'],
    interpretation: 'P-value < 0.05 suggests significant difference between group means.',
    route: '/inferential-stats/independent-samples-ttest',
    accessLevel: 'guest',
    icon: <CompareIcon />,
    examples: ['Compare test scores between two classes', 'Compare treatment vs control groups'],
    relatedMethods: ['mann_whitney', 'one_way_anova']
  }
];

// Analysis categories structure
const ANALYSIS_CATEGORIES: AnalysisCategory[] = [
  {
    id: 'descriptive',
    name: 'Descriptive Statistics',
    description: 'Explore and summarize your data characteristics',
    icon: <BarChartIcon />,
    color: '#2196F3',
    subcategories: [
      {
        id: 'central_tendency',
        name: 'Central Tendency & Variability',
        description: 'Measures of center and spread',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Central Tendency & Variability')
      },
      {
        id: 'distribution',
        name: 'Distribution Analysis',
        description: 'Examine data distributions and patterns',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Distribution Analysis')
      },
      {
        id: 'testing',
        name: 'Distribution Testing',
        description: 'Test distributional assumptions',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Distribution Testing')
      }
    ]
  },
  {
    id: 'inferential',
    name: 'Inferential Statistics',
    description: 'Test hypotheses and make statistical inferences',
    icon: <ScienceIcon />,
    color: '#4CAF50',
    subcategories: [
      {
        id: 'group_comparison',
        name: 'Group Comparison',
        description: 'Compare means between groups',
        methods: ANALYSIS_METHODS.filter(m => m.subcategory === 'Group Comparison')
      }
    ]
  }
];

// View modes
const VIEW_MODES: ViewMode[] = [
  { mode: 'recommendations', label: 'Smart Recommendations', icon: <AutoAwesomeIcon /> },
  { mode: 'tree', label: 'Browse Methods', icon: <AccountTreeIcon /> },
  { mode: 'search', label: 'Search', icon: <SearchIcon /> }
];

// Access level requirements mapping
const ACCESS_REQUIREMENTS = {
  descriptive_single: 'guest',
  frequency_table: 'guest',
  normality_tests: 'guest',
  independent_ttest: 'guest',
  mann_whitney: 'standard',
  one_way_anova: 'standard',
  two_way_anova: 'pro',
  repeated_measures_anova: 'pro'
};

const EnhancedStatisticalAnalysisAdvisor: React.FC<StatisticalAnalysisAdvisorProps> = ({
  currentDataset,
  datasetAnalysis
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { addResult } = useResults();
  const { 
    user, 
    isGuest, 
    accountType, 
    effectiveTier,
    canAccessAdvancedAnalysis,
    canAccessPublicationReady,
    isEducationalUser,
    educationalTier
  } = useContext(AuthContext);

  // State management
  const [viewMode, setViewMode] = useState<'recommendations' | 'tree' | 'search'>('recommendations');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedMethod, setSelectedMethod] = useState<AnalysisMethod | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [bookmarkedMethods, setBookmarkedMethods] = useState<Set<string>>(new Set());
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['descriptive']));
  const [showOnlyAccessible, setShowOnlyAccessible] = useState<boolean>(false);

  const isAuthenticated = !!user;

  // Helper function to check if user can access a specific method
  const canAccessMethod = useCallback((methodId: string): boolean => {
    const requiredLevel = ACCESS_REQUIREMENTS[methodId as keyof typeof ACCESS_REQUIREMENTS];
    if (!requiredLevel) return true;

    if (isGuest) return requiredLevel === 'guest';
    if (!user) return requiredLevel === 'guest';

    const tierHierarchy = { guest: 0, standard: 1, edu: 1, pro: 2, edu_pro: 2 };
    const requiredTierLevel = tierHierarchy[requiredLevel as keyof typeof tierHierarchy] || 0;
    const userTierLevel = tierHierarchy[effectiveTier as keyof typeof tierHierarchy] || 0;

    return userTierLevel >= requiredTierLevel;
  }, [isGuest, user, effectiveTier]);

  // Helper function to navigate to analysis
  const navigateToAnalysis = useCallback((method: AnalysisMethod) => {
    if (canAccessMethod(method.id)) {
      navigate(`/app${method.route}`);
    }
  }, [navigate, canAccessMethod]);

  // Helper function to toggle bookmark
  const toggleBookmark = useCallback((methodId: string) => {
    setBookmarkedMethods(prev => {
      const newSet = new Set(prev);
      if (newSet.has(methodId)) {
        newSet.delete(methodId);
      } else {
        newSet.add(methodId);
      }
      return newSet;
    });
  }, []);

  // Helper function to toggle category expansion
  const toggleCategoryExpansion = useCallback((categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  }, []);

  // Generate intelligent recommendations using DataContextAnalyzer
  const intelligentRecommendations = useMemo(() => {
    if (!currentDataset || !datasetAnalysis) return [];

    try {
      const analyzer = new DataContextAnalyzer(datasetAnalysis);
      const recommendations = analyzer.getRecommendations();

      // Map analyzer recommendations to our method structure
      return recommendations.map(rec => {
        const method = ANALYSIS_METHODS.find(m => m.id === rec.methodId);
        if (!method) return null;

        return {
          ...method,
          contextualRecommendations: rec.reasoning,
          priority: rec.priority,
          confidence: rec.confidence,
          estimatedTime: rec.estimatedTime,
          tips: rec.contextualTips
        };
      }).filter(Boolean);
    } catch (error) {
      console.error('Error generating intelligent recommendations:', error);
      return [];
    }
  }, [currentDataset, datasetAnalysis]);

  // Get data summary for context display
  const dataSummary = useMemo(() => {
    if (!currentDataset || !datasetAnalysis) return null;

    try {
      const analyzer = new DataContextAnalyzer(datasetAnalysis);
      return analyzer.getDataSummary();
    } catch (error) {
      console.error('Error generating data summary:', error);
      return null;
    }
  }, [currentDataset, datasetAnalysis]);

  // Filter methods based on search query and access level
  const filteredMethods = useMemo(() => {
    let methods = ANALYSIS_METHODS;

    if (searchQuery) {
      methods = methods.filter(method =>
        method.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        method.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        method.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (showOnlyAccessible) {
      methods = methods.filter(method => canAccessMethod(method.id));
    }

    return methods;
  }, [searchQuery, showOnlyAccessible, canAccessMethod]);

  // Method card component
  const MethodCard: React.FC<{ method: AnalysisMethod; showContext?: boolean }> = ({ method, showContext = false }) => {
    const hasAccess = canAccessMethod(method.id);
    const isBookmarked = bookmarkedMethods.has(method.id);

    return (
      <Card
        sx={{
          mb: 2,
          cursor: hasAccess ? 'pointer' : 'default',
          opacity: hasAccess ? 1 : 0.7,
          border: selectedMethod?.id === method.id ? `2px solid ${theme.palette.primary.main}` : 'none',
          '&:hover': hasAccess ? {
            boxShadow: theme.shadows[4],
            transform: 'translateY(-2px)',
            transition: 'all 0.2s ease-in-out'
          } : {}
        }}
        onClick={() => hasAccess && setSelectedMethod(method)}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
              <Avatar sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                width: 40,
                height: 40
              }}>
                {method.icon}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ fontSize: '1.1rem', mb: 0.5 }}>
                  {method.name}
                  {!hasAccess && (
                    <Chip
                      label="PRO"
                      size="small"
                      color="warning"
                      sx={{ ml: 1, fontSize: '0.7rem' }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {method.description}
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleBookmark(method.id);
                }}
              >
                {isBookmarked ? <BookmarkIcon color="primary" /> : <BookmarkBorderIcon />}
              </IconButton>
              {hasAccess && (
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<PlayArrowIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    navigateToAnalysis(method);
                  }}
                >
                  Run Analysis
                </Button>
              )}
            </Box>
          </Box>

          {showContext && method.contextualRecommendations && (
            <Alert severity="info" sx={{ mt: 1 }}>
              <Typography variant="body2">
                {method.contextualRecommendations[0]}
              </Typography>
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
            <Chip
              label={method.category}
              size="small"
              variant="outlined"
              color="primary"
            />
            <Chip
              label={method.subcategory}
              size="small"
              variant="outlined"
            />
            <Chip
              label={`${method.accessLevel.toUpperCase()} Access`}
              size="small"
              color={hasAccess ? 'success' : 'warning'}
            />
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PsychologyIcon color="primary" />
            <Typography variant="h6">
              Statistical Analysis Advisor
            </Typography>
            <Chip 
              icon={<AutoAwesomeIcon />} 
              label="AI" 
              color="primary" 
              size="small" 
            />
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Get intelligent recommendations for statistical analysis based on your data characteristics and research objectives.
        </Typography>

        {/* View Mode Toggle */}
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={(e, newMode) => newMode && setViewMode(newMode)}
          size="small"
          sx={{ mb: 2 }}
        >
          {VIEW_MODES.map((mode) => (
            <ToggleButton key={mode.mode} value={mode.mode}>
              {mode.icon}
              <Typography variant="caption" sx={{ ml: 0.5 }}>
                {mode.label}
              </Typography>
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>

      {/* Content based on view mode */}
      {viewMode === 'recommendations' && (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Smart Recommendations
            </Typography>
            {currentDataset && (
              <Chip
                icon={<CheckCircleIcon />}
                label={`Dataset: ${currentDataset.name || 'Loaded'}`}
                color="success"
                size="small"
              />
            )}
          </Box>

          {!currentDataset ? (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Load a dataset to get personalized analysis recommendations based on your data characteristics.
              </Typography>
            </Alert>
          ) : (
            <Box>
              {/* Data Summary */}
              {dataSummary && (
                <Paper elevation={1} sx={{ p: 2, mb: 3, bgcolor: alpha(theme.palette.info.main, 0.05) }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <InsightsIcon color="info" />
                    Data Overview
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    {dataSummary.overview}
                  </Typography>

                  {dataSummary.strengths.length > 0 && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="success.main" fontWeight="medium">
                        Strengths:
                      </Typography>
                      {dataSummary.strengths.map((strength, index) => (
                        <Typography key={index} variant="caption" sx={{ display: 'block', ml: 1 }}>
                          • {strength}
                        </Typography>
                      ))}
                    </Box>
                  )}

                  {dataSummary.concerns.length > 0 && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="warning.main" fontWeight="medium">
                        Concerns:
                      </Typography>
                      {dataSummary.concerns.map((concern, index) => (
                        <Typography key={index} variant="caption" sx={{ display: 'block', ml: 1 }}>
                          • {concern}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Paper>
              )}

              {intelligentRecommendations.length > 0 ? (
                <Box>
                  <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AutoAwesomeIcon color="primary" />
                    AI-Powered Recommendations
                  </Typography>
                  {intelligentRecommendations.map((method: any) => (
                    <EnhancedRecommendationCard
                      key={method.id}
                      method={method}
                      hasAccess={canAccessMethod(method.id)}
                      isBookmarked={bookmarkedMethods.has(method.id)}
                      onBookmarkToggle={toggleBookmark}
                      onRunAnalysis={navigateToAnalysis}
                      onViewDetails={setSelectedMethod}
                      showContext={true}
                      priority={method.priority}
                      confidence={method.confidence}
                      estimatedTime={method.estimatedTime}
                    />
                  ))}

                  <Divider sx={{ my: 3 }} />

                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    All Available Methods
                  </Typography>
                  {ANALYSIS_METHODS.filter(m => !intelligentRecommendations.find((r: any) => r.id === m.id))
                    .map((method) => (
                      <MethodCard key={method.id} method={method} />
                    ))}
                </Box>
              ) : (
                <Box>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      Unable to generate specific recommendations. Browse all available methods below.
                    </Typography>
                  </Alert>
                  {ANALYSIS_METHODS.map((method) => (
                    <MethodCard key={method.id} method={method} />
                  ))}
                </Box>
              )}
            </Box>
          )}
        </Box>
      )}

      {viewMode === 'tree' && (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Browse Analysis Methods
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={showOnlyAccessible}
                  onChange={(e) => setShowOnlyAccessible(e.target.checked)}
                  size="small"
                />
              }
              label="Show only accessible"
            />
          </Box>

          {ANALYSIS_CATEGORIES.map((category) => (
            <Accordion
              key={category.id}
              expanded={expandedCategories.has(category.id)}
              onChange={() => toggleCategoryExpansion(category.id)}
              sx={{ mb: 1 }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <Avatar sx={{
                    bgcolor: alpha(category.color, 0.1),
                    color: category.color,
                    width: 32,
                    height: 32
                  }}>
                    {category.icon}
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {category.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.description}
                    </Typography>
                  </Box>
                  <Chip
                    label={category.subcategories.reduce((acc, sub) => acc + sub.methods.length, 0)}
                    size="small"
                    sx={{ mr: 1 }}
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                {category.subcategories.map((subcategory) => (
                  <Box key={subcategory.id} sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                      {subcategory.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {subcategory.description}
                    </Typography>
                    {subcategory.methods
                      .filter(method => !showOnlyAccessible || canAccessMethod(method.id))
                      .map((method) => (
                        <MethodCard key={method.id} method={method} />
                      ))}
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}

      {viewMode === 'search' && (
        <Box>
          <TextField
            fullWidth
            placeholder="Search analysis methods..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'between', mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {filteredMethods.length} method{filteredMethods.length !== 1 ? 's' : ''} found
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={showOnlyAccessible}
                  onChange={(e) => setShowOnlyAccessible(e.target.checked)}
                  size="small"
                />
              }
              label="Show only accessible"
            />
          </Box>

          {filteredMethods.length > 0 ? (
            filteredMethods.map((method) => (
              <MethodCard key={method.id} method={method} />
            ))
          ) : (
            <Alert severity="info">
              <Typography variant="body2">
                No methods found matching your search criteria.
              </Typography>
            </Alert>
          )}
        </Box>
      )}

      {/* Method Details Panel */}
      {selectedMethod && (
        <Paper
          elevation={3}
          sx={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: 600,
            maxHeight: '80vh',
            overflow: 'auto',
            zIndex: 1300,
            p: 3
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h5">
              {selectedMethod.name}
            </Typography>
            <IconButton onClick={() => setSelectedMethod(null)}>
              <ArrowBackIcon />
            </IconButton>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {selectedMethod.description}
            </Typography>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={6}>
                <StatsCard
                  title="Category"
                  value={selectedMethod.category}
                  description={selectedMethod.subcategory}
                  icon={selectedMethod.icon}
                  color="primary"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <StatsCard
                  title="Access Level"
                  value={selectedMethod.accessLevel.toUpperCase()}
                  description={canAccessMethod(selectedMethod.id) ? "You have access" : "Upgrade required"}
                  icon={canAccessMethod(selectedMethod.id) ? <CheckCircleIcon /> : <UpgradeIcon />}
                  color={canAccessMethod(selectedMethod.id) ? "success" : "warning"}
                />
              </Grid>
            </Grid>
          </Box>

          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Data Requirements</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.dataRequirements.map((req, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircleIcon color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={req} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Statistical Assumptions</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.assumptions.map((assumption, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <InfoIcon color="info" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={assumption} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Interpretation Guide</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                {selectedMethod.interpretation}
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {selectedMethod.examples.map((example, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <LightbulbIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={example} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
            {canAccessMethod(selectedMethod.id) ? (
              <Button
                variant="contained"
                startIcon={<PlayArrowIcon />}
                onClick={() => navigateToAnalysis(selectedMethod)}
                fullWidth
              >
                Run Analysis in DataStatPro
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<UpgradeIcon />}
                onClick={() => navigate('/app#pricing')}
                fullWidth
                color="warning"
              >
                Upgrade to Access
              </Button>
            )}
            <Button
              variant="outlined"
              startIcon={bookmarkedMethods.has(selectedMethod.id) ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              onClick={() => toggleBookmark(selectedMethod.id)}
            >
              {bookmarkedMethods.has(selectedMethod.id) ? 'Bookmarked' : 'Bookmark'}
            </Button>
          </Box>
        </Paper>
      )}

      {/* Backdrop for method details */}
      {selectedMethod && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1200
          }}
          onClick={() => setSelectedMethod(null)}
        />
      )}
    </Box>
  );
};

export default EnhancedStatisticalAnalysisAdvisor;
