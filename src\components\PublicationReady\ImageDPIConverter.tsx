import React, { useState, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  Paper,
  Divider,
  Stack
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Delete,
  Preview,
  Settings,
  Info,
  CheckCircle,
  Error as ErrorIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

interface ProcessedImage {
  id: string;
  originalFile: File;
  originalUrl: string;
  processedUrl?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
  originalDimensions?: { width: number; height: number };
  processedDimensions?: { width: number; height: number };
}

interface JournalPreset {
  name: string;
  dpi: number;
  format: 'PNG' | 'TIFF' | 'JPEG';
  quality: number;
  description: string;
}

const JOURNAL_PRESETS: JournalPreset[] = [
  { name: 'Nature/Science', dpi: 300, format: 'TIFF', quality: 100, description: 'High-resolution TIFF for top-tier journals' },
  { name: 'PLOS ONE', dpi: 300, format: 'PNG', quality: 100, description: 'PNG format preferred by PLOS journals' },
  { name: 'Elsevier Journals', dpi: 300, format: 'JPEG', quality: 95, description: 'High-quality JPEG for Elsevier publications' },
  { name: 'IEEE Publications', dpi: 600, format: 'TIFF', quality: 100, description: 'Ultra-high resolution for IEEE standards' },
  { name: 'Web/Presentation', dpi: 150, format: 'PNG', quality: 90, description: 'Optimized for web and presentations' },
  { name: 'Print Publication', dpi: 300, format: 'TIFF', quality: 100, description: 'Standard print quality' }
];

const DPI_OPTIONS = [72, 150, 300, 600, 1200];
const FORMAT_OPTIONS = ['PNG', 'JPEG', 'TIFF'] as const;
type ImageFormat = typeof FORMAT_OPTIONS[number];

export const ImageDPIConverter: React.FC = () => {
  const [images, setImages] = useState<ProcessedImage[]>([]);
  const [selectedDPI, setSelectedDPI] = useState<number>(300);
  const [selectedFormat, setSelectedFormat] = useState<ImageFormat>('PNG');
  const [quality, setQuality] = useState<number>(95);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewImage, setPreviewImage] = useState<ProcessedImage | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const handleFileSelect = useCallback((files: FileList) => {
    const newImages: ProcessedImage[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.startsWith('image/')) {
        const id = generateId();
        const originalUrl = URL.createObjectURL(file);
        
        newImages.push({
          id,
          originalFile: file,
          originalUrl,
          status: 'pending'
        });
      }
    }
    
    setImages(prev => [...prev, ...newImages]);
  }, []);

  const loadImageDimensions = (url: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve({ width: img.width, height: img.height });
      img.onerror = reject;
      img.src = url;
    });
  };

  const convertImageDPI = async (image: ProcessedImage): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Canvas context not available'));
        return;
      }

      const img = new Image();
      img.onload = () => {
        try {
          // Calculate new dimensions based on DPI
          const scaleFactor = selectedDPI / 72; // Assuming original is 72 DPI
          const newWidth = Math.round(img.width * scaleFactor);
          const newHeight = Math.round(img.height * scaleFactor);

          canvas.width = newWidth;
          canvas.height = newHeight;

          // Use high-quality scaling
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          
          ctx.drawImage(img, 0, 0, newWidth, newHeight);

          // Convert to desired format
          let mimeType: string;
          let qualityValue: number | undefined;

          switch (selectedFormat) {
            case 'JPEG':
              mimeType = 'image/jpeg';
              qualityValue = quality / 100;
              break;
            case 'PNG':
              mimeType = 'image/png';
              break;
            case 'TIFF':
              // Note: Canvas doesn't natively support TIFF, so we'll use PNG as fallback
              mimeType = 'image/png';
              break;
            default:
              mimeType = 'image/png';
          }

          canvas.toBlob(
            (blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                resolve(url);
              } else {
                reject(new Error('Failed to create blob'));
              }
            },
            mimeType,
            qualityValue
          );
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = image.originalUrl;
    });
  };

  const processImages = async () => {
    setIsProcessing(true);
    
    for (const image of images) {
      if (image.status !== 'pending') continue;
      
      setImages(prev => prev.map(img => 
        img.id === image.id ? { ...img, status: 'processing' } : img
      ));

      try {
        // Load original dimensions
        const originalDimensions = await loadImageDimensions(image.originalUrl);
        
        // Process image
        const processedUrl = await convertImageDPI(image);
        const processedDimensions = await loadImageDimensions(processedUrl);
        
        setImages(prev => prev.map(img => 
          img.id === image.id ? {
            ...img,
            status: 'completed',
            processedUrl,
            originalDimensions,
            processedDimensions
          } : img
        ));
      } catch (error) {
        setImages(prev => prev.map(img => 
          img.id === image.id ? {
            ...img,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          } : img
        ));
      }
    }
    
    setIsProcessing(false);
  };

  const downloadSingle = (image: ProcessedImage) => {
    if (!image.processedUrl) return;
    
    const link = document.createElement('a');
    link.href = image.processedUrl;
    const extension = selectedFormat.toLowerCase();
    link.download = `${image.originalFile.name.split('.')[0]}_${selectedDPI}dpi.${extension}`;
    link.click();
  };

  const downloadAll = async () => {
    const completedImages = images.filter(img => img.status === 'completed' && img.processedUrl);
    
    if (completedImages.length === 0) return;
    
    if (completedImages.length === 1) {
      downloadSingle(completedImages[0]);
      return;
    }

    // Create ZIP file for multiple images
    const zip = new JSZip();
    
    for (const image of completedImages) {
      if (!image.processedUrl) continue;
      
      try {
        const response = await fetch(image.processedUrl);
        const blob = await response.blob();
        const extension = selectedFormat.toLowerCase();
        const filename = `${image.originalFile.name.split('.')[0]}_${selectedDPI}dpi.${extension}`;
        zip.file(filename, blob);
      } catch (error) {
        console.error('Error adding file to ZIP:', error);
      }
    }
    
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, `converted_images_${selectedDPI}dpi.zip`);
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const updated = prev.filter(img => img.id !== id);
      // Clean up URLs
      const removed = prev.find(img => img.id === id);
      if (removed) {
        URL.revokeObjectURL(removed.originalUrl);
        if (removed.processedUrl) {
          URL.revokeObjectURL(removed.processedUrl);
        }
      }
      return updated;
    });
  };

  const clearAll = () => {
    images.forEach(img => {
      URL.revokeObjectURL(img.originalUrl);
      if (img.processedUrl) {
        URL.revokeObjectURL(img.processedUrl);
      }
    });
    setImages([]);
  };

  const applyPreset = (preset: JournalPreset) => {
    setSelectedDPI(preset.dpi);
    setSelectedFormat(preset.format);
    setQuality(preset.quality);
    setShowSettings(false);
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const completedCount = images.filter(img => img.status === 'completed').length;
  const hasProcessedImages = completedCount > 0;

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <ImageIcon color="primary" />
        Image DPI Converter
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Convert your research figures to publication-ready formats with precise DPI control.
        Perfect for journal submissions and academic publications.
      </Typography>

      {/* Settings and Presets */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
            <Typography variant="h6">Quick Presets:</Typography>
            {JOURNAL_PRESETS.slice(0, 4).map((preset) => (
              <Chip
                key={preset.name}
                label={preset.name}
                onClick={() => applyPreset(preset)}
                variant="outlined"
                clickable
              />
            ))}
            <Button
              startIcon={<Settings />}
              onClick={() => setShowSettings(true)}
              variant="outlined"
              size="small"
            >
              More Settings
            </Button>
          </Stack>
          
          <Divider sx={{ my: 2 }} />
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>DPI</InputLabel>
                <Select
                  value={selectedDPI}
                  label="DPI"
                  onChange={(e) => setSelectedDPI(Number(e.target.value))}
                >
                  {DPI_OPTIONS.map(dpi => (
                    <MenuItem key={dpi} value={dpi}>{dpi} DPI</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Format</InputLabel>
                <Select
                  value={selectedFormat}
                  label="Format"
                  onChange={(e) => setSelectedFormat(e.target.value as ImageFormat)}
                >
                  {FORMAT_OPTIONS.map(format => (
                    <MenuItem key={format} value={format}>{format}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {selectedFormat === 'JPEG' && (
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Quality</InputLabel>
                  <Select
                    value={quality}
                    label="Quality"
                    onChange={(e) => setQuality(Number(e.target.value))}
                  >
                    {[70, 80, 90, 95, 100].map(q => (
                      <MenuItem key={q} value={q}>{q}%</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12} sm={3}>
              <Typography variant="body2" color="text.secondary">
                Current: {selectedDPI} DPI, {selectedFormat}
                {selectedFormat === 'JPEG' && `, ${quality}% quality`}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* File Upload Area */}
      <Paper
        sx={{
          p: 4,
          border: '2px dashed',
          borderColor: 'primary.main',
          borderRadius: 2,
          textAlign: 'center',
          cursor: 'pointer',
          mb: 3,
          '&:hover': {
            bgcolor: 'action.hover'
          }
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onClick={() => fileInputRef.current?.click()}
      >
        <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Drop images here or click to select
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Supports PNG, JPEG, GIF, BMP, and WebP formats
        </Typography>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          style={{ display: 'none' }}
          onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        />
      </Paper>

      {/* Processing Controls */}
      {images.length > 0 && (
        <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            onClick={processImages}
            disabled={isProcessing || images.every(img => img.status !== 'pending')}
            startIcon={<Settings />}
          >
            {isProcessing ? 'Processing...' : 'Convert Images'}
          </Button>
          
          <Button
            variant="outlined"
            onClick={downloadAll}
            disabled={!hasProcessedImages}
            startIcon={<Download />}
          >
            Download {completedCount > 1 ? 'All (ZIP)' : 'Image'}
          </Button>
          
          <Button
            variant="outlined"
            color="error"
            onClick={clearAll}
            startIcon={<Delete />}
          >
            Clear All
          </Button>
          
          <Typography variant="body2" color="text.secondary">
            {images.length} image(s) • {completedCount} processed
          </Typography>
        </Box>
      )}

      {/* Progress Bar */}
      {isProcessing && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Processing images...
          </Typography>
        </Box>
      )}

      {/* Image List */}
      {images.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Images ({images.length})
            </Typography>
            
            <List>
              {images.map((image) => (
                <ListItem key={image.id} divider>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2">
                          {image.originalFile.name}
                        </Typography>
                        {image.status === 'completed' && <CheckCircle color="success" fontSize="small" />}
                        {image.status === 'error' && <ErrorIcon color="error" fontSize="small" />}
                        {image.status === 'processing' && <LinearProgress sx={{ width: 100, ml: 1 }} />}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Size: {(image.originalFile.size / 1024 / 1024).toFixed(2)} MB
                        </Typography>
                        {image.originalDimensions && (
                          <Typography variant="body2" color="text.secondary">
                            Original: {image.originalDimensions.width} × {image.originalDimensions.height}px
                          </Typography>
                        )}
                        {image.processedDimensions && (
                          <Typography variant="body2" color="text.secondary">
                            Processed: {image.processedDimensions.width} × {image.processedDimensions.height}px
                          </Typography>
                        )}
                        {image.error && (
                          <Alert severity="error" sx={{ mt: 1 }}>
                            {image.error}
                          </Alert>
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {image.processedUrl && (
                        <>
                          <Tooltip title="Preview">
                            <IconButton
                              size="small"
                              onClick={() => setPreviewImage(image)}
                            >
                              <Preview />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="Download">
                            <IconButton
                              size="small"
                              onClick={() => downloadSingle(image)}
                            >
                              <Download />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                      
                      <Tooltip title="Remove">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeImage(image.id)}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Settings Dialog */}
      <Dialog open={showSettings} onClose={() => setShowSettings(false)} maxWidth="md" fullWidth>
        <DialogTitle>Journal Presets & Advanced Settings</DialogTitle>
        <DialogContent>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Journal-Specific Presets
          </Typography>
          
          <Grid container spacing={2}>
            {JOURNAL_PRESETS.map((preset) => (
              <Grid item xs={12} sm={6} key={preset.name}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => applyPreset(preset)}
                >
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      {preset.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {preset.description}
                    </Typography>
                    <Typography variant="body2">
                      {preset.dpi} DPI • {preset.format} • {preset.quality}% quality
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              <strong>DPI Guidelines:</strong><br />
              • 72-150 DPI: Web and presentations<br />
              • 300 DPI: Standard print quality<br />
              • 600+ DPI: High-resolution print and archival
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettings(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog 
        open={!!previewImage} 
        onClose={() => setPreviewImage(null)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Preview: {previewImage?.originalFile.name}
        </DialogTitle>
        <DialogContent>
          {previewImage && (
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Box sx={{ flex: 1, minWidth: 300 }}>
                <Typography variant="subtitle2" gutterBottom>Original</Typography>
                <img 
                  src={previewImage.originalUrl} 
                  alt="Original" 
                  style={{ width: '100%', height: 'auto', border: '1px solid #ddd' }}
                />
                {previewImage.originalDimensions && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {previewImage.originalDimensions.width} × {previewImage.originalDimensions.height}px
                  </Typography>
                )}
              </Box>
              
              {previewImage.processedUrl && (
                <Box sx={{ flex: 1, minWidth: 300 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Processed ({selectedDPI} DPI, {selectedFormat})
                  </Typography>
                  <img 
                    src={previewImage.processedUrl} 
                    alt="Processed" 
                    style={{ width: '100%', height: 'auto', border: '1px solid #ddd' }}
                  />
                  {previewImage.processedDimensions && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {previewImage.processedDimensions.width} × {previewImage.processedDimensions.height}px
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewImage(null)}>Close</Button>
          {previewImage?.processedUrl && (
            <Button 
              variant="contained" 
              startIcon={<Download />}
              onClick={() => {
                if (previewImage) downloadSingle(previewImage);
                setPreviewImage(null);
              }}
            >
              Download
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImageDPIConverter;