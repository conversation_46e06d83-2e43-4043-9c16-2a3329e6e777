-- User Quota Management System
-- This migration adds comprehensive quota management for datasets and storage

-- Create user_quotas table to store individual user quota overrides
CREATE TABLE IF NOT EXISTS public.user_quotas (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  dataset_limit integer,
  storage_limit_mb integer,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  created_by uuid, -- <PERSON><PERSON> who set the quota
  CONSTRAINT user_quotas_pkey PRIMARY KEY (id),
  CONSTRAINT user_quotas_user_id_key UNIQUE (user_id),
  CONSTRAINT user_quotas_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT user_quotas_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT user_quotas_dataset_limit_check CHECK (dataset_limit >= 0),
  CONSTRAINT user_quotas_storage_limit_check CHECK (storage_limit_mb >= 0)
);

-- Create tier_quotas table to store default quotas for each account type
CREATE TABLE IF NOT EXISTS public.tier_quotas (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  tier_name text NOT NULL,
  dataset_limit integer NOT NULL DEFAULT 2,
  storage_limit_mb integer NOT NULL DEFAULT 4, -- 4MB default (2 datasets * 2MB each)
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT tier_quotas_pkey PRIMARY KEY (id),
  CONSTRAINT tier_quotas_tier_name_key UNIQUE (tier_name),
  CONSTRAINT tier_quotas_dataset_limit_check CHECK (dataset_limit >= 0),
  CONSTRAINT tier_quotas_storage_limit_check CHECK (storage_limit_mb >= 0)
);

-- Create quota_audit_log table for tracking quota changes
CREATE TABLE IF NOT EXISTS public.quota_audit_log (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  target_user_id uuid NOT NULL,
  admin_user_id uuid NOT NULL,
  action_type text NOT NULL, -- 'set_user_quota', 'set_tier_quota', 'remove_user_quota'
  old_values jsonb,
  new_values jsonb,
  reason text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT quota_audit_log_pkey PRIMARY KEY (id),
  CONSTRAINT quota_audit_log_target_user_fkey FOREIGN KEY (target_user_id) REFERENCES auth.users(id),
  CONSTRAINT quota_audit_log_admin_user_fkey FOREIGN KEY (admin_user_id) REFERENCES auth.users(id)
);

-- Insert default tier quotas
INSERT INTO public.tier_quotas (tier_name, dataset_limit, storage_limit_mb) VALUES
  ('guest', 0, 0),
  ('standard', 2, 4),
  ('pro', 10, 50),
  ('edu', 5, 20),
  ('edu_pro', 15, 75)
ON CONFLICT (tier_name) DO NOTHING;

-- Function to get user's effective quota (user-specific override or tier default)
CREATE OR REPLACE FUNCTION public.get_user_effective_quota(target_user_id uuid)
RETURNS TABLE (
  user_id uuid,
  dataset_limit integer,
  storage_limit_mb integer,
  is_custom_quota boolean,
  tier_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier text;
BEGIN
  -- Get user's account type
  SELECT p.accounttype INTO user_tier
  FROM public.profiles p
  WHERE p.id = target_user_id;
  
  -- If no profile found, default to 'standard'
  IF user_tier IS NULL THEN
    user_tier := 'standard';
  END IF;
  
  -- Check if user has custom quota override
  IF EXISTS (SELECT 1 FROM public.user_quotas uq WHERE uq.user_id = target_user_id) THEN
    -- Return custom quota
    RETURN QUERY
    SELECT 
      uq.user_id,
      uq.dataset_limit,
      uq.storage_limit_mb,
      true as is_custom_quota,
      user_tier as tier_name
    FROM public.user_quotas uq
    WHERE uq.user_id = target_user_id;
  ELSE
    -- Return tier default quota
    RETURN QUERY
    SELECT 
      target_user_id as user_id,
      tq.dataset_limit,
      tq.storage_limit_mb,
      false as is_custom_quota,
      user_tier as tier_name
    FROM public.tier_quotas tq
    WHERE tq.tier_name = user_tier;
  END IF;
END;
$$;

-- Function to get user's current usage
CREATE OR REPLACE FUNCTION public.get_user_quota_usage(target_user_id uuid)
RETURNS TABLE (
  user_id uuid,
  dataset_count integer,
  storage_used_mb numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    target_user_id as user_id,
    COALESCE(COUNT(ud.id)::integer, 0) as dataset_count,
    COALESCE(SUM(LENGTH(so.metadata::text) / 1024.0 / 1024.0), 0)::numeric as storage_used_mb
  FROM public.user_datasets ud
  LEFT JOIN storage.objects so ON so.name = ud.file_path
  WHERE ud.user_id = target_user_id
  GROUP BY target_user_id;
END;
$$;

-- Function to set user-specific quota (admin only)
CREATE OR REPLACE FUNCTION public.set_user_quota(
  target_user_id uuid,
  new_dataset_limit integer,
  new_storage_limit_mb integer,
  reason_text text DEFAULT NULL
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  admin_id uuid;
  old_quota jsonb;
  new_quota jsonb;
BEGIN
  -- Check if user is admin
  admin_id := auth.uid();
  IF NOT public.is_user_admin(admin_id) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate inputs
  IF new_dataset_limit < 0 OR new_storage_limit_mb < 0 THEN
    RAISE EXCEPTION 'Quota limits cannot be negative';
  END IF;
  
  -- Get old quota for audit log
  SELECT jsonb_build_object(
    'dataset_limit', dataset_limit,
    'storage_limit_mb', storage_limit_mb
  ) INTO old_quota
  FROM public.get_user_effective_quota(target_user_id);
  
  -- Set new quota values
  new_quota := jsonb_build_object(
    'dataset_limit', new_dataset_limit,
    'storage_limit_mb', new_storage_limit_mb
  );
  
  -- Insert or update user quota
  INSERT INTO public.user_quotas (user_id, dataset_limit, storage_limit_mb, created_by)
  VALUES (target_user_id, new_dataset_limit, new_storage_limit_mb, admin_id)
  ON CONFLICT (user_id) DO UPDATE SET
    dataset_limit = EXCLUDED.dataset_limit,
    storage_limit_mb = EXCLUDED.storage_limit_mb,
    updated_at = now(),
    created_by = admin_id;
  
  -- Log the action
  INSERT INTO public.quota_audit_log (target_user_id, admin_user_id, action_type, old_values, new_values, reason)
  VALUES (target_user_id, admin_id, 'set_user_quota', old_quota, new_quota, reason_text);
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'User quota updated successfully',
    'old_quota', old_quota,
    'new_quota', new_quota
  );
END;
$$;

-- Function to update tier default quotas (admin only)
CREATE OR REPLACE FUNCTION public.set_tier_quota(
  tier_name_param text,
  new_dataset_limit integer,
  new_storage_limit_mb integer,
  reason_text text DEFAULT NULL
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  admin_id uuid;
  old_quota jsonb;
  new_quota jsonb;
BEGIN
  -- Check if user is admin
  admin_id := auth.uid();
  IF NOT public.is_user_admin(admin_id) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Validate inputs
  IF new_dataset_limit < 0 OR new_storage_limit_mb < 0 THEN
    RAISE EXCEPTION 'Quota limits cannot be negative';
  END IF;
  
  -- Get old quota for audit log
  SELECT jsonb_build_object(
    'dataset_limit', dataset_limit,
    'storage_limit_mb', storage_limit_mb
  ) INTO old_quota
  FROM public.tier_quotas
  WHERE tier_name = tier_name_param;
  
  -- Set new quota values
  new_quota := jsonb_build_object(
    'dataset_limit', new_dataset_limit,
    'storage_limit_mb', new_storage_limit_mb
  );
  
  -- Update tier quota
  UPDATE public.tier_quotas
  SET 
    dataset_limit = new_dataset_limit,
    storage_limit_mb = new_storage_limit_mb,
    updated_at = now()
  WHERE tier_name = tier_name_param;
  
  -- Log the action (using a dummy user_id for tier changes)
  INSERT INTO public.quota_audit_log (target_user_id, admin_user_id, action_type, old_values, new_values, reason)
  VALUES (admin_id, admin_id, 'set_tier_quota', 
    old_quota || jsonb_build_object('tier_name', tier_name_param), 
    new_quota || jsonb_build_object('tier_name', tier_name_param), 
    reason_text);
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Tier quota updated successfully',
    'tier_name', tier_name_param,
    'old_quota', old_quota,
    'new_quota', new_quota
  );
END;
$$;

-- Function to remove user-specific quota (revert to tier default)
CREATE OR REPLACE FUNCTION public.remove_user_quota(
  target_user_id uuid,
  reason_text text DEFAULT NULL
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  admin_id uuid;
  old_quota jsonb;
BEGIN
  -- Check if user is admin
  admin_id := auth.uid();
  IF NOT public.is_user_admin(admin_id) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  -- Get old quota for audit log
  SELECT jsonb_build_object(
    'dataset_limit', dataset_limit,
    'storage_limit_mb', storage_limit_mb
  ) INTO old_quota
  FROM public.user_quotas
  WHERE user_id = target_user_id;
  
  -- Remove user quota
  DELETE FROM public.user_quotas WHERE user_id = target_user_id;
  
  -- Log the action
  INSERT INTO public.quota_audit_log (target_user_id, admin_user_id, action_type, old_values, new_values, reason)
  VALUES (target_user_id, admin_id, 'remove_user_quota', old_quota, NULL, reason_text);
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'User quota removed successfully (reverted to tier default)',
    'old_quota', old_quota
  );
END;
$$;

-- Function to get all users with their quotas and usage (admin only)
CREATE OR REPLACE FUNCTION public.get_all_user_quotas(
  page_size integer DEFAULT 25,
  page_offset integer DEFAULT 0,
  search_term text DEFAULT NULL
)
RETURNS TABLE (
  user_id uuid,
  email text,
  full_name text,
  tier_name text,
  dataset_limit integer,
  storage_limit_mb integer,
  is_custom_quota boolean,
  dataset_count integer,
  storage_used_mb numeric,
  dataset_usage_percent numeric,
  storage_usage_percent numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    u.id as user_id,
    u.email,
    p.full_name,
    eq.tier_name,
    eq.dataset_limit,
    eq.storage_limit_mb,
    eq.is_custom_quota,
    COALESCE(usage.dataset_count, 0) as dataset_count,
    COALESCE(usage.storage_used_mb, 0) as storage_used_mb,
    CASE 
      WHEN eq.dataset_limit = 0 THEN 0
      ELSE ROUND((COALESCE(usage.dataset_count, 0)::numeric / eq.dataset_limit::numeric) * 100, 2)
    END as dataset_usage_percent,
    CASE 
      WHEN eq.storage_limit_mb = 0 THEN 0
      ELSE ROUND((COALESCE(usage.storage_used_mb, 0) / eq.storage_limit_mb::numeric) * 100, 2)
    END as storage_usage_percent
  FROM auth.users u
  LEFT JOIN public.profiles p ON p.id = u.id
  LEFT JOIN public.get_user_effective_quota(u.id) eq ON eq.user_id = u.id
  LEFT JOIN public.get_user_quota_usage(u.id) usage ON usage.user_id = u.id
  WHERE 
    (search_term IS NULL OR 
     u.email ILIKE '%' || search_term || '%' OR 
     p.full_name ILIKE '%' || search_term || '%')
  ORDER BY u.created_at DESC
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Function to get quota audit trail (admin only)
CREATE OR REPLACE FUNCTION public.get_quota_audit_trail(
  target_user_id_param uuid DEFAULT NULL,
  page_size integer DEFAULT 25,
  page_offset integer DEFAULT 0
)
RETURNS TABLE (
  id uuid,
  target_user_email text,
  target_user_name text,
  admin_user_email text,
  admin_user_name text,
  action_type text,
  old_values jsonb,
  new_values jsonb,
  reason text,
  created_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is admin
  IF NOT public.is_user_admin(auth.uid()) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;
  
  RETURN QUERY
  SELECT 
    qal.id,
    tu.email as target_user_email,
    tp.full_name as target_user_name,
    au.email as admin_user_email,
    ap.full_name as admin_user_name,
    qal.action_type,
    qal.old_values,
    qal.new_values,
    qal.reason,
    qal.created_at
  FROM public.quota_audit_log qal
  LEFT JOIN auth.users tu ON tu.id = qal.target_user_id
  LEFT JOIN public.profiles tp ON tp.id = qal.target_user_id
  LEFT JOIN auth.users au ON au.id = qal.admin_user_id
  LEFT JOIN public.profiles ap ON ap.id = qal.admin_user_id
  WHERE 
    (target_user_id_param IS NULL OR qal.target_user_id = target_user_id_param)
  ORDER BY qal.created_at DESC
  LIMIT page_size
  OFFSET page_offset;
END;
$$;

-- Enable RLS on new tables
ALTER TABLE public.user_quotas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tier_quotas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quota_audit_log ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admins can manage user quotas" ON public.user_quotas
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()))
  WITH CHECK (public.is_user_admin(auth.uid()));

CREATE POLICY "Admins can manage tier quotas" ON public.tier_quotas
  FOR ALL TO authenticated
  USING (public.is_user_admin(auth.uid()))
  WITH CHECK (public.is_user_admin(auth.uid()));

CREATE POLICY "Admins can view quota audit log" ON public.quota_audit_log
  FOR SELECT TO authenticated
  USING (public.is_user_admin(auth.uid()));

CREATE POLICY "System can insert quota audit log" ON public.quota_audit_log
  FOR INSERT TO authenticated
  WITH CHECK (true); -- Allow system to insert audit logs

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_user_effective_quota(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_quota_usage(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_user_quota(uuid, integer, integer, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_tier_quota(text, integer, integer, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_user_quota(uuid, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_all_user_quotas(integer, integer, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_quota_audit_trail(uuid, integer, integer) TO authenticated;

-- Add comments
COMMENT ON TABLE public.user_quotas IS 'Stores user-specific quota overrides';
COMMENT ON TABLE public.tier_quotas IS 'Stores default quotas for each account tier';
COMMENT ON TABLE public.quota_audit_log IS 'Audit trail for quota changes made by admins';
COMMENT ON FUNCTION public.get_user_effective_quota(uuid) IS 'Gets effective quota for a user (custom or tier default)';
COMMENT ON FUNCTION public.get_user_quota_usage(uuid) IS 'Gets current usage statistics for a user';
COMMENT ON FUNCTION public.set_user_quota(uuid, integer, integer, text) IS 'Sets custom quota for a specific user (admin only)';
COMMENT ON FUNCTION public.set_tier_quota(text, integer, integer, text) IS 'Updates default quota for an account tier (admin only)';
COMMENT ON FUNCTION public.remove_user_quota(uuid, text) IS 'Removes custom quota, reverting to tier default (admin only)';
COMMENT ON FUNCTION public.get_all_user_quotas(integer, integer, text) IS 'Gets all users with their quotas and usage (admin only)';
COMMENT ON FUNCTION public.get_quota_audit_trail(uuid, integer, integer) IS 'Gets quota change audit trail (admin only)';