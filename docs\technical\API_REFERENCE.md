# DataStatPro API Reference

## Overview

This document provides a comprehensive reference for the DataStatPro application's internal APIs, interfaces, and key functions. This is essential for AI assistants and developers working with the codebase.

## 🏗️ Core Type Definitions

### Data Types (`src/types/index.ts`)

#### Basic Data Types
```typescript
type DataValue = string | number | boolean | null | undefined;

type DataRow = {
  [key: string]: DataValue;
};

enum DataType {
  NUMERIC = 'numeric',
  CATEGORICAL = 'categorical',
  ORDINAL = 'ordinal',
  BINARY = 'binary',
  DATE = 'date',
  TEXT = 'text'
}

enum VariableRole {
  INDEPENDENT = 'independent',
  DEPENDENT = 'dependent',
  CONTROL = 'control',
  GROUPING = 'grouping',
  ID = 'id',
  WEIGHT = 'weight'
}
```

#### Dataset Structure
```typescript
interface Dataset {
  id: string;
  name: string;
  columns: Column[];
  rows: DataRow[];
  metadata?: DatasetMetadata;
  createdAt?: string;
  updatedAt?: string;
  userId?: string;
  isPublic?: boolean;
}

interface Column {
  id: string;
  name: string;
  type: DataType;
  role?: VariableRole;
  statistics?: ColumnStatistics;
  metadata?: ColumnMetadata;
  transformations?: Transformation[];
}

interface ColumnStatistics {
  count: number;
  missing: number;
  unique: number;
  mean?: number;
  median?: number;
  mode?: DataValue[];
  std?: number;
  min?: number;
  max?: number;
  q1?: number;
  q3?: number;
  skewness?: number;
  kurtosis?: number;
}
```

#### Statistical Test Results
```typescript
interface StatisticalTestResult {
  testName: string;
  statistic: number;
  pValue: number;
  degreesOfFreedom?: number;
  criticalValue?: number;
  effectSize?: number;
  confidenceInterval?: [number, number];
  interpretation: string;
  assumptions?: AssumptionCheck[];
  warnings?: string[];
}

interface AssumptionCheck {
  name: string;
  met: boolean;
  statistic?: number;
  pValue?: number;
  description: string;
}
```

## 🔐 Authentication Context API

### AuthContext Interface
```typescript
interface AuthContextType {
  // Session Management
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  
  // Authentication Methods
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signUp: (email: string, password: string, userData?: any) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<AuthResponse>;
  resetPassword: (email: string) => Promise<void>;
  
  // Profile Management
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
  
  // Access Control
  isGuest: boolean;
  isAdmin: boolean;
  isPro: boolean;
  canAccessSampleData: boolean;
  canImportData: boolean;
  canAccessProFeatures: boolean;
  
  // Subscription Management
  subscriptionData: SubscriptionData | null;
  updateSubscription: (planId: string) => Promise<void>;
}
```

### User Profile Structure
```typescript
interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  institution?: string;
  role?: 'student' | 'researcher' | 'professional' | 'educator';
  subscription_tier?: 'free' | 'pro' | 'academic' | 'enterprise';
  preferences?: UserPreferences;
  created_at: string;
  updated_at: string;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: boolean;
  autoSave: boolean;
  defaultChartType: string;
  significanceLevel: number;
}
```

## 📊 Data Context API

### DataContext Interface
```typescript
interface DataContextType {
  // Dataset Management
  datasets: Dataset[];
  activeDatasetId: string | null;
  activeDataset: Dataset | null;
  
  // Dataset Operations
  addDataset: (dataset: Omit<Dataset, 'id'>) => string;
  updateDataset: (id: string, updates: Partial<Dataset>) => void;
  removeDataset: (id: string) => void;
  setActiveDataset: (id: string | null) => void;
  renameDataset: (id: string, newName: string) => void;
  duplicateDataset: (id: string, newName?: string) => string;
  
  // Column Operations
  addColumn: (datasetId: string, column: Omit<Column, 'id'>) => void;
  updateColumn: (datasetId: string, columnId: string, updates: Partial<Column>) => void;
  removeColumn: (datasetId: string, columnId: string) => void;
  reorderColumns: (datasetId: string, columnIds: string[]) => void;
  
  // Row Operations
  addRow: (datasetId: string, row: DataRow) => void;
  updateRow: (datasetId: string, rowIndex: number, updates: Partial<DataRow>) => void;
  removeRow: (datasetId: string, rowIndex: number) => void;
  addRows: (datasetId: string, rows: DataRow[]) => void;
  
  // Data Selection
  selectedRows: number[];
  selectedColumns: string[];
  setSelectedRows: (rows: number[]) => void;
  setSelectedColumns: (columns: string[]) => void;
  clearSelection: () => void;
  
  // Data Import/Export
  importData: (file: File, options?: ImportOptions) => Promise<string>;
  exportData: (datasetId: string, format: ExportFormat, options?: ExportOptions) => Promise<Blob>;
  
  // Cloud Operations (for authenticated users)
  saveDataset: (datasetId: string) => Promise<void>;
  loadUserDatasets: () => Promise<void>;
  deleteUserDataset: (datasetId: string) => Promise<void>;
  
  // Utility Functions
  getColumnData: (datasetId: string, columnId: string) => DataValue[];
  getRowData: (datasetId: string, rowIndex: number) => DataRow;
  calculateColumnStatistics: (datasetId: string, columnId: string) => ColumnStatistics;
  detectColumnType: (data: DataValue[]) => DataType;
  
  // Data Filtering
  filters: DataFilter[];
  addFilter: (filter: DataFilter) => void;
  removeFilter: (filterId: string) => void;
  clearFilters: () => void;
  getFilteredData: (datasetId: string) => DataRow[];
}
```

### Data Import/Export Types
```typescript
interface ImportOptions {
  delimiter?: string;
  hasHeader?: boolean;
  encoding?: string;
  skipRows?: number;
  columnTypes?: { [columnName: string]: DataType };
}

interface ExportOptions {
  includeHeader?: boolean;
  delimiter?: string;
  selectedColumns?: string[];
  selectedRows?: number[];
  format?: 'csv' | 'excel' | 'json';
}

enum ExportFormat {
  CSV = 'csv',
  EXCEL = 'excel',
  JSON = 'json',
  SPSS = 'spss',
  R = 'r'
}
```

## 🎨 Theme Context API

### ThemeContext Interface
```typescript
interface ThemeContextType {
  themeMode: 'light' | 'dark' | 'system';
  appliedTheme: 'light' | 'dark';
  setThemeMode: (mode: 'light' | 'dark' | 'system') => void;
  theme: Theme; // Material-UI Theme object
}
```

## 📈 Results Context API

### ResultsContext Interface
```typescript
interface ResultsContextType {
  // Results Management
  results: AnalysisResult[];
  activeResultId: string | null;
  
  // Result Operations
  addResult: (result: Omit<AnalysisResult, 'id' | 'timestamp'>) => string;
  updateResult: (id: string, updates: Partial<AnalysisResult>) => void;
  removeResult: (id: string) => void;
  clearResults: () => void;
  setActiveResult: (id: string | null) => void;
  
  // Result Export
  exportResult: (id: string, format: 'pdf' | 'docx' | 'html') => Promise<Blob>;
  exportAllResults: (format: 'pdf' | 'docx' | 'html') => Promise<Blob>;
  
  // Result Comparison
  compareResults: (resultIds: string[]) => ComparisonResult;
}

interface AnalysisResult {
  id: string;
  type: AnalysisType;
  title: string;
  description?: string;
  datasetId: string;
  parameters: any;
  results: any;
  visualizations?: ChartConfig[];
  timestamp: string;
  userId?: string;
}
```

## 📊 Statistical Analysis APIs

### Descriptive Statistics
```typescript
// Located in utils/stats/descriptive.ts
export interface DescriptiveStats {
  count: number;
  mean: number;
  median: number;
  mode: number[];
  std: number;
  variance: number;
  min: number;
  max: number;
  range: number;
  q1: number;
  q3: number;
  iqr: number;
  skewness: number;
  kurtosis: number;
  standardError: number;
}

export function calculateDescriptiveStats(data: number[]): DescriptiveStats;
export function calculateFrequencyTable(data: DataValue[]): FrequencyTable;
export function calculateCrossTabulation(data1: DataValue[], data2: DataValue[]): CrossTabulation;
```

### Inferential Statistics
```typescript
// Located in utils/stats/inference.ts
export function oneSampleTTest(data: number[], mu: number, alpha?: number): TTestResult;
export function independentTTest(group1: number[], group2: number[], alpha?: number): TTestResult;
export function pairedTTest(before: number[], after: number[], alpha?: number): TTestResult;
export function oneWayANOVA(groups: number[][], alpha?: number): ANOVAResult;
export function chiSquareTest(observed: number[][], alpha?: number): ChiSquareResult;

interface TTestResult extends StatisticalTestResult {
  meanDifference: number;
  standardError: number;
  tStatistic: number;
  cohensD: number;
}

interface ANOVAResult extends StatisticalTestResult {
  fStatistic: number;
  betweenGroupsVariance: number;
  withinGroupsVariance: number;
  etaSquared: number;
  postHocTests?: PostHocResult[];
}
```

### Correlation Analysis
```typescript
// Located in utils/stats/correlation.ts
export function pearsonCorrelation(x: number[], y: number[]): CorrelationResult;
export function spearmanCorrelation(x: number[], y: number[]): CorrelationResult;
export function kendallCorrelation(x: number[], y: number[]): CorrelationResult;
export function correlationMatrix(data: number[][], method?: 'pearson' | 'spearman' | 'kendall'): CorrelationMatrix;

interface CorrelationResult {
  coefficient: number;
  pValue: number;
  confidenceInterval: [number, number];
  sampleSize: number;
  interpretation: string;
}

interface CorrelationMatrix {
  matrix: number[][];
  pValues: number[][];
  confidenceIntervals: [number, number][][];
  variableNames: string[];
}
```

### Regression Analysis
```typescript
// Located in utils/stats/regression.ts
export function simpleLinearRegression(x: number[], y: number[]): LinearRegressionResult;
export function multipleLinearRegression(X: number[][], y: number[], variableNames: string[]): MultipleRegressionResult;
export function logisticRegression(X: number[][], y: number[], variableNames: string[]): LogisticRegressionResult;

interface LinearRegressionResult {
  slope: number;
  intercept: number;
  rSquared: number;
  adjustedRSquared: number;
  standardError: number;
  fStatistic: number;
  pValue: number;
  residuals: number[];
  predicted: number[];
  coefficientTests: CoefficientTest[];
  diagnostics: RegressionDiagnostics;
}
```

## 📊 Visualization APIs

### Chart Configuration
```typescript
interface ChartConfig {
  id: string;
  type: ChartType;
  title: string;
  data: any[];
  xAxis: AxisConfig;
  yAxis: AxisConfig;
  series: SeriesConfig[];
  styling: ChartStyling;
  interactive: boolean;
  exportable: boolean;
}

enum ChartType {
  BAR = 'bar',
  LINE = 'line',
  SCATTER = 'scatter',
  HISTOGRAM = 'histogram',
  BOX_PLOT = 'boxPlot',
  PIE = 'pie',
  HEATMAP = 'heatmap',
  VIOLIN = 'violin'
}

interface AxisConfig {
  label: string;
  type: 'numeric' | 'categorical' | 'datetime';
  scale?: 'linear' | 'log' | 'sqrt';
  domain?: [number, number];
  tickFormat?: string;
}
```

## 🧮 Calculator APIs

### Sample Size Calculators
```typescript
// Located in utils/stats/sampleSize.ts
export function sampleSizeOneProportion(p: number, margin: number, confidence: number): SampleSizeResult;
export function sampleSizeTwoProportions(p1: number, p2: number, power: number, alpha: number): SampleSizeResult;
export function sampleSizeOneMean(sigma: number, margin: number, confidence: number): SampleSizeResult;
export function sampleSizeTwoMeans(sigma: number, delta: number, power: number, alpha: number): SampleSizeResult;

interface SampleSizeResult {
  sampleSize: number;
  power?: number;
  effectSize?: number;
  assumptions: string[];
  interpretation: string;
}
```

### Epidemiological Calculations
```typescript
// Located in components/EpiCalc/calculations/
export function calculateOddsRatio(a: number, b: number, c: number, d: number): EpiResult;
export function calculateRiskRatio(a: number, b: number, c: number, d: number): EpiResult;
export function calculateAttributableRisk(a: number, b: number, c: number, d: number): EpiResult;

interface EpiResult {
  estimate: number;
  confidenceInterval: [number, number];
  pValue?: number;
  interpretation: string;
  studyType: 'cross-sectional' | 'case-control' | 'cohort';
}
```

## 🛠️ Utility Functions

### Data Processing
```typescript
// Located in utils/dataAnalysis.ts
export function detectOutliers(data: number[], method: 'iqr' | 'zscore' | 'modified-zscore'): number[];
export function imputeMissingValues(data: DataValue[], method: 'mean' | 'median' | 'mode' | 'forward-fill'): DataValue[];
export function normalizeData(data: number[], method: 'z-score' | 'min-max' | 'robust'): number[];
export function binData(data: number[], bins: number | number[]): BinResult[];

interface BinResult {
  binStart: number;
  binEnd: number;
  count: number;
  frequency: number;
  values: number[];
}
```

### Type Conversion
```typescript
// Located in utils/typeConversion.ts
export function convertToNumeric(value: DataValue): number | null;
export function convertToCategorical(value: DataValue): string;
export function inferDataType(values: DataValue[]): DataType;
export function validateDataType(values: DataValue[], expectedType: DataType): boolean;
```

## 🔧 Service Layer APIs

### Dataset Service
```typescript
// Located in utils/services/datasetService.ts
export class DatasetService {
  static async saveDataset(dataset: Dataset): Promise<void>;
  static async loadDataset(id: string): Promise<Dataset>;
  static async deleteDataset(id: string): Promise<void>;
  static async getUserDatasets(userId: string): Promise<Dataset[]>;
  static async shareDataset(id: string, permissions: SharePermissions): Promise<string>;
}
```

### Project Management Service
```typescript
// Located in utils/services/projectService.ts
export class ProjectService {
  static async createProject(project: Omit<Project, 'id'>): Promise<string>;
  static async updateProject(id: string, updates: Partial<Project>): Promise<void>;
  static async deleteProject(id: string): Promise<void>;
  static async getProjectResults(id: string): Promise<AnalysisResult[]>;
}
```

## 🚨 Error Handling

### Error Types
```typescript
interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  userId?: string;
}

enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CALCULATION_ERROR = 'CALCULATION_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
```

## 📱 PWA APIs

### Service Worker Integration
```typescript
// Located in utils/pwa/
export function registerServiceWorker(): Promise<ServiceWorkerRegistration>;
export function checkForUpdates(): Promise<boolean>;
export function installUpdate(): Promise<void>;
export function getOfflineCapabilities(): OfflineCapabilities;

interface OfflineCapabilities {
  canAnalyzeData: boolean;
  canCreateCharts: boolean;
  canExportResults: boolean;
  availableFeatures: string[];
}
```

This API reference provides comprehensive coverage of the DataStatPro application's internal APIs and should be used as a guide for development and AI assistance.