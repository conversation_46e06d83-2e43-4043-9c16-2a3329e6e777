<?php
// Enhanced Social Meta Handler for DataStatPro
// This file serves proper meta tags to social media crawlers

// Get parameters
$path = $_GET['path'] ?? '';
$tutorial = $_GET['tutorial'] ?? '';

// Comprehensive meta data mapping
$metaData = [
    // Sample Size Calculators
    'sample-size/one-sample' => [
        'title' => 'One Sample Size Calculator - DataStatPro',
        'description' => 'Calculate required sample size for one-sample statistical tests with confidence intervals and power analysis. Free statistical tool for researchers.',
        'image' => 'https://datastatpro.com/images/calculators/one-sample-calculator.png',
        'type' => 'website',
        'keywords' => 'sample size calculator, one sample, statistical power, confidence interval'
    ],
    'sample-size/paired-sample' => [
        'title' => 'Paired Sample Size Calculator - DataStatPro',
        'description' => 'Determine sample size for paired t-tests and related samples analysis with statistical power calculations. Professional statistical tool.',
        'image' => 'https://datastatpro.com/images/calculators/paired-sample-calculator.png',
        'type' => 'website',
        'keywords' => 'paired sample, sample size, t-test, statistical analysis'
    ],
    'sample-size/two-sample' => [
        'title' => 'Two Sample Size Calculator - DataStatPro',
        'description' => 'Calculate sample size for independent two-sample tests with effect size and power analysis. Essential tool for experimental design.',
        'image' => 'https://datastatpro.com/images/calculators/two-sample-calculator.png',
        'type' => 'website',
        'keywords' => 'two sample, independent samples, effect size, power analysis'
    ],
    
    // Correlation Analysis
    'correlation/pearson' => [
        'title' => 'Pearson Correlation Calculator - DataStatPro',
        'description' => 'Calculate Pearson correlation coefficient with significance testing and confidence intervals. Professional correlation analysis tool.',
        'image' => 'https://datastatpro.com/images/calculators/pearson-correlation.png',
        'type' => 'website',
        'keywords' => 'Pearson correlation, correlation coefficient, statistical significance'
    ],
    'correlation/spearman' => [
        'title' => 'Spearman Correlation Calculator - DataStatPro',
        'description' => 'Calculate Spearman rank correlation with non-parametric analysis. Ideal for ordinal data and non-linear relationships.',
        'image' => 'https://datastatpro.com/images/calculators/spearman-correlation.png',
        'type' => 'website',
        'keywords' => 'Spearman correlation, rank correlation, non-parametric, ordinal data'
    ],
    
    // T-Test Calculators
    't-test/one-sample' => [
        'title' => 'One Sample T-Test Calculator - DataStatPro',
        'description' => 'Perform one-sample t-test with detailed statistical output including p-values, confidence intervals, and effect size.',
        'image' => 'https://datastatpro.com/images/calculators/one-sample-ttest.png',
        'type' => 'website',
        'keywords' => 'one sample t-test, hypothesis testing, p-value, confidence interval'
    ],
    't-test/paired' => [
        'title' => 'Paired T-Test Calculator - DataStatPro',
        'description' => 'Analyze paired samples with dependent t-test calculations. Perfect for before-after studies and matched pairs analysis.',
        'image' => 'https://datastatpro.com/images/calculators/paired-ttest.png',
        'type' => 'website',
        'keywords' => 'paired t-test, dependent samples, before after, matched pairs'
    ],
    't-test/independent' => [
        'title' => 'Independent T-Test Calculator - DataStatPro',
        'description' => 'Compare two independent groups with Welch\'s t-test and equal variance t-test options. Comprehensive statistical analysis.',
        'image' => 'https://datastatpro.com/images/calculators/independent-ttest.png',
        'type' => 'website',
        'keywords' => 'independent t-test, two sample, Welch test, equal variance'
    ],
    
    // ANOVA Calculators
    'anova/one-way' => [
        'title' => 'One-Way ANOVA Calculator - DataStatPro',
        'description' => 'Perform one-way analysis of variance with post-hoc tests and effect size calculations. Professional ANOVA tool.',
        'image' => 'https://datastatpro.com/images/calculators/one-way-anova.png',
        'type' => 'website',
        'keywords' => 'one-way ANOVA, analysis of variance, post-hoc tests, effect size'
    ],
    
    // Chi-Square Tests
    'chi-square/goodness-of-fit' => [
        'title' => 'Chi-Square Goodness of Fit Test - DataStatPro',
        'description' => 'Test if sample data fits expected distribution with chi-square goodness of fit analysis and detailed results.',
        'image' => 'https://datastatpro.com/images/calculators/chi-square-goodness.png',
        'type' => 'website',
        'keywords' => 'chi-square, goodness of fit, distribution testing, categorical data'
    ],
    'chi-square/independence' => [
        'title' => 'Chi-Square Test of Independence - DataStatPro',
        'description' => 'Analyze relationship between categorical variables with chi-square test of independence and contingency table analysis.',
        'image' => 'https://datastatpro.com/images/calculators/chi-square-independence.png',
        'type' => 'website',
        'keywords' => 'chi-square independence, contingency table, categorical variables'
    ],
    
    // Knowledge Base Tutorials
    'knowledge-base/correlation-analysis' => [
        'title' => 'Correlation Analysis Tutorial - DataStatPro Knowledge Base',
        'description' => 'Complete guide to correlation analysis including Pearson and Spearman correlations with practical examples and interpretation.',
        'image' => 'https://datastatpro.com/images/tutorials/correlation-analysis-tutorial.png',
        'type' => 'article',
        'keywords' => 'correlation tutorial, statistical analysis, data science education'
    ],
    'knowledge-base/t-test-guide' => [
        'title' => 'T-Test Complete Guide - DataStatPro Knowledge Base',
        'description' => 'Learn when and how to use one-sample, paired, and independent t-tests with step-by-step examples and assumptions.',
        'image' => 'https://datastatpro.com/images/tutorials/t-test-guide.png',
        'type' => 'article',
        'keywords' => 't-test tutorial, hypothesis testing, statistical methods'
    ],
    'knowledge-base/anova-explained' => [
        'title' => 'ANOVA Analysis Explained - DataStatPro Knowledge Base',
        'description' => 'Comprehensive guide to Analysis of Variance (ANOVA) including one-way, two-way ANOVA and post-hoc testing procedures.',
        'image' => 'https://datastatpro.com/images/tutorials/anova-explained.png',
        'type' => 'article',
        'keywords' => 'ANOVA tutorial, analysis of variance, statistical education'
    ],
    'knowledge-base/sample-size-determination' => [
        'title' => 'Sample Size Determination Guide - DataStatPro Knowledge Base',
        'description' => 'Learn how to calculate appropriate sample sizes for different study designs with power analysis and effect size considerations.',
        'image' => 'https://datastatpro.com/images/tutorials/sample-size-guide.png',
        'type' => 'article',
        'keywords' => 'sample size tutorial, power analysis, study design'
    ],
    'knowledge-base/chi-square-tests' => [
        'title' => 'Chi-Square Tests Tutorial - DataStatPro Knowledge Base',
        'description' => 'Master chi-square tests for categorical data analysis including goodness of fit and independence testing with examples.',
        'image' => 'https://datastatpro.com/images/tutorials/chi-square-tutorial.png',
        'type' => 'article',
        'keywords' => 'chi-square tutorial, categorical data, statistical testing'
    ],
    
    // Main Pages
    '' => [
        'title' => 'DataStatPro - Professional Statistical Analysis Tools',
        'description' => 'Free online statistical calculators and analysis tools for researchers, students, and data analysts. Sample size, t-tests, ANOVA, correlation analysis and more.',
        'image' => 'https://datastatpro.com/images/datastatpro-homepage.png',
        'type' => 'website',
        'keywords' => 'statistical analysis, online calculators, data science tools'
    ],
    'knowledge-base' => [
        'title' => 'Knowledge Base - DataStatPro Statistical Learning Center',
        'description' => 'Comprehensive statistical tutorials and guides covering correlation analysis, t-tests, ANOVA, sample size determination, and chi-square tests. Learn statistics with practical examples.',
        'image' => 'https://datastatpro.com/images/knowledge-base-main.png',
        'type' => 'website',
        'keywords' => 'statistical tutorials, data analysis guides, statistics education, research methods'
    ],
    'dashboard' => [
        'title' => 'Dashboard - DataStatPro',
        'description' => 'Access your statistical analysis history, saved calculations, and personalized tools dashboard.',
        'image' => 'https://datastatpro.com/images/dashboard.png',
        'type' => 'website',
        'keywords' => 'statistical dashboard, analysis history, data tools'
    ]
];

// Determine current meta data
$currentMeta = null;

// Handle tutorial-specific requests
if ($tutorial) {
    $tutorialPath = "knowledge-base/$tutorial";
    if (isset($metaData[$tutorialPath])) {
        $currentMeta = $metaData[$tutorialPath];
    }
}

// Handle general path requests
if (!$currentMeta && $path && isset($metaData[$path])) {
    $currentMeta = $metaData[$path];
}

// Default fallback
if (!$currentMeta) {
    $currentMeta = [
        'title' => 'DataStatPro - Statistical Analysis Tools',
        'description' => 'Professional statistical analysis tools and calculators for researchers, students, and data analysts. Free online statistical computing.',
        'image' => 'https://datastatpro.com/images/datastatpro-default-share.png',
        'type' => 'website',
        'keywords' => 'statistical analysis, online calculators, data science'
    ];
}

// Generate the canonical URL (with /app prefix for actual navigation)
$canonicalUrl = "https://datastatpro.com/app/" . ($path ?: ($tutorial ? "knowledge-base/$tutorial" : ''));

// Generate clean URL for social sharing (without /app prefix)
$socialUrl = "https://datastatpro.com/" . ($path ?: ($tutorial ? "knowledge-base/$tutorial" : ''));

// Set proper headers
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Primary Meta Tags -->
    <title><?php echo htmlspecialchars($currentMeta['title']); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($currentMeta['description']); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($currentMeta['keywords'] ?? ''); ?>">
    <meta name="author" content="DataStatPro">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="<?php echo htmlspecialchars($currentMeta['type']); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($socialUrl); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($currentMeta['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($currentMeta['description']); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($currentMeta['image']); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="DataStatPro">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@DataStatPro">
    <meta name="twitter:creator" content="@DataStatPro">
    <meta name="twitter:url" content="<?php echo htmlspecialchars($socialUrl); ?>">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($currentMeta['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($currentMeta['description']); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($currentMeta['image']); ?>">
    
    <!-- LinkedIn -->
    <meta property="linkedin:owner" content="DataStatPro">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo htmlspecialchars($canonicalUrl); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "<?php echo $currentMeta['type'] === 'article' ? 'Article' : 'WebPage'; ?>",
        "name": "<?php echo htmlspecialchars($currentMeta['title']); ?>",
        "description": "<?php echo htmlspecialchars($currentMeta['description']); ?>",
        "url": "<?php echo htmlspecialchars($socialUrl); ?>",
        "image": "<?php echo htmlspecialchars($currentMeta['image']); ?>",
        "publisher": {
            "@type": "Organization",
            "name": "DataStatPro",
            "url": "https://datastatpro.com"
        }
    }
    </script>
    
    <!-- Redirect script for human users -->
    <script>
        // Only redirect if not a crawler/bot
        if (!/bot|crawler|spider|crawling|facebookexternalhit|twitterbot|linkedinbot|whatsapp|slackbot|telegrambot/i.test(navigator.userAgent)) {
            // Small delay to ensure meta tags are processed
            setTimeout(function() {
                window.location.href = '<?php echo htmlspecialchars($canonicalUrl); ?>';
            }, 100);
        }
    </script>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo htmlspecialchars($currentMeta['image']); ?>" as="image">
</head>
<body>
    <!-- Content for crawlers and fallback -->
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <h1><?php echo htmlspecialchars($currentMeta['title']); ?></h1>
        <p><?php echo htmlspecialchars($currentMeta['description']); ?></p>
        
        <div style="margin: 20px 0;">
            <img src="<?php echo htmlspecialchars($currentMeta['image']); ?>" 
                 alt="<?php echo htmlspecialchars($currentMeta['title']); ?>" 
                 style="max-width: 100%; height: auto; border-radius: 8px;">
        </div>
        
        <p>
            <a href="<?php echo htmlspecialchars($canonicalUrl); ?>" 
               style="background: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Continue to DataStatPro →
            </a>
        </p>
        
        <hr style="margin: 40px 0; border: none; border-top: 1px solid #eee;">
        
        <p style="color: #666; font-size: 14px;">
            DataStatPro provides professional statistical analysis tools and calculators for researchers, students, and data analysts.
            <br><br>
            <strong>Features:</strong> Sample size calculators, t-tests, ANOVA, correlation analysis, chi-square tests, and comprehensive statistical tutorials.
        </p>
    </div>
    
    <!-- Analytics (optional) -->
    <script>
        // Track social media crawler visits
        if (/bot|crawler|spider|crawling|facebookexternalhit|twitterbot|linkedinbot|whatsapp|slackbot|telegrambot/i.test(navigator.userAgent)) {
            console.log('Social media crawler detected:', navigator.userAgent);
            // You can add analytics tracking here
        }
    </script>
</body>
</html>