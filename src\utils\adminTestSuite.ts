/**
 * Comprehensive test suite for admin dashboard functionality
 * Tests all critical components and database functions to ensure fixes work
 */

import { supabase } from './supabaseClient';
import { getEnhancedUserStatistics } from './adminStats';
import { adminLogger } from './adminLogger';

export interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  data?: any;
  duration: number;
}

export interface TestSuiteResults {
  overall: {
    passed: number;
    failed: number;
    total: number;
    success: boolean;
  };
  tests: TestResult[];
  summary: string;
}

/**
 * Run comprehensive admin dashboard test suite
 */
export const runAdminTestSuite = async (): Promise<TestSuiteResults> => {
  adminLogger.info('TEST_SUITE', 'Starting comprehensive admin dashboard test suite');
  const startTime = Date.now();
  const results: TestResult[] = [];

  // Test 1: Admin Connection Test
  results.push(await runTest('Admin Connection Test', async () => {
    const { data, error } = await supabase.rpc('test_admin_connection');
    if (error) throw error;
    if (!data?.success) throw new Error(data?.error || 'Connection test failed');
    return data;
  }));

  // Test 2: Enhanced User Statistics Function
  results.push(await runTest('Enhanced User Statistics Function', async () => {
    const { data, error } = await supabase.rpc('get_enhanced_user_statistics');
    if (error) throw error;
    if (!data || typeof data.total_users !== 'number') {
      throw new Error('Invalid statistics data structure');
    }
    return data;
  }));

  // Test 3: Original User Statistics Function (Fixed)
  results.push(await runTest('Original User Statistics Function', async () => {
    const { data, error } = await supabase.rpc('get_user_statistics');
    if (error) throw error;
    if (!data || typeof data.total_users !== 'number') {
      throw new Error('Invalid statistics data structure');
    }
    return data;
  }));

  // Test 4: Get All Users Function (Type-Fixed)
  results.push(await runTest('Get All Users Function', async () => {
    const { data, error } = await supabase.rpc('get_all_users', {
      page_size: 5,
      page_offset: 0,
      search_term: null
    });
    if (error) throw error;
    if (!Array.isArray(data)) {
      throw new Error('Expected array of users');
    }
    
    // Validate structure of first user if any exist
    if (data.length > 0) {
      const user = data[0];
      const requiredFields = ['id', 'email', 'username', 'accounttype', 'is_admin'];
      for (const field of requiredFields) {
        if (!(field in user)) {
          throw new Error(`Missing required field: ${field}`);
        }
      }
    }
    
    return { count: data.length, sample: data[0] || null };
  }));

  // Test 5: Materialized View Cache Refresh
  results.push(await runTest('Cache Refresh Function', async () => {
    const { data, error } = await supabase.rpc('refresh_user_stats_cache');
    if (error) throw error;
    return { refreshed: data };
  }));

  // Test 6: Materialized View Access
  results.push(await runTest('Materialized View Access', async () => {
    const { data, error } = await supabase
      .from('user_stats_cache')
      .select('*')
      .limit(1);
    
    if (error) throw error;
    if (!data || data.length === 0) {
      throw new Error('Materialized view is empty');
    }
    
    const cache = data[0];
    if (typeof cache.total_users !== 'number') {
      throw new Error('Invalid cache data structure');
    }
    
    return cache;
  }));

  // Test 7: Enhanced Statistics Integration
  results.push(await runTest('Enhanced Statistics Integration', async () => {
    const stats = await getEnhancedUserStatistics();
    if (!stats || typeof stats.total_users !== 'number') {
      throw new Error('Enhanced statistics failed');
    }
    
    // Validate all required fields
    const requiredFields = [
      'total_users', 'total_profiles', 'admin_users', 'standard_users',
      'pro_users', 'edu_users', 'edu_pro_users', 'users_with_datasets',
      'total_datasets', 'users_last_7_days', 'users_last_30_days',
      'active_users_last_7_days', 'active_users_last_30_days'
    ];
    
    for (const field of requiredFields) {
      if (typeof stats[field as keyof typeof stats] !== 'number') {
        throw new Error(`Invalid or missing field: ${field}`);
      }
    }
    
    return stats;
  }));

  // Test 8: Subscription Override Functions
  results.push(await runTest('Subscription Override Functions', async () => {
    const tests = [];
    
    // Test get_all_active_overrides
    const { data: activeOverrides, error: activeError } = await supabase.rpc('get_all_active_overrides', {
      page_size: 10,
      page_offset: 0
    });
    if (activeError) throw new Error(`get_all_active_overrides: ${activeError.message}`);
    tests.push({ function: 'get_all_active_overrides', count: activeOverrides?.length || 0 });

    // Test get_override_audit_trail
    const { data: auditTrail, error: auditError } = await supabase.rpc('get_override_audit_trail', {
      target_user_id: null,
      page_size: 10,
      page_offset: 0
    });
    if (auditError) throw new Error(`get_override_audit_trail: ${auditError.message}`);
    tests.push({ function: 'get_override_audit_trail', count: auditTrail?.length || 0 });

    // Test get_expiring_overrides
    const { data: expiringOverrides, error: expiringError } = await supabase.rpc('get_expiring_overrides', {
      days_ahead: 7
    });
    if (expiringError) throw new Error(`get_expiring_overrides: ${expiringError.message}`);
    tests.push({ function: 'get_expiring_overrides', count: expiringOverrides?.length || 0 });

    return tests;
  }));

  // Test 9: Error Handling and Fallbacks
  results.push(await runTest('Error Handling and Fallbacks', async () => {
    // Test with invalid parameters to ensure graceful handling
    try {
      const { data, error } = await supabase.rpc('get_all_users', {
        page_size: -1, // Invalid parameter
        page_offset: 0,
        search_term: null
      });
      
      // Should either handle gracefully or return a proper error
      if (error && error.code) {
        return { errorHandling: 'proper', errorCode: error.code };
      } else if (data) {
        return { errorHandling: 'graceful', result: 'handled_invalid_params' };
      }
    } catch (err: any) {
      return { errorHandling: 'exception', message: err.message };
    }
    
    return { errorHandling: 'unknown' };
  }));

  // Test 10: Performance Benchmarks
  results.push(await runTest('Performance Benchmarks', async () => {
    const benchmarks = [];
    
    // Benchmark enhanced statistics
    const statsStart = Date.now();
    await getEnhancedUserStatistics();
    const statsTime = Date.now() - statsStart;
    benchmarks.push({ operation: 'enhanced_statistics', duration: statsTime });
    
    // Benchmark user query
    const usersStart = Date.now();
    await supabase.rpc('get_all_users', { page_size: 10, page_offset: 0, search_term: null });
    const usersTime = Date.now() - usersStart;
    benchmarks.push({ operation: 'get_users', duration: usersTime });
    
    // Check for performance issues
    const slowOperations = benchmarks.filter(b => b.duration > 5000);
    if (slowOperations.length > 0) {
      throw new Error(`Slow operations detected: ${slowOperations.map(o => `${o.operation}: ${o.duration}ms`).join(', ')}`);
    }
    
    return benchmarks;
  }));

  // Calculate overall results
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const total = results.length;
  const overallSuccess = failed === 0;

  const summary = `Test Suite Complete: ${passed}/${total} tests passed${failed > 0 ? `, ${failed} failed` : ''}`;
  
  const testSuiteResults: TestSuiteResults = {
    overall: {
      passed,
      failed,
      total,
      success: overallSuccess
    },
    tests: results,
    summary
  };

  const totalDuration = Date.now() - startTime;
  adminLogger.performance('admin_test_suite', totalDuration, testSuiteResults);
  
  if (overallSuccess) {
    adminLogger.info('TEST_SUITE', `✅ ${summary}`, testSuiteResults);
  } else {
    adminLogger.error('TEST_SUITE', `❌ ${summary}`, undefined, testSuiteResults);
  }

  return testSuiteResults;
};

/**
 * Run individual test with error handling and timing
 */
async function runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    adminLogger.debug('TEST', `Starting test: ${name}`);
    const data = await testFn();
    const duration = Date.now() - startTime;
    
    adminLogger.debug('TEST', `✅ Test passed: ${name}`, { duration, data });
    return {
      name,
      success: true,
      data,
      duration
    };
  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    adminLogger.error('TEST', `❌ Test failed: ${name}`, error, { duration });
    return {
      name,
      success: false,
      error: error.message || 'Unknown error',
      duration
    };
  }
}

/**
 * Quick health check for critical functions
 */
export const runQuickHealthCheck = async (): Promise<boolean> => {
  adminLogger.info('HEALTH_CHECK', 'Running quick health check');
  
  try {
    // Test basic admin connection
    const { data: connectionTest, error: connectionError } = await supabase.rpc('test_admin_connection');
    if (connectionError || !connectionTest?.success) {
      throw new Error('Admin connection failed');
    }

    // Test enhanced statistics
    const stats = await getEnhancedUserStatistics();
    if (!stats || typeof stats.total_users !== 'number') {
      throw new Error('Enhanced statistics failed');
    }

    adminLogger.info('HEALTH_CHECK', '✅ Quick health check passed');
    return true;
  } catch (error: any) {
    adminLogger.error('HEALTH_CHECK', '❌ Quick health check failed', error);
    return false;
  }
};