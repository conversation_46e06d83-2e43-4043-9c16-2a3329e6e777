/**
 * Comprehensive logging utility for admin dashboard debugging
 * Provides structured logging with different levels and context tracking
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  error?: Error;
  context?: Record<string, any>;
}

class AdminLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private currentLevel = LogLevel.DEBUG;

  constructor() {
    // Enable debug logging in development
    if (process.env.NODE_ENV === 'development') {
      this.currentLevel = LogLevel.DEBUG;
    } else {
      this.currentLevel = LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLevel;
  }

  private createLogEntry(
    level: LogLevel,
    category: string,
    message: string,
    data?: any,
    error?: Error,
    context?: Record<string, any>
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      error,
      context
    };
  }

  private addLog(entry: LogEntry): void {
    this.logs.push(entry);
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with appropriate styling
    const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
    const levelColors = ['#888', '#007bff', '#ffc107', '#dc3545'];
    const levelName = levelNames[entry.level];
    const levelColor = levelColors[entry.level];

    const consoleMethod = entry.level >= LogLevel.ERROR ? 'error' :
                         entry.level >= LogLevel.WARN ? 'warn' :
                         entry.level >= LogLevel.INFO ? 'info' : 'log';

    console[consoleMethod](
      `%c[${levelName}] %c[${entry.category}] %c${entry.message}`,
      `color: ${levelColor}; font-weight: bold`,
      'color: #6c757d; font-weight: bold',
      'color: inherit',
      entry.data ? entry.data : '',
      entry.error ? entry.error : ''
    );
  }

  debug(category: string, message: string, data?: any, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;
    this.addLog(this.createLogEntry(LogLevel.DEBUG, category, message, data, undefined, context));
  }

  info(category: string, message: string, data?: any, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;
    this.addLog(this.createLogEntry(LogLevel.INFO, category, message, data, undefined, context));
  }

  warn(category: string, message: string, data?: any, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;
    this.addLog(this.createLogEntry(LogLevel.WARN, category, message, data, undefined, context));
  }

  error(category: string, message: string, error?: Error, data?: any, context?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;
    this.addLog(this.createLogEntry(LogLevel.ERROR, category, message, data, error, context));
  }

  // Specialized logging methods for admin operations
  adminOperation(operation: string, status: 'start' | 'success' | 'error', data?: any, error?: Error): void {
    const category = 'ADMIN_OPERATION';
    const context = { operation, status };

    switch (status) {
      case 'start':
        this.info(category, `Starting ${operation}`, data, context);
        break;
      case 'success':
        this.info(category, `✅ ${operation} completed successfully`, data, context);
        break;
      case 'error':
        this.error(category, `❌ ${operation} failed`, error, data, context);
        break;
    }
  }

  databaseQuery(query: string, status: 'start' | 'success' | 'error', data?: any, error?: Error): void {
    const category = 'DATABASE';
    const context = { query, status };

    switch (status) {
      case 'start':
        this.debug(category, `🔄 Executing: ${query}`, data, context);
        break;
      case 'success':
        this.debug(category, `✅ Query successful: ${query}`, data, context);
        break;
      case 'error':
        this.error(category, `❌ Query failed: ${query}`, error, data, context);
        break;
    }
  }

  retryAttempt(operation: string, attempt: number, maxAttempts: number, error?: Error): void {
    const category = 'RETRY';
    const context = { operation, attempt, maxAttempts };
    
    if (attempt < maxAttempts) {
      this.warn(category, `🔄 Retry ${attempt}/${maxAttempts} for ${operation}`, { error: error?.message }, context);
    } else {
      this.error(category, `❌ All retry attempts failed for ${operation}`, error, undefined, context);
    }
  }

  performance(operation: string, duration: number, data?: any): void {
    const category = 'PERFORMANCE';
    const context = { operation, duration };
    
    if (duration > 5000) {
      this.warn(category, `⚠️ Slow operation: ${operation} took ${duration}ms`, data, context);
    } else {
      this.debug(category, `⏱️ ${operation} completed in ${duration}ms`, data, context);
    }
  }

  // Get logs for debugging
  getLogs(level?: LogLevel, category?: string, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level >= level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs;
  }

  // Export logs for support
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  // Clear logs
  clearLogs(): void {
    this.logs = [];
    this.info('LOGGER', 'Logs cleared');
  }

  // Get summary statistics
  getLogSummary(): Record<string, number> {
    const summary: Record<string, number> = {
      total: this.logs.length,
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };

    this.logs.forEach(log => {
      switch (log.level) {
        case LogLevel.DEBUG:
          summary.debug++;
          break;
        case LogLevel.INFO:
          summary.info++;
          break;
        case LogLevel.WARN:
          summary.warn++;
          break;
        case LogLevel.ERROR:
          summary.error++;
          break;
      }
    });

    return summary;
  }
}

// Create singleton instance
export const adminLogger = new AdminLogger();

// Convenience functions for common admin operations
export const logAdminStats = {
  fetchStart: (method: string) => adminLogger.adminOperation(`fetch-stats-${method}`, 'start'),
  fetchSuccess: (method: string, data: any) => adminLogger.adminOperation(`fetch-stats-${method}`, 'success', data),
  fetchError: (method: string, error: Error) => adminLogger.adminOperation(`fetch-stats-${method}`, 'error', undefined, error),
  
  cacheRefresh: (success: boolean, error?: Error) => {
    if (success) {
      adminLogger.adminOperation('cache-refresh', 'success');
    } else {
      adminLogger.adminOperation('cache-refresh', 'error', undefined, error);
    }
  },
  
  diagnostics: (results: any) => adminLogger.adminOperation('diagnostics', 'success', results),
  
  connectionTest: (success: boolean, data?: any, error?: Error) => {
    if (success) {
      adminLogger.adminOperation('connection-test', 'success', data);
    } else {
      adminLogger.adminOperation('connection-test', 'error', data, error);
    }
  }
};

export default adminLogger;