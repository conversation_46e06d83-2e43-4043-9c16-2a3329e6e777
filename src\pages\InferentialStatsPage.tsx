import React from 'react';
import { Box, Container } from '@mui/material';
import InferentialStatsOptions from '../components/InferentialStats/InferentialStatsOptions';
import SocialShareWidget from '../components/UI/SocialShareWidget';
import useSocialMeta from '../hooks/useSocialMeta';

interface InferentialStatsPageProps {
  onNavigate: (path: string) => void;
  initialSubPage?: string; // Add initialSubPage prop
}

const InferentialStatsPage: React.FC<InferentialStatsPageProps> = ({ onNavigate, initialSubPage }) => {
  // Initialize social meta for inferential stats page
  useSocialMeta();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <InferentialStatsOptions onNavigate={onNavigate} initialCategory={initialSubPage} /> {/* Pass initialSubPage as initialCategory */}
      </Box>
      <SocialShareWidget
        variant="floating"
        position="bottom-right"
        platforms={['twitter', 'linkedin', 'facebook', 'email']}
        collapsible
      />
    </Container>
  );
};

export default InferentialStatsPage;
