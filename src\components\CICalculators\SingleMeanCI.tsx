import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip,
  useTheme,
  alpha,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  Info as InfoIcon,
  ContentCopy as CopyIcon,
  TrendingUp as MeanIcon,
} from '@mui/icons-material';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMath from 'remark-math';
import remarkRehype from 'remark-rehype';
import rehypeKatex from 'rehype-katex';
import rehypeStringify from 'rehype-stringify';
import 'katex/dist/katex.min.css';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mean-ci-tabpanel-${index}`}
      aria-labelledby={`mean-ci-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface CIResult {
  lowerBound: number;
  upperBound: number;
  margin: number;
  method: string;
  interpretation: string;
  formula: string;
}

const SingleMeanCI: React.FC = () => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState<number>(0);
  const [sampleMean, setSampleMean] = useState<string>('');
  const [sampleSize, setSampleSize] = useState<string>('');
  const [standardDeviation, setStandardDeviation] = useState<string>('');
  const [confidenceLevel, setConfidenceLevel] = useState<number>(95);
  const [populationSD, setPopulationSD] = useState<boolean>(false);
  const [result, setResult] = useState<CIResult | null>(null);
  const [error, setError] = useState<string>('');

  // Render mathematical formulas using KaTeX
  const renderFormula = (formula: string): string => {
    try {
      const result = unified()
        .use(remarkParse)
        .use(remarkMath)
        .use(remarkRehype)
        .use(rehypeKatex)
        .use(rehypeStringify)
        .processSync(`$$${formula}$$`);
      return String(result);
    } catch (error) {
      console.error('Error rendering formula:', error);
      return formula;
    }
  };

  // Critical values for common confidence levels
  const getCriticalValue = useCallback((confidence: number, df?: number): number => {
    const alpha = (100 - confidence) / 100;
    const alphaHalf = alpha / 2;
    
    if (populationSD) {
      // Use z-distribution
      const zValues: { [key: number]: number } = {
        90: 1.645,
        95: 1.96,
        99: 2.576,
        99.9: 3.291
      };
      return zValues[confidence] || 1.96;
    } else {
      // Use t-distribution (simplified approximation)
      if (!df || df < 1) return 1.96;
      
      // Simplified t-values for common confidence levels and degrees of freedom
      const tTable: { [key: number]: { [key: number]: number } } = {
        90: { 1: 6.314, 2: 2.920, 5: 2.015, 10: 1.812, 20: 1.725, 30: 1.697, 50: 1.676, 100: 1.660, 1000: 1.645 },
        95: { 1: 12.706, 2: 4.303, 5: 2.571, 10: 2.228, 20: 2.086, 30: 2.042, 50: 2.009, 100: 1.984, 1000: 1.962 },
        99: { 1: 63.657, 2: 9.925, 5: 4.032, 10: 3.169, 20: 2.845, 30: 2.750, 50: 2.678, 100: 2.626, 1000: 2.581 }
      };
      
      const tValues = tTable[confidence];
      if (!tValues) return 1.96;
      
      // Find closest df
      const dfKeys = Object.keys(tValues).map(Number).sort((a, b) => a - b);
      let closestDf = dfKeys[dfKeys.length - 1]; // Default to largest
      
      for (const key of dfKeys) {
        if (df <= key) {
          closestDf = key;
          break;
        }
      }
      
      return tValues[closestDf] || 1.96;
    }
  }, [populationSD]);

  const calculateCI = useCallback(() => {
    setError('');
    
    // Validate inputs
    const mean = parseFloat(sampleMean);
    const n = parseInt(sampleSize);
    const sd = parseFloat(standardDeviation);
    
    if (isNaN(mean)) {
      setError('Please enter a valid sample mean.');
      return;
    }
    
    if (isNaN(n) || n <= 0) {
      setError('Please enter a valid sample size (positive integer).');
      return;
    }
    
    if (isNaN(sd) || sd <= 0) {
      setError('Please enter a valid standard deviation (positive number).');
      return;
    }
    
    if (confidenceLevel <= 0 || confidenceLevel >= 100) {
      setError('Confidence level must be between 0 and 100.');
      return;
    }
    
    // Calculate confidence interval
    const df = n - 1;
    const criticalValue = getCriticalValue(confidenceLevel, df);
    const standardError = sd / Math.sqrt(n);
    const margin = criticalValue * standardError;
    
    const lowerBound = mean - margin;
    const upperBound = mean + margin;
    
    const method = populationSD ? 'Z-distribution' : `t-distribution (df = ${df})`;
    
    // Determine the appropriate formula
    let formula: string;
    if (populationSD) {
      formula = '\\bar{x} \\pm z_{\\alpha/2} \\cdot \\frac{\\sigma}{\\sqrt{n}}';
    } else {
      formula = '\\bar{x} \\pm t_{\\alpha/2,df} \\cdot \\frac{s}{\\sqrt{n}}';
    }
    
    let interpretation = `We are ${confidenceLevel}% confident that the true population mean is between ${lowerBound.toFixed(2)} and ${upperBound.toFixed(2)}.`;
    
    if (!populationSD && n < 30) {
      interpretation += ' Note: Small sample size - ensure data is approximately normally distributed.';
    }
    
    setResult({
      lowerBound,
      upperBound,
      margin,
      method,
      interpretation,
      formula
    });
    
    // Automatically navigate to results tab after successful calculation
    setActiveTab(1);
  }, [sampleMean, sampleSize, standardDeviation, confidenceLevel, populationSD, getCriticalValue]);

  const copyResults = () => {
    if (!result) return;
    
    const text = `Single Mean Confidence Interval Results:
` +
      `Confidence Level: ${confidenceLevel}%
` +
      `Method: ${result.method}
` +
      `Lower Bound: ${result.lowerBound.toFixed(2)}
` +
      `Upper Bound: ${result.upperBound.toFixed(2)}
` +
      `Margin of Error: ${result.margin.toFixed(2)}
` +
      `Interpretation: ${result.interpretation}`;
    
    navigator.clipboard.writeText(text);
  };

  const resetForm = () => {
    setSampleMean('');
    setSampleSize('');
    setStandardDeviation('');
    setConfidenceLevel(95);
    setPopulationSD(false);
    setResult(null);
    setError('');
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <MeanIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          Single Mean Confidence Interval
        </Typography>
      </Box>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Calculate confidence intervals for a single population mean using t-distribution or z-distribution.
      </Typography>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Calculator" />
          <Tab label="Results" disabled={!result} />
          <Tab label="Guide" />
        </Tabs>
      </Box>

      {/* Calculator Tab */}
      <TabPanel value={activeTab} index={0}>
        <Grid container spacing={4}>
          {/* Input Section */}
          <Grid item xs={12} md={6}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Input Parameters
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Sample Size (n)"
                    value={sampleSize}
                    onChange={(e) => setSampleSize(e.target.value)}
                    type="number"
                    inputProps={{ min: 1, step: 1 }}
                    helperText="Number of observations in your sample"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Sample Mean (x̄)"
                    value={sampleMean}
                    onChange={(e) => setSampleMean(e.target.value)}
                    type="number"
                    inputProps={{ step: 'any' }}
                    helperText="The mean of your sample data"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={populationSD ? "Population Standard Deviation (σ)" : "Sample Standard Deviation (s)"}
                    value={standardDeviation}
                    onChange={(e) => setStandardDeviation(e.target.value)}
                    type="number"
                    inputProps={{ min: 0, step: 'any' }}
                    helperText={populationSD ? "Known population standard deviation" : "Sample standard deviation"}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Confidence Level</InputLabel>
                    <Select
                      value={confidenceLevel}
                      label="Confidence Level"
                      onChange={(e) => setConfidenceLevel(Number(e.target.value))}
                    >
                      <MenuItem value={90}>90%</MenuItem>
                      <MenuItem value={95}>95%</MenuItem>
                      <MenuItem value={99}>99%</MenuItem>
                      <MenuItem value={99.9}>99.9%</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Standard Deviation Type</InputLabel>
                    <Select
                      value={populationSD ? 'population' : 'sample'}
                      label="Standard Deviation Type"
                      onChange={(e) => setPopulationSD(e.target.value === 'population')}
                    >
                      <MenuItem value="sample">Sample SD (use t-distribution)</MenuItem>
                      <MenuItem value="population">Population SD (use z-distribution)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              
              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
              
              <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  onClick={calculateCI}
                  startIcon={<CalculateIcon />}
                  sx={{ flex: 1 }}
                >
                  Calculate CI
                </Button>
                <Button
                  variant="outlined"
                  onClick={resetForm}
                  sx={{ flex: 1 }}
                >
                  Reset
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Results Section */}
        <Grid item xs={12} md={6}>
          {result && (
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Results
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CopyIcon />}
                    onClick={copyResults}
                  >
                    Copy
                  </Button>
                </Box>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.1), borderRadius: 1 }}>
                      <Typography variant="subtitle2" color="primary" gutterBottom>
                        {confidenceLevel}% Confidence Interval
                      </Typography>
                      <Typography variant="h5" fontWeight="bold">
                        [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Lower Bound
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {result.lowerBound.toFixed(2)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Upper Bound
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {result.upperBound.toFixed(2)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Margin of Error
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      ±{result.margin.toFixed(2)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Chip 
                      label={result.method} 
                      color="primary" 
                      variant="outlined" 
                      size="small"
                    />
                  </Grid>
                </Grid>
                
                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Interpretation
                </Typography>
                <Typography variant="body2">
                  {result.interpretation}
                </Typography>
              </CardContent>
            </Card>
          )}
          
          {/* Information Card */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                About This Calculator
              </Typography>
              <Typography variant="body2" paragraph>
                This calculator computes confidence intervals for a single population mean. 
                The choice between t-distribution and z-distribution depends on whether the 
                population standard deviation is known.
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                When to use:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
                <li>• <strong>t-distribution:</strong> When population SD is unknown (most common)</li>
                <li>• <strong>z-distribution:</strong> When population SD is known or n ≥ 30</li>
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      </TabPanel>

      {/* Results Tab */}
      <TabPanel value={activeTab} index={1}>
        {result && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Confidence Interval Results
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
                    <Typography variant="subtitle2" color="primary">
                      {confidenceLevel}% Confidence Interval
                    </Typography>
                    <Typography variant="h5" sx={{ mt: 1 }}>
                      [{result.lowerBound.toFixed(2)}, {result.upperBound.toFixed(2)}]
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2, bgcolor: 'secondary.50' }}>
                    <Typography variant="subtitle2" color="secondary">
                      Margin of Error
                    </Typography>
                    <Typography variant="h5" sx={{ mt: 1 }}>
                      ±{result.margin.toFixed(2)}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 3 }} />
              
              <Typography variant="subtitle2" gutterBottom>
                Method: {result.method}
              </Typography>
              
              <Box sx={{ my: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Formula:
                </Typography>
                <Box
                  dangerouslySetInnerHTML={{ __html: renderFormula(result.formula) }}
                  sx={{ '& .katex': { fontSize: '1.1em' } }}
                />
              </Box>
              
              <Typography variant="body2" sx={{ mt: 2 }}>
                {result.interpretation}
              </Typography>
              
              <Button
                variant="outlined"
                startIcon={<CopyIcon />}
                onClick={copyResults}
                sx={{ mt: 2 }}
              >
                Copy Results
              </Button>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Guide Tab */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Single Mean Confidence Interval Guide
            </Typography>
            
            <Typography variant="body1" paragraph>
              A confidence interval for a single mean provides a range of plausible values 
              for the true population mean based on your sample data.
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              When to Use:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• You have a sample from a population and want to estimate the population mean</li>
              <li>• Your data is approximately normally distributed (especially important for small samples)</li>
              <li>• You want to quantify the uncertainty in your estimate</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Distribution Choice:
            </Typography>
            <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
              <li>• <strong>t-distribution:</strong> Use when population standard deviation is unknown (most common case)</li>
              <li>• <strong>z-distribution:</strong> Use when population standard deviation is known or sample size ≥ 30</li>
            </Typography>
            
            <Typography variant="subtitle2" gutterBottom sx={{ mt: 3 }}>
              Interpretation:
            </Typography>
            <Typography variant="body2">
              A 95% confidence interval means that if you repeated this sampling process many times, 
              approximately 95% of the intervals would contain the true population mean.
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>
    </Box>
  );
};

export default SingleMeanCI;