# Missing Data Handling Implementation

## Overview

This document outlines the comprehensive missing data handling system implemented in DataStatPro. The system provides variable-level configuration of missing value codes, centralized processing, and consistent integration across all statistical analysis components.

## Key Features

### 1. Variable-Level Missing Value Configuration
- Users can define custom missing value codes per variable (e.g., "na", "N.A.", "-", "999")
- Support for multiple missing value representations per variable
- Validation and suggestion system for missing value codes
- Backward compatibility with existing datasets

### 2. Centralized Missing Data Processing
- Consistent missing value detection across all components
- Automatic conversion of user-defined codes to standardized null values
- Enhanced data extraction functions that respect missing value codes
- Comprehensive missing data statistics and reporting

### 3. Visual Indicators
- Clear visual indicators for missing values in Data Editor
- Tooltips showing which missing value code was matched
- Distinctive styling (dashed borders, muted colors, italic text)

### 4. Statistical Analysis Integration
- All analysis components automatically exclude missing values
- Accurate sample size reporting after missing value exclusion
- Enhanced data extraction for statistical calculations
- Consistent handling across descriptive stats, t-tests, ANOVA, regression, etc.

## Implementation Details

### Database Schema Changes

#### Column Interface Enhancement
```typescript
export interface Column {
  id: string;
  name: string;
  description?: string;
  type: DataType;
  role: VariableRole;
  statistics?: ColumnStatistics;
  transformations?: Transformation[];
  categoryOrder?: string[];
  missingValueCodes?: string[]; // NEW: User-defined missing value codes
}
```

#### New Type Definitions
```typescript
export interface MissingValueInfo {
  originalValue: DataValue;
  isMissing: boolean;
  matchedCode?: string;
}

export interface CleanedDataResult {
  cleanedData: DataRow[];
  missingValueSummary: {
    totalMissing: number;
    missingByColumn: Record<string, number>;
    missingByRow: number[];
  };
}
```

### Core Utilities

#### Missing Data Detection
- `isMissingValue(value, column)`: Checks if a value is missing based on column configuration
- `cleanDataset(dataset)`: Converts all missing values to null for processing
- `getColumnMissingSummary(data, column)`: Provides comprehensive missing data statistics

#### Enhanced Data Extraction
- `extractNumericValuesWithMissingCodes(data, column)`: Extracts numeric values respecting missing codes
- `extractCategoricalValuesWithMissingCodes(data, column)`: Extracts categorical values respecting missing codes
- `getValidSampleSize(data, column)`: Returns count of non-missing values

#### Validation and Suggestions
- `validateMissingValueCodes(codes, columnType)`: Validates missing value code configuration
- `suggestMissingValueCodes(data, columnName)`: Suggests common missing patterns from data

### User Interface Components

#### Variable Editor Enhancements
- New "Missing Value Codes" section in variable configuration dialog
- Add/remove missing value codes with validation
- "Suggest from Data" functionality to detect common missing patterns
- Real-time validation with error and warning messages

#### Data Editor Visual Indicators
- Missing values displayed with "(missing)" text
- Dashed border styling with warning color theme
- Tooltips showing matched missing value code
- Distinctive visual treatment to clearly identify missing data

### Statistical Analysis Integration

#### Updated Components
1. **Descriptive Statistics**: Uses enhanced extraction, reports missing counts
2. **T-Tests**: Respects missing codes in all test types (one-sample, independent, paired)
3. **ANOVA**: Enhanced grouping with missing value awareness
4. **Correlation Analysis**: Pairwise deletion respecting missing codes
5. **Regression Analysis**: Automatic missing value exclusion

#### Analysis Assistant Integration
- Enhanced data quality assessment with missing value analysis
- Recommendations based on missing data patterns
- Integration with user-defined missing codes in quality reports

## Usage Examples

### Defining Missing Value Codes
```typescript
const column: Column = {
  id: 'survey-response',
  name: 'satisfaction',
  type: DataType.NUMERIC,
  role: VariableRole.DEPENDENT,
  missingValueCodes: ['na', 'N.A.', 'refused', '999', '-']
};
```

### Checking Missing Values
```typescript
const value = 'na';
const missingInfo = isMissingValue(value, column);
// Returns: { originalValue: 'na', isMissing: true, matchedCode: 'na' }
```

### Extracting Valid Data
```typescript
const numericValues = extractNumericValuesWithMissingCodes(dataset.data, column);
// Returns only valid numeric values, excluding all missing codes
```

## Backward Compatibility

The implementation maintains full backward compatibility:

1. **Existing Datasets**: Columns without `missingValueCodes` property work as before
2. **Default Behavior**: null, undefined, and empty strings are always treated as missing
3. **Legacy Components**: Existing analysis components continue to work without modification
4. **Data Storage**: No changes required to existing dataset storage format

## Performance Considerations

1. **Efficient Processing**: Missing value detection uses optimized string comparison
2. **Caching**: Missing value summaries can be cached for large datasets
3. **Lazy Evaluation**: Missing value analysis performed only when needed
4. **Memory Usage**: Minimal overhead for missing value code storage

## Testing

Comprehensive test suite covers:
- Missing value detection accuracy
- Data extraction correctness
- Statistical analysis integration
- User interface functionality
- Backward compatibility
- Performance with large datasets

## Future Enhancements

Potential future improvements:
1. **Pattern-Based Missing Codes**: Support for regex patterns in missing value codes
2. **Conditional Missing Values**: Context-dependent missing value definitions
3. **Missing Data Imputation**: Built-in imputation methods for missing values
4. **Advanced Missing Data Analysis**: MCAR/MAR/MNAR pattern detection and testing
5. **Export/Import**: Missing value code templates for reuse across datasets

## Migration Guide

For existing users:
1. **No Action Required**: Existing datasets continue to work without changes
2. **Optional Enhancement**: Users can add missing value codes to improve analysis accuracy
3. **Gradual Adoption**: Missing value codes can be added incrementally to variables as needed

## Support and Documentation

- User guide available in application help system
- Video tutorials for missing data configuration
- Best practices documentation for different data types
- Community forum for questions and discussions
